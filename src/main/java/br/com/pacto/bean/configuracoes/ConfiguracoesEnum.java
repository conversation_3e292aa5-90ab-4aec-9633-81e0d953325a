/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.configuracoes;

import br.com.pacto.util.UteisValidacao;

import java.util.Arrays;
import java.util.List;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public enum ConfiguracoesEnum {

    /**
     * ATENÇÃO!!! Lembrar de: 1. Criar a entrada nos arquivos de
     * Internacionalização (title.properties, msg.properties, etc) com o mesmo
     * NAME do Enum
     */
    PORC_ACIMA_NOTIFICAR(ConfiguracaoTipoEnum.INTEGER, null, "1","Informe a porcentagem mínima acima da carga que, quando o aluno alterar a carga no app para uma carga maior, o professor receba uma notificação informando sobre a alteração.", AbaConfiguracaoEnum.APLICATIVO,false),
    PORC_ABAIXO_NOTIFICAR(ConfiguracaoTipoEnum.INTEGER, null, "1","Informe a porcentagem mínima abaixo da carga que, quando o aluno alterar a carga no app para uma carga menor, o professor receba uma notificação informando sobre a alteração.", AbaConfiguracaoEnum.APLICATIVO,false),
    SUGERIR_TIPO_ALTERNADO(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.GLOBAL,true),
    SUGERIR_DESCANSO(ConfiguracaoTipoEnum.STRING, "99:99", "00:30","", AbaConfiguracaoEnum.GLOBAL,true),
    AGENDA_NOVA_ABA(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.GLOBAL,true),
    DIAS_ANTES_VENCIMENTO(ConfiguracaoTipoEnum.INTEGER, null, "1"," Aqui deve ser informado a quantidade de dias que antecederão o término dos programas dos alunos, o valor informado será considerado no Relatório de Carteiras dos Professores e nos filtros de alunos com treino a vencer/vencido.", AbaConfiguracaoEnum.GESTAO,true),
    DIAS_DEPOIS_VENCIMENTO(ConfiguracaoTipoEnum.INTEGER, null, "1","", AbaConfiguracaoEnum.NENHUMA,true),
    PERMITIR_APENAS_ALUNOS_ATIVOS(ConfiguracaoTipoEnum.BOOLEAN, null,"", "", AbaConfiguracaoEnum.GLOBAL,true),
    USAR_GESTAO_PERSONAL(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.NENHUMA,true),
    NR_AULA_EXPERIMENTAL_ALUNO(ConfiguracaoTipoEnum.INTEGER, null, "10","Este número define a quantidade de aulas experimentais que cada aluno possuí. São consideradas aulas experimentais as turmas com modalidades que o aluno não adquiriu em seu contrato.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    MINUTOS_AGENDAR_COM_ANTECEDENCIA(ConfiguracaoTipoEnum.MINUTOS, null, "0","Você poderá escolher entre minutos, horas e dias o tempo que o aluno pode marcar sua aula com antecedência, ou seja, em quanto tempo o aluno pode agendar antes do início da aula.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    PERMITE_ENVIO_RELATORIOS(ConfiguracaoTipoEnum.BOOLEAN, null, "false","Se habilitado, permite que o aluno clique em enviar relatório de pontos pelo Retira Fichas Web. O relatório será enviado para o e-mail do aluno. ", AbaConfiguracaoEnum.SALA_CHEIA, true),
    DIAS_MOSTRAR_TOTEM(ConfiguracaoTipoEnum.COMBO, null, "43200","Configuração que determina por quanto tempo a partir da data da consulta, as aulas irão ser exibidas no Retira Fichas e nos Aplicativos. Ex.: Caso configure 4 horas, apenas serão apresentadas as aulas que acontecerão em 4 horas.", AbaConfiguracaoEnum.SALA_CHEIA,true,
            new SelectItem(15, "15 minutos"),new SelectItem(60, "1 hora"),new SelectItem(240, "4 horas"),
            new SelectItem((24*60), "Dia"), new SelectItem((7*24*60), "Semana"), new SelectItem((30*24*60), "Mês")),
    VALOR_VERMELHO_BAIXA_FREQUENCIA(ConfiguracaoTipoEnum.INTEGER, null, "30","Essa configuração é definida em percentual e determina qual é a meta mínima para uma aula de baixa frequência. As aulas que não atingirem essa meta, serão consideradas aulas vermelhas.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    VALOR_VERMELHO_ALTA_FREQUENCIA(ConfiguracaoTipoEnum.INTEGER, null, "50","Essa configuração é definida em percentual e determina qual é a meta mínima para uma aula de alta frequência. As aulas que não atingirem essa meta, serão consideradas aulas vermelhas.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    VALOR_VERDE_BAIXA_FREQUENCIA(ConfiguracaoTipoEnum.INTEGER, null, "60","Configuração que sugere para o gestor no momento da criação de uma nova aula, qual será a meta de baixa frequência.  A meta é definida em percentual e pode ser alterada no momento do cadastro.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    VALOR_VERDE_ALTA_FREQUENCIA(ConfiguracaoTipoEnum.INTEGER, null, "100","Configuração que sugere para o gestor no momento da criação de uma nova aula, qual será a meta de alta frequência.  A meta é definida em percentual e pode ser alterada no momento do cadastro.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    USAR_SALA_CHEIA(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.NENHUMA,true),
    VALIDAR_MODALIDADE(ConfiguracaoTipoEnum.BOOLEAN, null, "true","Caso esteja marcado, apenas alunos com a modalidade no contrato poderão fazer a aula da modalidade correspondente, se o aluno não tiver a modalidade, será contabilizado como aula experimental.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    MOBILE_SEMPRE_ATUALIZAR_CARGA_FICHA(ConfiguracaoTipoEnum.BOOLEAN, null, "false","Se marcada, sempre que o aluno alterar a carga no aplicativo, a carga será automaticamente alterada também na ficha do aluno.\nAtenção: Essa configuração é válida apenas para o aplicativo antigo", AbaConfiguracaoEnum.APLICATIVO, true),
    EMITIR_FICHA_APOS_VENCIMENTO_TREINO(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.GLOBAL,true),
    AGENDA_ABRIR_LISTA(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.GLOBAL,true),
    PERIODO_USADO_BI(ConfiguracaoTipoEnum.INTEGER, null, "30","", AbaConfiguracaoEnum.GESTAO,true),
    USAR_COMPLEMENTO_REPETICAO_CARGA(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.GLOBAL,true),
    DURACAO_ALUNO_NA_ACADEMIA(ConfiguracaoTipoEnum.INTEGER, null, "120","", AbaConfiguracaoEnum.GLOBAL,true),
    LOGIN(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.EMAIL,true),
    SENHA(ConfiguracaoTipoEnum.SENHA, null, "","", AbaConfiguracaoEnum.EMAIL,true),
    REMETENTE(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.EMAIL,true),
    EMAIL_PADRAO(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.EMAIL,true),
    MAIL_SERVER(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.EMAIL,true),
    CONEXAO_SEGURA(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.EMAIL,true),
    INICIAR_TLS(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.EMAIL,true),
    VENCIMENTO_APENAS_ATIVOS(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.GESTAO,false),

    NOTIFICACAO(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.NOTIFICACAO,true),
    INATIVOS_A_X_DIAS(ConfiguracaoTipoEnum.INTEGER, null, "0","", AbaConfiguracaoEnum.GESTAO,true),
    HORARIO_DEFAULT_AC(ConfiguracaoTipoEnum.INTEGER, null, "5","Configuração que determina em qual horário agenda será iniciada. Por padrão, a agenda se inicia às 5 horas. Deverá ser configurado para as academias que iniciam suas atividades antes das 5 horas ou aquelas que são 24 horas.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    HORARIO_DEFAULT_TW(ConfiguracaoTipoEnum.INTEGER, null, "5","", AbaConfiguracaoEnum.GLOBAL,true),
    NUMERO_IMPRESSAO_FICHA(ConfiguracaoTipoEnum.INTEGER, null, "0","", AbaConfiguracaoEnum.GLOBAL,true),
    USUARIO_SERVICE(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.ATENDIMENTO,true),
    SENHA_SERVICE(ConfiguracaoTipoEnum.SENHA, null, "","", AbaConfiguracaoEnum.ATENDIMENTO,true),
    EMAIL_SERVICE(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.ATENDIMENTO,true),
    TELEFONE_SERVICE(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.ATENDIMENTO,true),
    BLOQUEAR_IMPRESSAO_FICHA_APOS_TODAS_EXECUCOES(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.GLOBAL,true),
    MOSTRAR_NOME_PROFESSORES_GRAFICO(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.NENHUMA,true),
    PERMITIR_REAGENDAMENTO_POR_ALUNO(ConfiguracaoTipoEnum.BOOLEAN, null, "true","Com essa opção marcada, o aplicativo mobile irá permitir que o aluno realize o reagendamento de algum tipo de evento que o professor agendou o aluno." +
            "Ex: O aluno poderia reagendar pelo aplicativo uma Avaliação física.", AbaConfiguracaoEnum.APLICATIVO,true),
    CALCULAR_NR_AULAS_PREVISTAS_AUTOMATICO(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.GLOBAL,true),
    TIPO_AULAS_AGENDA_TOTAL(ConfiguracaoTipoEnum.STRING, null, "TODAS","", AbaConfiguracaoEnum.NENHUMA,true),
    /**
     * O módulo aulas é um exemplo de grupo de configuração. Para criar um grupo de configuração
     * As configurações filhas(que compõe o grupo, devem ser definidas em sequência,
     *  e por último deve ser definido o grupo(Verifique que há um construtor específico para o tipo Grupo de Configurações
     */
    MODULO_AULAS_ABA_SALDO(ConfiguracaoTipoEnum.BOOLEAN, true, null, "false",
            "Informa a quantidade/saldo de créditos que o aluno possui (esta opção só se aplica para planos do tipo Crédito Treino", AbaConfiguracaoEnum.APLICATIVO, null),
    MODULO_AULAS_ABA_TURMAS(ConfiguracaoTipoEnum.BOOLEAN, true,  null, "false",
            "Permite aos alunos que estão alocados em turmas, realizar reposições e marcações de aulas diretamente pelo app", AbaConfiguracaoEnum.APLICATIVO, IconeTabBarAppEnum.AULAS_TURMAS),
    MODULO_AULAS_ABA_AULAS_COLETIVAS(ConfiguracaoTipoEnum.BOOLEAN, true,  null, "false",
            "Permite ao aluno, agendar aulas em grupo (aplicar somente se utilizar o módulo Aula Cheia)", AbaConfiguracaoEnum.APLICATIVO, IconeTabBarAppEnum.AULAS_TURMAS),
    MODULO_AULAS(null, "false","",AbaConfiguracaoEnum.APLICATIVO, IconeTabBarAppEnum.AULAS_TURMAS, MODULO_AULAS_ABA_SALDO, MODULO_AULAS_ABA_TURMAS,MODULO_AULAS_ABA_AULAS_COLETIVAS),
    //Fim do grupo de Configurações

    MODULO_TREINAR(ConfiguracaoTipoEnum.BOOLEAN, null, "true",
            "Habilita o módulo treinar na tela principal do aplicativo, permitindo ao aluno a execução do seu treino",
            AbaConfiguracaoEnum.APLICATIVO, IconeTabBarAppEnum.TREINO),
    
    DATA_ULTIMA_ATUALIZACAO_ALUNO_ZW(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.NENHUMA,true),
    SOMENTE_ALUNO_CONTRATO_DESISTENTE(ConfiguracaoTipoEnum.BOOLEAN, null, "true","Com a configuração marcada, apenas serão considerados alunos com a situação de Inativo Desistente no indicador, caso deseje incluir no cálculo os alunos vencidos, cancelados e trancados, desmarque essa configuração.", AbaConfiguracaoEnum.GESTAO, true),
    VALIDAR_HORARIO_CONTRATO(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Caso esteja marcado, o aluno não conseguirá marcar uma aula fora do horário de seu contrato, a menos que seja uma aula experimental.", AbaConfiguracaoEnum.SALA_CHEIA,true),
    ALUNO_MARCAR_PROPRIA_AULA(ConfiguracaoTipoEnum.BOOLEAN, null, "true",
            "Com esta opção marcada o próprio aluno poderá participar da aula desejada, marcando-a através do aplicativo ou do RetiraFicha. Com a configuração desmarcada, ao clicar em uma aula disponível, o aplicativo notificar o aluno que a operação não poderá ser realizada. No caso do RetiraFicha, será impresso apenas o QRCODE ao clicar na aula desejada.", AbaConfiguracaoEnum.APLICATIVO, true),
    AGRUPAMENTO_SERIES_SET(ConfiguracaoTipoEnum.BOOLEAN, null, "false","Ao marcar a configuração as atividades que são do tipo BI-set e TRI-set serão agrupadas e enviadas para os aplicativos/retira fichas como uma única atividade.", AbaConfiguracaoEnum.GLOBAL, true),
    NUMERO_DIAS_NOTIFICAR_TREINO_VENCIDO(ConfiguracaoTipoEnum.INTEGER, null, "0","Essa configuração define ate quantos dias o cliente deve receber a notificação de treino vencido.", AbaConfiguracaoEnum.NOTIFICACAO, true),

    TROCA_NOMENCLATURA_CROSSFIT(ConfiguracaoTipoEnum.BOOLEAN,null,"false",
            "Ao marcar esta opção, ao invés do sistema trabalhar com as nomenclaturas padrões do Crossfit, será exibido outras nomenclaturas conforme exemplo:\n" +
            "WOD = Objetivo,\n" +
            "TIPO WOD = Periodização,\n" +
            "OPÇÕES TIPO WOD = Bloco, Técnico, Bloco força lenta, Bloco força rápida, Especial.",AbaConfiguracaoEnum.GLOBAL,true),
    UTILIZAR_AVALIACAO_INTEGRADA(ConfiguracaoTipoEnum.BOOLEAN,null,"false","",AbaConfiguracaoEnum.GLOBAL,true),
    //avaliacao fisica
    LANCAR_AGENDAMENTO_PROXIMA_AVALIACAO(ConfiguracaoTipoEnum.BOOLEAN, null, "true",
            "Esta configuração permite que o sistema lance um agendamento ao informar a data da próxima avaliação física.", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    LANCAR_PRODUTO_AVALIACAO(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Com esta opção marcada, o sistema lançará automaticamente a venda de um produto de avaliação física para o aluno.", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    VALIDAR_PRODUTO_AVALIACAO(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Caso esteja marcado, somente será possível realizar uma avaliação física para alunos que tenham um produto de avaliação física vigente e quitado.", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    PRODUTO_AVALIACAO(ConfiguracaoTipoEnum.INTEGER, null, "0",
            "Escolha o produto a ser validado/lançado para a avaliação física", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),


    CFG_OBJETIVOS_ANAMNESE(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_PESO_ALTURA_PA_FC(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_PARQ(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_DOBRAS_CUTANEAS(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_PERIMETRIA(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_COMPOSICAO_CORPORAL(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_FLEXIBILIDADE(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_POSTURA(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_RML(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_VO2MAX(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_RECOMENDACOES(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_VENTILOMETRIA(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_TESTES_CAMPO(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_TESTE_BIKE(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_SOMATOTIPIA(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    VALIDAR_AGENDA_AULACHEIA(ConfiguracaoTipoEnum.BOOLEAN, null, "true",
            "Quando o aluno através do aplicativo ou o consultor pelo sistema, tentar incluir um evento da agenda do treino, o sistema validará se o aluno possui uma aula coletica no mesmo horário e vice-versa. O sistema não irá permitir a inclusão da operação informando que o aluno já possui compromisso naquele horário. Caso não queira esta validação, basta desmarcar esta configuração", AbaConfiguracaoEnum.GLOBAL, true),
    INTEGRACAO_SISTEMA_OLYMPIA(ConfiguracaoTipoEnum.BOOLEAN, null, "false","Olympia é um sistema do próprio SESI. Marque esta opção para ativar a integração com o sistema Olympia e o sistema irá buscar os dados dos alunos que estão no Olympia. Ao marcar, será habilitado uma nova aba no menu lateral \"Integração Olympia\" para que seja informado os parâmetros de integração.\n", AbaConfiguracaoEnum.GLOBAL, true),
    URL_SISTEMA_OLYMPIA(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.OLYMPIA,true),
    TOKEN_SISTEMA_OLYMPIA(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.OLYMPIA,true),
    USUARIO_SISTEMA_OLYMPIA(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.OLYMPIA,true),
    SENHA_SISTEMA_OLYMPIA(ConfiguracaoTipoEnum.SENHA, null, "","", AbaConfiguracaoEnum.OLYMPIA,true),
    DATA_GERACAO_TOKEN_SISTEMA_OLYMPIA(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.NENHUMA,true),
    CFG_TESTE_QUEENS(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    AGENDA_ABRIR_VAZIA(ConfiguracaoTipoEnum.BOOLEAN, null, "false","Com essa configuração marcada, a agenda irá abrir com nenhum tipo de evento selecionado.", AbaConfiguracaoEnum.GLOBAL, true),
    HABILITAR_CROSSFIT(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Habilita o módulo Crossfit na barra de ferramentas do aplicativo, permitindo o aluno visualizar o seu WOD", AbaConfiguracaoEnum.APLICATIVO, IconeTabBarAppEnum.CROSSFIT),
    MODULO_PRINCIPAL_APLICATIVO(ConfiguracaoTipoEnum.RADIO, null, "TREINO(MODULO_TREINAR)","Define qual módulo é o principal na barra de ferramentas do aplicativo", AbaConfiguracaoEnum.APLICATIVO,false,
            new SelectItem("NENHUM", "Nenhum"), new SelectItem("TREINO(MODULO_TREINAR)", "Treino"),new SelectItem("CROSSFIT(HABILITAR_CROSSFIT)", "Crossfit")),
    PRODUTO_INSCRICAO_GAME(ConfiguracaoTipoEnum.PRODUTO, null, "0",
            "Escolha o produto a ser lançado para a inscrição de alunos em game de Crossfit", AbaConfiguracaoEnum.GLOBAL,true),
    MINUTOS_DESMARCAR_COM_ANTECEDENCIA(ConfiguracaoTipoEnum.MINUTOS, null, "0","Você poderá escolher entre minutos, horas e dias o tempo que o aluno pode desmarcar sua aula com antecedência, ou seja, em até quanto tempo o aluno pode desmarcar antes do início da aula.", AbaConfiguracaoEnum.SALA_CHEIA, true),

    LANCAR_PRODUTO_AVALIACAO_DATA_VENCIMENTO(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Caso esteja marcado, a data de vencimento da parcela do produto avaliação será igual a data do agendamento. Caso desmarcado será a data atual.", AbaConfiguracaoEnum.AVALIACAO_FISICA, true),
    PROIBIR_MARCAR_AULA_PARCELA_VENCIDA(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Com esta opção marcada o aluno será impedido de marcar aulas coletivas pelo Aplicativo, Agenda e RetiraFicha, caso exista alguma mensagem de bloqueio do tipo Parcela Vencida em Aberto.",
            AbaConfiguracaoEnum.APLICATIVO, true),
    NAO_EXIBIR_NUMERO_DE_VEZES_NO_APP(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Com esta opção marcada não deve aparecer o número de vezes da Modalidade que estiver no contrato do aluno.",
            AbaConfiguracaoEnum.APLICATIVO,true),

    USAR_NOVA_MONTAGEM(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.GLOBAL,true),
    DIAS_SEM_OBSERVACAO_CONSIDERAR_DESACOMPANHADO(ConfiguracaoTipoEnum.INTEGER, null, "0",
            "Caso o valor aqui seja maior que 0, o filtro de alunos Desacompanhados passa a olhar para os alunos que estão a X dias sem " +
                    "ter observações registradas.", AbaConfiguracaoEnum.GLOBAL,true),
    USAR_INTEGRACAO_BIOIMPEDANCIA(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.GLOBAL,true),
    PERMITIR_ALUNO_MARCAR_AULA_POR_TIPO_MODALIDADE(ConfiguracaoTipoEnum.BOOLEAN, null, "false","ATENÇÃO: ao selecionar a troca de aulas baseada no TIPO DE MODALIDADE, o aluno só conseguirá trocar de aula se o tipo de modalidade da aula for o mesmo da aula cadastrada em seu contrato.", AbaConfiguracaoEnum.SALA_CHEIA,true),
    URL_GOOGLE_PLAY(ConfiguracaoTipoEnum.STRING, null, null, "Link da loja do APP na Google Play que será encaminhado no E-email de envio automatico", AbaConfiguracaoEnum.APLICATIVO,false),
    URL_ITUNES(ConfiguracaoTipoEnum.STRING, null, null, "Link da loja do APP na Apple Store que será encaminhado no E-email de envio automatico", AbaConfiguracaoEnum.APLICATIVO,false),
    NOME_APLICATIVO_PARA_ENVIO_EMAIL(ConfiguracaoTipoEnum.STRING, null, null, "", AbaConfiguracaoEnum.APLICATIVO,true),
    LINK_APP_EMAIL(ConfiguracaoTipoEnum.RADIO, null, "TREINO","Define qual vai ser o aplicativo que será redirecionado no e-mail enviado ao aluno ou colaborador",
            AbaConfiguracaoEnum.APLICATIVO,true,
            new SelectItem("TREINO", "Treino"), new SelectItem("MINHA_ACADEMIA", "Minha academia"), new SelectItem("MEU_BOX", "Meu box")),
    LANCAR_ATESTADO_ZILLYONWEB(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    PRODUTO_ATESTADO(ConfiguracaoTipoEnum.INTEGER, null, "0", "Escolha o produto atestado", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    TRAZER_ULTIMA_AVALIACAO_INTEGRADA(ConfiguracaoTipoEnum.BOOLEAN, null, "false", "Trazer a Última Avaliação Integrada Preenchida Quando Criar uma Nova", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    CFG_AVALIACAO_INTEGRADA(ConfiguracaoTipoEnum.BOOLEAN, null, "false", "Utilizar a Avaliação Integrada como aba na Avaliação Física", AbaConfiguracaoEnum.NENHUMA,true),
    USAR_PRESSAO_SISTOLICA_DIASTOLICA(ConfiguracaoTipoEnum.BOOLEAN, null, "false","Ao habilitar essa configuração, dentro da Aba: \"Peso, Altura, PA e FC\" da avaliação, será exibido dois campos separados para preenchimento da Pressão arterial, sendo pressão arterial sistólica e diastólica.", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    USAR_ORDEM_DOBRAS_CUTANEAS(ConfiguracaoTipoEnum.BOOLEAN, null, "false","Quando selecionado esta opção, será liberado uma configuração para reordenar os campos das dobras. Com isso, irá mudar a forma de visualização das dobras cutâneas, passando a ser exibida como uma lista.", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    DESCRICAO_AVALIACAO_INTEGRADA(ConfiguracaoTipoEnum.STRING, null, null, "Campo exclusivo para informação padrão, da avaliação integrada do aluno. Maximo 150 caracteres.", AbaConfiguracaoEnum.AVALIACAO_FISICA, null),
    REDE_SOCIAL_FACEBOOK(ConfiguracaoTipoEnum.STRING, null, null, "Utilizado no layout customizado da avaliação integrada", AbaConfiguracaoEnum.GLOBAL, null),
    REDE_SOCIAL_INSTAGRAM(ConfiguracaoTipoEnum.STRING, null, null, "Utilizado no layout customizado da avaliação integrada", AbaConfiguracaoEnum.GLOBAL, null),
    MINUTOS_AGENDAR_COM_ANTECEDENCIA_PERFIL(ConfiguracaoTipoEnum.MINUTOS, null, "0"," Esta permissão funciona apenas em perfis com \"Usuário pode marcar aula com antecedência\" habilitados. Tempo em que o usuário pode marcar sua aula com antecedência, ou seja, em quanto tempo o usuário pode agendar antes do iní\u00ADcio da aula.", AbaConfiguracaoEnum.SALA_CHEIA, false),
    MODELO_EVOLUCAO_FISICA(ConfiguracaoTipoEnum.RADIO, null, "PRIMEIRA_ULTIMA","Você pode escolher se deseja comparar a primeira avaliação com a última avaliação ou pode escolher entre primeira, penúltima e última. Lembrando que somente a opção de primeira, penúltima e última levará em consideração todos os itens da evolução.",
            AbaConfiguracaoEnum.AVALIACAO_FISICA,false,
            new SelectItem("PRIMEIRA_ULTIMA", "Primeira e Última"), new SelectItem("PRIMEIRA_PENULTIMA_ULTIMA", "Primeira, penúltima e última")),
    CONTROLAR_POR_FREEPASS(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Ao marcar esta opção o aluno poderá realizar aula experimental apenas se tiver um freepass lançado, além disso" +
                    " alunos de gympass poderão ingressar nas aulas coletivas após efetuar o check-in.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    SMS_NOTIFICACAO(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.GLOBAL,true),
    DESCONTINUAR_TREINO(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.GLOBAL,true),
    INTEGRACOES(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.GLOBAL,true),
    MGB(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.GLOBAL,true),
    BLOQUEAR_MESMO_AMBIENTE(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Com esta configuração marcada, a agenda não vai permitir que sejam realizadas marcações em aulas distintas num mesmo ambiente e no mesmo horário.",
            AbaConfiguracaoEnum.SALA_CHEIA,true),
    HABILITAR_FILA_ESPERA(ConfiguracaoTipoEnum.BOOLEAN, null, "true", "O recurso  fila de espera terá a função de inserir  o aluno  em uma lista de espera, com a função de identificar que a aula está cheia e bloquear novos agendamentos, perguntando se o aluno deseja entrar na fila de espera ou agendar em outro horário.", AbaConfiguracaoEnum.APLICATIVO, true),
    USAR_INTEGRACAO_MYZONE(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.GLOBAL,true),
    PROIBIR_BUSCAR_PROGRAMA_PARCELA_VENCIDA(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Com esta opção marcada o aluno será impedido de buscar programa pelo Aplicativo, Agenda e RetiraFicha, caso exista alguma mensagem de bloqueio do tipo Parcela Vencida em Aberto.",
            AbaConfiguracaoEnum.APLICATIVO, true),
    VISUALIZAR_MENSAGEM_AVISO(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),
    BLOQUEAR_AULA_COLETIVA_NAO_PERTENCE_MODALIDADE(ConfiguracaoTipoEnum.BOOLEAN, null, "false","Com esta configuração marcada, somente aulas coletivas que pertencem a modalidade será apresentada.", AbaConfiguracaoEnum.SALA_CHEIA,true),
    DESATIVAR_TELA_ALUNO_TREINO(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Usar somente a versão mais atualizada da tela do aluno", AbaConfiguracaoEnum.GLOBAL, true),
    CONFIGURACOES_TREINO_REPLICAR_REDE_EMPRESA(ConfiguracaoTipoEnum.BOOLEAN, null, "false",
            "Visualizar aba replicar configuracoes rede empresa", AbaConfiguracaoEnum.GLOBAL, true),
    APLICATIVO_PERSONALIZADO(ConfiguracaoTipoEnum.BOOLEAN,null, "false","",AbaConfiguracaoEnum.NENHUMA, true),
    APLICATIVO_PERSONALIZADO_NOME(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.NENHUMA,true),
    APLICATIVO_PERSONALIZADO_URL(ConfiguracaoTipoEnum.STRING, null, "","", AbaConfiguracaoEnum.NENHUMA,true),
    DESMARCAR_AULAS_FUTURAS_PARCELA_ATRASADA(ConfiguracaoTipoEnum.INTEGER, null, "0","", AbaConfiguracaoEnum.SALA_CHEIA, true),
    CFG_RML_SEPARADO(ConfiguracaoTipoEnum.BOOLEAN, null, "false","Caso esteja marcado, a tabela referência para o RML será diferente para homem e mulher ",AbaConfiguracaoEnum.NENHUMA, true),
    CFG_RML_OPCOES(ConfiguracaoTipoEnum.BOOLEAN, null, "true","Selecione as opções para o RML",AbaConfiguracaoEnum.NENHUMA, true),
    PERMITIR_VISUALIZAR_WOD(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),
    PERMITIR_VISUALIZAR_CREF(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),
    PERMITIR_VISUALIZAR_PAR_Q_10_PERGUNTAS(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),

    LAST_DDL_UPDATE(ConfiguracaoTipoEnum.STRING, null, "", "", AbaConfiguracaoEnum.NENHUMA, false),
    MANTER_REPOSICAO_AULA_COLETIVA(ConfiguracaoTipoEnum.BOOLEAN, null, "false","", AbaConfiguracaoEnum.SALA_CHEIA, true),
    LIMITE_DIAS_REPOSICAO_AULA_COLETIVA(ConfiguracaoTipoEnum.INTEGER, null, "0","", AbaConfiguracaoEnum.SALA_CHEIA, true),
    OBRIGAR_CAMPOS_DOBRAS_BIOIMPEDANCIA(ConfiguracaoTipoEnum.BOOLEAN, null, "false","Quando selecionado esta opção, será obrigatório informar os campos que contém * nas dobras para o protocolo de bioimpedância.", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    HABILITAR_VER_WOD_TODAS_EMPRESAS_APP(ConfiguracaoTipoEnum.BOOLEAN, null, "true", "Esta opção permite os alunos verem o WOD de outras unidades da sua empresa. Caso esteja desabilitada, os alunos verão apenas o WOD da unidade em que estão logados no aplicativo.", AbaConfiguracaoEnum.APLICATIVO, IconeTabBarAppEnum.CROSSFIT),
    PERMITIR_VISUALIZAR_AVISO_DE_PENDENCIAS(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),
    ESCOLHER_TIPO_LISTA_RAPIDA_ACESSOS(ConfiguracaoTipoEnum.BOOLEAN, null, "true",
            "Permitir ao usuário escolher se quer ver todos os acessos ou apenas dos que possuem algum vínculo consigo na lista rápida de acessos", AbaConfiguracaoEnum.GLOBAL, true),
    NR_VALIDAR_VEZES_MODALIDADE(ConfiguracaoTipoEnum.INTEGER, null, "0","Restrinja quantos agendamentos concomitantes de aula o aluno pode marcar previamente dentro da mesma modalidade. Caso você não deseje restringir, deixe em branco, isso permitirá ao aluno marcar quantas aulas quiser na mesma modalidade.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    QTD_FALTAS_BLOQUEIO_ALUNO(ConfiguracaoTipoEnum.INTEGER, null, "0", "Defina quantas faltas impedirá o aluno de realizar um novo agendamento", AbaConfiguracaoEnum.NENHUMA, true),
    QTD_TEMPO_BLOQUEIO_ALUNO(ConfiguracaoTipoEnum.INTEGER, null, "0", "Defina o tempo de espera que o aluno precisa cumprir até ser liberado para agendar outra aula", AbaConfiguracaoEnum.NENHUMA, true),
    MINUTOS_CANCELAR_COM_ANTECEDENCIA(ConfiguracaoTipoEnum.MINUTOS, null, "0","Você poderá escolher entre minutos, horas e dias o tempo que o cliente pode cancelar sua locação com antecedência, ou seja, em até quanto tempo o cliente pode cancelar antes do início da locação.", AbaConfiguracaoEnum.GLOBAL, true),
    PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),
    FORCAR_CRIAR_NOVO_PROGRAMA(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "Forçar criação de novo programa na renovação do treino", AbaConfiguracaoEnum.GLOBAL,true),
    PERMITIR_VISUALIZAR_LEI_PARQ(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),
    TEMPO_APROVACAO_AUTOMATICA(ConfiguracaoTipoEnum.INTEGER, null, "0","", AbaConfiguracaoEnum.GLOBAL,true),
    TEMPO_MAXIMO_REVISAO(ConfiguracaoTipoEnum.INTEGER, null, "0","", AbaConfiguracaoEnum.GLOBAL,true),
    HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),
    DESCONTAR_CREDITO_AO_MARCAR_AULA_SEM_CONFIRMAR_PRESENCA(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),
    PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA_PIX(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "Com esta opção marcada, o aluno será impedido de realizar o check-in em aulas coletivas pelo Aplicativo, Agenda e RetiraFicha, caso o plano possua a forma de pagamento PIX e a primeira parcela ainda não tenha sido paga.", AbaConfiguracaoEnum.SALA_CHEIA,true),
    UTILIZAR_NUMERACAO_SEQUENCIAL_IDENTIFICADOR_EQUIPAMENTO(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "Quando esta configuração estiver habilitada, o equipamento no mapa de reservas será identificado por uma numeração sequencial em vez da sua posição no mapa.", AbaConfiguracaoEnum.SALA_CHEIA,true),
    PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "Com esta opção marcada, o aluno será impedido de realizar o check-in em aulas coletivas pelo Aplicativo, Agenda e RetiraFicha, caso a primeira parcela ainda não tenha sido paga.", AbaConfiguracaoEnum.SALA_CHEIA,true),
    BLOQUEAR_GERAR_REPOSICAO_AULA_JA_REPOSTA(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "Caso esteja habilitada, o aluno não conseguirá utilizar uma reposição que anteriormente já foi desmarcada", AbaConfiguracaoEnum.SALA_CHEIA,true),
    QUANTIDADE_FICHAS_HABILITAR_TREINO_IA(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),
    NIVEL_INICIANTE(ConfiguracaoTipoEnum.INTEGER, null, "0","", AbaConfiguracaoEnum.GLOBAL,true),
    NIVEL_INTERMEDIARIO(ConfiguracaoTipoEnum.INTEGER, null, "0","", AbaConfiguracaoEnum.GLOBAL,true),
    NIVEL_AVANCADO(ConfiguracaoTipoEnum.INTEGER, null, "0","", AbaConfiguracaoEnum.GLOBAL,true),
    PERMITIR_ALUNO_CRIAR_TREINO_IA_APP(ConfiguracaoTipoEnum.BOOLEAN, null,"false", "", AbaConfiguracaoEnum.GLOBAL,true),
    CFG_IMPORTACAO_BIOSANNY(ConfiguracaoTipoEnum.BOOLEAN, null, "true","", AbaConfiguracaoEnum.AVALIACAO_FISICA,true),
    MINUTOS_ALTERAR_EQUIPAMENTO_COM_ANTECEDENCIA(ConfiguracaoTipoEnum.MINUTOS, null, "0","Você poderá escolher entre minutos, horas e dias o tempo que o aluno pode alterar o equipamento com antecedência, ou seja, em quanto tempo o aluno pode alterar o equipamento antes do início da aula.", AbaConfiguracaoEnum.SALA_CHEIA, true),
    ;


    private ConfiguracaoTipoEnum tipo;
    private String mascara;
    private String valorPadrao;
    private AbaConfiguracaoEnum aba;
    private List<SelectItem> itens;
    private String textoReferencia;
    private List<ConfiguracoesEnum> configsFilhas;
    private boolean configFilha;
    private IconeTabBarAppEnum icone;
    private boolean aparecer;

    //Usado para grupo de configurações. A configuração pai deve ser tipo boolean, pois ela habilita as filhas
    ConfiguracoesEnum(final String mascara, final String valorPadrao,String textoReferencia,
                      final AbaConfiguracaoEnum aba, IconeTabBarAppEnum icone, ConfiguracoesEnum... filhos) {
        this(ConfiguracaoTipoEnum.GROUP, mascara, valorPadrao, textoReferencia, aba,true);
        this.configsFilhas = Arrays.asList(filhos);
        this.icone = icone;
    }

    ConfiguracoesEnum(ConfiguracaoTipoEnum tipo, boolean ehConfigFilha, final String mascara, 
            final String valorPadrao,String textoReferencia,
            final AbaConfiguracaoEnum aba, IconeTabBarAppEnum icone, SelectItem ... itens) {
        this(tipo, mascara, valorPadrao, textoReferencia, aba,true, itens);
        this.configFilha = ehConfigFilha;
        this.icone = icone;
    }
    
    ConfiguracoesEnum(ConfiguracaoTipoEnum tipo, final String mascara, final String valorPadrao,String textoReferencia,
                      final AbaConfiguracaoEnum aba, IconeTabBarAppEnum icone) {
        this(tipo, mascara, valorPadrao, textoReferencia, aba,true);
        this.icone = icone;
    }

    ConfiguracoesEnum(ConfiguracaoTipoEnum tipo, final String mascara, final String valorPadrao, String textoReferencia,
                      final AbaConfiguracaoEnum aba, boolean aparecer, SelectItem... itens) {
        this.tipo = tipo;
        this.mascara = mascara;
        this.valorPadrao = valorPadrao;
        this.aba = aba;
        this.itens = Arrays.asList(itens);
        this.textoReferencia = textoReferencia;
        this.aparecer = aparecer;
    }

    public String getLabelCfgSelect(Integer valor){
        for(SelectItem si : itens){
            if(((Integer)si.getValue()).equals(valor)){
                return si.getLabel();
            }
        }
        return "";
    }

    public static ConfiguracoesEnum getFromOrdinal(Integer cod) {
        for (ConfiguracoesEnum cfg : ConfiguracoesEnum.values()) {
            if (cod != null && cfg.ordinal() == cod) {
                return cfg;
            }
        }
        return null;

    }

    public static ConfiguracoesEnum getFromName(String name) {
        for (ConfiguracoesEnum cfg : ConfiguracoesEnum.values()) {
            if (!UteisValidacao.emptyString(name) && name.equals(cfg.name())) {
                return cfg;
            }
        }
        return null;
    }

    public List<SelectItem> getItens() {
        return itens;
    }

    public ConfiguracaoTipoEnum getTipo() {
        return tipo;
    }

    public String getMascara() {
        return mascara;
    }

    public String getValorPadrao() {
        return valorPadrao;
    }

    public AbaConfiguracaoEnum getAba() {
        return aba;
    }

    public String getTextoReferencia() {
        return textoReferencia;
    }

    public List<ConfiguracoesEnum> getConfigsFilhas() {
        return configsFilhas;
    }

    public boolean ehGrupoDeConfigs() {
        return configsFilhas != null && !configsFilhas.isEmpty();
    }

    public boolean isConfigFilha() {
        return configFilha;
    }

    public IconeTabBarAppEnum getIcone() {
        return icone;
    }

    public boolean isIconeHabilitavel() {
        return icone != null;
    }

    public boolean isAparecer() {
        return aparecer;
    }

}

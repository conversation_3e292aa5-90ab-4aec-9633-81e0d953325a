/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

import br.com.pacto.bean.animacao.Animacao;
import br.com.pacto.bean.animacao.TipoAnimacaoEnum;
import br.com.pacto.bean.aparelho.AparelhoAjuste;
import static java.util.Objects.isNull;



import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.ColecaoUtils;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.faces.model.SelectItem;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import br.com.pacto.util.enumeradores.MidiasNiveisEnum;
import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.collections.Predicate;
import org.hibernate.Hibernate;
import org.hibernate.Session;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
        @UniqueConstraint(columnNames = {"nome"}))
public class Atividade implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @Column(columnDefinition = "text", length = 9999)
    private String descricao;
    private boolean ativo = true;
    private Boolean seriesApenasDuracao = false;
    @Enumerated(EnumType.ORDINAL)
    private TipoAtividadeEnum tipo = TipoAtividadeEnum.ANAEROBICO;
    @JsonIgnore
    @OneToMany(targetEntity = AtividadeAparelho.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE}, mappedBy = "atividade")
    private List<AtividadeAparelho> aparelhos = new ArrayList<AtividadeAparelho>();
    @OneToMany(targetEntity = AtividadeMusculo.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY, mappedBy = "atividade")
    private List<AtividadeMusculo> musculos = new ArrayList<AtividadeMusculo>();
    @JsonIgnore
    @OneToMany(targetEntity = AtividadeGrupoMuscular.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY, mappedBy = "atividade")
    private List<AtividadeGrupoMuscular> gruposMusculares = new ArrayList<AtividadeGrupoMuscular>();
    @OneToMany(targetEntity = AtividadeAnimacao.class, fetch = FetchType.LAZY, mappedBy = "atividade")
    private List<AtividadeAnimacao> animacoes = new ArrayList<AtividadeAnimacao>();
    @JsonIgnore
    @OneToMany(targetEntity = AtividadeCategoriaAtividade.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY, mappedBy = "atividade")
    private List<AtividadeCategoriaAtividade> categorias = new ArrayList<AtividadeCategoriaAtividade>();
    @JsonIgnore
    @OneToMany(targetEntity = AtividadeNivel.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY, mappedBy = "atividade")
    private List<AtividadeNivel> niveis = new ArrayList<AtividadeNivel>();
    @JsonIgnore
    @OneToMany(targetEntity = AtividadeFicha.class, cascade = {CascadeType.MERGE}, fetch = FetchType.LAZY, mappedBy = "atividade")
    private List<AtividadeFicha> fichas = new ArrayList<AtividadeFicha>();
    @JsonIgnore
    @OneToMany(targetEntity = AtividadeEmpresa.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY, mappedBy = "atividade")
    private List<AtividadeEmpresa> empresasHabilitadas = new ArrayList<AtividadeEmpresa>();
    @Transient
    private Boolean selecionado = Boolean.FALSE;
    @Transient
    private List<AtividadeFichaAjuste> ajustes = new ArrayList<AtividadeFichaAjuste>();
    @Transient
    private Boolean adicionado = Boolean.FALSE;
    @Transient
    private String nomeLinkTransient;
    @Column(columnDefinition = "boolean DEFAULT true")
    private Boolean todasEmpresas = true;
    @Column(columnDefinition = "integer DEFAULT 0")
    private Integer versao = 0;
    @Column(columnDefinition = "boolean DEFAULT false")
    private Boolean crossfit = Boolean.FALSE;
    private String linkVideo;
    @Enumerated(EnumType.ORDINAL)
    private UnidadeMedidaEnum unidadeMedida = UnidadeMedidaEnum.NENHUM;
    @Enumerated(EnumType.ORDINAL)
    private CategoriaAtividadeWodEnum categoriaAtividadeWod = CategoriaAtividadeWodEnum.NENHUM;
    private String idIA;
    private String nomeOriginalIA;
    private String editadoPor;
    private Long ultimaEdicao;
    @JsonIgnore
    @OneToMany(targetEntity = AtividadeAlternativa.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY, mappedBy = "atividade")
    private List<AtividadeAlternativa> atividadesAlternativas = new ArrayList<AtividadeAlternativa>();
    private Boolean usarNaPrescricao;
    private Integer idIA2;

    @OneToMany(targetEntity = AtividadeVideo.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "atividade")
    private List<AtividadeVideo> linkVideos = new ArrayList<AtividadeVideo>();

    public Atividade clone(){
        Atividade atividade = new Atividade();
        atividade.setCodigo(codigo);
        atividade.setNome(nome);
        atividade.setDescricao(descricao);
        atividade.setAtivo(ativo);
        atividade.setSeriesApenasDuracao(seriesApenasDuracao);
        atividade.setTipo(tipo);
        atividade.setAparelhos(aparelhos);
        atividade.setMusculos(musculos);
        atividade.setGruposMusculares(gruposMusculares);
        atividade.setAnimacoes(animacoes);
        atividade.setCategorias(categorias);
        atividade.setNiveis(niveis);
        atividade.setFichas(fichas);
        atividade.setEmpresasHabilitadas(empresasHabilitadas);
        atividade.setSelecionado(selecionado);
        atividade.setAjustes(ajustes);
        atividade.setAdicionado(adicionado);
        atividade.setNomeLinkTransient(nomeLinkTransient);
        atividade.setTodasEmpresas(todasEmpresas);
        atividade.setVersao(versao);
        atividade.setCrossfit(crossfit);
        atividade.setLinkVideo(linkVideo);
        atividade.setUnidadeMedida(unidadeMedida);
        atividade.setCategoriaAtividadeWod(categoriaAtividadeWod);
        atividade.setIdIA(idIA);
        atividade.setNomeOriginalIA(nomeOriginalIA);
        atividade.setEditadoPor(editadoPor);
        atividade.setUltimaEdicao(ultimaEdicao);
        atividade.setAtividadesAlternativas(atividadesAlternativas);
        atividade.setUsarNaPrescricao(usarNaPrescricao);
        atividade.setLinkVideos(linkVideos);
        return atividade;
    }

    public List<AtividadeFichaAjuste> getAjustes() {
        if (ajustes == null) {
            ajustes = new ArrayList<>();
        }
        return ajustes;
    }

    public void setAjustes(List<AtividadeFichaAjuste> ajustes) {
        getAjustes().addAll(ajustes);
    }

    public List<AtividadeFicha> getFichas() {
        if (fichas == null) {
            fichas = new ArrayList<>();
        }
        return fichas;
    }

    public void setFichas(List<AtividadeFicha> fichas) {
        getFichas().addAll(fichas);
    }

    public List<AtividadeNivel> getNiveis() {
        if (niveis == null) {
            niveis = new ArrayList<>();
        }
        return niveis;
    }

    public void setNiveis(List<AtividadeNivel> niveis) {
        getNiveis().addAll(niveis);
    }

    public void setNiveisPadrao(List<AtividadeNivel> niveis) {
        this.niveis = niveis;
    }

    public Atividade() {
    }

    public Atividade(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigo() {
        if (codigo != null && codigo.equals(0)) {
            codigo = null;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }
    
    public String getNomeSemAspas() {
        try {
            return nome.replaceAll("'", "");
        } catch (Exception e) {
            return "";
        }
        
    }

    public String getNomeLower() {
        return nome.toLowerCase();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public TipoAtividadeEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEnum tipo) {
        this.tipo = tipo;
    }

    public List<AtividadeAparelho> getAparelhos() {
        if (aparelhos == null) {
            aparelhos = new ArrayList<>();
        }
        return aparelhos;
    }

    public void setAparelhos(List<AtividadeAparelho> aparelhos) {
        getAparelhos().addAll(aparelhos);
    }

    public void setAparelhosPadrao(List<AtividadeAparelho> aparelhos) {
        this.aparelhos = aparelhos;
    }

    public List<AtividadeMusculo> getMusculos() {
        if (musculos == null) {
            musculos = new ArrayList<>();
        }
        return musculos;
    }

    public void setMusculos(List<AtividadeMusculo> musculos) {
        getMusculos().addAll(musculos);
    }

    public void setMusculosPadrao(List<AtividadeMusculo> musculos) {
        this.musculos = musculos;
    }

    public List<AtividadeGrupoMuscular> getGruposMusculares() {
        if (gruposMusculares == null) {
            gruposMusculares = new ArrayList<>();
        }
        return gruposMusculares;
    }

    public void setGruposMusculares(List<AtividadeGrupoMuscular> gruposMusculares) {
        getGruposMusculares().addAll(gruposMusculares);
    }

    public void setGruposMuscularesPadrao(List<AtividadeGrupoMuscular> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

    public List<AtividadeAnimacao> getAnimacoes() {
        if (animacoes == null) {
            animacoes = new ArrayList<>();
        }
        return animacoes;
    }

    public void setAnimacoes(List<AtividadeAnimacao> animacoes) {
        getAnimacoes().clear();
        getAnimacoes().addAll(animacoes);
    }

    public void setAnimacoesPadrao(List<AtividadeAnimacao> animacoes) {
        this.animacoes = animacoes;
    }

    public List<AtividadeCategoriaAtividade> getCategorias() {
        if (categorias == null) {
            categorias = new ArrayList<>();
        }
        return categorias;
    }

    public void setCategorias(List<AtividadeCategoriaAtividade> categorias) {
        getCategorias().addAll(categorias);
    }

    public void setCategoriasPadrao(List<AtividadeCategoriaAtividade> categorias) {
        this.categorias = categorias;
    }

    public String getURLImg() {
        final String srcImg = "";
        for (AtividadeAnimacao atividadeAnimacao : getAnimacoes()) {
            if (atividadeAnimacao.getAnimacao() != null && TipoAnimacaoEnum.IMAGEM.equals(atividadeAnimacao.getAnimacao().getTipo())) {
                return atividadeAnimacao.getAnimacao().getUrl();
            }
        }
        return srcImg;

    }

    @Override
    public String toString() {
        return this.hashCode() + " - " + this.nome;
    }

    @Override
    public int hashCode() {
        return this.getCodigo() != null && this.getCodigo() > 0
                ? this.getCodigo().hashCode() : super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof Atividade)) {
            return false;
        }
        final Atividade other = (Atividade) obj;
        if (this.getCodigo() != other.getCodigo() && (this.getCodigo() == null || !this.getCodigo().equals(other.getCodigo()))) {
            return false;
        }
        return true;
    }

    public void setTipoCod(Integer codigo) {
        this.tipo = TipoAtividadeEnum.getFromId(codigo);
    }

    public Integer getTipoCod() {
        return this.tipo == null ? null : this.tipo.getId();
    }

    public Animacao getAnimacaoDefault() {
        if (getAnimacoes() != null && !getAnimacoes().isEmpty()) {
            return getAnimacoes().get(0).getAnimacao();
        } else {
            return null;
        }
    }

    public String getUrlDefault() {
        Animacao animacaoDefault;
        try {
            animacaoDefault = getAnimacaoDefault();
            return (animacaoDefault == null || animacaoDefault.getUrl() == null
                    || animacaoDefault.getUrl().isEmpty()) ? "" : animacaoDefault.getUrl();
        }catch (Exception e){
            return "";
        }

    }

    public String getFotoKey() {
        if (getAnimacoes() != null && !getAnimacoes().isEmpty()) {
            return getAnimacoes().get(0).getUrlFoto();
        } else {
            return null;
        }
    }

    public String getFotoKeyMin() {
        if (getAnimacoes() != null && !getAnimacoes().isEmpty()) {
            return UteisValidacao.emptyString(getAnimacoes().get(0).getUrlFotoMin()) ? getAnimacoes().get(0).getUrlFoto() : getAnimacoes().get(0).getUrlFotoMin();
        } else {
            return null;
        }
    }

    public String getFotoKeyPequena() {
        if (getAnimacoes() != null && !getAnimacoes().isEmpty()) {
            return UteisValidacao.emptyString(getAnimacoes().get(0).getUrlFotoPeq()) ? getAnimacoes().get(0).getUrlFoto() : getAnimacoes().get(0).getUrlFotoPeq();
        } else {
            return null;
        }
    }



    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public boolean getAerobica() {
        return tipo.equals(TipoAtividadeEnum.AEROBICO);

    }

    public List<SelectItem> getListaAjustes() {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        for (AtividadeAparelho atvAparelho : aparelhos) {
            for (AparelhoAjuste apAj : atvAparelho.getAparelho().getAjustes()) {
                lista.add(new SelectItem(apAj.getNome(), apAj.getNome()));
            }
        }
        return lista;
    }

    public boolean getTemImagem() {
        try {
            return animacoes != null && !animacoes.isEmpty();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

    public String getDescricaoTipo() {
        try {
            return tipo.getDescricao();
        } catch (Exception e) {
            return "";
        }
    }

    public String getDescricaoAtivo() {
        try {
            ViewUtils view = ((ViewUtils) UtilContext.getBean(ViewUtils.class));
            return ativo ? view.getLabel("cadastros.sim") : view.getLabel("cadastros.nao");
        } catch (Exception e) {
            return "";
        }
    }

    public String getName() {
        return getSelecionado() ? "atividadeSelecionada" : "atv" + getCodigo();
    }

    public Boolean getAdicionado() {
        return adicionado;
    }

    public void setAdicionado(Boolean adicionado) {
        this.adicionado = adicionado;
    }

    public Boolean getSeriesApenasDuracao() {
        return seriesApenasDuracao;
    }

    public void setSeriesApenasDuracao(Boolean seriesApenasDuracao) {
        this.seriesApenasDuracao = seriesApenasDuracao;
    }

    public List<AtividadeEmpresa> getEmpresasHabilitadas() {
        if (empresasHabilitadas == null) {
            empresasHabilitadas = new ArrayList<>();
        }
        return empresasHabilitadas;
    }

    public void setEmpresasHabilitadas(List<AtividadeEmpresa> empresasHabilitadas) {
        getEmpresasHabilitadas().addAll(empresasHabilitadas);
    }

    public void setEmpresasHabilitadasPadrao(List<AtividadeEmpresa> empresasHabilitadas) {
        this.empresasHabilitadas = empresasHabilitadas;
    }

    public Boolean getTodasEmpresas() {
        return todasEmpresas;
    }

    public void setTodasEmpresas(Boolean todasEmpresas) {
        this.todasEmpresas = todasEmpresas;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public String getArrayNomeEmpresas() {
        List<String> tmp = new ArrayList<String>();
        for (AtividadeEmpresa atve : empresasHabilitadas) {
            String nomeTmp = atve.getEmpresa().getNome();
            if (nomeTmp.contains("-")) {
                try {
                    nomeTmp = nomeTmp.substring(nomeTmp.indexOf("-") + 1).trim();
                } catch (Exception e) {
                }
            }
            tmp.add(nomeTmp);
        }
        if (tmp.isEmpty()) {
            return "Todas";
        } else {
            return Uteis.splitFromArray(tmp.toArray(), false);
        }
    }

    public String getIdentificadorPorEmpresa(boolean treinoIndependente, final Integer empresa) {
        List<AtividadeEmpresa> tmp = new ArrayList(empresasHabilitadas);
        ColecaoUtils.filter(tmp, new Predicate() {
            @Override
            public boolean evaluate(Object o) {
                return treinoIndependente ? ((AtividadeEmpresa) o).getEmpresa().getCodigo().equals(empresa) :
                        ((AtividadeEmpresa) o).getEmpresa().getCodZW().equals(empresa);
            }
        });
        StringBuilder retorno = new StringBuilder();
        for (AtividadeEmpresa atve : tmp) {
            retorno.append(",").append(atve.getIdentificador());
        }
        return retorno.toString().replaceFirst(",", "");
    }

    public Boolean getCrossfit() {
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }

    public List<String> getListaVideos(AtividadeFicha atividadeFicha){
        List<String> list = new ArrayList<String>();
        list.add(atividadeFicha.getAtividade().getNomeLinkTransient());
        list.add(atividadeFicha.getLinkVideoSet());
        if (atividadeFicha.getTriSet()) {
            list.add(atividadeFicha.getLinkVideoTriSet());
        }

        List<String> retorno = new ArrayList<String>();
        for(String s : list){
            if(!UteisValidacao.emptyString(s) && !retorno.contains(s)){
                retorno.add(s);
            }
        }

        return retorno;
    }

    public String getNomeLinkTransient() {
        if (nomeLinkTransient == null || nomeLinkTransient.trim().isEmpty()) {
            if (linkVideo == null || linkVideo.trim().isEmpty()) {
                try {
                    linkVideo = getLinkVideo();
                } catch (Exception ex) {}
            }
            nomeLinkTransient = linkVideo;
        }
        return nomeLinkTransient;
    }

    public String getLinkVideo() {
        if (linkVideo == null) {
            linkVideo = "";
        }
        return linkVideo;
    }

    public void setLinkVideo(String linkVideo) {
        this.linkVideo = linkVideo;
    }

    public String obterCodigoYouTube() {
        String videoID = "";
        String linkYouTube = this.getLinkVideo();
        if (linkYouTube != null && linkYouTube.trim().length() > 0 && linkYouTube.startsWith("http")) {
            String expression = "^.*((youtu.be" + "\\/)" + "|(v\\/)|(\\/u\\/w\\/)|(embed\\/)|(watch\\?))\\??v?=?([^#\\&\\?]*).*";
            CharSequence input = linkYouTube;
            Pattern pattern = Pattern.compile(expression, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(input);
            if (matcher.matches()) {
                String groupIndex1 = matcher.group(7);
                if (groupIndex1 != null && groupIndex1.length() == 11)
                    videoID = groupIndex1;
            }
        }
        return videoID;
    }

    public UnidadeMedidaEnum getUnidadeMedida() {
        if (unidadeMedida == null) {
            unidadeMedida = UnidadeMedidaEnum.NENHUM;
        }
        return unidadeMedida;
    }

    public void setUnidadeMedida(UnidadeMedidaEnum unidadeMedida) {
        this.unidadeMedida = unidadeMedida;
    }

    public CategoriaAtividadeWodEnum getCategoriaAtividadeWod() {
        if (categoriaAtividadeWod == null) {
            categoriaAtividadeWod = CategoriaAtividadeWodEnum.NENHUM;
        }
        return categoriaAtividadeWod;
    }

    public void setCategoriaAtividadeWod(CategoriaAtividadeWodEnum categoriaAtividadeWod) {
        this.categoriaAtividadeWod = categoriaAtividadeWod;
    }

    public Boolean getUsarNaPrescricao() {
        if(usarNaPrescricao == null){
            usarNaPrescricao = Boolean.TRUE;
        }
        return usarNaPrescricao;
    }

    public void setUsarNaPrescricao(Boolean usarNaPrescricao) {
        this.usarNaPrescricao = usarNaPrescricao;
    }

    public List<String> getImagemAtiviadeAssocidada(String urlBase) {
            List<String> list = new ArrayList<String>();
            for (AtividadeAnimacao atvAnimacao : getAnimacoes()) {
                if (atvAnimacao.getFotoKey() == null) {
                    if (atvAnimacao.getAnimacao() != null) {
                        if (atvAnimacao.getAnimacao().toString() != null && !atvAnimacao.getAnimacao().toString().equalsIgnoreCase(""))
                            list.add(urlBase + MidiasNiveisEnum.MOBILE_RETANGULO.getLocal() + atvAnimacao.getAnimacao());
                    }
                } else {
                    if (atvAnimacao.getUrlFotoPeq() == null || atvAnimacao.getUrlFotoPeq().isEmpty())
                        list.add(Aplicacao.obterUrlFotoDaNuvem(atvAnimacao.getFotoKey()));
                    else
                        list.add(atvAnimacao.getUrlFotoPeq());
                }
            }

            List<String> retorno = new ArrayList<String>();
            for (String s : list) {
                if (!UteisValidacao.emptyString(s) && !retorno.contains(s)) {
                    retorno.add(s);
                }
            }
            return retorno;
    }
    public String getDescricaoParaLog(Atividade v2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.differenceReference(this, v2));

            if (v2 == null) {
                v2 = new Atividade();
            }

            //Compara as Atividades Aparelho
            if (!isNull(this.getAnimacoes()) && !isNull(v2.getAnimacoes())) {

                Iterator<AtividadeAnimacao> ListaV1 = this.getAnimacoes().iterator();
                Iterator<AtividadeAnimacao> ListaV2 = v2.getAnimacoes().iterator();

                while (ListaV1.hasNext() || ListaV2.hasNext()) {
                    AtividadeAnimacao v1Atividade = ListaV1.hasNext() ? ListaV1.next() : null;
                    AtividadeAnimacao v2Atividade = ListaV2.hasNext() ? ListaV2.next() : null;

                    log.append(UtilReflection.difference(v1Atividade, v2Atividade));
                }
            }

            //Compara as linkVideos
            if (!isNull(this.getLinkVideos()) && !isNull(v2.getLinkVideos())) {

                Iterator<AtividadeVideo> ListaV1 = this.getLinkVideos().iterator();
                Iterator<AtividadeVideo> ListaV2 = v2.getLinkVideos().iterator();

                while (ListaV1.hasNext() || ListaV2.hasNext()) {
                    AtividadeVideo v1Atividade = ListaV1.hasNext() ? ListaV1.next() : null;
                    AtividadeVideo v2Atividade = ListaV2.hasNext() ? ListaV2.next() : null;

                    log.append(UtilReflection.difference(v1Atividade, v2Atividade));
                }
            }

            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }
    public void setNomeLinkTransient(String nomeLinkTransient) {
        this.nomeLinkTransient = nomeLinkTransient;
    }

    public String getNomeOriginalIA() {
        return nomeOriginalIA;
    }

    public void setNomeOriginalIA(String nomeOriginalIA) {
        this.nomeOriginalIA = nomeOriginalIA;
    }

    public String getEditadoPor() {
        return editadoPor;
    }

    public void setEditadoPor(String editadoPor) {
        this.editadoPor = editadoPor;
    }

    public Long getUltimaEdicao() {
        return ultimaEdicao;
    }

    public void setUltimaEdicao(Long ultimaEdicao) {
        this.ultimaEdicao = ultimaEdicao;
    }

    public String getIdIA() {
        return idIA;
    }

    public void setIdIA(String idIA) {
        this.idIA = idIA;
    }

    public List<AtividadeAlternativa> getAtividadesAlternativas() {
        return atividadesAlternativas;
    }

    public void setAtividadesAlternativas(List<AtividadeAlternativa> atividadesAlternativas) {
        this.atividadesAlternativas = atividadesAlternativas;
    }

    public List<AtividadeVideo> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<AtividadeVideo> linkVideos) {
        this.linkVideos = linkVideos;
    }

    public Integer getIdIA2() {
        return idIA2;
    }

    public String getIdIA2String() {
        return idIA2 == null ? null : idIA2.toString();
    }

    public void setIdIA2(Integer idIA2) {
        this.idIA2 = idIA2;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

import br.com.pacto.bean.aparelho.AparelhoAjuste;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.bean.serie.SerieTO;
import br.com.pacto.dao.intf.atividade.AtividadeAnimacaoDao;
import br.com.pacto.dao.intf.atividade.AtividadeAparelhoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.atividadeEmpresa.AtividadeEmpresaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.enumeradores.MidiasNiveisEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Entity
public class AtividadeFicha implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private Integer ordem;
    private Integer intensidade = null;
    private Boolean nomeAtividadeAlteradoManualmente = false;
    private Boolean descanso = true;
    private String setId;
    @Transient
    private Integer codigoAntigo;
    @ManyToOne
    private Atividade atividade;
    @ManyToOne
    private Ficha ficha;
    private String complementoNomeAtividade;
    @OneToMany(cascade = {CascadeType.PERSIST,CascadeType.MERGE,CascadeType.REMOVE}, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "atividadeFicha", targetEntity = Serie.class)
    private List<Serie> series = new ArrayList<Serie>();
    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, orphanRemoval = true, mappedBy = "atividadeFicha", targetEntity = AtividadeFichaAjuste.class)
    private List<AtividadeFichaAjuste> ajustes = new ArrayList<AtividadeFichaAjuste>();
    @Enumerated(EnumType.ORDINAL)
    private MetodoExecucaoEnum metodoExecucao;
    private Integer versao;
    @Transient
    private Integer nrSeries = 0;
    @Transient
    private Integer nrSeriesRealizadas = 0;
    @Transient
    private Integer indexSeries = -1;
    @Transient
    private Boolean escolhida = false;
    @Transient
    private Boolean escolhidaSet = false;
    @Transient
    private List<Serie> seriesRemover;
    @Transient
    private String nomeTransient;
    @Transient
    private AtividadeFicha atividadeAssociada1;
    @Transient
    private AtividadeFicha atividadeAssociada2;
    @Transient
    private boolean notificarNomeInvalido;
    private Integer codigo_professor_acompanhamento ;

    public AtividadeFicha() {
    }

    public void addAssociado(AtividadeFicha atv){
        if(this.getAtividadeAssociada1() == null){
            setAtividadeAssociada1(atv);
        }else{
            if(getAtividadeAssociada1().getOrdem() < atv.getOrdem()){
                setAtividadeAssociada2(atv);
            }else{
                setAtividadeAssociada2(getAtividadeAssociada1());
                setAtividadeAssociada1(atv);
            }
        }
    }

    public AtividadeFicha getClone() {
        AtividadeFicha clone = new AtividadeFicha();
        clone.setCodigo(this.codigo != null ? codigo : 0);
        clone.setNome(this.nome != null ? this.nome : "");
        clone.setOrdem(this.ordem != null ? this.ordem : 0);
        clone.setAtividade(this.atividade != null ? this.getAtividade() : new Atividade());
        clone.setFicha(this.ficha != null ? this.ficha : new Ficha());
        clone.setSeries(this.series);
        clone.setAjustes(this.ajustes);
        clone.setMetodoExecucao(this.metodoExecucao);
        clone.setVersao(this.versao != null ? versao : 0);
        clone.setNrSeries(this.nrSeries != null ? nrSeries : 0);
        clone.setIndexSeries(this.indexSeries != null ? indexSeries : -1);
        return clone;
    }

    public AtividadeFicha(final String nome, Atividade atividade, Ficha ficha, Integer ordem, MetodoExecucaoEnum metodo,
                          List<AtividadeFichaAjuste> ajustes, boolean nomeAlteradoManualmente) {
        this.nome = nome;
        this.atividade = atividade;
        this.ficha = ficha;
        this.ordem = ordem;
        this.metodoExecucao = metodo;
        this.ajustes = new ArrayList<AtividadeFichaAjuste>();
        this.nomeAtividadeAlteradoManualmente = nomeAlteradoManualmente;
        for (AtividadeFichaAjuste atvFa : ajustes) {
            this.ajustes.add(new AtividadeFichaAjuste(atvFa.getNome(), atvFa.getValor()));
        }
    }

    public AtividadeFicha(Integer codigo, final String nome, final String setId, Atividade atividade, Ficha ficha, Integer ordem, MetodoExecucaoEnum metodo,
                          List<AtividadeFichaAjuste> ajustes, boolean nomeAlteradoManualmente, String complementoNomeAtividade) {
        this.codigoAntigo = codigo;
        this.nome = nome;
        this.atividade = atividade;
        this.ficha = ficha;
        this.ordem = ordem;
        this.metodoExecucao = metodo;
        this.ajustes = new ArrayList<AtividadeFichaAjuste>();
        this.nomeAtividadeAlteradoManualmente = nomeAlteradoManualmente;
        for (AtividadeFichaAjuste atvFa : ajustes) {
            this.ajustes.add(new AtividadeFichaAjuste(atvFa.getNome(), atvFa.getValor()));
        }
        this.setId = setId;
        this.complementoNomeAtividade = complementoNomeAtividade;
    }
    
    private List<SerieTO> seriesTO(final List<Serie> series) {
        List<SerieTO> l = new ArrayList<SerieTO>();
        for (Serie s: series){
            SerieTO sTO = new SerieTO(s, this);
            l.add(sTO);
        }
        return l;
    }

    private List<SerieTO> seriesTO(final List<Serie> series, AtividadeFicha atividade) {
        List<SerieTO> l = new ArrayList<SerieTO>();
        for (Serie s : series) {
            SerieTO sTO = new SerieTO(s, atividade);
            l.add(sTO);
        }
        return l;
    }

    public List<SerieTO> preencherSeriesConcatenadas() {
        List<SerieTO> seriesSet = new ArrayList<SerieTO>();
        List<Integer> seriesAdicionadas = new ArrayList<Integer>();

        if (getAtividadeAssociada1() != null) {
            Ordenacao.ordenarLista(getAtividadeAssociada1().getSeries(), "ordem");
            adicionarSeriesConcatenadas(seriesTO(getSeries()), seriesSet, seriesAdicionadas);
            adicionarSeriesConcatenadas(seriesTO(getAtividadeAssociada1().getSeries()), seriesSet, seriesAdicionadas);
        } else {
            adicionarSeriesConcatenadas(seriesTO(getSeries()), seriesSet, seriesAdicionadas);
        }
        if (getAtividadeAssociada2() != null) {
            Ordenacao.ordenarLista(getAtividadeAssociada2().getSeries(), "ordem");
            adicionarSeriesConcatenadas(seriesTO(getAtividadeAssociada2().getSeries()), seriesSet, seriesAdicionadas);
        }
        return seriesSet;
    }
    
    public void adicionarSeriesConcatenadas(List<SerieTO> series, List<SerieTO> seriesSet, List<Integer> seriesAdicionadas) {
        for (int i = 0; i < series.size(); i++) {
            SerieTO serie1 = null;
            SerieTO serie2 = null;
            if (getAtividadeAssociada1() != null && getAtividadeAssociada1().getSeries().size() > i) {
                serie1 = seriesTO(getAtividadeAssociada1().getSeries()).get(i);
                if (serie1.getCodigo().equals(series.get(i).getCodigo())) {
                    serie1 = null;
                } else {
                    serie1.setAdicionarDescanso(getAtividadeAssociada1().getDescanso());
                }
            }
            if (getAtividadeAssociada2() != null && getAtividadeAssociada2().getSeries().size() > i) {
                serie2 = seriesTO(getAtividadeAssociada2().getSeries()).get(i);
                if (serie2.getCodigo().equals(series.get(i).getCodigo())) {
                    serie1 = null;
                } else {
                    serie2.setAdicionarDescanso(getAtividadeAssociada2().getDescanso());
                }
            }

            if (series.get(i) != null) {
                if (!seriesAdicionadas.contains(series.get(i).getCodigo())) {
                    series.get(i).setAdicionarDescanso(getDescanso());
                    seriesSet.add(Serie.concatenarSeries(series.get(i), serie1, serie2));
                    seriesAdicionadas.add(series.get(i).getCodigo());
                    if (serie1 != null && !UteisValidacao.emptyNumber(serie1.getCodigo())) {
                        seriesAdicionadas.add(serie1.getCodigo());
                    }
                    if (serie2 != null && !UteisValidacao.emptyNumber(serie2.getCodigo())) {
                        seriesAdicionadas.add(serie2.getCodigo());
                    }
                }
            } else {
                seriesSet.add(series.get(i));
            }
        }
    }
    public List<SerieTO> preencherSeriesSet(Boolean seriesAgrupadas) {
        List<SerieTO> seriesSet = new ArrayList<SerieTO>();
        List<SerieTO> seriesFicha = seriesTO(getSeries());
        List<SerieTO> seriesAtv1 = null;
        List<SerieTO> seriesAtv2 = null;

        if (getAtividadeAssociada1() != null) {
            seriesAtv1 = seriesTO(getAtividadeAssociada1().getSeries(), getAtividadeAssociada1());
            Ordenacao.ordenarLista(seriesAtv1, "ordem");
        }
        if (getAtividadeAssociada2() != null) {
            seriesAtv2 = seriesTO(getAtividadeAssociada2().getSeries(), getAtividadeAssociada2());
            Ordenacao.ordenarLista(seriesAtv2, "ordem");
        }
        for (int i = 0; i < seriesFicha.size(); i++) {
            if (seriesAgrupadas && (getBiSet() || getTriSet())) {
                if (getAtividadeAssociada1() != null && seriesAtv1 != null && seriesAtv1.size() > i) {
                    seriesSet.add(seriesAtv1.get(i));
                }
                if (getAtividadeAssociada2() != null && seriesAtv2 != null && seriesAtv2.size() > i) {
                    seriesSet.add(seriesAtv2.get(i));
                }
            } else {
                seriesSet.add(seriesFicha.get(i));
            }
        }
        if(seriesAtv1 != null && seriesAgrupadas && (getBiSet() || getTriSet())) {
            if(seriesAtv1.size() > seriesFicha.size()) {
                for(int i = seriesFicha.size(); i < seriesAtv1.size(); i++) {
                    if(i > 0){
                        seriesSet.add(seriesAtv1.get(i - 1));
                    }

                }
            }
        }
        return seriesSet;
    }

    public AtividadeFicha(Atividade atividade) {
        this.atividade = atividade;
    }

    public AtividadeFicha(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigo() {
        if (codigo != null && codigo.equals(0)) {
            codigo = null;
        }
        return codigo;
    }

    public List<String> getImagemAtiviadeAssocidada(String urlBase, String ctx) throws ServiceException {
        List<String> list = new ArrayList<String>();
        try {
            for(AtividadeAnimacao atvAnimacao : getAtividade().getAnimacoes()){
                if(atvAnimacao.getFotoKey() == null){
                    if(atvAnimacao.getAnimacao() != null){
                        if (atvAnimacao.getAnimacao().toString() != null && !atvAnimacao.getAnimacao().toString().equalsIgnoreCase(""))
                            list.add(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + atvAnimacao.getAnimacao());
                    }
                }else{
                    if (atvAnimacao.getUrlFoto() == null || atvAnimacao.getUrlFoto().isEmpty())
                        list.add(Aplicacao.getProp(Aplicacao.obterUrlFotoDaNuvem(atvAnimacao.getFotoKey())));
                    else
                        list.add(atvAnimacao.getUrlFoto());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if(getAtividadeAssociada1() != null){
            try {
                getAtividadeAssociada1().getAtividade().getFotoKey();
                getAtividadeAssociada1().getAtividade().getAparelhos().isEmpty();
                getAtividadeAssociada1().getAtividade().getEmpresasHabilitadas().isEmpty();
            } catch (Exception e) {
                getAtividadeAssociada1().setAtividade(recarregarAtividade(ctx, getAtividadeAssociada1().getAtividade(), true));
            }
            for(AtividadeAnimacao atvAnimacao : getAtividadeAssociada1().getAtividade().getAnimacoes()){
                if(atvAnimacao.getFotoKey() == null){
                    list.add(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + getAtividadeAssociada1().getAtividade().getURLImg());
                }else{
                    list.add(atvAnimacao.getUrlFotoPeq());
                }
            }
        }

        if(getAtividadeAssociada2() != null){
            for(AtividadeAnimacao atvAnimacao : getAtividadeAssociada2().getAtividade().getAnimacoes()){
                if(atvAnimacao.getFotoKey() == null){
                    list.add(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + getAtividadeAssociada2().getAtividade().getURLImg());
                }else{
                    list.add(atvAnimacao.getUrlFotoPeq());
                }
            }
        }

        List<String> retorno = new ArrayList<String>();
        for(String s : list){
            if(!UteisValidacao.emptyString(s) && !retorno.contains(s)){
                retorno.add(s);
            }
        }
        return retorno;
    }

    public List<ImgMediumUrlsTO> getListImagemAtiviadeAssocidada(String urlBase, String ctx) throws ServiceException {
        List<ImgMediumUrlsTO> list = new ArrayList<ImgMediumUrlsTO>();
        try {
            for(AtividadeAnimacao atvAnimacao : getAtividade().getAnimacoes()){
                if(atvAnimacao.getFotoKey() == null){
                    if(atvAnimacao.getAnimacao() != null){
                        if (atvAnimacao.getAnimacao().toString() != null && !atvAnimacao.getAnimacao().toString().equalsIgnoreCase("")){
                            ImgMediumUrlsTO novaImg = new ImgMediumUrlsTO();
                            novaImg.setLinkImg(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + atvAnimacao.getAnimacao());
                            novaImg.setProfessor(atvAnimacao.getProfessor());
                            list.add(novaImg);
                        }
                    }
                }else{
                    if (atvAnimacao.getUrlFoto() == null || atvAnimacao.getUrlFoto().isEmpty()){
                        ImgMediumUrlsTO novaImg = new ImgMediumUrlsTO();
                        novaImg.setLinkImg(Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/" +atvAnimacao.getFotoKey());
                        novaImg.setProfessor(atvAnimacao.getProfessor());
                        list.add(novaImg);
                    }else{
                        ImgMediumUrlsTO novaImg = new ImgMediumUrlsTO();
                        novaImg.setLinkImg(atvAnimacao.getUrlFoto());
                        novaImg.setProfessor(atvAnimacao.getProfessor());
                        list.add(novaImg);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if(getAtividadeAssociada1() != null){
            try {
                getAtividadeAssociada1().getAtividade().getFotoKey();
                getAtividadeAssociada1().getAtividade().getAparelhos().isEmpty();
                getAtividadeAssociada1().getAtividade().getEmpresasHabilitadas().isEmpty();
            } catch (Exception e) {
                getAtividadeAssociada1().setAtividade(recarregarAtividade(ctx, getAtividadeAssociada1().getAtividade(), true));
            }
            for(AtividadeAnimacao atvAnimacao : getAtividadeAssociada1().getAtividade().getAnimacoes()){
                if(atvAnimacao.getFotoKey() == null){
                    ImgMediumUrlsTO novaImg = new ImgMediumUrlsTO();
                    novaImg.setLinkImg(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + getAtividadeAssociada1().getAtividade().getURLImg());
                    novaImg.setProfessor(atvAnimacao.getProfessor());
                    list.add(novaImg);
                }else{
                    ImgMediumUrlsTO novaImg = new ImgMediumUrlsTO();
                    novaImg.setLinkImg(atvAnimacao.getUrlFotoPeq());
                    novaImg.setProfessor(atvAnimacao.getProfessor());
                    list.add(novaImg);
                }
            }
        }

        if(getAtividadeAssociada2() != null){
            for(AtividadeAnimacao atvAnimacao : getAtividadeAssociada2().getAtividade().getAnimacoes()){
                if(atvAnimacao.getFotoKey() == null){
                    ImgMediumUrlsTO novaImg = new ImgMediumUrlsTO();
                    novaImg.setLinkImg(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + getAtividadeAssociada2().getAtividade().getURLImg());
                    novaImg.setProfessor(atvAnimacao.getProfessor());
                    list.add(novaImg);
                }else{
                    ImgMediumUrlsTO novaImg = new ImgMediumUrlsTO();
                    novaImg.setLinkImg(atvAnimacao.getUrlFotoPeq());
                    novaImg.setProfessor(atvAnimacao.getProfessor());
                    list.add(novaImg);
                }
            }
        }
        return list;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Atividade getAtividade() {
        return atividade;
    }

    public void setAtividade(Atividade atividade) {
        this.atividade = atividade;
    }

    public Ficha getFicha() {
        return ficha;
    }

    public void setFicha(Ficha ficha) {
        this.ficha = ficha;
    }

    public List<Serie> getSeries() {
        if (series == null) {
            series = new ArrayList<>();
        }
        return series;
    }

    public void setSeries(List<Serie> series) {
        getSeries().clear();
        getSeries().addAll(series);
    }

    public void setSeriesPadrao(List<Serie> series) {
        this.series = series;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public List<AtividadeFichaAjuste> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AtividadeFichaAjuste> ajustes) {
        if (this.ajustes == null) {
            this.ajustes = ajustes;
        } else if(this.ajustes != ajustes) {
            this.ajustes.clear();
            if(ajustes != null){
                this.ajustes.addAll(ajustes);
            }
        }
    }

    public MetodoExecucaoEnum getMetodoExecucao() {
        return metodoExecucao;
    }

    public void setMetodoExecucao(MetodoExecucaoEnum metodoExecucao) {
        this.metodoExecucao = metodoExecucao;
    }

    public void addSeries(AtividadeFicha atividadeFicha, final int fator, String repeticao, String carga, int descanso,
                          boolean instanciar,
                          boolean alternar,String complemento) throws Exception{
        if (instanciar) {
            series = new ArrayList<Serie>();
        }
        String[] repeticoes =  new String[0];
        String[] cargas = new String[0];
        if(!UteisValidacao.emptyString(repeticao)){
            repeticoes = repeticao.split("/").length > 1 ? repeticao.split("/") : new String[0];
            repeticoes = repeticoes.length > 1 ? repeticoes : repeticao.split("-");
            repeticoes = repeticoes.length > 1 ? repeticoes : repeticao.split(",");
        }
        if(!UteisValidacao.emptyString(carga)){
            cargas = carga.split("/").length > 1 ? carga.split("/") : new String[0];
            cargas = cargas.length > 1 ? cargas : carga.split("-");
            cargas = cargas.length > 1 ? cargas : carga.split(",");
        }
        for (int i = 1; i <= fator; i++) {
            Serie s = new Serie();
            s.setAtividadeFicha(atividadeFicha);
            try {
                if(alternar){
                    s.setCarga(new Double(cargas.length > i ? cargas[i - 1] : cargas[cargas.length - 1]));
                }else{
                    s.setCargaComp(carga);
                }

            }catch (Exception ex){
                throw new ServiceException("mensagem.treinoRapido.carga");
            }
            try {
                if(alternar){
                    s.setRepeticao(new Integer(repeticoes.length > i ? repeticoes[i - 1] : repeticoes[repeticoes.length - 1]));
                }else{
                    s.setRepeticaoComp(repeticao);
                    s.setarCompValores();
                }

            }catch (Exception ex){
                throw new ServiceException("mensagem.treinoRapido.repeticao");
            }
            s.setDescanso(descanso);
            s.setComplemento(complemento);
            series.add(s);
        }
    }

    public void addSeries(AtividadeFicha atividadeFicha, final int fator, int duracao, int distancia, double velocidade, int descanso) {
        for (int i = 1; i <= fator; i++) {
            Serie s = new Serie();
            s.setAtividadeFicha(atividadeFicha);
            s.setDistancia(distancia);
            s.setDuracao(duracao);
            s.setVelocidade(velocidade);
            s.setDescanso(descanso);
            series.add(s);
        }
    }

    public void addAjuste(AtividadeFicha atividadeFicha, final String nome, final String valor) {
        AtividadeFichaAjuste ajuste = new AtividadeFichaAjuste(atividadeFicha, nome, valor);
        ajustes.add(ajuste);
    }

    public String getNomeAtividade() {
        return getAtividade().getNome();
    }

    public Integer getNrSeries() {
        if (nrSeries == null || nrSeries == 0) {
            nrSeries = getSeries() == null ? 0 : getSeries().size();
        }
        return nrSeries;
    }

    public void setNrSeries(Integer nrSeries) {
        this.nrSeries = nrSeries;
    }

    public Integer getNrSeriesRealizadas() {
        return nrSeriesRealizadas;
    }

    public void setNrSeriesRealizadas(Integer nrSeriesRealizadas) {
        this.nrSeriesRealizadas = nrSeriesRealizadas;
    }

    public String getNrSeriesApres() {
        return getNrSeries() > 10 ? getNrSeries().toString() : "0" + getNrSeries().toString();
    }

    public String getNrSeriesRealizadasApres() {
        return getNrSeriesRealizadas() > 10 ? getNrSeriesRealizadas().toString() : "0" + getNrSeriesRealizadas().toString();
    }

    public Integer getVersao() {
        return versao == null ? 0 : versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public Integer getIndexSeries() {
        if (this.codigo != null & series != null && series.size() == 1) {
            return 0;
        } else if (series != null && series.size() > 0) {
            return indexSeries < 0 ? 0 : series.size() - 1;
        }
        return indexSeries < 0 ? 0 : indexSeries;
    }

    public void setIndexSeries(Integer indexSeries) {
        this.indexSeries = indexSeries;
    }

    public String getNumSeries() {
        Integer num = series != null && !series.isEmpty() ? series.size() : 0;
        return num.toString();
    }

    public List<Serie> getSeriesRemover() {
        if (seriesRemover == null) {
            seriesRemover = new ArrayList<Serie>();
        }
        return seriesRemover;
    }

    public void setSeriesRemover(List<Serie> seriesRemover) {

        this.seriesRemover = seriesRemover;
    }

    public Boolean getEscolhida() {
        return escolhida;
    }

    public void setEscolhida(Boolean escolhida) {
        this.escolhida = escolhida;
    }

    public void initAjustes() {
        Map<String, AtividadeFichaAjuste> mapaAjustes = new HashMap<String, AtividadeFichaAjuste>();
        if (atividade != null && atividade.getAparelhos() != null && !atividade.getAparelhos().isEmpty()) {
            for (AtividadeAparelho ap : atividade.getAparelhos()) {
                for (AparelhoAjuste ajuste : ap.getAparelho().getAjustes()) {
                    AtividadeFichaAjuste atvAjuste = new AtividadeFichaAjuste(this, ajuste.getNome(), "");
                    mapaAjustes.put(ajuste.getNome(), atvAjuste);
                }
            }
        }
        ajustes = new ArrayList(mapaAjustes.values());
        ajustes = Ordenacao.ordenarLista(ajustes, "nome");
    }

    public String getNomeTransient() {
        if (nomeTransient == null || nomeTransient.trim().isEmpty()) {
            if (nome == null || nome.trim().isEmpty()) {
                try {
                    nome = getNomeAtividade();
                } catch (Exception ex) {}
            }
            nomeTransient = nome;
        }
        return nomeTransient;
    }

    public String getNomeSet() {
        return getBiSet() ? (atividadeAssociada1 == null ? "" : " BI-Set com "+atividadeAssociada1.getNomeTransient()) :
                getTriSet() ? (atividadeAssociada1 == null ? "" : " TRI-Set com "+atividadeAssociada1.getNomeTransient()) +
                        (atividadeAssociada2 == null ? "" : " e "+atividadeAssociada2.getNomeTransient())
        : "";
    }

    public String getLinkVideoSet() {
        return getBiSet() ? (atividadeAssociada1 == null ? "" : atividadeAssociada1.getAtividade().getNomeLinkTransient()) :
                getTriSet() ? (atividadeAssociada1 == null ? "" : atividadeAssociada1.getAtividade().getNomeLinkTransient())
                        : "";
    }

    public String getLinkVideoTriSet() {
        return getTriSet() ? (atividadeAssociada2 == null ? "" : atividadeAssociada2.getAtividade().getNomeLinkTransient())
                        : "";
    }

    public void setNomeTransient(String novo) {
        this.nome = novo;
        this.nomeTransient = novo;
        this.nomeAtividadeAlteradoManualmente = true;
        this.notificarNomeInvalido = novo != null && novo.replaceAll("[\\t\\s]", "").matches("\\d+");
    }

    public AtividadeFicha getAtividadeAssociada1() {
        return atividadeAssociada1;
}

    public void setAtividadeAssociada1(AtividadeFicha atividadeAssociada1) {
        this.atividadeAssociada1 = atividadeAssociada1;
    }

    public AtividadeFicha getAtividadeAssociada2() {
        return atividadeAssociada2;
    }

    public void setAtividadeAssociada2(AtividadeFicha atividadeAssociada2) {
        this.atividadeAssociada2 = atividadeAssociada2;
    }

    public Boolean getBiSet(){
        return metodoExecucao != null &&
                MetodoExecucaoEnum.BI_SET.equals(metodoExecucao);
    }

    public Boolean getTriSet(){
        return metodoExecucao != null &&
                MetodoExecucaoEnum.TRI_SET.equals(metodoExecucao);
    }

    public String getSetId() {
        return setId;
    }

    public void setSetId(String setId) {
        this.setId = setId;
    }

    public Boolean getNomeAtividadeAlteradoManualmente() {
        return nomeAtividadeAlteradoManualmente;
    }

    public void setNomeAtividadeAlteradoManualmente(Boolean nomeAtividadeAlteradoManualmente) {
        this.nomeAtividadeAlteradoManualmente = nomeAtividadeAlteradoManualmente;
    }

    public Boolean getDescancoOrDescansoAssociado() {
        if (getDescanso()) return true;
        if (getAtividadeAssociada1() != null && getAtividadeAssociada1().getDescanso()) return true;
        if (getAtividadeAssociada2() != null && getAtividadeAssociada2().getDescanso()) return true;

        return false;
    }

    public Boolean getDescanso() {
        if(descanso == null){
            descanso = true;
        }
        return descanso;
    }

    public void setDescanso(Boolean descanso) {
        this.descanso = descanso;
    }

    public Boolean getEscolhidaSet() {
        return escolhidaSet;
    }

    public void setEscolhidaSet(Boolean escolhidaSet) {
        this.escolhidaSet = escolhidaSet;
    }

    public Integer getIntensidade() {
        return intensidade;
    }

    public void setIntensidade(Integer intensidade) {
        this.intensidade = intensidade;
    }

    public boolean isNotificarNomeInvalido() {
        return notificarNomeInvalido;
    }

    public Integer getCodigoAntigo() {
        return codigoAntigo;
    }

    public void setCodigoAntigo(Integer codigoAntigo) {
        this.codigoAntigo = codigoAntigo;
    }

    public Integer getCodigo_professor_acompanhamento() {
        return codigo_professor_acompanhamento;
    }

    public void setCodigo_professor_acompanhamento(Integer codigo_professor_acompanhamento) {
        this.codigo_professor_acompanhamento = codigo_professor_acompanhamento;
    }

    public void printSeries(){
        if (series != null) {
            System.out.println(String.format("Atividade %s", nome == null ? atividade.getNome() : nome)); 
            for (Serie s : series) {
                System.out.println(String.format("Serie %s => Rep: %s Carga: %s CompRep: %s CompCarg: %s ", 
                        s.getCodigo(), s.getRepeticao(), s.getCarga(), s.getRepeticaoComp(), s.getCargaComp())); 
            }
        }
    }


    public String getComplementoNomeAtividade() {
           return complementoNomeAtividade;
    }

    public String getComplementoNomeAtividadeRetiraFicha() {
        if (StringUtils.isNotBlank(complementoNomeAtividade)) {
            return (" ( " + complementoNomeAtividade + " ) ");
        } else {
            return "";
        }
    }

    public void setComplementoNomeAtividade(String complementoNomeAtividade) {
        this.complementoNomeAtividade = complementoNomeAtividade;
    }

    //tratamento para LazyLoadException
    public static Atividade recarregarAtividade(String ctx, Atividade atividade, Boolean recarregaAtividadeCompleta) throws ServiceException {
        Atividade atv = new Atividade();
        atv.setAnimacoes(getAtividadeAnimacaoDao().obterImagens(ctx, atividade.getCodigo(), recarregaAtividadeCompleta));
        atv.setAparelhos(getAtividadeAparelhoDao().obterAparelhosPorAtividade(ctx, atividade.getCodigo()));

        StringBuilder sqlAtvEmpresas = new StringBuilder();
        sqlAtvEmpresas.append("SELECT obj FROM AtividadeEmpresa obj WHERE obj.atividade.codigo = :atividadeId");
        HashMap<String, Object> param = new HashMap<>();
        param.put("atividadeId", atividade.getCodigo());
        atv.setEmpresasHabilitadas(getAtividadeEmpresaService().obterPorParam(ctx, sqlAtvEmpresas.toString(), param));

        atv.setNome(atividade.getNome());
        atv.setCodigo(atividade.getCodigo());
        atv.setDescricao(atividade.getDescricao());
        atv.setAtivo(atividade.isAtivo());
        atv.setSeriesApenasDuracao(atividade.getSeriesApenasDuracao());
        atv.setTipo(atividade.getTipo());
        atv.setSelecionado(atividade.getSelecionado());
        atv.setAjustes(atividade.getAjustes());
        atv.setAdicionado(atividade.getAdicionado());
        atv.setTodasEmpresas(atividade.getTodasEmpresas());
        atv.setVersao(atividade.getVersao());
        atv.setCrossfit(atividade.getCrossfit());
        atv.setLinkVideo(atividade.getLinkVideo());
        if(atv.getAnimacoes() == null) {
            atv.setAnimacoes(atividade.getAnimacoes());
        }
        atv.setUnidadeMedida(atividade.getUnidadeMedida());
        atv.setCategoriaAtividadeWod(atividade.getCategoriaAtividadeWod());
        if(atv.getAparelhos() == null) {
            atv.setAparelhos(atividade.getAparelhos());
        }

        return atv;
    }

    public static AtividadeAnimacaoDao getAtividadeAnimacaoDao() {
        return (AtividadeAnimacaoDao) UtilContext.getBean(AtividadeAnimacaoDao.class);
    }

    public static AtividadeAparelhoDao getAtividadeAparelhoDao() {
        return (AtividadeAparelhoDao) UtilContext.getBean(AtividadeAparelhoDao.class);
    }

    public static AtividadeEmpresaService getAtividadeEmpresaService() {
        return (AtividadeEmpresaService) UtilContext.getBean(AtividadeEmpresaService.class);
    }

    public String getDescricaoParaLog(AtividadeFicha atividadeFichaAnterior) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, atividadeFichaAnterior));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

}

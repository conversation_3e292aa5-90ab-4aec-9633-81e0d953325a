package br.com.pacto.controller.json.agendamento;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.AgendamentoBuilder;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.ConfiguracoesDiasDeBloqueioDTO;
import br.com.pacto.controller.json.programa.AparelhoController;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.controller.json.wod.WodController;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.HorarioConcomitanteException;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.agenda.TipoFixarAluno;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.agenda.AlunoFixoService;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import br.com.pacto.service.intf.agenda.HorarioDisponibilidadeService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONException;
import org.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by alcides on 09/10/2018.
 */
@Controller
@RequestMapping("/psec/agenda")
public class AgendaController {

    private final AgendaService agendaService;
    private final AgendamentoService agendamentoService;
    private final AlunoFixoService alunoFixoService;
    private final DisponibilidadeService disponibilidadeService;
    private final TipoEventoService tipoEventoService;
    private final HorarioDisponibilidadeService horarioDisponibilidadeService;
    private final SessaoService sessaoService;
    private final UsuarioService usuarioService;

    @Autowired
    public AgendaController(AgendaService agendaService,
                            AlunoFixoService alunoFixoService,
                            AgendamentoService agendamentoService,
                            DisponibilidadeService disponibilidadeService,
                            TipoEventoService tipoEventoService, HorarioDisponibilidadeService horarioDisponibilidadeService, SessaoService sessaoService, UsuarioService usuarioService) {
        this.sessaoService = sessaoService;
        this.usuarioService = usuarioService;
        Assert.notNull(agendaService, "O serviço de agendamento não foi injetado corretamente");
        this.agendaService = agendaService;
        this.tipoEventoService = tipoEventoService;
        this.disponibilidadeService = disponibilidadeService;
        this.agendamentoService = agendamentoService;
        this.alunoFixoService = alunoFixoService;
        this.horarioDisponibilidadeService = horarioDisponibilidadeService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/{horario-turma-id}/substituir-professor", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> substituirProfessor(@PathVariable("horario-turma-id") Integer aulaHorarioId,
                                                                   @RequestParam("dia") Long dia,
                                                                   @RequestBody SubstituirProfessorDTO dados) {
        try {
            agendaService.substituirProfessor(dados, Calendario.getDate("yyyyMMdd", dia.toString()), aulaHorarioId);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agenda", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/marcar-aluno-horario-conversas-ia/{horario-turma-id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> marcarAlunoHorarioConversasIA(
            @PathVariable("horario-turma-id") Integer codigoHorarioTurma,
            @RequestHeader("empresaId") Integer empresaId,
            @RequestParam("matricula") Integer alunoMatricula,
            @RequestParam("dia") Long dia, // formato yyyymmdd - 20250702
            @RequestParam(value = "acao", required = false) String acao,
            @RequestParam(value = "origem", required = false) String origem,
            HttpServletRequest request
    ) {
        // ESTE FLUXO É EXCLUSIVO PARA O CONVERSA AI ***********
        try {
            return ResponseEntityFactory.ok(
                    agendaService.marcarAlunoHorarioConversasIA(
                            null, null, empresaId, codigoHorarioTurma, alunoMatricula,
                            Calendario.getDate("yyyyMMdd", dia.toString()),
                            (StringUtils.isBlank(acao) ? null : AcaoAulaTurmaEnum.valueOf(acao)), origem, request
                    )
            );
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar marcar aluno", e);
            if (e.getCause() != null && e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getCause() != null ? e.getCause().getCause().getMessage() : e.getCause().getMessage());
            }
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/{horario-turma-id}/marcar-aluno", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> adicionarAlunoAula(
                                                                @RequestHeader("empresaId") Integer empresaId,
                                                                @PathVariable("horario-turma-id") Integer aulaHorario,
                                                                @RequestParam("matricula") Integer alunoMatricula,
                                                                @RequestParam("dia") Long dia,
                                                                @RequestParam(value = "autorizado", required = false) Boolean autorizado,
                                                                @RequestParam(value = "acao", required = false) String acao,
                                                                @RequestParam(value = "produtoId", required = false) Integer produtoId,
                                                                @RequestParam(value = "aulaDesmarcarId", required = false) Integer aulaDesmarcarId,
                                                                @RequestParam(value = "diaAulaDesmarcar", required = false) Integer diaAulaDesmarcar,
                                                                HttpServletRequest request
    ) {
        try {
            Uteis.logarDebug("================ ENDPOINT CHAMADO agenda/turmas/{horario-turma-id}/marcar-aluno CHAMADO A SEGUIR VALORES RECEBIDOS ================" +
                    " \nempresaId: " + empresaId + " \naulaHorario: " + aulaHorario + " \nalunoMatricula: " + alunoMatricula +
                    " \ndia: " + Calendario.getDate("yyyyMMdd", dia.toString()) + " \nautorizado: " + autorizado + " \nacao: " + acao + " \nprodutoId: " + produtoId +
                    " \naulaDesmarcarId: " + aulaDesmarcarId + " \ndiaAulaDesmarcar: " + diaAulaDesmarcar);

            return ResponseEntityFactory.ok(agendaService.adicionarAlunoTurma(null, null, empresaId, aulaHorario, alunoMatricula, Calendario.getDate("yyyyMMdd", dia.toString()),
                    produtoId, aulaDesmarcarId, (diaAulaDesmarcar != null ? Calendario.getDate("yyyyMMdd",
                            diaAulaDesmarcar.toString()) : null), (StringUtils.isBlank(acao) ? null : AcaoAulaTurmaEnum.valueOf(acao)),
                    autorizado == null ? false : autorizado,
                    request));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar marcar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                if ((e.getCause().getCause() != null && e.getCause().getCause().getMessage().contains("após o início da aula")) || e.getCause().getMessage().contains("após o início da aula")) {
                    return ResponseEntityFactory.mensagemFront("aposInicioAula",
                            e.getCause().getCause().getMessage().split("marcar")[1].split("após")[0].trim());
                }
                if ((e.getCause().getCause() != null && e.getCause().getCause().getMessage().contains("antes do início da aula")) || e.getCause().getMessage().contains("antes do início da aula")) {
                    return ResponseEntityFactory.mensagemFront("antesInicioAula",
                            e.getCause().getCause().getMessage().split("até")[1].split("antes")[0].trim());
                }
                if ((e.getCause().getCause() != null && e.getCause().getCause().getMessage().contains("A aula já está cheia!")) || e.getCause().getMessage().contains("A aula já está cheia!")) {
                    return ResponseEntityFactory.mensagemFront("aulaEstaCheia",
                            "");
                }
                if ((e.getCause().getCause() != null && e.getCause().getCause().getMessage().contains("Você já se matriculou nessa aula!")) || e.getCause().getMessage().contains("Você já se matriculou nessa aula!")) {
                    return ResponseEntityFactory.mensagemFront("jaMatriculouNaAula",
                            "");
                }
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getCause() != null ? e.getCause().getCause().getMessage() : e.getCause().getMessage());
            }
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/{horario-turma-id}/desmarcar-aluno", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAlunoAula(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("horario-turma-id") Integer aulaHorario,
            @RequestParam(value = "matricula", required = false) Integer alunoMatricula,
            @RequestParam(value = "codigoPassivo", required = false) Integer codigoPassivo,
            @RequestParam("dia") Long dia,
            @RequestParam(value = "alunoVinculoAula", required = false) AlunoVinculoAulaEnum alunoVinculoAula,
            @RequestParam(value = "justificativa") String justificativa,
            @RequestParam(value = "autorizadoGestaoRede", required = false) Boolean autorizadoGestaoRede,
            @RequestParam(value = "codAcessoAutorizado", required = false) String codAcessoAutorizado,
            @RequestParam(value = "matriculaAutorizado", required = false) Integer matriculaAutorizado,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendaService.removerAlunoAula(null, null, empresaId, alunoMatricula, codigoPassivo,
                    aulaHorario, Calendario.getDate("yyyyMMdd", dia.toString()), alunoVinculoAula, justificativa, request, autorizadoGestaoRede, codAcessoAutorizado, matriculaAutorizado));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover aluno da aula", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                String mensagem = e.getCause().getMessage().replaceFirst("Erro", "").trim();
                if(mensagem.contains("soPodeDesmarcarAulaAte")){
                    return ResponseEntityFactory.erroRegistroDuplicado("tratamento_zw_ui",mensagem);
                }
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada",
                        Character.toUpperCase(mensagem.charAt(0)) + mensagem.substring(1));
            }
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/{horario-turma-id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAula(@PathVariable("horario-turma-id") Integer horarioAulaId,
                                                           @RequestParam("dia") Long dia,
                                                           @RequestParam("justificativa")String justificativa,
                                                           @RequestHeader ("empresaId") Integer empresaId) {
        try {
            agendaService.removerAula(Calendario.getDate("yyyyMMdd", dia.toString()), horarioAulaId, justificativa, empresaId);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agenda", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agenda", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarAgendamento(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestBody AgendamentoDTO agendamentoDTO,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.criarAgendamentoAluno(request, empresaId, agendamentoDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos-recorente", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarAgendamentoRecorrente(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestBody AgendamentoDTO agendamentoDTO,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.criarAgendamentoAlunoRecorrente(request, empresaId, agendamentoDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e){
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/validar", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarAgendamento(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestBody AgendamentoDTO agendamentoDTO,
            HttpServletRequest request
    ) {
        try {
            agendamentoService.validaAgendamento(empresaId, null, agendamentoDTO);
            return ResponseEntityFactory.ok();
        } catch (HorarioConcomitanteException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/disponibilidade", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarDisponibilidade(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestBody DisponibilidadeDTO disponibilidadeDTO,
            HttpServletRequest request
    ) {
        try {
            disponibilidadeService.criarDisponibilidade(request, empresaId, disponibilidadeDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/disponibilidadeV2", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarDisponibilidadeV2(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestBody br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO disponibilidadeDTO,
            HttpServletRequest request
    ) {
        try {
            disponibilidadeService.criarDisponibilidadeV2(empresaId, disponibilidadeDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/disponibilidades", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidades(@RequestHeader("empresaId") Integer empresaId,
                                                                @RequestParam("dia") String dia,
                                                                @RequestParam("periodo") PeriodoFiltrarEnum periodo,
                                                                @RequestParam(value = "filtros", required = false) JSONObject filtros,
                                                                HttpServletRequest request) {
        try {
            FiltrosAgendamentosDTO filtrosAgendamentos = new FiltrosAgendamentosDTO(filtros);
            return ResponseEntityFactory.ok(disponibilidadeService.disponibilidades(empresaId, Calendario.getDate("yyyyMMdd", dia), periodo, filtrosAgendamentos, request));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarServicosAgendados(
            @RequestHeader(value = "empresaId") Integer empresaId,
            @RequestParam(value = "ref") Long dia,
            @RequestParam(value = "periodo") PeriodoFiltrarEnum periodoFiltrar,
            @RequestParam(value = "filtros", required = false) JSONObject filtros,
            HttpServletRequest request
    ) throws JSONException {
        try {
            FiltrosAgendamentosDTO filtrosAgendamentos = new FiltrosAgendamentosDTO(filtros);
            return ResponseEntityFactory.ok(agendaService.servicosAgendado(empresaId, request, Calendario.getDate("yyyyMMdd", dia.toString()), periodoFiltrar, filtrosAgendamentos));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agendamentos", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_formatar_data", "Erro ao formatar a data");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/disponibilidades", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidades(
            @RequestHeader(value = "empresaId") Integer empresaId,
            @RequestParam(value = "ref") Long dia,
            @RequestParam(value = "periodo") PeriodoFiltrarEnum periodoFiltrar,
            @RequestParam(value = "filtros", required = false) JSONObject filtros,
            HttpServletRequest request
    ) throws JSONException {
        try {
            String disponibilidadeFiltro = filtros.getString("disponibilidadeFiltro");
            FiltrosAgendamentosDTO filtrosAgendamentos = new FiltrosAgendamentosDTO(filtros);
            return ResponseEntityFactory.ok(disponibilidadeService.disponibilidadesAgendaV3(empresaId, request,
                    Calendario.getDate("yyyyMMdd", dia.toString()), periodoFiltrar,
                    disponibilidadeFiltro != null && disponibilidadeFiltro.equals("DISPONIVEIS"),
                    filtrosAgendamentos, null));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agendamentos", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_formatar_data", "Erro ao formatar a data");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/tipo-disponiveis", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadesTipoEvento(@RequestParam(value = "filters", required = false) JSONObject filtro,
                                                                          @RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            List<TipoAgendamentoDTO> tipos = disponibilidadeService.consultarDisponibilidadesHorario(new Date(filtro.getLong("dia")),
                    filtro.getInt("hora"),
                    empresaId);
            tipos.addAll(disponibilidadeService.consultarDisponibilidadesHorarioTipoAgendamento(new Date(filtro.getLong("dia")),
                    filtro.getInt("hora"),
                    empresaId));
            return ResponseEntityFactory.ok(Ordenacao.ordenarLista(tipos, "nome"));
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os Tipos de Eventos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agendamentos da hora", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/professores-disponiveis", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadesTipoEventoProfessores(@RequestParam(value = "filters", required = false) JSONObject filtro,
                                                                          @RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            List<ColaboradorSimplesTO> colaboradorSimplesTOS = disponibilidadeService.consultarDisponibilidadesHorarioProfessor(new Date(filtro.getLong("dia")),
                    filtro.getInt("hora"),
                    filtro.getInt("tipo"),
                    empresaId);
            colaboradorSimplesTOS.addAll(disponibilidadeService.consultarDisponibilidadesHorarioProfessorTipoAgendamento(new Date(filtro.getLong("dia")),
                    filtro.getInt("hora"),
                    filtro.getInt("tipo"),
                    empresaId));
            return ResponseEntityFactory.ok(Ordenacao.ordenarLista(colaboradorSimplesTOS, "nome"));
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os Tipos de Eventos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agendamentos da hora", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/detalhe-disponibilidade", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalheDisponibilidade(@RequestParam(value = "filters", required = false) JSONObject filtro,
                                                                          @RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            Date diaFormatado = Calendario.getDate("yyyyMMdd", filtro.getString("dia"));
            AgendaDisponibilidadeDTO dto = disponibilidadeService.detalheDisponibilidade(diaFormatado,
                    filtro.getInt("hora"),
                    filtro.getInt("tipo"),
                    filtro.getInt("professor"),
                    empresaId);
            if(dto.getId() == null){
                dto = disponibilidadeService.detalheDisponibilidadeAgenda(new Date(filtro.getLong("dia")),
                        filtro.getInt("hora"),
                        filtro.getInt("tipo"),
                        filtro.getInt("professor"),
                        empresaId);
            }
            return ResponseEntityFactory.ok(dto);
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os Tipos de Eventos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agendamentos da hora", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/tipo-agendamento-duracao/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tipoAgendamentoDuracao(
            @RequestHeader(value = "empresaId") Integer empresaId,
            @PathVariable(value = "id") Integer id
    ) {
        try {
            return ResponseEntityFactory.ok(this.tipoEventoService.obterPorId(id));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agendamentos", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/disponibilidade-tipo-agendamento-duracao/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadeTipoAgendamentoDuracao(
            @RequestHeader(value = "empresaId") Integer empresaId,
            @PathVariable(value = "id") Integer id,
            @RequestParam(value = "ambienteId", required = false) Integer ambienteId,
            @RequestParam(value = "inicio", required = false) String inicio,
            @RequestParam(value = "fim", required = false) String fim
    ) {
        try {
            return ResponseEntityFactory.ok(this.disponibilidadeService.obterPorIdTipoAgendamentoDuracao(id, empresaId, ambienteId, inicio, fim));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agendamentos", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/agendamentos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosdia(@RequestParam("dia") Long dia) {
        try {
            return ResponseEntityFactory.ok(agendaService.agendamentos(Uteis.dataHoraZeradaUTC(dia)));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agendamentos do professor", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/agendamentos/mes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosmes(@RequestParam("mes") Integer mes,
                                                               @RequestParam("ano") Integer ano,
                                                               @RequestHeader("empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(agendaService.agendamentosMes(mes, ano, empresaId));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter agendamentos do mes", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/disponibilidades/tipoAgendamento/{tipo}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadesTipoProfessor(@PathVariable("tipo") Integer tipo,
                                                                             @RequestParam("professorId") Integer professorId) {
        try {
            return ResponseEntityFactory.ok(disponibilidadeService.disponibilidadesTipoProfessor(tipo, professorId));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/disponibilidades/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerDisponibilidade(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestParam(required =  false) Boolean posterioresTambem,
            @PathVariable(value = "id") Integer id) {
        try {
            disponibilidadeService.removerDisponibilidade(empresaId, id, posterioresTambem);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover uma disponibilidade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroRegistroDuplicado(e.getMessage(), e.getCause().getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAgendamento(  @RequestHeader("empresaId") Integer empresaId,
                                                                    @PathVariable("id") Integer id,
                                                                    @RequestBody AgendamentoDTO agendamentoDTO,
                                                                    HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.alterarAgendamentoAluno(request, empresaId, id, agendamentoDTO));
        } catch (HorarioConcomitanteException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAgendamento(@PathVariable("id") Integer id) {
        try {
            agendamentoService.removerAgendamentoAluno(id);
            return ResponseEntityFactory.ok();
        } catch (HorarioConcomitanteException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/{horario-turma-id}/confirmar-presenca", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> confirmarPresenca(
                                                                @RequestHeader("empresaId") Integer empresaId,
                                                                @PathVariable("horario-turma-id") Integer aulaHorario,
                                                                @RequestParam("dia") Long dia,
                                                                @RequestParam(value = "matricula", required = false) Integer matriculaAluno,
                                                                @RequestParam(value = "codigoPassivo", required = false) Integer codigoPassivo,
                                                                @RequestParam(value = "codigoIndicado", required = false) Integer codigoIndicado,
                                                                @RequestParam(value = "alunoVinculoAula", required = false) AlunoVinculoAulaEnum alunoVinculoAula,
                                                                @RequestParam(value = "origemSistema", required = false) String origemSistema,
                                                                @RequestParam(value = "autorizadoGestaoRede", required = false) Boolean autorizadoGestaoRede,
                                                                @RequestParam(value = "codAcessoAutorizado", required = false) String codAcessoAutorizado,
                                                                @RequestParam(value = "matriculaAutorizado", required = false) Integer matriculaAutorizado,
                                                                HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(agendaService.alterarSituacaoAlunoAula(
                    empresaId,
                    aulaHorario,
                    Calendario.getDate("yyyyMMdd", dia.toString()),
                    matriculaAluno,
                    codigoPassivo,
                    codigoIndicado,
                    alunoVinculoAula,
                    false,
                    request,
                    UteisValidacao.emptyString(origemSistema) ? OrigemSistemaEnum.AULA_CHEIA : OrigemSistemaEnum.valueOf(origemSistema),
                    autorizadoGestaoRede,
                    codAcessoAutorizado,
                    matriculaAutorizado
            ));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getMessage());
            }
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/{horario-turma-id}/confirmar-todas-presencas", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> confirmarTodasPresencas(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("horario-turma-id") Integer aulaHorarioId,
            @RequestParam("dia") Long dia,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(agendaService.confirmarTodasPresencas(empresaId, aulaHorarioId, Calendario.getDate("yyyyMMdd", dia.toString()), request, OrigemSistemaEnum.AULA_CHEIA));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar confirmar presença de todos alunos", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getMessage());
            }
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }

    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/{horario-turma-id}/desconfirmar-presenca", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> desconfirmarPresenca(
                                                                    @RequestHeader("empresaId") Integer empresaId,
                                                                    @PathVariable("horario-turma-id") Integer aulaHorario,
                                                                    @RequestParam("dia") Long dia,
                                                                    @RequestParam("matricula") Integer matriculaAluno,
                                                                    @RequestParam(value = "alunoVinculoAula", required = false) AlunoVinculoAulaEnum alunoVinculoAula,
                                                                    @RequestParam(value = "origemSistema", required = false) String origemSistema,
                                                                    @RequestParam(value = "autorizadoGestaoRede", required = false) Boolean autorizadoGestaoRede,
                                                                    @RequestParam(value = "codAcessoAutorizado", required = false) String codAcessoAutorizado,
                                                                    @RequestParam(value = "matriculaAutorizado", required = false) Integer matriculaAutorizado,
                                                                    HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(agendaService.alterarSituacaoAlunoAula(
                    empresaId,
                    aulaHorario,
                    Calendario.getDate("yyyyMMdd", dia.toString()),
                    matriculaAluno,
                    0,
                    0,
                    alunoVinculoAula,
                    true,
                    request,
                    UteisValidacao.emptyString(origemSistema) ? OrigemSistemaEnum.AULA_CHEIA : OrigemSistemaEnum.valueOf(origemSistema),
                    autorizadoGestaoRede,
                    codAcessoAutorizado,
                    matriculaAutorizado));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getMessage());
            }
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/agenda-config/mes", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirAlterarFiltroConfigMes(@RequestParam(value = "config", required = false) String config) throws Exception {
        try {
            boolean mes = true;
            return ResponseEntityFactory.ok(agendaService.incluirAtualizarConfigAgenda(config, mes));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar a config", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "agenda-config/mes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterConfigAgendaMes() throws Exception {
        try {
            boolean mes = true;
            return ResponseEntityFactory.ok(agendaService.obterConfigAgenda().getMes());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao obter config", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/agenda-config/dia", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirAlterarFiltroConfigDia(@RequestParam(value = "config", required = false) String config) throws Exception {
        try {
            boolean mes = false;
            return ResponseEntityFactory.ok(agendaService.incluirAtualizarConfigAgenda(config, mes));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar a config", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "agenda-config/dia", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterConfigAgendaDia() throws Exception {
        try {
            boolean mes = false;
            return ResponseEntityFactory.ok(agendaService.obterConfigAgenda().getDia());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao obter config", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodasTurmas(
            @RequestHeader(value = "empresaId") Integer empresaId,
            @RequestParam(value = "ref") Long ref,
            @RequestParam(value = "periodo") PeriodoFiltrarEnum periodo,
            @RequestParam (value = "filtros", required = false) JSONObject filtros,
            HttpServletRequest request
            ) throws JSONException {
        try {
            FiltroTurmaDTO filtroTurmaDTO = new FiltroTurmaDTO(filtros);
            return ResponseEntityFactory.ok(agendaService.obterTodasTurmas(null, request, empresaId, Calendario.getDate("yyyyMMdd", ref.toString()), periodo, filtroTurmaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter turmas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas-zw/aluno/{matricula-aluno}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTurmasAlunoPorDia(
            @PathVariable("matricula-aluno") Integer matriculaAluno,
            @RequestParam(value = "data", required = false) Long dia
    ) {
        try {
            return ResponseEntityFactory.ok(agendaService.turmasAlunoDia(matriculaAluno, (dia == null ? null : Calendario.getDate("yyyyMMdd", dia.toString()))));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter turmas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/produtos-zw/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterProdutosZW(
            @RequestHeader("empresaId") Integer empresaId
    ) {
        try {
            return ResponseEntityFactory.ok(agendaService.produtosZW(empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter produtos do ZW", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/{horario-turma-id}/aula-detalhada", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulaDetalhada(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("horario-turma-id") Integer aulaHorario,
            @RequestParam("dia") Long dia,
            @RequestParam("completo") Boolean completo,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(agendaService.aulaDetalhada(null, empresaId, aulaHorario,
                    Calendario.getDate("yyyyMMdd", dia.toString()), completo, request));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getMessage());
            }
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/disponibilidades/info/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> infoDisponibilidade(
            @PathVariable(value = "id") Integer id) {
        try {
            return ResponseEntityFactory.ok(disponibilidadeService.configDisponibilidade(id));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover uma disponibilidade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroRegistroDuplicado(e.getMessage(), e.getCause().getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/disponibilidades/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarDisponibilidade(  @RequestHeader("empresaId") Integer empresaId,
                                                                    @PathVariable("id") Integer id,
                                                                    @RequestBody DisponibilidadeEditDTO edit
    ) {
        try {
            disponibilidadeService.alterarDisponibilidade(id, edit);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar a disponibilidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/validar/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarAgendamento(  @RequestHeader("empresaId") Integer empresaId,
                                                                    @PathVariable("id") Integer id,
                                                                    @RequestBody AgendamentoDTO agendamentoDTO,
                                                                    HttpServletRequest request
    ) {
        try {
            agendamentoService.validaAgendamento(empresaId, id, agendamentoDTO);
            return ResponseEntityFactory.ok();
        } catch (HorarioConcomitanteException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/{horario-turma-id}/fixar-aluno", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> fixarAlunoAula(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("horario-turma-id") Integer aulaHorario,
            @RequestParam("matricula") String matricula,
            @RequestParam("dataAula") String dataAula,
            @RequestParam("dataFinal") String dataFinal,
            @RequestParam("tipo") TipoFixarAluno tipo,
            @RequestParam(value = "origem", required = false) String origem,
            @RequestParam(value = "validarAulaCheia", required = false) Boolean validarAulaCheia
    ) {
        try {
            alunoFixoService.fixarAlunoTurma(
                    aulaHorario,
                    matricula,
                    Uteis.getDate(dataAula, "yyyyMMdd").getTime(),
                    tipo.equals(TipoFixarAluno.DATA_DETERMINADA) ? Long.valueOf(dataFinal) : null,
                    tipo, origem, validarAulaCheia != null && validarAulaCheia);
            return ResponseEntityFactory.ok();
        } catch (ValidacaoException ve) {
            Map<String, List<String>> aulasCheias = new HashMap<>();
            aulasCheias.put("aulasCheias", ve.getMensagens());
            return ResponseEntityFactory.ok(aulasCheias);
        } catch (Exception e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar fixar aluno", e);
            return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getCause() != null ? e.getCause().getCause().getMessage() : e.getCause().getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/turmas/{horario-turma-id}/desafixar-aluno", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> desafixarAlunoAula(
            @PathVariable("horario-turma-id") Integer aulaHorario,
            @RequestParam("matricula") String matricula,
            @RequestParam("dataDesafixar") String dataDesafixar
    ) {
        try {
            alunoFixoService.desafixarAlunoTurma(
                    aulaHorario,
                    matricula,
                    Uteis.getDate(dataDesafixar, "yyyyMMdd").getTime());
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar desafixar aluno", e);
            return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getCause() != null ? e.getCause().getCause().getMessage() : e.getCause().getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/servicos/validarV2", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarAgendamentoV2(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestBody AgendamentoDTO agendamentoDTO,
            HttpServletRequest request
    ) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer usuarioId = sessaoService.getUsuarioAtual().getId();
            Usuario usuario = usuarioService.obterPorId(ctx,usuarioId);
            Agendamento agendamento = AgendamentoBuilder.agendamentoDtoToAgendamento(ctx, agendamentoDTO);

           agendamentoService.validaAgendamentoV2(ctx, agendamento, usuario, empresaId);

            return ResponseEntityFactory.ok();
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar agendamento de aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/relatorio/alunos-faltosos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAlunosFaltosos(
            @RequestHeader(value = "empresaId") Integer empresaId,
            @RequestParam(value = "page", required = false, defaultValue = "1") Long page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Long size,
            @RequestParam(value = "filters", required = false) JSONObject filters,
            PaginadorDTO paginadorDTO) {

        try {
            FiltroAlunosFaltososDTO filtro;
            try {
                filtro = new FiltroAlunosFaltososDTO(filters);
            } catch (JSONException e) {
                filtro = null;
                e.printStackTrace();
            }

            List<Map<String, Object>> alunosFaltosos = agendaService.listarAlunosFaltosos(paginadorDTO, empresaId, filtro);
            EnvelopeRespostaDTO respostaDTO = EnvelopeRespostaDTO.of(alunosFaltosos, paginadorDTO);
            return ResponseEntityFactory.ok(respostaDTO);

        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao listar alunos faltosos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao processar a requisição", e);
            return ResponseEntityFactory.erroInterno("erro_interno", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/configurar-dias-bloqueio", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> configurarDiasBloqueio(
            @RequestBody ConfiguracoesDiasDeBloqueioDTO configuracoesDiasDeBloqueioDTO,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.configurarDiasBloqueio(configuracoesDiasDeBloqueioDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/buscar-configuracao-dias-bloqueio", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarConfiguracaoDiasBloqueio(
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.buscarConfiguracaoDiasBloqueio());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter configuração dias de bloqueio", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/equipamento/{horario-turma-id}/{alunoId}/{dia}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirHorarioEquipamentoAluno(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("horario-turma-id") Integer aulaHorarioId,
            @PathVariable("alunoId") Integer alunoId,
            @PathVariable("dia") String dia,
            @RequestParam("equipamento") String equipamento,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer usuarioId = sessaoService.getUsuarioAtual().getId();
            if (!UteisValidacao.emptyString(equipamento) && equipamento.startsWith(";")) {
                    equipamento = equipamento.split(";")[1];
            }
            return ResponseEntityFactory.ok(agendaService.incluirHorarioEquipamentoAluno(ctx, empresaId, aulaHorarioId, alunoId, equipamento, usuarioId, dia, false));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir equipamento", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/selfloops/atividades/{horario-turma-id}/{dia}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAtividadesCourseDoDiaIntegracaoSelfloops(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("horario-turma-id") Integer aulaHorarioId,
            @PathVariable("dia") String dia,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            agendaService.obterAtividadesCourseDoDiaIntegracaoSelfloops(ctx, empresaId, aulaHorarioId, dia);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir equipamento", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/buscarEquipamentoAula/{turma-id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarHorarioEquipamento(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("turma-id") Integer aulaId,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(agendaService.horarioEquipamento(ctx, aulaId, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar equipamento", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/buscarEquipamentoOcupado/{horario-turma-id}/{dia}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarHorarioEquipamentoOcupado(
            @PathVariable("horario-turma-id") Integer aulaHorarioId,
            @PathVariable("dia") String dia,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(agendaService.horarioEquipamentoOcupado(ctx, Calendario.getDate("yyyyMMdd", dia), aulaHorarioId));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar equipamento", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getMessage());
            }
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/deletarEquipamento/{horario-turma-id}/{alunoId}/{dia}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletarHorarioEquipamentoOcupado(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("horario-turma-id") Integer aulaHorarioId,
            @PathVariable("alunoId") Integer alunoId,
            @PathVariable("dia") String dia,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            agendaService.deletarHorarioEquipamentoAluno(ctx, empresaId, aulaHorarioId, alunoId, Calendario.getDate("yyyyMMdd", dia));
            return ResponseEntityFactory.ok("sucesso");
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar equipamento", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getCause().getMessage());
            }
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }
}

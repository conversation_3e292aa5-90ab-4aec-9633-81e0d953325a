package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.aulaDia.MapaEquipamentoAparelhoDTO;
import br.com.pacto.controller.json.modalidade.ModalidadeSimplesDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by paulo 08/08/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TurmaResponseDTO {

    private Integer horarioTurmaId;
    private String dia;
    private String nome;
    private Boolean aulaCheia;
    private String horarioInicio;
    private String horarioFim;
    private AmbienteResponseTO ambiente;
    private ModalidadeSimplesDTO modalidade;
    private ColaboradorSimplesTO professor;
    private ColaboradorSimplesTO professorSubstituto;
    private NivelResponseTO nivel;
    private Integer capacidade;
    private Integer numeroAlunos;
    private Boolean permitirAulaExperimental;
    private List<AlunoTurmaDTO> alunos = new ArrayList<>();
    private Boolean permiteAlunoOutraEmpresa;
    private Boolean bloqueado = Boolean.FALSE;
    private Boolean atualizarAgenda = Boolean.FALSE;
    private Boolean permiteFixar = Boolean.FALSE;
    private Boolean aulaColetiva = Boolean.FALSE;
    private Boolean bloquearMatriculasAcimaLimite = Boolean.FALSE;
    private String mapaEquipamentos;
    private String equipamentosOcupados;
    private List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho;
    private Integer idadeMinima;
    private Integer idadeMaxima;
    private Integer idadeMinimaMeses;
    private Integer idadeMaximaMeses;
    private Integer qtdeMaximaAlunoExperimental;
    private Integer qtdeAulaExperimentalDisponivel;

    public TurmaResponseDTO() {
    }

    public Integer getHorarioTurmaId() {
        return horarioTurmaId;
    }

    public void setHorarioTurmaId(Integer horarioTurmaId) {
        this.horarioTurmaId = horarioTurmaId;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAulaCheia() {
        return aulaCheia;
    }

    public void setAulaCheia(Boolean aulaCheia) {
        this.aulaCheia = aulaCheia;
    }

    public String getHorarioInicio() {
        return horarioInicio;
    }

    public void setHorarioInicio(String horarioInicio) {
        this.horarioInicio = horarioInicio;
    }

    public String getHorarioFim() {
        return horarioFim;
    }

    public void setHorarioFim(String horarioFim) {
        this.horarioFim = horarioFim;
    }

    public AmbienteResponseTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteResponseTO ambiente) {
        this.ambiente = ambiente;
    }

    public ModalidadeSimplesDTO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeSimplesDTO modalidade) {
        this.modalidade = modalidade;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public ColaboradorSimplesTO getProfessorSubstituto() {
        return professorSubstituto;
    }

    public void setProfessorSubstituto(ColaboradorSimplesTO professorSubstituto) {
        this.professorSubstituto = professorSubstituto;
    }

    public NivelResponseTO getNivel() {
        return nivel;
    }

    public void setNivel(NivelResponseTO nivel) {
        this.nivel = nivel;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getNumeroAlunos() {
        return numeroAlunos;
    }

    public void setNumeroAlunos(Integer numeroAlunos) {
        this.numeroAlunos = numeroAlunos;
    }

    public Boolean getPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public void setPermitirAulaExperimental(Boolean permitirAulaExperimental) {
        this.permitirAulaExperimental = permitirAulaExperimental;
    }

    public List<AlunoTurmaDTO> getAlunos() {
        if (alunos == null) {
            alunos = new ArrayList<>();
        }
        return alunos;
    }

    public void setAlunos(List<AlunoTurmaDTO> alunos) {
        if (getAlunos().size() > 0) {
            getAlunos().clear();
        }
        getAlunos().addAll(alunos);
    }

    public Boolean getPermiteAlunoOutraEmpresa() {
        return permiteAlunoOutraEmpresa;
    }

    public void setPermiteAlunoOutraEmpresa(Boolean permiteAlunoOutraEmpresa) {
        this.permiteAlunoOutraEmpresa = permiteAlunoOutraEmpresa;
    }

    public Boolean getBloqueado() {
        return bloqueado;
    }

    public void setBloqueado(Boolean bloqueado) {
        this.bloqueado = bloqueado;
    }

    public Boolean getAtualizarAgenda() {
        return atualizarAgenda;
    }

    public void setAtualizarAgenda(Boolean atualizarAgenda) {
        this.atualizarAgenda = atualizarAgenda;
    }

    public String getPeriodo(){
        return horarioInicio + "-" + horarioFim;
    }

    public Boolean getPermiteFixar() {
        return permiteFixar;
    }

    public void setPermiteFixar(Boolean permiteFixar) {
        this.permiteFixar = permiteFixar;
    }

    public Boolean getAulaColetiva() {
        return aulaColetiva;
    }

    public void setAulaColetiva(Boolean aulaColetiva) {
        this.aulaColetiva = aulaColetiva;
    }

    public Boolean getBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public void setBloquearMatriculasAcimaLimite(Boolean bloquearMatriculasAcimaLimite) {
        this.bloquearMatriculasAcimaLimite = bloquearMatriculasAcimaLimite;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public String getEquipamentosOcupados() {
        return equipamentosOcupados;
    }

    public void setEquipamentosOcupados(String equipamentosOcupados) {
        this.equipamentosOcupados = equipamentosOcupados;
    }

    public List<MapaEquipamentoAparelhoDTO> getListaMapaEquipamentoAparelho() {
        return listaMapaEquipamentoAparelho;
    }

    public void setListaMapaEquipamentoAparelho(List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho) {
        this.listaMapaEquipamentoAparelho = listaMapaEquipamentoAparelho;
    }

    public Integer getIdadeMaxima() {
        return idadeMaxima;
    }

    public void setIdadeMaxima(Integer idadeMaxima) {
        this.idadeMaxima = idadeMaxima;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public void setIdadeMinima(Integer idadeMinima) {
        this.idadeMinima = idadeMinima;
    }

    public Integer getIdadeMinima() {
        return idadeMinima;
    }

    public void setQtdeMaximaAlunoExperimental(Integer qtdeMaximaAlunoExperimental) {
        this.qtdeMaximaAlunoExperimental = qtdeMaximaAlunoExperimental;
    }

    public Integer getQtdeMaximaAlunoExperimental() {
        return qtdeMaximaAlunoExperimental;
    }

    public void setQtdeAulaExperimentalDisponivel(Integer qtdeAulaExperimentalDisponivel) {
        this.qtdeAulaExperimentalDisponivel = qtdeAulaExperimentalDisponivel;
    }

    public Integer getQtdeAulaExperimentalDisponivel() {
        return qtdeAulaExperimentalDisponivel;
    }
}

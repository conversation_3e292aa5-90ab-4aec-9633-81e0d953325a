package br.com.pacto.controller.json.log;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.atividade.AtividadeCompletaResponseTO;
import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.dao.intf.anamnese.RespostaClienteDao;
import br.com.pacto.controller.json.turma.TurmaResponseDTO;
import br.com.pacto.dao.intf.anamnese.RespostaClienteDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.log.LogTO;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.avaliacao.LogAvaliacaoFisicaService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.log.LogProgramaService;
import br.com.pacto.service.intf.log.LogService;
import br.com.pacto.service.intf.nivel.NivelService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.service.intf.turma.TurmaService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/psec/log")
public class LogController {

    @Autowired
    private LogService logService;
    @Autowired
    private AulaService aulaService;
    @Autowired
    private TipoEventoService tipoEventoService;
    @Autowired
    private AnamneseService anamneseService;
    @Autowired
    private NivelService nivelService;
    @Autowired
    private ProgramaTreinoService programaTreinoService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private FichaService fichaService;
    @Autowired
    private AvaliacaoFisicaService avaliacaoFisicaService;
    @Autowired
    private LogProgramaService logProgramaService;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private LogAvaliacaoFisicaService logAvaliacaoFisicaService;
    @Autowired
    private AtividadeService atividadeService;
    @Autowired
    private TurmaService turmaService;
    @Autowired
    private RespostaClienteDao respostaClienteDao;

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logs(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                    PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(null, filtros, paginadorDTO, null, "", ""),
                    paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/atividades/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atividades(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                         @PathVariable Integer id,
                                                                         PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.ATIVIDADE,
                    filtros, paginadorDTO, "'"+id+"'", "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/atividades", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atividades(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          PaginadorDTO paginadorDTO) throws JSONException {
        try {
            List<LogTO> lista = logService.listarLog(EntidadeLogEnum.ATIVIDADE, filtros, paginadorDTO,
                    "", "", "");
            for(LogTO log : lista){
                if(log.getChave()!=null){
                    AtividadeCompletaResponseTO atividade = atividadeService.buscarAtividade(Integer.valueOf(log.getChave()));
                    log.setChave(log.getChave()+" - "+atividade.getNome());
                }

            }
            return ResponseEntityFactory.ok(lista, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/atividades/ia", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atividadesIa(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.ATIVIDADE, filtros, paginadorDTO,
                    "", "", "", true), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(
            value = "/avaliacoes-aluno/{codAluno}",
            method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoes(
            @RequestParam(value = "filters", required = false) String filtrosStr,
            @PathVariable Integer codAluno,
            PaginadorDTO paginadorDTO
    ) throws JSONException {
        try {
            JSONObject filtros = new JSONObject();
            if (filtrosStr != null) {
                try { filtros = new JSONObject(filtrosStr); }
                catch (Exception ignored) {}
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Long originalSize = paginadorDTO.getSize();
            List<LogTO> logAva = logService.listarLog(EntidadeLogEnum.AVALIACAOFISICA, filtros, paginadorDTO, "", "'" + codAluno + "'", "AVALIAÇÃO FÍSICA");
            paginadorDTO.setSize(paginadorDTO.getSize() - logAva.size());
            Long totalAvaFisica = logAva.isEmpty() ? 0 : paginadorDTO.getQuantidadeTotalElementos();
            logAva.addAll(logAvaliacaoFisicaService.listarLogAtividade(filtros, paginadorDTO, codAluno, null));
            List<RespostaCliente> respostasAnamnese;
            try {
                Map<String,Object> params = Collections.singletonMap("codAluno", codAluno);
                respostasAnamnese = respostaClienteDao.findByParam(ctx, "select rc from RespostaCliente rc join rc.itemAvaliacao ia where ia.cliente.codigo = :codAluno", params);
            } catch (Exception e) {
                throw new ServiceException(e);
            }

            List<LogTO> logAnamnese = Collections.emptyList();
            if (!respostasAnamnese.isEmpty()) {
                String chavesPrimarias = respostasAnamnese.stream()
                        .map(rc -> "'" + rc.getCodigo() + "'")
                        .collect(Collectors.joining(","));
                String chavesSecundarias = respostasAnamnese.stream()
                        .map(rc -> "'" + rc.getPerguntaAnamnese().getCodigo() + "'")
                        .collect(Collectors.joining(","));

                logAnamnese = logService.listarLog(
                        EntidadeLogEnum.ANAMNESE,
                        filtros,
                        paginadorDTO,
                        chavesPrimarias,
                        chavesSecundarias,
                        "ALTERAÇÃO RESPOSTA ANAMNESE"
                );
                logAva.addAll(logAnamnese);
            }

            paginadorDTO.setSize(originalSize);
            paginadorDTO.setQuantidadeTotalElementos(
                    totalAvaFisica + (logAnamnese == null ? 0 : logAnamnese.size())
            );

            return ResponseEntityFactory.ok(logAva, paginadorDTO);

        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName())
                    .log(Level.SEVERE, "Erro ao listar logs de avaliação", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/avaliacoes-aluno/{codAluno}/{codAvaliacao}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoes(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          @PathVariable Integer codAluno,
                                                          @PathVariable Integer codAvaliacao,
                                                          PaginadorDTO paginadorDTO) throws JSONException {
        try {
            List<LogTO> logConsolidado = logAvaliacaoFisicaService
                    .listarLogConsolidadoAvaliacaoFisica(filtros, paginadorDTO, codAluno, codAvaliacao);
            return ResponseEntityFactory.ok(logConsolidado, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/categoria-atividades", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> categoriaAtividades(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLogCategoriaAtividade(filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/atividades-crossfit", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atividadesCrossfit(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLogAtividade(filtros, paginadorDTO, true, null), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aparelhos/{crossfit}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aparelhos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                         @PathVariable Boolean crossfit,
                                                         PaginadorDTO paginadorDTO) throws JSONException {
        try {
            if (crossfit) {
                return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.APARELHO, filtros, paginadorDTO, null, "", ""), paginadorDTO);
            } else {
                return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.APARELHO,filtros, paginadorDTO, null, "", ""), paginadorDTO);
            }
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar aparelhos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/wods", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> wods(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.WOD,filtros, paginadorDTO, "", "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar WOD", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/benchmarks", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> banchmark(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarBanchmark(filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/tipos-benchmarks", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tiposBenchmarks(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarTiposbenchmarks(filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/colaboradoresUsiario", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> colaboradores(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                               PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarColaboradoresUsuario(filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/musculos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> musculos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarMusculos(filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/grupos-musculares", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gruposMusculares(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLogGruposMusculares(filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/avaliacao-objetivos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacaoObjetivos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLogAvaliacaoObjetivos(filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os objetivos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/anamneses", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> anamneses(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                         PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.ANAMNESE,filtros, paginadorDTO, null, "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar o log de anamneses", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/anamneses/{codigo}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> anamneses(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                         @PathVariable Integer codigo,
                                                         PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.ANAMNESE, filtros, paginadorDTO,
                    "'"+codigo+"'", "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar o log de anamneses", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/tipos-wod", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tiposWods(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarTiposWod(filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aula/{id}/{dia}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aula(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                    PaginadorDTO paginadorDTO,
                                                    @RequestHeader (value = "empresaId", required = true) Integer empresaId,
                                                    @PathVariable String id,
                                                    @PathVariable String dia) throws JSONException {
        try {
            return ResponseEntityFactory.ok(aulaService.listarLogAula(dia, id, empresaId, filtros, paginadorDTO)
                    , paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar log da aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aula/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendaAula(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                    PaginadorDTO paginadorDTO,
                                                    @RequestHeader (value = "empresaId", required = true) Integer empresaId,
                                                    @PathVariable String id) throws JSONException {
        try {
            return ResponseEntityFactory.ok(aulaService.listarLogAgendaAulas(id, empresaId, filtros, paginadorDTO)
                    , paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aula", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendaAulas(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          PaginadorDTO paginadorDTO,
                                                          @RequestHeader (value = "empresaId", required = true) Integer empresaId) throws JSONException {
        try {
            List<LogTO> lista = logService.listarLog(EntidadeLogEnum.AULA_COLETIVA, filtros, paginadorDTO, "", "", "");
            for(LogTO log : lista){
                if(log.getChave()!=null){
                    TurmaResponseDTO turma = turmaService.obterTurma(Integer.valueOf(log.getChave()));
                    log.setChave(log.getChave()+" - "+turma.getIdentificador());
                }
            }
            return ResponseEntityFactory.ok(lista, paginadorDTO);

        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/turma", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendaTurmas(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.TURMA,filtros, paginadorDTO, null, "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }



    @ResponseBody
    @RequestMapping(value = "/horarioTurma/{turma}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendaHorariosTurma(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            PaginadorDTO paginadorDTO, @PathVariable String turma) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.HORARIO_TURMA,filtros, paginadorDTO, null, "'"+turma+"'", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/programas-predefinidos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> programasPredefinidos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(
                    EntidadeLogEnum.PROGRAMA,
                    filtros, paginadorDTO, null, "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os logs dos programas predefinidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/programa/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> programa(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                        @PathVariable Integer id,
                                                        PaginadorDTO paginadorDTO) throws JSONException {
        try {
            long tamanhoPagina = paginadorDTO.getSize();

            ProgramaTreino prog = programaTreinoService.obterPorId(sessaoService.getUsuarioAtual().getChave(), id);
            StringBuilder codigos = new StringBuilder("'"+prog.getCodigo().toString()+"'");
            for (ProgramaTreinoFicha programaFicha : prog.getProgramaFichas()) {
                codigos.append(",").append("'").append(programaFicha.getFicha().getCodigo()).append("'");
            }
            List<LogTO> logs = logService.listarLog(EntidadeLogEnum.FICHA, filtros,
                    paginadorDTO, codigos.toString(), "", "");
            logs.addAll(logService.listarLog(EntidadeLogEnum.FICHA,
                    filtros, paginadorDTO, "", "'"+prog.getCodigo()+"'", "EXCLUSÃO"));

            if (logs.size() < tamanhoPagina) {
                PaginadorDTO paginadorDTO1 = new PaginadorDTO(paginadorDTO);
                logs.addAll(logProgramaService.listarLogProgramas(null, id, filtros, paginadorDTO));
                paginadorDTO.setQuantidadeTotalElementos(paginadorDTO.getQuantidadeTotalElementos()
                        + paginadorDTO1.getQuantidadeTotalElementos());
            }
            return ResponseEntityFactory.ok(logs, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os logs do programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/programas-aluno/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> programasAluno(@RequestParam(value = "filters", required = false) String filtrosStr,
                                                              @PathVariable Integer id,
                                                              PaginadorDTO paginadorDTO) throws JSONException {
        try {
            JSONObject filtros = new JSONObject();
            if (filtrosStr != null) {
                try {
                    filtros = new JSONObject(filtrosStr);
                } catch (Exception ignored) {}
            }
            long tamanhoPagina = paginadorDTO.getSize();
            List<LogTO> logNovo = logService.listarLog(EntidadeLogEnum.PROGRAMA, filtros, paginadorDTO, "",
                    "'" + id.toString() + "'", "PROGRAMA");
            if (logNovo.size() < tamanhoPagina) {
                PaginadorDTO paginadorDTO1 = new PaginadorDTO(paginadorDTO);
                logNovo.addAll(logProgramaService.listarLogProgramas(id, null, filtros, paginadorDTO1));
                paginadorDTO.setQuantidadeTotalElementos(paginadorDTO.getQuantidadeTotalElementos()
                        + paginadorDTO1.getQuantidadeTotalElementos());
            }
            return ResponseEntityFactory.ok(logNovo, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os logs dos programas do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/fichas-predefinidas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> fichasPredefinidas(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                     PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.FICHAPREDEFINIDA,filtros, paginadorDTO, "", "", "PRÉ-DEFINIDA"), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os logs de fichas predefinidas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/ficha-predefinida/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> fichaPredefinida(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  @PathVariable Integer id,
                                                                  PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.FICHAPREDEFINIDA,
                    filtros, paginadorDTO, "'"+id.toString()+"'", "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os logs da ficha predefinida", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/perfil-acesso", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> perfilAcesso(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLogPerfilAcesso(filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os perfis de acesso", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/disponibilidades", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidades(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                PaginadorDTO paginadorDTO) throws JSONException {
        try {
            long tamanhoPagina = paginadorDTO.getSize();
            List<LogTO> logNovo = logService.listarLog(EntidadeLogEnum.DISPONIBILIDADE, filtros, paginadorDTO, null, null, "");

            if (logNovo.size() < tamanhoPagina) {
                PaginadorDTO paginadorDTO1 = new PaginadorDTO(paginadorDTO);
                logNovo.addAll(logService.listarLogDisponibilidades(filtros, paginadorDTO1, false, null));
                paginadorDTO.setQuantidadeTotalElementos(paginadorDTO.getQuantidadeTotalElementos()
                        + paginadorDTO1.getQuantidadeTotalElementos());
            }

            return ResponseEntityFactory.ok(logNovo, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/disponibilidades/{cod}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadesCodigo(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          @PathVariable Integer cod,
                                                          PaginadorDTO paginadorDTO) throws JSONException {
        try {
            List<LogTO> logNovo = new ArrayList<>();
            long tamanhoPagina = paginadorDTO.getSize();

            Integer nsuAgendamento = agendamentoService.obterNsuPorCodigoAgendamento(sessaoService.getUsuarioAtual().getChave(), cod);
            if (!UteisValidacao.emptyNumber(nsuAgendamento)) {
                logNovo.addAll(logService.listarLog(
                        EntidadeLogEnum.DISPONIBILIDADE,
                        filtros,
                        paginadorDTO,
                        "'" + nsuAgendamento + "'",
                        null,
                        "INCLUSÃO"));
            }

            logNovo.addAll(logService.listarLog(EntidadeLogEnum.DISPONIBILIDADE, filtros, paginadorDTO, "'"+cod.toString()+"'", null, ""));
            if(logNovo.size() < tamanhoPagina){
                PaginadorDTO paginadorDTO1 = new PaginadorDTO(paginadorDTO);
                logNovo.addAll(logService.listarLogDisponibilidades(filtros, paginadorDTO1, false, cod));
                paginadorDTO.setQuantidadeTotalElementos(paginadorDTO.getQuantidadeTotalElementos()
                        + paginadorDTO1.getQuantidadeTotalElementos());
            }
            return ResponseEntityFactory.ok(logNovo, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/agendamentos/{cod}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosCodigo(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          @PathVariable Integer cod,
                                                          PaginadorDTO paginadorDTO) throws JSONException {
        try {
            long tamanhoPagina = paginadorDTO.getSize();
            List<LogTO> logNovo = logService.listarLog(EntidadeLogEnum.AGENDADESERVICO, filtros, paginadorDTO, "'"+cod.toString()+"'", null, "");
            if(logNovo.size() < tamanhoPagina){
                PaginadorDTO paginadorDTO1 = new PaginadorDTO(paginadorDTO);
                logNovo.addAll(logService.listarLogDisponibilidades(filtros, paginadorDTO1, false, cod));
                paginadorDTO.setQuantidadeTotalElementos(paginadorDTO.getQuantidadeTotalElementos()
                        + paginadorDTO1.getQuantidadeTotalElementos());
            }
            return ResponseEntityFactory.ok(logNovo, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os agendamentos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/agendamentos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.AGENDADESERVICO,filtros, paginadorDTO, null, "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os agendamentos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ResponseBody
    @RequestMapping(value = "/perfil-acesso/{perfilId}/{categoriaRecurso}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> perfilAcessoRecurso(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   @PathVariable Integer perfilId,
                                                                   @PathVariable String categoriaRecurso,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLogPerfilAcessoRecurso(filtros, perfilId, categoriaRecurso, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os perfis de acesso", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/tipo-agendamento/{cod}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tipoAgendamentoCodigo(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                      @PathVariable Integer cod,
                                                                      PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.TIPOEVENTO, filtros, paginadorDTO,
                    "'"+cod.toString()+"'", "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar log de tipo de agendamento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/tipo-agendamento", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tipoAgendamento(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                      PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.TIPOEVENTO, filtros, paginadorDTO,
                    null, "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar log de tipo de agendamento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/niveis/{cod}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> niveisCodigo(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                      @PathVariable Integer cod,
                                                                      PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.NIVEL,
                    filtros, paginadorDTO, "'"+cod.toString()+"'", "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os niveis", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/niveis", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> niveis(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                      PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.NIVEL,
                    filtros, paginadorDTO, null, "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar log de tipo de agendamento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/integracoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> integracoes(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLogDisponibilidades(filtros, paginadorDTO, true, null), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/alunos/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> programasAluno(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                              @PathVariable String matricula,
                                                              PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.ALUNO,
                    filtros, paginadorDTO, "'"+matricula+"'", "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os logs do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/log-perfil-acesso/{entidade}/{codigoperfil}/{categoriarecurso}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logPerfilAcesso(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                           @PathVariable String entidade,
                                                           @PathVariable String codigoperfil,
                                                           @PathVariable String categoriarecurso,
                                                          PaginadorDTO paginadorDTO) throws JSONException {
        try {

            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.fromEntidade(entidade),
                    filtros, paginadorDTO, (codigoperfil.equals("null") ? null : "'"+codigoperfil+"'") ,(categoriarecurso.equals("null") ? null : "'"+categoriarecurso+"'"), ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar log", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/nivel-wod", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> nivelWod(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                               PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.NIVELWOD,
                    filtros, paginadorDTO, "", "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os Níveis wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/listar-log-exportacao/{entidade}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarLogExportacao(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   @PathVariable String entidade,
                                                                   PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLogExportacaoZW(ItemExportacaoEnum.obterPorId(entidade),
                    filtros, paginadorDTO, "", "", "",false), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os Níveis wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/locacoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> locacoes(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.LOCACAO, filtros, paginadorDTO, null, "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os logs das locações", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/locacoes-horarios", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> locacoesHorarios(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                        PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.LOCACAO_HORARIO, filtros, paginadorDTO, null, "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os logs das locações", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/categoria-ficha", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> categoriaFicha(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                        PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CATEGORIA_FICHA_PREDEFINIDA,
                    filtros, paginadorDTO, "", "", ""), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LogController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os Níveis wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}

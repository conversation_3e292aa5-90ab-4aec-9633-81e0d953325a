/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.ficha.read;

import br.com.pacto.bean.atividade.*;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.serie.SerieTO;
import br.com.pacto.controller.json.atividade.read.AjusteJSON;
import br.com.pacto.controller.json.atividade.read.AtividadeFichaJSON;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.atividade.read.SerieJSON;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.programa.read.OrigemEnum;
import br.com.pacto.controller.json.serialization.JsonDateTimeSerializerYYYYMMDDHHNN;
import br.com.pacto.dao.impl.ficha.FichaDaoImpl;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.ficha.FichaServiceImpl;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.enumeradores.MidiasNiveisEnum;
import br.com.pacto.util.impl.Ordenacao;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static br.com.pacto.bean.atividade.AtividadeFicha.getAtividadeAnimacaoDao;

/**
 * <AUTHOR>
 */
public class FichaJSON extends SuperJSON {

    private Integer cod;
    private String versao;
    private String nome;
    private List<AtividadeFichaJSON> atividades = new ArrayList<AtividadeFichaJSON>();
    private String codPrograma = "";
    private String mensagemAluno = "";
    private String categoria;
    private Integer tipoExecucao;
    private List<String> diaSemana = new ArrayList<String>();//<List>DiasSemana.class    
    private Date ultimaExecucao;
    private Long ultimaExecucaoLong;
    private AtividadeService atividadeService;
    private Boolean fichaDoDiaAtual;

    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getVersao() {
        return versao;
    }

    public void setVersao(String versao) {
        this.versao = versao;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<AtividadeFichaJSON> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeFichaJSON> exercicios) {
        this.atividades = exercicios;
    }

    public String getCodPrograma() {
        return codPrograma;
    }

    public void setCodPrograma(String codPrograma) {
        this.codPrograma = codPrograma;
    }

    public String getMensagemAluno() {
        return mensagemAluno;
    }

    public void setMensagemAluno(String mensagemAluno) {
        this.mensagemAluno = mensagemAluno;
    }

    public String getCategoria() {
        if (categoria == null) {
            categoria = "";
        }
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public Integer getTipoExecucao() {
        return tipoExecucao;
    }

    public void setTipoExecucao(Integer tipoExecucao) {
        this.tipoExecucao = tipoExecucao;
    }

    public List<String> getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(List<String> diaSemana) {
        this.diaSemana = diaSemana;
    }

    @JsonSerialize(using = JsonDateTimeSerializerYYYYMMDDHHNN.class)
    public Date getUltimaExecucao() {
        return ultimaExecucao;
    }

    public void setUltimaExecucao(Date ultimaExecucao) {
        this.ultimaExecucao = ultimaExecucao;
    }

    public static FichaJSON obterJSON(boolean treinoIndependente, ProgramaTreino programa,
                                      ProgramaTreinoFicha programaFicha, Ficha fichaUnica,
                                      final String urlBase, Map<Integer, AtividadeJSON> mapaTodasAtividades, Boolean seriesAgrupadas, String ctx, OrigemEnum origemEnum) throws Exception {
        FichaJSON fichaJSON = new FichaJSON();
        if (origemEnum != null){
            if (origemEnum == OrigemEnum.APP){
                montarJSONAPP(treinoIndependente, programa, programaFicha, fichaUnica, urlBase, mapaTodasAtividades, seriesAgrupadas, ctx, fichaJSON);
            }
        } else {
            montarJSON(treinoIndependente, programa, programaFicha, fichaUnica, urlBase, mapaTodasAtividades, seriesAgrupadas, ctx, fichaJSON);
        }
        return fichaJSON;
    }

    public static void montarJSON(boolean treinoIndependente, ProgramaTreino programa,
                                  ProgramaTreinoFicha programaFicha, Ficha fichaUnica,
                                  final String urlBase, Map<Integer, AtividadeJSON> mapaTodasAtividades, Boolean seriesAgrupadas, String ctx, FichaJSON fichaJSON) throws Exception {
        Ficha ficha = fichaUnica != null ? fichaUnica : programaFicha.getFicha();
        String versao = null;
        if (fichaUnica != null && fichaUnica.getVersao() != null) {
            versao = fichaUnica.getVersao().toString();
        } else if (programaFicha != null && programaFicha.getVersao() != null) {
            versao = programaFicha.getVersao().toString();
        }
        if (programaFicha != null) {
            if (programaFicha.getTipoExecucao() != null) {
                fichaJSON.setTipoExecucao(programaFicha.getTipoExecucao().getId());
            } else {
                throw new Exception("O Tipo de execução está vazio para ficha: " + programaFicha.getFicha().getNome() + ", procure o seu professor.");
            }
            fichaJSON.setDiaSemana(programaFicha.getDiaSemana());
        }
        fichaJSON.setCod(ficha.getCodigo());
        fichaJSON.setNome(ficha.getNomeConcatenado());
        fichaJSON.setVersao(versao);
        fichaJSON.setCodPrograma(programa != null ? programa.getCodigo().toString() : "");
        fichaJSON.setMensagemAluno(ficha.getMensagemAluno());
        fichaJSON.setCategoria(ficha.getNomeCategoria());
        fichaJSON.setUltimaExecucao(ficha.getUltimaExecucao());
        if (ficha.getUltimaExecucao() != null) {
            fichaJSON.setUltimaExecucaoLong(ficha.getUltimaExecucao().getTime());
        }
        List<String> setsAdicionados = new ArrayList<String>();
        List<AtividadeFicha> atividadesRecarregadas = new ArrayList<>();
        for (AtividadeFicha atividadeFicha : ficha.getAtividades()) {
            try {
                setarAssociadas(ctx, atividadeFicha, ficha.getAtividades());
                atividadesRecarregadas.add(atividadeFicha);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        ficha.setAtividades(atividadesRecarregadas);
        Ordenacao.ordenarLista(ficha.getAtividades(), "ordem");
        for (AtividadeFicha atividadeFicha : ficha.getAtividades()) {
            if ((atividadeFicha.getBiSet() || atividadeFicha.getTriSet())
                    && !UteisValidacao.emptyString(atividadeFicha.getSetId()) && seriesAgrupadas) {
                boolean jaadicionei = false;
                for (String st : setsAdicionados) {
                    if (verificarSeExisteVinculoSet(st, atividadeFicha.getCodigo().toString())) {
                        jaadicionei = true;
                    }
                }
                if (jaadicionei) {
                    continue;
                } else {
                    setsAdicionados.add(atividadeFicha.getSetId());
                }
            }
            AtividadeFichaJSON atividadeFichaJSON = new AtividadeFichaJSON();
            atividadeFichaJSON.setCodFicha(ficha.getCodigo());
            if (atividadeFicha.getIntensidade() != null) {
                atividadeFichaJSON.setIntensidade(atividadeFicha.getIntensidade().toString().concat("%"));
                if (programa.getCliente() != null && programa.getCliente().getFcRepouso() != null && programa.getCliente().getFcMaxima() != null) {
                    atividadeFichaJSON.setBatimentos(Uteis.calcularFCMaxima(programa.getCliente().getFcRepouso(),
                            programa.getCliente().getFcMaxima(),
                            atividadeFicha.getIntensidade()).toString().concat(" bpm"));
                } else {
                    atividadeFichaJSON.setBatimentos("");
                }
            } else {
                atividadeFichaJSON.setIntensidade("");
                atividadeFichaJSON.setBatimentos("");
            }

            atividadeFichaJSON.setAtividade(atividadeFicha.getCodigo().toString());
            atividadeFichaJSON.setCodigoAtividade(atividadeFicha.getAtividade().getCodigo().toString());
            atividadeFichaJSON.setOrdem(atividadeFicha.getOrdem());
            if (atividadeFicha.getMetodoExecucao() != null) {
                atividadeFichaJSON.setCodMetodoExecucao(atividadeFicha.getMetodoExecucao().getId());
                atividadeFichaJSON.setNomeMetodoExecucao(atividadeFicha.getMetodoExecucao().getDescricao());
            }
            //
            List<AtividadeFichaAjuste> ajustes = atividadeFicha.getAjustes();
            // tratamento para LazyLoadException
            try {
                ajustes.isEmpty();
            } catch (Exception e) {
                ajustes = getFichaServiceImpl().obterAtividadeFichaAjustePorAtividadeFicha(ctx, atividadeFicha.getCodigo());
            }
            if (ajustes != null) {
                for (AtividadeFichaAjuste ajuste : ajustes) {
                    AjusteJSON novo = new AjusteJSON(ajuste.getNome(), ajuste.getValor());
                    novo.setCodAtividade(ajuste.getAtividadeFicha().getCodigo());
                    atividadeFichaJSON.getAjustes().add(novo);
            }
            }
            Ordenacao.ordenarLista(atividadeFicha.getSeries(), "ordem");
            Integer ordem = 1;
            if (seriesAgrupadas && (atividadeFicha.getBiSet() || atividadeFicha.getTriSet())) {
                for (SerieTO serie : (seriesAgrupadas ? atividadeFicha.preencherSeriesConcatenadas() : atividadeFicha.preencherSeriesSet(seriesAgrupadas))) {
                    serie.setOrdem(ordem);
                    atividadeFichaJSON.getSeries().add(criarSerieJSON(serie, seriesAgrupadas, atividadeFicha.getDescancoOrDescansoAssociado(), ctx));
                    ++ordem;
                }
            } else {
                for (SerieTO serie : atividadeFicha.preencherSeriesSet(seriesAgrupadas)) {
                    atividadeFichaJSON.getSeries().add(criarSerieJSON(serie, seriesAgrupadas, atividadeFicha.getDescancoOrDescansoAssociado(), ctx));
                }
            }
            fichaJSON.getAtividades().add(atividadeFichaJSON);
            //SEPARAR ATIVIDADE EM UMA LISTA INDEXADA PARA O JSON
            if (mapaTodasAtividades != null) {
                // tratamento para LazyLoadException
                try {
                    atividadeFicha.getAtividade().getFotoKey();
                    atividadeFicha.getAtividade().getAparelhos().isEmpty();
                    atividadeFicha.getAtividade().getEmpresasHabilitadas().isEmpty();
                } catch (Exception e) {
                    atividadeFicha.setAtividade(atividadeFicha.recarregarAtividade(ctx, atividadeFicha.getAtividade(), false));
                }

                AtividadeJSON atividadeJSON = new AtividadeJSON();
                atividadeJSON.setCod(atividadeFicha.getCodigo());
                atividadeJSON.setImgMediumUrls(new ArrayList<>());
                atividadeJSON.setCodigoAtividade(atividadeFicha.getAtividade().getCodigo());
                atividadeJSON.setTipo(atividadeFicha.getAtividade().getTipo().getId());
                atividadeJSON.setNome(atividadeFicha.getAtividade().getNome() + atividadeFicha.getNomeSet() + atividadeFicha.getComplementoNomeAtividadeRetiraFicha());
                atividadeJSON.setDescricao(atividadeFicha.getAtividade().getDescricao());
                try {
                    if (UteisValidacao.emptyString(atividadeFicha.getAtividade().getFotoKey())) {
                        atividadeFicha.getAtividade().setAnimacoes(getAtividadeAnimacaoDao().obterImagens(ctx, atividadeFicha.getAtividade().getCodigo(), true));
                        if (!UteisValidacao.emptyString(atividadeFicha.getAtividade().getURLImg())) {
                            atividadeJSON.setImg(urlBase + MidiasNiveisEnum.MOBILE_RETANGULO.getLocal() + atividadeFicha.getAtividade().getURLImg());
                            atividadeJSON.setThumb(urlBase + MidiasNiveisEnum.MOBILE_QUADRADA.getLocal() + atividadeFicha.getAtividade().getURLImg());
                            atividadeJSON.setImgMedium(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + atividadeFicha.getAtividade().getURLImg());
                        }
                    } else {
                        atividadeJSON.setImg(atividadeFicha.getAtividade().getFotoKey());
                        atividadeJSON.setThumb(atividadeFicha.getAtividade().getFotoKeyMin());
                        atividadeJSON.setImgMedium(atividadeFicha.getAtividade().getFotoKeyPequena());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                List<String> imagensAssociadas = atividadeFicha.getImagemAtiviadeAssocidada(urlBase, ctx);
                List<ImgMediumUrlsTO> listImagensAssociadas = atividadeFicha.getListImagemAtiviadeAssocidada(urlBase, ctx);

                if (MetodoExecucaoEnum.BI_SET.equals(atividadeFicha.getMetodoExecucao()) || MetodoExecucaoEnum.TRI_SET.equals(atividadeFicha.getMetodoExecucao()) || (imagensAssociadas != null && imagensAssociadas.size() > 0)) {
                    atividadeJSON.setImgMediumUrls(imagensAssociadas);
                    atividadeJSON.setListImgMediumUrls(listImagensAssociadas);
                } else if(!UteisValidacao.emptyString(atividadeJSON.getImg())) {
                    List<String> imgMedionURLs = new ArrayList<>();
                    imgMedionURLs.add(atividadeJSON.getImg());
                    atividadeJSON.setImgMediumUrls(imgMedionURLs);
                }
                List<AtividadeVideoTO> listaAtividadeVideosJSON = new ArrayList<>();
                try {
                    if(atividadeFicha.getAtividade().getLinkVideos() == null || atividadeFicha.getAtividade().getLinkVideos().isEmpty()) {
                        atividadeFicha.getAtividade().setLinkVideos(getAtividadeService().obterLinkVideos(ctx, atividadeFicha.getAtividade().getCodigo()));
                    }
                    for(AtividadeVideo link : atividadeFicha.getAtividade().getLinkVideos()){
                        listaAtividadeVideosJSON.add(new AtividadeVideoTO(link));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if(MetodoExecucaoEnum.BI_SET.equals(atividadeFicha.getMetodoExecucao()) || MetodoExecucaoEnum.TRI_SET.equals(atividadeFicha.getMetodoExecucao())) {
                    if(atividadeFicha.getAtividadeAssociada1() != null && atividadeFicha.getAtividadeAssociada1().getAtividade() != null
                            && atividadeFicha.getAtividadeAssociada1().getAtividade().getLinkVideos() != null) {
                        for(AtividadeVideo link : atividadeFicha.getAtividadeAssociada1().getAtividade().getLinkVideos()){
                            listaAtividadeVideosJSON.add(new AtividadeVideoTO(link));
                        }
                    }
                    if(atividadeFicha.getAtividadeAssociada2() != null && atividadeFicha.getAtividadeAssociada2().getAtividade() != null
                            && atividadeFicha.getAtividadeAssociada2().getAtividade().getLinkVideos() != null) {
                        for(AtividadeVideo link : atividadeFicha.getAtividadeAssociada2().getAtividade().getLinkVideos()){
                            listaAtividadeVideosJSON.add(new AtividadeVideoTO(link));
                        }
                    }
                }
                atividadeJSON.setUrlVideo(atividadeFicha.getAtividade().getLinkVideo());
                atividadeJSON.setUrlLinkVideos(listaAtividadeVideosJSON);
                if (atividadeFicha.getBiSet() || atividadeFicha.getTriSet()){
                    atividadeJSON.setUrlVideos(atividadeFicha.getAtividade().getListaVideos(atividadeFicha));
                }
                atividadeJSON.setOrdem(atividadeFicha.getOrdem());
                if(atividadeFicha.getAtividade().getAtividadesAlternativas() != null && atividadeJSON.getAtividadesAlternativas() != null){
                    for(AtividadeAlternativa aa : atividadeFicha.getAtividade().getAtividadesAlternativas()){
                        atividadeJSON.getAtividadesAlternativas().get(aa.getAtividadeAlternativa());
                    }
                }
                if (programa != null && programa.getCliente() != null) {
                    try {
                        atividadeJSON.setIdentificador(atividadeFicha.getAtividade().
                                getIdentificadorPorEmpresa(treinoIndependente, programa.getCliente().getEmpresa()));
                        StringBuilder aparelhos = new StringBuilder();
                        for (AtividadeAparelho atap : atividadeFicha.getAtividade().getAparelhos()) {
                            aparelhos.append(atap.getAparelho().obterNome(programa.getCliente().getEmpresa())).append(" / ");
                        }
                        int indBarra = aparelhos.lastIndexOf("/");
                        if (aparelhos.length() > 0) {
                            atividadeJSON.setAparelhos(aparelhos.substring(0, indBarra - 1));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                mapaTodasAtividades.put(atividadeFicha.getCodigo(), atividadeJSON);
            }
        }
        //ordenar atividades para o JSON em função da Ordem de Execução
        fichaJSON.setAtividades(Ordenacao.ordenarLista(fichaJSON.getAtividades(), "ordem"));
    }

    public static boolean verificarSeExisteVinculoSet(String setsAdicionados, String codigoAtividade) {
        String[] sets = setsAdicionados.split("\\|");
        Boolean ret = false;
        if (sets.length > 1) {
            for (String set : sets) {
                if (set.equals(codigoAtividade)) {
                    ret = true;
                    continue;
                }
            }
        }
        return ret;
    }

    public static void montarJSONAPP(boolean treinoIndependente, ProgramaTreino programa,
                                  ProgramaTreinoFicha programaFicha, Ficha fichaUnica,
                                  final String urlBase, Map<Integer, AtividadeJSON> mapaTodasAtividades, Boolean seriesAgrupadas, String ctx, FichaJSON fichaJSON) throws Exception {
        Ficha ficha = fichaUnica != null ? fichaUnica : programaFicha.getFicha();
        String versao = null;
        if (fichaUnica != null && fichaUnica.getVersao() != null) {
            versao = fichaUnica.getVersao().toString();
        } else if (programaFicha != null && programaFicha.getVersao() != null) {
            versao = programaFicha.getVersao().toString();
        }
        if (programaFicha != null) {
            if (programaFicha.getTipoExecucao() != null) {
                fichaJSON.setTipoExecucao(programaFicha.getTipoExecucao().getId());
            } else {
                throw new Exception("O Tipo de execução está vazio para ficha: " + programaFicha.getFicha().getNome() + ", procure o seu professor.");
            }
            fichaJSON.setDiaSemana(programaFicha.getDiaSemana());
        }
        fichaJSON.setCod(ficha.getCodigo());
        fichaJSON.setNome(ficha.getNomeConcatenado());
        fichaJSON.setVersao(versao);
        fichaJSON.setCodPrograma(programa != null ? programa.getCodigo().toString() : "");
        fichaJSON.setMensagemAluno(ficha.getMensagemAluno());
        fichaJSON.setCategoria(ficha.getNomeCategoria());
        fichaJSON.setUltimaExecucao(ficha.getUltimaExecucao());
        if (ficha.getUltimaExecucao() != null) {
            fichaJSON.setUltimaExecucaoLong(ficha.getUltimaExecucao().getTime());
        }
        List<String> setsAdicionados = new ArrayList<String>();
        for (AtividadeFicha atividadeFicha : ficha.getAtividades()) {
            setarAssociadas(ctx, atividadeFicha, ficha.getAtividades());
        }
        Ordenacao.ordenarLista(ficha.getAtividades(), "ordem");
        for (AtividadeFicha atividadeFicha : ficha.getAtividades()) {
            if ((atividadeFicha.getBiSet() || atividadeFicha.getTriSet())
                    && !UteisValidacao.emptyString(atividadeFicha.getSetId()) && seriesAgrupadas) {
                boolean jaadicionei = false;
                for (String st : setsAdicionados) {
                    if (verificarSeExisteVinculoSet(st, atividadeFicha.getCodigo().toString())) {
                        jaadicionei = true;
                    }
                }
                if (jaadicionei) {
                    continue;
                } else {
                    setsAdicionados.add(atividadeFicha.getSetId());
                }
            }
            AtividadeFichaJSON atividadeFichaJSON = new AtividadeFichaJSON();
            atividadeFichaJSON.setCodFicha(ficha.getCodigo());
            if (atividadeFicha.getIntensidade() != null) {
                atividadeFichaJSON.setIntensidade(atividadeFicha.getIntensidade().toString().concat("%"));
                if (programa.getCliente().getFcRepouso() != null && programa.getCliente().getFcMaxima() != null) {
                    atividadeFichaJSON.setBatimentos(Uteis.calcularFCMaxima(programa.getCliente().getFcRepouso(),
                            programa.getCliente().getFcMaxima(),
                            atividadeFicha.getIntensidade()).toString().concat(" bpm"));
                } else {
                    atividadeFichaJSON.setBatimentos("");
                }
            } else {
                atividadeFichaJSON.setIntensidade("");
                atividadeFichaJSON.setBatimentos("");
            }

            atividadeFichaJSON.setAtividade(atividadeFicha.getCodigo().toString());
            atividadeFichaJSON.setCodigoAtividade(atividadeFicha.getAtividade().getCodigo().toString());
            atividadeFichaJSON.setOrdem(atividadeFicha.getOrdem());
            if (atividadeFicha.getMetodoExecucao() != null) {
                atividadeFichaJSON.setCodMetodoExecucao(atividadeFicha.getMetodoExecucao().getId());
                atividadeFichaJSON.setNomeMetodoExecucao(atividadeFicha.getMetodoExecucao().getDescricao());
            }
            //
            try {
                List<AtividadeFichaAjuste> ajustes = atividadeFicha.getAjustes();
                if (ajustes != null) {
                    for (AtividadeFichaAjuste ajuste : ajustes) {
                        AjusteJSON novo = new AjusteJSON(ajuste.getNome(), ajuste.getValor());
                        novo.setCodAtividade(ajuste.getAtividadeFicha().getCodigo());
                        atividadeFichaJSON.getAjustes().add(novo);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            Ordenacao.ordenarLista(atividadeFicha.getSeries(), "ordem");
            Integer ordem = 1;
            if (atividadeFicha.getBiSet() || atividadeFicha.getTriSet()) {
                for (SerieTO serie : (seriesAgrupadas ? atividadeFicha.preencherSeriesSet(seriesAgrupadas) : atividadeFicha.preencherSeriesConcatenadas())) {
                    serie.setOrdem(ordem);
                    atividadeFichaJSON.getSeries().add(criarSerieJSON(serie, seriesAgrupadas, atividadeFicha.getDescancoOrDescansoAssociado(), ctx));
                    ++ordem;
                }
            } else {
                for (SerieTO serie : atividadeFicha.preencherSeriesConcatenadas()) {
                    atividadeFichaJSON.getSeries().add(criarSerieJSON(serie, seriesAgrupadas, atividadeFicha.getDescancoOrDescansoAssociado(), ctx));
                }
            }
            fichaJSON.getAtividades().add(atividadeFichaJSON);
            //SEPARAR ATIVIDADE EM UMA LISTA INDEXADA PARA O JSON
            if (mapaTodasAtividades != null && mapaTodasAtividades.size() > 0) {
                AtividadeJSON atividadeJSON = new AtividadeJSON();
                atividadeJSON.setCod(atividadeFicha.getCodigo());
                atividadeJSON.setCodigoAtividade(atividadeFicha.getAtividade().getCodigo());
                atividadeJSON.setTipo(atividadeFicha.getAtividade().getTipo().getId());
                atividadeJSON.setNome(atividadeFicha.getNomeTransient() + atividadeFicha.getNomeSet() + atividadeFicha.getComplementoNomeAtividadeRetiraFicha());
                atividadeJSON.setDescricao(atividadeFicha.getAtividade().getDescricao());
                atividadeJSON.setAtividadesAlternativas(new ArrayList<>());
                if(atividadeFicha.getAtividade().getAtividadesAlternativas() != null){
                    for(AtividadeAlternativa aa : atividadeFicha.getAtividade().getAtividadesAlternativas()){
                        atividadeJSON.getAtividadesAlternativas().get(aa.getAtividadeAlternativa());
                    }
                }
                if (UteisValidacao.emptyString(atividadeFicha.getAtividade().getFotoKey())) {
                    if (!UteisValidacao.emptyString(atividadeFicha.getAtividade().getURLImg())) {
                        atividadeJSON.setImg(urlBase + MidiasNiveisEnum.MOBILE_RETANGULO.getLocal() + atividadeFicha.getAtividade().getURLImg());
                        atividadeJSON.setThumb(urlBase + MidiasNiveisEnum.MOBILE_QUADRADA.getLocal() + atividadeFicha.getAtividade().getURLImg());
                        atividadeJSON.setImgMedium(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + atividadeFicha.getAtividade().getURLImg());
                    }
                } else {
                    atividadeJSON.setImg(atividadeFicha.getAtividade().getFotoKey());
                    atividadeJSON.setThumb(atividadeFicha.getAtividade().getFotoKeyMin());
                    atividadeJSON.setImgMedium(atividadeFicha.getAtividade().getFotoKeyPequena());
                }

                atividadeJSON.setImgMediumUrls(atividadeFicha.getImagemAtiviadeAssocidada(urlBase, ctx));
                atividadeJSON.setUrlVideo(atividadeFicha.getAtividade().getLinkVideo());
                atividadeJSON.setOrdem(atividadeFicha.getOrdem());
                if (programa != null) {
                    atividadeJSON.setIdentificador(atividadeFicha.getAtividade().
                            getIdentificadorPorEmpresa(treinoIndependente, programa.getCliente().getEmpresa()));
                    StringBuilder aparelhos = new StringBuilder();
                    for (AtividadeAparelho atap : atividadeFicha.getAtividade().getAparelhos()) {
                        aparelhos.append(atap.getAparelho().obterNome(programa.getCliente().getEmpresa())).append(" / ");
                    }
                    int indBarra = aparelhos.lastIndexOf("/");
                    if (aparelhos.length() > 0) {
                        atividadeJSON.setAparelhos(aparelhos.substring(0, indBarra - 1));
                    }
                }
                mapaTodasAtividades.put(atividadeFicha.getCodigo(), atividadeJSON);
            }
        }
        //ordenar atividades para o JSON em função da Ordem de Execução
        fichaJSON.setAtividades(Ordenacao.ordenarLista(fichaJSON.getAtividades(), "ordem"));

    }

    public static SerieJSON criarSerieJSON(SerieTO serie, Boolean concatenarSerieSet, Boolean comDescanso, String ctx) {
        SerieJSON serieJSON = new SerieJSON();
        if (serie.getAtualizadoApp()) {
            serieJSON.setCargaApp(serie.getCargaApp());
            serieJSON.setRepeticaoApp(serie.getRepeticaoApp());
            if (FichaDaoImpl.validarAtualizarNovamente(serie.getCargaApp(), serie.getRepeticaoApp(), serie.getCarga(), serie.getRepeticao())) {
                FichaDaoImpl.atualizarCargaApp(serie, ctx);
                serieJSON.setCargaApp(serie.getCargaApp());
                serieJSON.setRepeticaoApp(serie.getRepeticaoApp());
            }
        } else {
            FichaDaoImpl.atualizarCargaApp(serie, ctx);
            serieJSON.setCargaApp(serie.getCargaApp());
            serieJSON.setRepeticaoApp(serie.getRepeticaoApp());
        }
        serieJSON.setCodSerie(serie.getCodigo());
        serieJSON.setDescanso(serie.getDescanso().toString());
        if (!comDescanso) {
            serieJSON.setDescanso("0");
        }
        serieJSON.setComplemento(serie.getComplementoFormat());
        if (!UteisValidacao.emptyString(serie.getSetId())
                && !serie.getSetId().equals(serie.getCodigoAtvFicha() + "|")) {
            serieJSON.setComplemento((concatenarSerieSet && serie.isSerieBiSet())
                    ? serie.getComplementoConcatenado()
                    : ("Atividade : " + (serie.getNomeTransient()) +
                    (serie.getComplemento().isEmpty() ? "" : (" - " + serie.getComplemento()))));
        }
        serieJSON.setValor1(serie.getValor1(serie.getTipo()));
        serieJSON.setCadencia(serie.getCadencia());
        serieJSON.setOrdem(serie.getOrdem());
        if (serie.getTipo().equals(TipoAtividadeEnum.ANAEROBICO)) {
            serieJSON.setRepeticaoComp(serie.getRepeticaoComp());
            serieJSON.setCargaComp(serie.getCargaComp());
        }
        serieJSON.setValor2(serie.getValor2(serie.getTipo()));
        serieJSON.setValor3(serie.getValor3(serie.getTipo()));
//            private Integer repeticao = 0;
        serieJSON.setRepeticao(serie.getRepeticao());
//    private Double carga = 0.0;//gramas
        serieJSON.setCarga(serie.getCarga());
//    private Integer duracao = 0;//minutos
        serieJSON.setDuracao(serie.getDuracao());
//    private Integer distancia = 0;//metros
        serieJSON.setDistancia(serie.getDistancia());
//    private Double velocidade = 0.0;//km/h
        serieJSON.setVelocidade(serie.getVelocidade());
        serieJSON.setCodAtividade(serie.getCodigoAtvFicha());
        serieJSON.setTipoAtividade(serie.getTipo().getId());

        return serieJSON;
    }

    public static void setarAssociadas(String ctx, AtividadeFicha atv, List<AtividadeFicha> atividadesFicha) {
        try {
            atv.setAjustes(getAtividadeService().consultarAjustes(ctx, atv));
        } catch (Exception e) {

        }
        if (UteisValidacao.emptyString(atv.getSetId())) {
            return;
        }
        //Incluido ppois existe duplicidade de id
        Set<String> setIds = new HashSet<String>(Arrays.asList(atv.getSetId().split("\\|")));

        for (String i : setIds) {
            try {
                if (!UteisValidacao.emptyString(i)) {
                    Integer codigo = Integer.valueOf(i);
                    for (AtividadeFicha atvFich : atividadesFicha) {
                        if (atvFich.getCodigo().equals(codigo)
                                && !atv.getCodigo().equals(codigo)) {
                            atv.addAssociado(atvFich);
                        }
                    }
                }
            } catch (Exception e) {
                Uteis.logar(e, FichaJSON.class);
            }
        }
    }

    public Long getUltimaExecucaoLong() {
        return ultimaExecucaoLong;
    }

    public void setUltimaExecucaoLong(Long ultimaExecucaoLong) {
        this.ultimaExecucaoLong = ultimaExecucaoLong;
    }

    public static AtividadeService getAtividadeService() {
        return (AtividadeService) UtilContext.getBean(AtividadeService.class);
    }

    public static FichaServiceImpl getFichaServiceImpl() {
        return (FichaServiceImpl) UtilContext.getBean(FichaServiceImpl.class);
    }

    public Boolean getFichaDoDiaAtual() {
        return fichaDoDiaAtual;
    }

    public void setFichaDoDiaAtual(Boolean fichaDoDiaAtual) {
        this.fichaDoDiaAtual = fichaDoDiaAtual;
    }
}

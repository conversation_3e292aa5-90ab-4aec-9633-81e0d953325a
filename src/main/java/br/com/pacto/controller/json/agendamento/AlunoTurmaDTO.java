package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.aluno.SituacaoContratoZWEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class AlunoTurmaDTO {

    private Integer id;
    private Integer matriculaZW;
    private String nome;
    private String unidade;
    private String imageUri;
    private SituacaoContratoZWEnum situacaoContrato;
    private SituacaoAlunoEnum situacaoAluno;
    private AlunoVinculoAulaEnum vinculoComAula;
    private Boolean confirmado;
    private Boolean fixo = false;
    private String justificativa;
    private Long horarioMarcacao;
    private Integer codigoPassivo;
    private Integer codigoIndicado;
    private String equipamentoReservado;
    private String userIdSelfloops;
    private Integer averagePower;
    private Integer calories;
    private String credito;
    private String toolTipCredito;
    private Integer codigoPessoa;
    private String dataNascimento = null;
    private Integer codigoCliente;
    private Integer tempoDeAula;
    private Integer posicaoRankingAluno;
    private String origemSistema;
    private boolean parqPositivo;

    private Boolean autorizadoGestaoRede = false;
    private String codAcessoAutorizado = "";
    private Integer matriculaAutorizado = 0;

    public AlunoTurmaDTO() {
    }

    public AlunoTurmaDTO(AlunoResponseTO alunoResponseTO) {
        this.id = alunoResponseTO.getId();
        this.matriculaZW = alunoResponseTO.getMatriculaZW();
        this.nome = alunoResponseTO.getNome();
        this.imageUri = alunoResponseTO.getImageUri();
        this.situacaoContrato = alunoResponseTO.getSituacaoContratoZW();
        this.situacaoAluno = alunoResponseTO.getSituacaoAluno();
        this.confirmado = alunoResponseTO.getConfirmado() != null ? alunoResponseTO.getConfirmado() : false;
        this.justificativa = alunoResponseTO.getJustificativa();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMatriculaZW() {
        return matriculaZW;
    }

    public void setMatriculaZW(Integer matriculaZW) {
        this.matriculaZW = matriculaZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public SituacaoContratoZWEnum getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(SituacaoContratoZWEnum situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public SituacaoAlunoEnum getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(SituacaoAlunoEnum situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public AlunoVinculoAulaEnum getVinculoComAula() {
        return vinculoComAula;
    }

    public void setVinculoComAula(AlunoVinculoAulaEnum vinculoComAula) {
        this.vinculoComAula = vinculoComAula;
    }

    public Boolean getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Boolean confirmado) {
        this.confirmado = confirmado;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public String getJustificativa() { return justificativa; }

    public void setJustificativa(String justificativa) { this.justificativa = justificativa; }

    public Long getHorarioMarcacao() {
        return horarioMarcacao;
    }

    public void setHorarioMarcacao(Long horarioMarcacao) {
        this.horarioMarcacao = horarioMarcacao;
    }

    public Boolean getFixo() {
        return fixo;
    }

    public void setFixo(Boolean fixo) {
        this.fixo = fixo;
    }

    public Integer getCodigoPassivo() {
        return codigoPassivo;
    }

    public void setCodigoPassivo(Integer codigoPassivo) {
        this.codigoPassivo = codigoPassivo;
    }

    public Integer getCodigoIndicado() {
        return codigoIndicado;
    }

    public void setCodigoIndicado(Integer codigoIndicado) {
        this.codigoIndicado = codigoIndicado;
    }

    public String getEquipamentoReservado() {
        return equipamentoReservado;
    }

    public void setEquipamentoReservado(String equipamentoReservado) {
        this.equipamentoReservado = equipamentoReservado;
    }

    public String getUserIdSelfloops() {
        return userIdSelfloops;
    }

    public void setUserIdSelfloops(String userIdSelfloops) {
        this.userIdSelfloops = userIdSelfloops;
    }

    public Boolean getAutorizadoGestaoRede() {
        if (autorizadoGestaoRede == null) {
            autorizadoGestaoRede = false;
        }
        return autorizadoGestaoRede;
    }

    public void setAutorizadoGestaoRede(Boolean autorizadoGestaoRede) {
        this.autorizadoGestaoRede = autorizadoGestaoRede;
    }

    public String getCodAcessoAutorizado() {
        if (codAcessoAutorizado == null) {
            codAcessoAutorizado = "";
        }
        return codAcessoAutorizado;
    }

    public void setCodAcessoAutorizado(String codAcessoAutorizado) {
        this.codAcessoAutorizado = codAcessoAutorizado;
    }

    public Integer getMatriculaAutorizado() {
        if (matriculaAutorizado == null){
            matriculaAutorizado = 0;
        }
        return matriculaAutorizado;
    }

    public void setMatriculaAutorizado(Integer matriculaAutorizado) {
        this.matriculaAutorizado = matriculaAutorizado;
    }

    public Integer getAveragePower() {
        return averagePower;
    }

    public void setAveragePower(Integer averagePower) {
        this.averagePower = averagePower;
    }

    public Integer getCalories() {
        return calories;
    }

    public void setCalories(Integer calories) {
        this.calories = calories;
    }

    public String getCredito() {
        return credito;
    }

    public void setCredito(String credito) {
        this.credito = credito;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }


    public String getToolTipCredito() {
        return toolTipCredito;
    }

    public void setToolTipCredito(String toolTipCredito) {
        this.toolTipCredito = toolTipCredito;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getTempoDeAula() {
        return tempoDeAula;
    }

    public void setTempoDeAula(Integer tempoDeAula) {
        this.tempoDeAula = tempoDeAula;
    }

    public Integer getPosicaoRankingAluno() {
        return posicaoRankingAluno;
    }

    public void setPosicaoRankingAluno(Integer posicaoRankingAluno) {
        this.posicaoRankingAluno = posicaoRankingAluno;
    }


    public void setOrigemSistema(String origemSistema) {
        this.origemSistema = origemSistema;
    }

    public String getOrigemSistema() {
        return origemSistema;
    }

    public boolean isParqPositivo() {
        return parqPositivo;
    }

    public void setParqPositivo(boolean parqPositivo) {
        this.parqPositivo = parqPositivo;
    }
}

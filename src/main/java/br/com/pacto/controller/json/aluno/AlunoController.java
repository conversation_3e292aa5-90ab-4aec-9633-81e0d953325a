package br.com.pacto.controller.json.aluno;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.programa.FiltroProgramaTreinoJSON;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClientePesquisaService;
import br.com.pacto.service.intf.cliente.ClienteService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.graduacao.GraduacaoService;
import br.com.pacto.service.intf.cliente.ListaRapidaAcessosService;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.json.TipoLinhaEnum;
import br.com.pacto.webservice.TreinoWS;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.controller.json.base.SuperControle.STATUS_ERRO;

/**
 * Created by ulisses on 27/08/2018.
 */
@Controller
@Api(value = "Aluno", tags = {"Clientes"})
@RequestMapping("/psec/alunos")
public class AlunoController {

    private final ClienteSinteticoService clienteSinteticoService;
    private final ListaRapidaAcessosService listaRapidaAcessosService;
    private final ClienteService clienteService;
    private final ClientePesquisaService clientePesquisaService;
    private final NotificacaoService notificacaoService;
    private final ProgramaTreinoService programaTreinoService;
    private final GraduacaoService graduacaoService;

    @Autowired
    HttpServletRequest request;
    @Autowired
    private SessaoService sessaoService;

    @Autowired
    public AlunoController(ClienteSinteticoService clienteSinteticoService,
                           ClientePesquisaService clientePesquisaService,
                           NotificacaoService notificacaoService,
                           ClienteService clienteService,
                           ProgramaTreinoService programaTreinoService,
                           ListaRapidaAcessosService listaRapidaAcessosService,
                           GraduacaoService graduacaoService){
        this.clienteService = clienteService;
        Assert.notNull(clienteSinteticoService, "O serviço de cliente sintético não foi injetado corretamente");
        Assert.notNull(clienteSinteticoService, "O serviço de pesquisa de cliente não foi injetado corretamente");
        this.clienteSinteticoService = clienteSinteticoService;
        this.clientePesquisaService = clientePesquisaService;
        this.notificacaoService= notificacaoService;
        this.programaTreinoService= programaTreinoService;
        this.graduacaoService= graduacaoService;
        this.listaRapidaAcessosService = listaRapidaAcessosService;
    }

    private JSONObject desencriptarPlayloadZWUI(String content) throws Exception {
        try {
            JSONObject jsonObject = new JSONObject(content);
            String encryptedData = jsonObject.getString("data");
            String decryptedContent = Uteis.desencriptarZWUI(encryptedData);
            JSONObject data = new JSONObject(decryptedContent);
            return data;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("Não foi possível ler os dados recebidos");
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarAluno(
            @RequestBody AlunoDTO AlunoDTO,
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.cadastrarAluno(null, AlunoDTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/p9Q2t9stT45sc9v3RT85s6dgFifbjuoHFUf0ykjGyio", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterUmAlunoCrypt(@RequestBody String content,
                                                                 @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                 HttpServletRequest request) {
        try {
            JSONObject data = desencriptarPlayloadZWUI(content);

            AlunoResponseTO alunoResponseTO = clienteSinteticoService.obterUmAluno(data.optInt("id"), null, empresaId, request);
            String jsonReturn = new JSONObject(alunoResponseTO).toString();

            return ResponseEntityFactory.ok(Uteis.encriptarZWUI(jsonReturn));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(STATUS_ERRO, e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterUmAluno(@PathVariable("id") Integer alunoID,
                                                            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                            HttpServletRequest request){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterUmAluno(alunoID,null,empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}/{atestadoId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> downloadArquivo(@PathVariable("id") Integer alunoID,
                                                               @PathVariable("atestadoId") Integer atestadoId){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.downloadAnexoAtestado(alunoID, atestadoId));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter arquivo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunos(
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @RequestParam(value = "permiteAlunoOutraEmpresa", required = false) Boolean permiteAlunoOutraEmpresa,
            @RequestParam(value = "incluirAutorizado", required = false) Boolean incluirAutorizado,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request) throws JSONException {
        try {
            FiltroAlunoJSON filtroAlunoJSON = new FiltroAlunoJSON(filtros);
            return ResponseEntityFactory.ok(clienteSinteticoService.listaAlunos(filtroAlunoJSON, paginadorDTO, request, empresaId, permiteAlunoOutraEmpresa, incluirAutorizado), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/fila-espera", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosAndPassivos(
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @RequestParam(value = "permiteAlunoOutraEmpresa", required = false) Boolean permiteAlunoOutraEmpresa,
            @RequestParam(value = "incluirAutorizado", required = false) Boolean incluirAutorizado,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request) throws JSONException {
        try {
            List<AlunoResponseTO> retorno = new ArrayList<>();
            FiltroAlunoJSON filtroAlunoJSON = new FiltroAlunoJSON(filtros);
            List<AlunoResponseTO> passivos = clienteSinteticoService.listaPassivosFila(filtroAlunoJSON, empresaId);
            List<AlunoResponseTO>  alunos = clienteSinteticoService.listaAlunos(filtroAlunoJSON, paginadorDTO, request, empresaId, permiteAlunoOutraEmpresa, incluirAutorizado);
            retorno.addAll(alunos);
            retorno.addAll(passivos);
            paginadorDTO.setSize((long) retorno.size());
            paginadorDTO.setQuantidadeTotalElementos(paginadorDTO.getQuantidadeTotalElementos()+((long) passivos.size()));
            return ResponseEntityFactory.ok(retorno, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarAluno(@PathVariable("id") final Integer id, @RequestBody AlunoDTO AlunoDTO){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.alterarAluno(id, AlunoDTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletarAluno(@PathVariable("id") Integer id) {
        try {
            clienteSinteticoService.deletarUmAluno(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/situacao/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarSituacaoAluno(@PathVariable("id") Integer id, @RequestBody AlunoDTO alunoDTO) {
        try {
            alunoDTO.setCodigo(id);
            clienteSinteticoService.editarSituacao(alunoDTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> all(){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.all());
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/perfil/avaliacao-fisica/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoesFisicaAluno(
            @PathVariable("id") Integer id
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.carregarAvaliacoesAluno(id));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar avaliações fisica do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }

    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/linha-tempo/{mat}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> linhaDeTempoAluno(
            @PathVariable("mat") Integer mat,
            @RequestParam(value = "tiposEvento", required = false) List<TipoLinhaEnum> tiposEvento,
            @RequestParam(value = "dataInicio", required = false) Long dataInicio,
            @RequestParam(value = "dataFim", required = false) Long dataFim
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.carregarLinhaDeTempoAluno(mat, new FiltroLinhaTempoDTO(tiposEvento, dataInicio, dataFim)));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar linha de tempo do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/perfil/avaliacao-fisica/{avaliacaoId}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarDataAvaliacao (
        @PathVariable("avaliacaoId") Integer avaliacaoId,
        @RequestBody AvaliacaoAlunoDTO avaliacaoAlunoDTO
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.alterarDataAvaliacaoFisica(avaliacaoId, avaliacaoAlunoDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar linha de tempo do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/rJ6urBkIMHhEfn0jpIfrUKtbWTbTN4MXAJxAsyJW9KB", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE) //
    public ResponseEntity<EnvelopeRespostaDTO> detalhesTreinoAlunoCrypt(@RequestBody String content) {
        try {
            JSONObject data = desencriptarPlayloadZWUI(content);

            DetalheTreinoAlunoDTO detalheTreinoAlunoDTO = clienteSinteticoService.detalhesTreinamentoAluno(data.optInt("alunoId"));
            String jsonReturn = new JSONObject(detalheTreinoAlunoDTO).toString();

            return ResponseEntityFactory.ok(Uteis.encriptarZWUI(jsonReturn));
        } catch (ServiceException e) {
            if (Objects.equals(e.getMessage(), "Ocorreu um erro ao pesquisar o Programa Treino informado")) {
                return ResponseEntityFactory.erroNoContent(e.getChaveExcecao(), e.getMessage());
            } else {
                Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar detalhes do treino do aluno", e);
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar detalhes do treino do aluno", e);
            return ResponseEntityFactory.erroInterno(STATUS_ERRO, e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/perfil/programa-treino/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesTreinoAluno(@PathVariable("id") Integer alunoID) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.detalhesTreinamentoAluno(alunoID));
        } catch (ServiceException e) {
            if(e.getMessage() == "Ocorreu um erro ao pesquisar o Programa Treino informado"){
                return ResponseEntityFactory.erroNoContent(e.getChaveExcecao(), e.getMessage());
            }else {
                Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar detalhes do treino do aluno", e);
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}/reenviar-confirmacao-app", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reenviarUserApp (
        @PathVariable("id") Integer id ){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.reenviarConfirmacaoApp(id));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao reenviar usuário do aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
    @ApiIgnore
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/buscar-aluno-olympia/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarAlunoOlympia(@PathVariable() String id) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.buscarAlunoOlympia(id));
        }catch (ServiceException ex){
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Error Ao tentar busacar aluno Olympia.");
            switch (ex.getCodigoError()) {
                case 400:
                    return ResponseEntityFactory.erroConhecido(ex.getChaveExcecao(), ex.getMessage());
                default:
                    return ResponseEntityFactory.erroInterno(ex.getChaveExcecao(), ex.getMessage());
            }
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}/observacoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarObservacao(@PathVariable("id") final Integer id,
                                                                @RequestParam(required = false) Boolean codigoClienteZw,
                                                                @RequestParam(required = false) Boolean matricula){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterObservacoes(id, codigoClienteZw, matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}/observacoes", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarObservacao(@PathVariable("id") final Integer id,
                                                                   @RequestBody AlunoObservacaoDTO alunoObservacaoDTO){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.gravarObservacao(id, alunoObservacaoDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/observacoes/{idObservacao}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletarObservacao(@PathVariable("idObservacao") final Integer idObservacao){
        try {
            clienteSinteticoService.deletarObservacao(idObservacao);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}/anexosAvaliacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarObservacaoAnexos(@PathVariable("id") final Integer id,
                                                                      @RequestParam(required = false) Boolean buscarAnexoZw,
                                                                      PaginadorDTO paginadorDTO){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterObservacoesAnexos(id, buscarAnexoZw, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}/anexosAvaliacao/tela-aluno", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarObservacaoAnexosDocumentos(@PathVariable("id") final Integer id,
                                                                                PaginadorDTO paginadorDTO){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterObservacoesAnexos(id, true, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}/anexosAvaliacao", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarObservacaoAtestado(@PathVariable("id") final Integer id,
                                                                   @RequestBody AlunoObservacaoDTO alunoObservacaoDTO){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.gravarObservacaoAnexos(id, alunoObservacaoDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{alunoId}/anexosAvaliacao/{anexoId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterObservacaoAnexos(@PathVariable("alunoId") final Integer alunoId,
                                                                     @PathVariable("anexoId") final Integer anexoId) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterObservacaoAnexos(anexoId));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/anexosAvaliacao/{idAnexoAvaliacao}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletarObservacaoAnexos(@PathVariable("idAnexoAvaliacao") final Integer idObservacao){
        try {
            clienteSinteticoService.deletarObservacao(idObservacao);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}/{tipoMensagem}/mensagem", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarClienteMensagem(@PathVariable("id") final Integer id, @PathVariable("tipoMensagem") final String tipoMensagem){
            return ResponseEntityFactory.ok(clienteSinteticoService.obterClienteMensagem(null, id, tipoMensagem));
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}/atestados", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarAnexosAtestados(@PathVariable("id") final Integer id){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterAnexosAtestados(id));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/{id}/notificacoesAluno", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarNotificacoes(
            @PathVariable("id") Integer id, PaginadorDTO paginadorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(notificacaoService.getNotificacoesAlunos(id, paginadorDTO),  paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar notificacões do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }

    }
    @ApiIgnore
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/validar-cadastro-olympia/{codigoExterno}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarCadastroOlympia(@PathVariable() String codigoExterno) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.validarCadastroOlympia(codigoExterno));
        }catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar cadastro Olympia");
            switch(e.getCodigoError()) {
                case 400:
                    return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getMessage());
                default:
                    return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/aluno-zw", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarAlunoZW(
            @RequestParam(value = "nome", required = false) String nome,
            @RequestParam(value = "cpf", required = false) String cpf,
            @RequestParam(value = "matricula", required = false) Integer matricula,
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterAlunoZW(nome, cpf, matricula, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao importar aluno do ZW", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else if (e.getCause().getMessage().equals("informe_um_filtro")) {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/aluno-zw", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarAlunoZW (@RequestBody AlunoZWDTO alunoZWDTO) {
        try {
            clienteSinteticoService.cadastrarAlunoZW(alunoZWDTO);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro reenviar usuário do aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }

    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/usuario", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarUsuarioMovel (@RequestBody UsuarioSimplesAlunoDTO usuarioSimplesAlunoDTO) {
        try {
            clienteSinteticoService.criarUsuarioMovelAluno(usuarioSimplesAlunoDTO);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao criar usuário movel do aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }

    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/gerar-pdf-avaliacao-progresso-aluno", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gerarPdfAvaliacaoProgressoAluno(@RequestParam(value = "matricula", required = true) Integer matricula,
                                                                               @RequestParam(value = "alunoId", required = true) Integer alunoId,
                                                                               @RequestParam(value = "fichaId", required = true) Integer fichaId,
                                                                               @RequestParam(value = "avaliacaoAlunoRespondidaId", required = true) Integer avaliacaoAlunoRespondidaId,
                                                                               HttpServletRequest request){
        try {
            return ResponseEntityFactory.ok(graduacaoService.gerarPdfAvaliacaoProgressoV2(matricula, alunoId, fichaId, avaliacaoAlunoRespondidaId, request));
        } catch (Exception ex) {
            return ResponseEntityFactory.erroInterno(STATUS_ERRO ,ex.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/search",method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosPesquisa(
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @RequestParam(value = "parametro", required = true) String parametro,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request) throws JSONException {
        try {
            return ResponseEntityFactory.ok(clientePesquisaService.listaAlunos(parametro, paginadorDTO, request, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/matricula/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterUmAlunoMatricula(@PathVariable("id") Integer alunoMatricula,
                                                                     @RequestHeader(value = "empresaId", required = true) Integer empresaId){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterSomenteCodigo(alunoMatricula, true));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/Qer3Rrr4xa55Xgy1t0SecbtrtpaErETa9VZxVBhtQqs", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunoPorMatriculaCrypt(@RequestBody String content,
                                                                           @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                           HttpServletRequest request) {
        try {
            JSONObject data = desencriptarPlayloadZWUI(content);

            AlunoResponseTO alunoResponseTO = clienteSinteticoService.obterUmAluno(null, data.getInt("matricula"), empresaId, request);
            String jsonReturn = new JSONObject(alunoResponseTO).toString();

            return ResponseEntityFactory.ok(Uteis.encriptarZWUI(jsonReturn));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(STATUS_ERRO, e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/obter-aluno-completo-por-matricula/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunoPorMatricula(@PathVariable("matricula") Integer matricula,
                                                            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                            HttpServletRequest request){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterUmAluno(null, matricula, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @ApiOperation(value="Retorna os treinos do aluno", notes = "Este métdo retorna os dados do treino do aluno cuja matrícula foi passada por parâmetro", tags = {"Treino"})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Treino encontrado", response = ObterProgramasAlunoResponseDTO.class),
    })
    @RequestMapping(value = "/obter-programas-aluno/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterProgramasAluno(@ApiParam(value = "Matrícula do aluno", defaultValue = "000100", required = true) @PathVariable("matricula") Integer matricula,
                                                                   @ApiParam(value = "Filtros", defaultValue = "{\"quicksearchFields\" : [\"nome\"], \"situacaoPrograma\" : [\"ativo\"], \"generoPrograma\" : [\"M\"], \"ordenacao\" : \"ASC\"}", required = false) @RequestParam(value = "filters", required = false) String filtros,
                                                                   PaginadorDTO paginadorDTO){
        try {
            FiltroProgramaTreinoJSON filtroJSON = new FiltroProgramaTreinoJSON(new JSONObject(filtros));
            return ResponseEntityFactory.ok(programaTreinoService.obterProgramasAluno(filtroJSON, paginadorDTO, matricula), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{matricula}/obter-aluno-simples", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunoSimples(@PathVariable("matricula") Integer matricula){
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterAlunoSimples(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os contatos do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{id}/{tipoMensagem}/mensagem-matricula", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarClienteMensagemMatricula(@PathVariable("id") final Integer id, @PathVariable("tipoMensagem") final String tipoMensagem){
        return ResponseEntityFactory.ok(clienteService.obterClienteMensagem(id, tipoMensagem));
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{matricula}/{tipoMensagem}/mensagem", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarAviso(@PathVariable("matricula") final Integer matricula,
                                                           @PathVariable("tipoMensagem") final String tipoMensagem,
                                                           @RequestBody String mensagem){
        try {
            clienteService.salvarClienteMensagem(matricula, tipoMensagem, mensagem);
            return ResponseEntityFactory.ok();
        }catch (ServiceException e){
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{matricula}/{tipoMensagem}/mensagem", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deleteMensagem(@PathVariable("matricula") final Integer matricula,
                                                           @PathVariable("tipoMensagem") final String tipoMensagem){
        try {
            clienteService.deletarClienteMensagem(matricula, tipoMensagem);
            return ResponseEntityFactory.ok();
        }catch (ServiceException e){
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/select", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosSelect(
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @RequestParam(value = "permiteAlunoOutraEmpresa", required = false) Boolean permiteAlunoOutraEmpresa,
            @RequestParam(value = "incluirAutorizado", required = false) Boolean incluirAutorizado,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request) throws JSONException {
        try {
            FiltroAlunoJSON filtroAlunoJSON = new FiltroAlunoJSON(filtros);
            return ResponseEntityFactory.ok(clienteSinteticoService.listaAlunosSelect(filtroAlunoJSON, paginadorDTO, request, empresaId, permiteAlunoOutraEmpresa, incluirAutorizado), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/alunoApp", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunoAppInfo(@RequestParam(required = false) Integer alunoid,
                                                            @RequestParam(required = false) Integer pessoa,
                                                            @RequestParam(required = false) Integer cliente) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.alunoAppInfo(alunoid, pessoa, cliente));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir a observacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/nivel/{matricula}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarNivelAluno(@PathVariable("matricula") final Integer matricula,
                                                                   @RequestBody String json){
        try {
            Integer nivelId = new JSONObject(json).optInt("nivelId");
            clienteSinteticoService.alterarNivelAluno(matricula, nivelId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/professor/{matricula}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarProfessorAluno(@PathVariable("matricula") final Integer matricula,
                                                                   @RequestBody String json){
        try {
            Integer professorId = new JSONObject(json).optInt("professorId");
            clienteSinteticoService.alterarProfessorAluno(matricula, professorId, request);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/professor", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarProfessorAlunos(@RequestBody String body){
        try {
            if (body == null || body.trim().isEmpty()) {
                return ResponseEntityFactory.erroConhecido("ERRO", "Corpo da requisição não pode estar vazio");
            }
            JSONObject json = new JSONObject(body);
            clienteSinteticoService.alterarProfessorAlunos(request, json);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
    @ApiIgnore
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/alunoColaborador", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obtemAlunosColaborador(@RequestHeader(value = "empresaId", required = false) Integer empresaId,
                                                                      @RequestParam(required = false) Boolean carteiras,
                                                                      @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                      PaginadorDTO paginadorDTO){
        try {
            FiltroPessoaJSON filtroAlunoJSON = new FiltroPessoaJSON(filtros);
            filtroAlunoJSON.setVisualizarOutrasCarteiras(carteiras != null && carteiras);
            return ResponseEntityFactory.ok(clienteService.listaAlunosColaboradores(filtroAlunoJSON, paginadorDTO, empresaId));
        } catch (Exception ex) {
            return ResponseEntityFactory.erroInterno(STATUS_ERRO ,ex.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "lista/matriculas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosMatriculas(
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @RequestParam(value = "statusClienteEnum", required = false) String statusClienteEnum,
            @RequestParam(value = "niveis", required = false) String niveis,
            @RequestParam(value = "colaboradorZw", required = false) String colaboradorZw,
            @RequestParam(value = "filters", required = false) JSONObject filtros) throws JSONException {
        try {
            FiltroAlunoJSON filtroAlunoJSON = new FiltroAlunoJSON(filtros);
            if (!UteisValidacao.emptyString(statusClienteEnum)) {
                filtroAlunoJSON.setStatusClienteEnum(new ArrayList<>());
                filtroAlunoJSON.getStatusClienteEnum().add(statusClienteEnum);
            }
            if (!UteisValidacao.emptyString(niveis)) {
                filtroAlunoJSON.setNiveisIds(new ArrayList<>());
//                filtroAlunoJSON.getNiveisIds().add(statusClienteEnum);
            }
            if (!UteisValidacao.emptyString(colaboradorZw)) {
                filtroAlunoJSON.setColaboradorZw(new ArrayList<>());
                for (String codZw : colaboradorZw.split(",")) {
                    try {
                        if (!UteisValidacao.emptyNumber(new Integer(codZw))) {
                            filtroAlunoJSON.getColaboradorZw().add(new Integer(codZw));
                        }
                    }catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
            return ResponseEntityFactory.ok(clienteSinteticoService.listaAlunosMatricula(filtroAlunoJSON, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "{ctx}/importar-aluno/{empresa}", method = {RequestMethod.POST, RequestMethod.GET})
    public ResponseEntity<EnvelopeRespostaDTO> importall(@PathVariable String ctx,
                                                         @PathVariable Integer empresa,
                                                         @RequestParam(required = false) Integer matricula) {
        try {
            DashboardBIService cs = (DashboardBIService) UtilContext.getBean(DashboardBIService.class);
            if (!UteisValidacao.emptyNumber(matricula)) {
                return ResponseEntityFactory.ok(cs.importarAlunoForaTreino(ctx, empresa, matricula));
            } else {
                cs.importarAlunosForaTreinoAll(ctx, empresa);
                return ResponseEntityFactory.ok("Processo iniciado, aguarde e confie");
            }
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar aluno", e);
            return ResponseEntityFactory.erroInterno("Erro", e.getMessage());
        }
    }
    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "lista-rapida-acessos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaRapidaAcessos(@RequestParam(value = "tipo") Integer tipo,
                                                                  @RequestParam(value = "limite", required = false) Integer limite,
            @RequestHeader(value = "empresaId", required = false) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(listaRapidaAcessosService.listaUltimosAcessos(empresaId, TipoListaAcessoEnum.getTipo(tipo), limite));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "lista-rapida-acessos-cache", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaRapidaAcessosCache(@RequestParam(value = "tipo") Integer tipo,
                                                                  @RequestParam(value = "limite", required = false) Integer limite,
            @RequestHeader(value = "empresaId", required = false) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(listaRapidaAcessosService.listaUltimosAcessos(empresaId, TipoListaAcessoEnum.getTipo(tipo), limite, true));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "lista-rapida-acessos-notf/{tipo}/{valor}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gravarConfiguracaoNotificacaoAcesso(@PathVariable(value = "tipo") String tipo,
                                                                                   @PathVariable(value = "valor") String valor) throws JSONException {
        try {
            listaRapidaAcessosService.gravarCfgNotificacoesAcessosUsuarios(PendenciasAcessoEnum.valueOf(tipo), Boolean.valueOf(valor));
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @RequestMapping(value = "/excluir-aluno", method = RequestMethod.POST)
    public @ResponseBody String excluirAluno(@RequestHeader String key, @RequestBody Integer codigoCliente) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            if (codigoCliente == null) {
                return "ERRO: Código do cliente não pode ser nulo";
            }
            clienteService.excluirAluno(key, codigoCliente);
            return "OK";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/validar-sincronizacao-aluno-zw-tr", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarSincronizacaoAlunoZwTr(@RequestBody String body,
                                                                             @RequestHeader("empresaId") Integer empresaId) {
        try {
            if (body == null || body.trim().isEmpty()) {
                return ResponseEntityFactory.erroConhecido("ERRO", "Corpo da requisição não pode estar vazio");
            }
            JSONObject json = new JSONObject(body);
            if (!json.has("codigoClienteZw")) {
                return ResponseEntityFactory.erroConhecido("ERRO", "Campo 'codigoClienteZw' é obrigatório");
            }

            Integer codigoClienteZw = json.getInt("codigoClienteZw");

            if (codigoClienteZw <= 0) {
                return ResponseEntityFactory.erroConhecido("ERRO", "codigoClienteZw deve ser um número positivo");
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(clienteSinteticoService.validarSincronizacaoAlunoZwTr(ctx, codigoClienteZw));
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar aluno", e);
            return ResponseEntityFactory.erroInterno("Erro", e.getMessage());
        }
    }
}

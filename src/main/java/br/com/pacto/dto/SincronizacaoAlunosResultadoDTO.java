package br.com.pacto.dto;

import java.util.List;

/**
 * DTO para representar o resultado da sincronização de alunos entre ZW e TR
 * 
 * <AUTHOR>
 * @since 2025-08-03
 */
public class SincronizacaoAlunosResultadoDTO {
    
    private String total;
    private String foramAtualizados;
    private String jaAtualizados;
    private String importados;
    private List<String> logs;
    
    public SincronizacaoAlunosResultadoDTO() {
    }
    
    public SincronizacaoAlunosResultadoDTO(String total, String foramAtualizados, String jaAtualizados, String importados, List<String> logs) {
        this.total = total;
        this.foramAtualizados = foramAtualizados;
        this.jaAtualizados = jaAtualizados;
        this.importados = importados;
        this.logs = logs;
    }
    
    public String getTotal() {
        return total;
    }
    
    public void setTotal(String total) {
        this.total = total;
    }
    
    public String getForamAtualizados() {
        return foramAtualizados;
    }
    
    public void setForamAtualizados(String foramAtualizados) {
        this.foramAtualizados = foramAtualizados;
    }
    
    public String getJaAtualizados() {
        return jaAtualizados;
    }
    
    public void setJaAtualizados(String jaAtualizados) {
        this.jaAtualizados = jaAtualizados;
    }

    public String getImportados() {
        return importados;
    }

    public void setImportados(String importados) {
        this.importados = importados;
    }

    public List<String> getLogs() {
        return logs;
    }
    
    public void setLogs(List<String> logs) {
        this.logs = logs;
    }
}

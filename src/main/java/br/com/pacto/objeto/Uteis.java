package br.com.pacto.objeto;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.log.Log;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.log.AlteracoesTO;
import br.com.pacto.service.impl.programa.ProgramaTreinoServiceImpl;
import br.com.pacto.util.AppInfo;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.commons.io.FileUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import javax.faces.context.FacesContext;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.awt.image.VolatileImage;
import java.io.*;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.*;
import java.util.List;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class Uteis {

    public static boolean realizarUpperCaseDadosAntesPersistencia = true;
    public static Uteis uteis;
    public static final long UM_DIA = 86400000L;// em millisegundos
    public static final long UM_MES = UM_DIA * 30;// em millisegundos
    public static final long UM_ANO = 31536000000L;// em millisegundos
    public static final int DEPOIS = 1;
    public static final int IGUAL = 0;
    public static final int ANTES = -1;
    public static final String nomeArqCFG = "br/com/pacto/base/oamd/cfgBD.xml";
    public static boolean debug = false;
    private static final String CHAVE_CRIPTOGRAFIA = "tr3in0ck";
    private static final String CHAVE_CRIPT_ZAW_OFFLINE = "ZAW:{OFFLINE}";
    private static final String CHAVE_CRIPT_MIDIA = "MID:{AMAZON}";
    public static final int TAMANHO_MSG_SMS = 110;
    private static final String CHAVE_CRIPT_SOL = "SOL:{INTRANET}";
    // Uri usadas de redirect para ZillyonWeb
    public static final String URI_REDIRECT_INICIO = "uriInicial";
    public static final String URI_REDIRECT_BI = "uriBI";
    public static final String URI_REDIRECT_CADASTROS = "uriCadastros";
    public static final String URI_REDIRECT_CLIENTES = "uriClientes";
    public static final String URI_REDIRECT_AGENDA = "uriAgenda";
    public static final String URI_REDIRECT_FINAN = "uriFinan";
    public static final String URI_REDIRECT_TREINO = "treino";
    public static final String URI_REDIRECT_CRM = "uriCRM";
    public static final String URI_REDIRECT_BI_DETALHADO = "uriBIDetalhado";
    public static final String URI_REDIRECT_BI_TREINO = "uriBITreino";
    public static final String URI_REDIRECT_CRM_BI = "uriBICRM";
    public static final String FRMT_NUM_PADRAO = "#,###,##0.00";
    private static final Locale BRASIL = new Locale("pt", "BR");
    // usados nas formatações de data
    public static final String FORMATO_DATA = "dd/MM/yy HH:mm";
    public static final String FORMATO_DATA_SEM_HORA = "dd/MM/yy";
    public static final Map<String, SimpleDateFormat> sdfMap = new HashMap<>();
    public static final int NIVELMONTARDADOS_TODOS = 0;
    public static final int NIVELMONTARDADOS_DADOSBASICOS = 1;
    private static final Pattern USER_AGENT_PATTERN_APP = Pattern.compile("([A-Za-z0-9]+)_(?:IOS|ANDROID) / v:([0-9]+(?:\\.[0-9]+)*).*");
    ;

    public static String SECRET_AES_ALUNO;

    static {
        init();
    }

    public static void init() {
        SECRET_AES_ALUNO = Aplicacao.getProp(Aplicacao.aesSecretKeyAppTreino);
    }


    private Uteis() {
    }

    public static Uteis getInstance() {
        if (uteis == null) {
            uteis = new Uteis();
        }
        return uteis;
    }

    public static List arrayJsonToList(JSONArray array){
        return new ArrayList(){{
            for(int i = 0; i < array.length(); i++){
                add(array.get(i));
            }
        }};
    }

    public static String arrayJsonToString(JSONArray array, String separador) {
        String str = "";
        for (int i = 0; i < array.length(); i++) {
            str += separador + array.get(i);
        }
        return str.replaceFirst(separador, "");
    }

    public static boolean isNumeroValido(String valor) {
        try {
            if ((valor == null) || (valor.trim().equals(""))) {
                return false;
            }
            Pattern pat = Pattern.compile("[0-9]+");
            Matcher mat = pat.matcher(valor);
            return mat.matches();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static java.sql.Timestamp getDataJDBCTimestamp(java.util.Date dataConverter) {
        if (dataConverter == null) {
            return null;
        }
        java.sql.Timestamp dataSQL = new java.sql.Timestamp(dataConverter.getTime());
        return dataSQL;
    }

    public static String encriptar(String senha) throws UnsupportedEncodingException {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256"); // "MD5"
            md.update(senha.getBytes());
            return converterParaHexa(md.digest());
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    private static String converterParaHexa(byte[] bytes) {
        StringBuilder s = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            int parteAlta = ((bytes[i] >> 4) & 0xf) << 4;
            int parteBaixa = bytes[i] & 0xf;
            if (parteAlta == 0) {
                s.append('0');
            }
            s.append(Integer.toHexString(parteAlta | parteBaixa));
        }
        return s.toString();
    }

    public static java.sql.Date getSQLData(java.util.Date dataConverter) {
        if (dataConverter == null) {
            return null;
        }
        java.sql.Date dataSQL = new java.sql.Date(dataConverter.getTime());
        return dataSQL;
    }

    public static java.sql.Timestamp getDataHoraJDBC(java.util.Date dataConverter, String hora) throws Exception {
        if (dataConverter == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(dataConverter);
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), Integer.valueOf(hora.substring(0, 2)), Integer.valueOf(hora.substring(3, 5)));
        return new java.sql.Timestamp(cal.getTimeInMillis());
    }

    public static java.sql.Date getDataJDBC(java.util.Date dataConverter) throws Exception {
        if (dataConverter == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(dataConverter);
        java.sql.Date dataSQL = new java.sql.Date(dataConverter.getTime());
        return dataSQL;
    }

    /**
     * M�todo usado para consultas de valores monet�rios transformando um tipo
     * String para um valor double
     *
     * @param valorConsulta
     * @return
     * @throws Exception
     */
    public static double getObterDoubleDeValorReal(String valorConsulta) throws Exception {
        // Aceita valor com virgula, inteiro e com ponto
        String v = valorConsulta;
        DecimalFormat dff = (DecimalFormat) DecimalFormat.getInstance();
        double valorDouble = 0;
        try {
            if (!v.contains(".")) {
                valorDouble = dff.parse(v).doubleValue();
            } else {
                valorDouble = Double.parseDouble(valorConsulta);
            }

        } catch (ParseException ex) {
            throw new Exception("Para consultar coloque o valor com v�rgula");
        }
        return valorDouble;
    }

    public static String getDiaDaSemana(Date data) throws Exception {
        if (data == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        return getDiaDaSemanaApresentar(cal.get(Calendar.DAY_OF_WEEK));
    }

    public static Integer getDiaDaSemanaNumero(Date data) {
        if (data == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        return cal.get(Calendar.DAY_OF_WEEK);
    }

    public static String getDiaDaSemanaApresentar(int diaCalendar) {
        if (diaCalendar == 1) {
            return "Dom";
        } else if (diaCalendar == 2) {
            return "Seg";
        } else if (diaCalendar == 3) {
            return "Ter";
        } else if (diaCalendar == 4) {
            return "Qua";
        } else if (diaCalendar == 5) {
            return "Qui";
        } else if (diaCalendar == 6) {
            return "Sex";
        } else if (diaCalendar == 7) {
            return "Sáb";
        }
        return "";
    }

    public static List<String> getMesesStringEntreDatas(Date dataInicial, Date dataFinal) throws Exception {
        List<String> listaMeses = new ArrayList<String>();
        List<Date> mesesEntreDatas = getMesesEntreDatas(dataInicial, dataFinal);
        for (Date data : mesesEntreDatas) {
            Calendar calendarInicial = Calendario.getInstance();
            calendarInicial.setTime(data);
            int mes = calendarInicial.get(Calendar.MONTH) + 1;
            int ano = calendarInicial.get(Calendar.YEAR);
            listaMeses.add(String.format("%02d/%d", mes, ano));
        }

        return listaMeses;
    }

    public static List<Date> getMesesEntreDatas(Date dataInicial, Date dataFinal) throws Exception {
        if (dataInicial.getTime() > dataFinal.getTime()) {
            return null;
        }

        Calendar calendarInicial = Calendario.getInstance();
        calendarInicial.setTime(dataInicial);
        Calendar calendarFinal = Calendario.getInstance();
        calendarFinal.setTime(dataFinal);
        List<Date> listaMeses = new ArrayList<Date>();

        while (calculaIntegerData(calendarInicial) <= calculaIntegerData(calendarFinal)) {
            Date data = calendarInicial.getTime();
            // WM - 23/12/2010
            // Exemplo: data inicial - 02/11/2010 data final 04/02/2010
            // a lista de meses teria: novembro: 02/11 - 30/11
            // dezembro: 01/12 - 31/12
            // janeiro: 01/01 - 30/01
            // fevereiro:01/02 - 04/02
            if (listaMeses.isEmpty()) {
                listaMeses.add(getDataComHoraZerada(data));
            } else {
                Date dataInicioNova = getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(data));
                listaMeses.add(dataInicioNova);
                calendarInicial.setTime(dataInicioNova);
            }
            calendarInicial.add(Calendar.MONTH, 1);
        }

        return listaMeses;
    }

    public static List<Date> getMesesEntreDatasAleatorias(Date dataInicial, Date dataFinal) throws Exception {
        if (dataInicial.getTime() > dataFinal.getTime()) {
            return null;
        }

        Calendar calendarInicial = Calendario.getInstance();
        calendarInicial.setTime(dataInicial);
        Calendar calendarFinal = Calendario.getInstance();
        calendarFinal.setTime(dataFinal);
        List<Date> listaMeses = new ArrayList<Date>();

        int aux = 0;
        while (calculaIntegerData(calendarInicial) <= calculaIntegerData(calendarFinal)) {
            Date data = calendarInicial.getTime();
            data = (aux++ > 0 ? obterPrimeiroDiaMes(data) : data);
            listaMeses.add(getDataComHoraZerada(data));
            calendarInicial.add(Calendar.MONTH, 1);
        }

        return listaMeses;
    }

    private static int calculaIntegerData(Calendar data) { // mmYYYY
        return (data.get(Calendar.MONTH) + 1) + (data.get(Calendar.YEAR) * 100);
    }

    public static List<Date> getMesesEntreDatasFinalMes(Date dataInicial, Date dataFinal) throws Exception {
        Calendar calendarInicial = Calendario.getInstance();
        calendarInicial.setTime(obterUltimoDiaMesUltimaHora(dataInicial));
        Calendar calendarFinal = Calendario.getInstance();
        calendarFinal.setTime(obterPrimeiroDiaMes(dataFinal));
        List<Date> listaMeses = new ArrayList<Date>();

        while ((calendarInicial.get(Calendar.YEAR) != calendarFinal.get(Calendar.YEAR))
                || (calendarInicial.get(Calendar.MONTH) != (calendarFinal.get(Calendar.MONTH) + 1))) {
            Date data = calendarInicial.getTime();
            listaMeses.add(data);
            calendarInicial.add(Calendar.MONTH, 1);
        }

        return listaMeses;
    }

    /**
     * @param data
     * @return
     * @deprecated Substitu�do por Calendario.getDataComHoraZerada
     */
    public static Date getDataComHoraZerada(Date data) {
        Calendar calendar = Calendario.getInstance(data);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    public static String getDataFormatoBD(java.util.Date dataConverter) {
        return getData(dataConverter, "bd");
    }

    public static String getData(java.util.Date dataConverter, String padrao) {
        if (dataConverter == null) {
            return ("");
        }
        String dataStr;
        if (padrao.equals("bd")) {

            SimpleDateFormat formatador = new SimpleDateFormat("yyyy-MM-dd", Calendario.getDefaultLocale());
            dataStr = formatador.format(dataConverter);
        } else if (padrao.equals("br")) {
            SimpleDateFormat formatador = new SimpleDateFormat("dd/MM/yyyy", Calendario.getDefaultLocale());
            dataStr = formatador.format(dataConverter);
        } else {
            DateFormat formatador = DateFormat.getDateInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
            dataStr = formatador.format(dataConverter);
        }
        return (dataStr);
    }

    public static String getDataAplicandoFormatacao(Date data, String mascara) {
        if (data == null) {
            return "";
        }
        SimpleDateFormat formatador = new SimpleDateFormat(mascara);
        String dataStr = formatador.format(data);
        return dataStr;
    }

    public static String getDataAplicandoFormatacaoTZ(Date data, String mascara, String tzId) {
        if (data == null) {
            return "";
        }
        SimpleDateFormat formatador = new SimpleDateFormat(mascara);
        formatador.setTimeZone(TimeZone.getTimeZone(tzId));
        return formatador.format(data);
    }

    public static String arrendondarForcando2CadasDecimaisComVirgula(double valor) {
        valor = Uteis.arredondar(valor, 2);
        String valorStr = String.valueOf(valor);

        String inteira = valorStr.substring(0, valorStr.indexOf("."));
        String extensao = valorStr.substring(valorStr.indexOf(".") + 1, valorStr.length());
        if (extensao.length() == 1) {
            extensao += "0";
        }
        valorStr = Uteis.removerMascara(inteira) + "," + extensao;
        return valorStr;
    }

    public static double adicionarCaractereApos1CasaDecimal(double valor) {
        String validacao = String.valueOf(Double.toString(valor).charAt(1));
        if (validacao.equals(".")) {
            return valor;
        }
        int i = (int)valor;
        String sentenca = i+"";
        char caractere = sentenca.charAt(0);
        String valorFormatado = caractere + ".";
        return Double.parseDouble(sentenca.replaceFirst(String.valueOf(caractere), valorFormatado));
    }

    public static double arredondarForcando2CasasDecimais(double valor) {
        valor = Uteis.arredondar(valor, 2);
        String valorStr = String.valueOf(valor);

        String inteira = valorStr.substring(0, valorStr.indexOf("."));
        String extensao = valorStr.substring(valorStr.indexOf(".") + 1, valorStr.length());
        if (extensao.length() == 1) {
            extensao += "0";
        }
        valorStr = Uteis.removerMascara(inteira) + "." + extensao;
        return Double.parseDouble(valorStr);
    }

    public static double forcarCasasDecimais(int casas, BigDecimal valor) {
        String mascara = "#.";
        for (int e = 0; e < casas; e++) {
            mascara += "#";
        }
        DecimalFormat formato = new DecimalFormat(mascara);
        return Double.valueOf(formato.format(valor).replace(".", "").replace(",", "."));
    }

    public static double forcarCasasDecimais(int casas, Double valor) {
        String mascara = "#.";
        for (int e = 0; e < casas; e++) {
            mascara += "#";
        }
        DecimalFormat formato = new DecimalFormat(mascara);
        return Double.valueOf(formato.format(valor).replace(".", "").replace(",", "."));
    }

    public static double arredondarForcando2CasasDecimaisMantendoSinal(double valor) {
        valor = Uteis.arredondar(valor, 2);
        String valorStr = String.valueOf(valor);

        String inteira = valorStr.substring(0, valorStr.indexOf("."));
        String extensao = valorStr.substring(valorStr.indexOf(".") + 1, valorStr.length());
        if (extensao.length() == 1) {
            extensao += "0";
        }
        String sinal = inteira.charAt(0) == '-' ? "-" : "";
        valorStr = sinal + Uteis.removerMascara(inteira) + "." + extensao;
        return Double.parseDouble(valorStr);
    }

    /*
     * Defini-se a mascar� a ser aplicada a data atual de acordo com o padr�o - dd/mm/yyyy ou MM.yy.dd e assim por diante
     */
    public static String getDataAtualAplicandoFormatacao(String mascara) {
        Date hoje = Calendario.hoje();
        return getDataAplicandoFormatacao(hoje, mascara);
    }

    public static Date getDataHora00(Date data) {
        return Uteis.getDateTime(data, 00, 00, 00);
    }

    public static Date getDataHora2359(Date data) {
        return Uteis.getDateTime(data, 23, 59, 00);
    }

    public static Date hojeHora00() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTime();
    }

    public static Date hojeHora2359() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTime();
    }

    public static String getData(java.util.Date dataConverter) {
        return (getData(dataConverter, "br"));
    }

    public static java.util.Date getDate(String data) throws Exception {
        java.util.Date valorData = null;
        DateFormat formatador = DateFormat.getDateInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
        valorData = formatador.parse(data);
        Calendar cal = Calendario.getInstance();
        cal.setTime(Calendario.hoje());
        int hora = cal.get(Calendar.HOUR_OF_DAY);
        int minuto = cal.get(Calendar.MINUTE);
        int segundo = cal.get(Calendar.SECOND);

        cal.setTime(valorData);
        cal.set(Calendar.HOUR_OF_DAY, hora);
        cal.set(Calendar.MINUTE, minuto);
        cal.set(Calendar.SECOND, segundo);

        return cal.getTime();
    }

    public static java.util.Date getDate(String data, Locale local) throws Exception {
        Date valorData = null;
        if (local == null) {
            DateFormat formatador = DateFormat.getDateInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
            valorData = formatador.parse(data);
        } else {
            DateFormat formatador = DateFormat.getDateInstance(DateFormat.SHORT, local);
            valorData = formatador.parse(data);
        }
        return valorData;
    }

    public static java.util.Date getDateTime(String data, Locale local) throws Exception {
        java.util.Date valorData = Calendario.hoje();
        if (local == null) {
            DateFormat formatador = DateFormat.getDateTimeInstance(DateFormat.SHORT,
                    DateFormat.SHORT, Calendario.getDefaultLocale());
            valorData = formatador.parse(data);
        } else {
            DateFormat formatador = DateFormat.getDateTimeInstance(DateFormat.SHORT,
                    DateFormat.SHORT,
                    local);
            valorData = formatador.parse(data);
        }
        return valorData;
    }

    public static String getAnoDataAtual() {
        Date hoje;
        String hojeStr;
        DateFormat formatador;
        formatador = DateFormat.getDateInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
        hoje = Calendario.hoje();
        hojeStr = formatador.format(hoje);
        return (hojeStr.substring(hojeStr.lastIndexOf("/") + 1));
    }

    public static String getDataAtual() {
        Date hoje = Calendario.hoje();
        return (Uteis.getData(hoje));
    }

    public static String getHoraAtual() {
        Date hoje;
        DateFormat formatador;
        formatador = DateFormat.getTimeInstance(DateFormat.SHORT, Calendario.getDefaultLocale());
        hoje = Calendario.hoje();

        String horaStr;
        horaStr = formatador.format(hoje);
        return (horaStr);
    }

    public static Date getDateTime(Date data, int hora, int minuto, int segundo) {
        Calendar cal = Calendar.getInstance(Calendario.getDefaultLocale());
        cal.setTime(data);
        cal.set(Calendar.HOUR_OF_DAY, hora);
        cal.set(Calendar.MINUTE, minuto);
        cal.set(Calendar.SECOND, segundo);

        return cal.getTime();
    }

    public static String gethoraHHMMSS(Date data) {
        Calendar cal = Calendar.getInstance(Calendario.getDefaultLocale());
        cal.setTime(data);
        String hora = String.valueOf(cal.get(Calendar.HOUR_OF_DAY) < 10 ? "0" + cal.get(Calendar.HOUR_OF_DAY) : cal.get(Calendar.HOUR_OF_DAY));
        String minuto = String.valueOf(cal.get(Calendar.MINUTE) < 10 ? "0" + cal.get(Calendar.MINUTE) : cal.get(Calendar.MINUTE));
        String segundo = String.valueOf(cal.get(Calendar.SECOND) < 10 ? "0" + cal.get(Calendar.SECOND) : cal.get(Calendar.SECOND));

        return hora + minuto + segundo;
    }

    public static String getDataComHora(Date data) {
        return getData(data) + " - " + gethoraHHMMSSAjustado(data);

    }

    public static String getDataComHHMM(Date data) {
        return getData(data) + " - " + gethoraHHMMAjustado(data);

    }

    public static Integer gethoraHH(Date data) {
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        Integer hora = cal.get(Calendar.HOUR_OF_DAY);
        return hora;
    }

    public static double arredondar(double valor, int casas) {
        valor = (new BigDecimal(valor).setScale(casas, BigDecimal.ROUND_HALF_UP)).doubleValue();
        return valor;
    }

    public static long nrDiasEntreDatas(Date dataInicial, Date dataFinal) {
        Date dataIni = Calendario.getDataComHoraZerada(dataInicial);
        Date dataFin = Calendario.getDataComHoraZerada(dataFinal);
        long dias = (dataFin.getTime() - dataIni.getTime()) / (Uteis.UM_DIA);
        return dias;
    }

    public static long nrDiasEntreDatasSemHoraZerada(Date dataInicial, Date dataFinal) {
        Date dataIni = dataInicial;
        Date dataFin = dataFinal;
        long dias = (dataFin.getTime() - dataIni.getTime()) / (Uteis.UM_DIA);
        return dias;
    }

    /**
     * Calcula o tempo em minutos entre
     * <code>dataInicial</code> e
     * <code>dataFinal</code>.
     *
     * @param dataInicial Data inicial.
     * @param dataFinal   Data final.
     * @return Quantidade de minutos entre as duas datas.
     * <AUTHOR>
     */
    public static int minutosEntreHoras(String startTime, String endTime) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
                Date startDate = sdf.parse(startTime);
                Date endDate = sdf.parse(endTime);

                long timeDifferenceInMilliseconds = endDate.getTime() - startDate.getTime();
                Long timeDifferenceInMinutes = timeDifferenceInMilliseconds / (60 * 1000);

                return timeDifferenceInMinutes.intValue();
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return 0;
    }
    public static long minutosEntreDatas(Date dataInicial, Date dataFinal) {
        Calendar calendar = Calendario.getInstance();
        calendar.setTime(dataFinal);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        long mins = calendar.getTime().getTime() / (1000 * 60);

        calendar = Calendario.getInstance();
        calendar.setTime(dataInicial);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        long minsFinal = calendar.getTime().getTime() / (1000 * 60);

        return mins - minsFinal;
//		return mins - (calendar.getTime().getTime() / (1000 * 60));
    }

    /**
     * Soma algum campo de uma Data. Ex.:
     * somarCampo(negocio.comuns.utilitarias.Calendario.hoje(),
     * Calendar.DAY_OF_MONTH, 5);
     *
     * @param data       Data na qual o campo ser� somado.
     * @param campo      O campo do calend�rio.
     * @param quantidade A quantidade de tempo a ser acrescentada no campo.
     * @return Data com o campo somado.
     * <AUTHOR>
     */
    public static Date somarCampoData(Date data, Integer campo, int quantidade) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(campo, quantidade);

        return cal.getTime();
    }

    public static Date obterDataFutura(Date dataInicial, long nrDiasProgredir) {
        long dataEmDias = dataInicial.getTime() / (Uteis.UM_DIA);
        dataEmDias = dataEmDias + nrDiasProgredir;
        Date dataFutura = new Date(dataEmDias * (Uteis.UM_DIA));
        return dataFutura;
    }

    public static Date obterDataAnterior(Integer mes) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(Calendario.hoje());
        dataCalendar.add(Calendar.MONTH, -mes);
        return dataCalendar.getTime();
    }

    public static Date obterDataAnterior(Date dataInicial, int nrDiasProgredir) {
        Date dataIni = getDataComHoraZerada(dataInicial);
        Calendar c = Calendario.getInstance(dataIni);
        c.add(Calendar.DATE, nrDiasProgredir * (-1));
        return getDataComHoraZerada(c.getTime());
    }

    /**
     * incrementa a qtde de dias do parametro na data informada
     *
     * @param dataInicial
     * @param nrDiasProgredir
     * @return
     */
    public static Date obterDataFutura2(Date dataInicial, int nrDiasProgredir) {
        Calendar dataCalendar = Calendario.getInstance();
        Date dataIni = getDataComHoraZerada(dataInicial);
        dataCalendar.setTime(dataIni);

        dataCalendar.add(Calendar.DAY_OF_MONTH, nrDiasProgredir);
        return dataCalendar.getTime();
    }

    /**
     * Incrementa a qtde de meses informada na data atual
     *
     * @param dataInicial
     * @param nrMesesProgredir
     * @return
     */
    public static Date obterDataFutura3(Date dataInicial, int nrMesesProgredir) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataInicial);
        dataCalendar.add(Calendar.MONTH, nrMesesProgredir);
        return dataCalendar.getTime();
    }

    public static int obterNumeroDiasDoMes(Date dataInicial) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataInicial);
        int numeroDias = dataCalendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        return numeroDias;
    }

    public static String obterPrimeiroNomeConcatenadoSobreNome(String nome, boolean somentePrimeiroSobrenome) {
        try {
            List<String> preposicoes2 = new ArrayList();
            preposicoes2.add("dos");
            preposicoes2.add("das");
            preposicoes2.add("de");
            preposicoes2.add("da");
            preposicoes2.add("e");
            preposicoes2.add("a");
            preposicoes2.add("i");
            preposicoes2.add("o");
            String[] listaNome = nome.trim().split(" ");
            StringBuilder resultado = new StringBuilder();
            for (int i = 0; i < listaNome.length; i++) {
                String s = listaNome[i];
                if (s.trim().isEmpty()) {
                    continue;
                }
                if (i == 0) {
                    resultado.append(s);
                } else if (preposicoes2.contains(s)) {
                    resultado.append("");
                } else {
                    resultado.append(s.subSequence(0, 1)).append(".");
                    if (somentePrimeiroSobrenome) {
                        return resultado.toString();
                    }
                }
                resultado.append(" ");
            }
            return resultado.toString();
        } catch (Exception e) {
            // TODO: handle exception
        }
        return "";
    }

    public static String obterIniciais(String nome) {
        try {
            List<String> preposicoes2 = new ArrayList();
            preposicoes2.add("dos");
            preposicoes2.add("das");
            preposicoes2.add("de");
            preposicoes2.add("da");
            preposicoes2.add("e");
            preposicoes2.add("a");
            preposicoes2.add("i");
            preposicoes2.add("o");
            String[] listaNome = nome.trim().split(" ");
            StringBuilder resultado = new StringBuilder();
            for (int i = 0; i < listaNome.length; i++) {
                String s = listaNome[i];
                if (s.trim().isEmpty()) {
                    continue;
                }
                if (!preposicoes2.contains(s)) {
                    resultado.append(s.subSequence(0, 1));
                }
                resultado.append(" ");
            }
            return resultado.toString();
        } catch (Exception e) {
            // TODO: handle exception
        }
        return "";
    }

    // public static void main(String[] args) throws Exception {
    // String x = "aaaaa[(10){}Codigo_Modalidade,aaaaaaa[(10){}Codigo_Modalidade,aaaaa";
    // String tag = "[(10){}Codigo_Modalidade,";
    // String x2 = x.substring(0, x.indexOf(tag)+tag.length()+2);
    // x2 = x2.replace(tag, "X" );
    // x = x2 + x.substring(x.indexOf(tag)+tag.length()+2);
    // System.out.print(x);
    // }
    //
    // }
    // // public static Date obterDataFuturaParcela(Date dataInicial) throws Exception {
    // if (dataInicial == null) {
    // return null;
    // }
    // int nrMesesProgredir = 1;
    //
    // int dia = Uteis.getDiaMesData(dataInicial);
    // int mes = Uteis.getMesData(dataInicial);
    // int ano = Uteis.getAnoData(dataInicial);
    //
    // // // PROGREDINDO OS ANOS
    // // if (nrMesesProgredir > 12) {
    // // while (nrMesesProgredir > 12) {
    // // ano++;
    // // nrMesesProgredir += -12;
    // // }
    // // }
    //
    // // PROGREDINDO OS MESES
    // mes += nrMesesProgredir;
    // if (mes > 12) {
    // mes -= 12;
    // ano++;
    // }
    //
    // if (dia > 30) {
    // dia = 1;
    // mes++;
    // } else {
    // // CASO MES SEJA FEVEREIRO
    // if ((dia == 29) && (mes == 2)) {
    // dia = 1;
    // mes++;
    // }
    // }
    //
    // Date dataFutura = Uteis.getDate(dia + "/" + mes + "/" + ano);
    // return dataFutura;
    // }
    public static Date obterDataFuturaParcela(Date dataInicial, int mesAtual) throws Exception {
        // if (dataInicial == null) {
        // return null;
        // }
        // int nrMesesProgredir = 1;
        //
        // int dia = Uteis.getDiaMesData(dataInicial);
        // int mes = mesAtual;
        // int ano = Uteis.getAnoData(dataInicial);
        //
        // // PROGREDINDO OS MESES
        // mes += nrMesesProgredir;
        // if (mes > 12) {
        // mes -= 12;
        // ano++;
        // }
        //
        // if (dia > 30) {
        // // se o resto da divisao do numero do m�s for igual a 1 o m�s � impar e se o mes for igual ou menor que 7: (mes 1, 3 , 5, 7)
        // //ou se o resto da divisao do numero do m�s for igual a zero e o m�s for maior que 8 e menor que 12: (mes 8, 10, 12) significa
        // que o M�s tem 31 dias
        // if ((mes % 2 == 1 && mes <= 7) || (mes % 2 == 0 && (mes >= 8 && mes <= 12))) {
        // dia = 31;
        // } else {
        // dia = 30;
        // }
        // //mes++;
        // if (mes == 2) {
        // Date dataFutura = Uteis.getDate("01/02/" + ano);
        // return dataFutura = obterUltimoDiaMesUltimaHora(dataFutura);
        // }
        // } else {
        // // CASO MES SEJA FEVEREIRO
        // if ((dia >= 29) && (mes == 2)) {
        // // dia = 1;
        // // mes++;
        // Date dataFutura = Uteis.getDate("01/02/" + ano);
        // return dataFutura = obterUltimoDiaMesUltimaHora(dataFutura);
        //
        // }
        // }
        //
        // Date dataFutura = Uteis.getDate(dia + "/" + mes + "/" + ano);
        // return dataFutura;
        GregorianCalendar gc = new GregorianCalendar();
        gc.setTime(dataInicial);
        gc.add(GregorianCalendar.MONTH, mesAtual);
        return gc.getTime();
    }

    public static String getMesReferenciaData(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        int ano = dataCalendar.get(Calendar.YEAR);
        int mes = dataCalendar.get(Calendar.MONTH) + 1;

        String mesStr = String.valueOf(mes);
        if (mesStr.length() == 1) {
            mesStr = "0" + mesStr;
        }
        String mesRef = mesStr + "/" + ano;
        return mesRef;
    }

    public static String getMesReferencia(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        int mes = dataCalendar.get(Calendar.MONTH) + 1;

        String mesStr = String.valueOf(mes);
        if (mesStr.length() == 1) {
            mesStr = "0" + mesStr;
        }
        String mesRef = mesStr;
        return mesRef;
    }

    public static String getMesNomeReferencia(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);
        int mes = dataCalendar.get(Calendar.MONTH);
        return (mes == 0 ? "Janeiro"
                : mes == 1 ? "Fevereiro"
                : mes == 2 ? "Marco"
                : mes == 3 ? "Abril"
                : mes == 4 ? "Maio"
                : mes == 5 ? "Junho"
                : mes == 6 ? "Julho"
                : mes == 7 ? "Agosto"
                : mes == 8 ? "Setembro"
                : mes == 9 ? "Outubro"
                : mes == 10 ? "Novembro"
                : "Dezembro");
    }

    public static String getMesReferencia(int mes, int ano) {
        String mesStr = String.valueOf(mes);
        if (mesStr.length() == 1) {
            mesStr = "0" + mesStr;
        }
        mesStr = mesStr + "/" + String.valueOf(ano);
        return mesStr;
    }

    public static Double getGeraNumeroMesReferencia(String mesReferencia) {
        Double valor = 0.0;
        String mes = mesReferencia.substring(0, mesReferencia.indexOf("/"));
        String ano = mesReferencia.substring(mesReferencia.indexOf("/") + 1, mesReferencia.length());
        valor = Double.parseDouble(mes) + Double.parseDouble(ano);
        return valor;
    }

    public static int compareMesReferencia(String mesInicial, String mesFinal) {
        String mesInicialOrdenado = mesInicial.substring(mesInicial.indexOf("/") + 1) + mesInicial.substring(0, mesInicial.indexOf("/"));
        String mesFinalOrdenado = mesFinal.substring(mesFinal.indexOf("/") + 1) + mesFinal.substring(0, mesFinal.indexOf("/"));
        return mesInicialOrdenado.compareTo(mesFinalOrdenado);
    }

    public static String getDataDiaMesAnoConcatenado() {
        String dataAtual = Uteis.getDataAtual();
        String ano = "";
        String mes = "";
        String dia = "";
        int cont = 1;
        while (cont != 3) {
            int posicao = dataAtual.lastIndexOf("/");
            if (posicao != -1) {
                cont++;
                if (cont == 2) {
                    ano = dataAtual.substring(posicao + 1);
                    dataAtual = dataAtual.substring(0, posicao);
                } else if (cont == 3) {
                    mes = dataAtual.substring(posicao + 1);
                    dia = dataAtual.substring(0, posicao);
                }
            }
        }
        return dia + mes + ano;
    }

    public static String getDataMesAnoConcatenado() {
        // return MM/AAAA
        int mesAtual = Calendario.getInstance().get(Calendar.MONTH) + 1;
        int anoAtual = Calendario.getInstance().get(Calendar.YEAR);
        String mesAtualStr = String.valueOf(mesAtual);
        if (mesAtualStr.length() == 1) {
            mesAtualStr = "0" + mesAtualStr;
        }
        return mesAtualStr + "/" + anoAtual;
        /*
         * String dataAtual = Uteis.getDataAtual(); String ano = ""; String mes = ""; int cont = 1; while (cont != 3) { int posicao =
         * dataAtual.lastIndexOf("/"); if (posicao != -1) { cont++; if (cont == 2) { ano = dataAtual.substring(posicao + 1); dataAtual =
         * dataAtual.substring(0, posicao); } else if (cont == 3) { mes = dataAtual.substring(posicao + 1); } } } return (mes + "/" + ano);
         */
    }

    public static String removerMascara(String campo) {
        String campoSemMascara = "";
        for (int i = 0; i < campo.length(); i++) {
            if ((campo.charAt(i) != ',') && (campo.charAt(i) != '.') && (campo.charAt(i) != '-') && (campo.charAt(i) != ':')
                    && (campo.charAt(i) != '/')) {
                campoSemMascara = campoSemMascara + campo.substring(i, i + 1);
            }
        }
        return campoSemMascara;
    }


    public static String aplicarMascara(String dado, String mascara) {
        if (dado == null) {
            return dado;
        }
        if (dado.equals("")) {
            return dado;
        }
        if (dado.length() == mascara.length()) {
            return dado;
        }
        dado = removerMascara(dado);
        int posDado = 0;
        String dadoComMascara = "";
        for (int i = 0; i < mascara.length(); i++) {
            if (posDado >= dado.length()) {
                break;
            }
            String caracter = mascara.substring(i, i + 1);
            if (caracter.equals("9")) {
                dadoComMascara = dadoComMascara + dado.substring(posDado, posDado + 1);
                posDado++;
            } else {
                dadoComMascara = dadoComMascara + caracter;
            }
        }
        return dadoComMascara;
    }

    public static String formatString(Double dado, String mascara) {
        NumberFormat nf = new DecimalFormat(mascara, new DecimalFormatSymbols(new Locale("pt", "BR")));
        return nf.format(dado);
    }

    public static String getDoubleFormatado(double valor) {
        NumberFormat nf = NumberFormat.getInstance();
        nf.setMinimumFractionDigits(2);
        nf.setMaximumFractionDigits(2);
        return nf.format(valor);
    }

    public static int getDiaMesData(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        int dia = dataCalendar.get(Calendar.DAY_OF_MONTH);
        return dia;
    }

    public static int getMesData(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        int mes = dataCalendar.get(Calendar.MONTH) + 1;
        return mes;
    }

    public static int getAnoData(Date dataPrm) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);

        int ano = dataCalendar.get(Calendar.YEAR);
        return ano;
    }

    public static Date obterPrimeiroDiaMes(Date dataPrm) throws ParseException, Exception {
        if (dataPrm == null) {
            return null;
        }
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPrm);
        int mes = dataCalendar.get(Calendar.MONTH);
        int ano = dataCalendar.get(Calendar.YEAR);
        Date data = Calendario.hoje();
        String a = "01/" + (mes + 1) + "/" + ano;
        data = getDate(a);
        return data;
    }

    public static Date obterPrimeiroDiaAno(Integer ano) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.set(Calendar.YEAR, ano);
        dataCalendar.set(Calendar.MONTH, 0);
        dataCalendar.set(Calendar.DAY_OF_MONTH, 1);
        return dataCalendar.getTime();
    }

    public static Date obterUltimoDiaAno(Integer ano) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.set(Calendar.YEAR, ano);
        dataCalendar.set(Calendar.MONTH, 11);
        dataCalendar.set(Calendar.DAY_OF_MONTH, 31);
        return dataCalendar.getTime();
    }

    public static Date obterUltimoDiaMes(Date data) {
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(data);

        int dia = obterNumeroDiasDoMes(data);
        dataCalendar.set(Calendar.DAY_OF_MONTH, dia);
        return dataCalendar.getTime();
    }

    public static Date obterUltimoDiaMesUltimaHora(Date dataPrm) throws Exception {
        Date data = obterPrimeiroDiaMes(dataPrm);
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(data);

        dataCalendar.add(Calendar.MONTH, 1);
        dataCalendar.set(Calendar.AM_PM, 0);
        dataCalendar.set(Calendar.HOUR, 0);
        dataCalendar.set(Calendar.MINUTE, 0);
        dataCalendar.set(Calendar.SECOND, 0);
        dataCalendar.set(Calendar.MILLISECOND, 0);

        dataCalendar.add(Calendar.SECOND, -1);

        return dataCalendar.getTime();
    }

    public static String getValorMonetarioParaIntegracao_SemPontoNemVirgula(double valor) {
        String valorStr = String.valueOf(valor);

        String inteira = valorStr.substring(0, valorStr.indexOf("."));
        String extensao = valorStr.substring(valorStr.indexOf(".") + 1, valorStr.length());
        if (extensao.length() == 1) {
            extensao += "0";
        }
        valorStr = Uteis.removerMascara(inteira + extensao);
        return valorStr;
    }

    public static int obterDiaData(Date dataPrm) {
        if (dataPrm == null) {
            return 0;
        }
        // Desmembrando a data de nascimento
        Calendar nascimentoCalendario = Calendario.getInstance();
        nascimentoCalendario.setTime(dataPrm);
        int diaMes = nascimentoCalendario.get(Calendar.DAY_OF_MONTH);
        return diaMes;
    }

    public static String obterDiaSemanaData(Date dataPrm) {
        if (dataPrm == null) {
            return "";
        }
        // Desmembrando a data de nascimento
        Calendar nascimentoCalendario = Calendario.getInstance();
        nascimentoCalendario.setTime(dataPrm);
        int diaSemana = nascimentoCalendario.get(Calendar.DAY_OF_WEEK);
        if (diaSemana == 1) {
            return "DM";
        } else if (diaSemana == 2) {
            return "SG";
        } else if (diaSemana == 3) {
            return "TR";
        } else if (diaSemana == 4) {
            return "QA";
        } else if (diaSemana == 5) {
            return "QI";
        } else if (diaSemana == 6) {
            return "SX";
        } else {
            return "SB";
        }
    }

    public static String getMontarMatricula(String codigo, int tamanhoMascaraMatricula) throws Exception {
        int tamanhoCodigo = codigo.length();
        String matricula = "";
        if (tamanhoMascaraMatricula < tamanhoCodigo) {
            throw new Exception(
                    "A Quantidade de caracteres da Mascara est� menor que o tamanho da matr�cula. Sendo assim n�o � poss�vel gerar a matr�cula!");
        }
        int diferenca = tamanhoMascaraMatricula - tamanhoCodigo;
        while (diferenca > 0) {
            matricula = matricula + "0";
            diferenca--;
        }
        return matricula + codigo;
    }

    public static String getDesmontarMatricula(String matricula) throws Exception {
        int i = 0;
        int tamanho = matricula.length();
        int tamanhoMatricula = matricula.length();
        while (i < tamanhoMatricula) {
            if (matricula.charAt(0) == '0') {
                matricula = matricula.substring(1, tamanho);
                tamanho = tamanho - 1;
                i++;
            } else {
                return matricula;
            }
        }
        return matricula;
    }

    public static Integer calcularIdadePessoa(Date dataAtualPrm, Date dataNascimentoPrm) {
        if ((dataNascimentoPrm == null) || (dataAtualPrm == null)) {
            return 0;
        }
        // Desmembrando a data de nascimento
        Calendar nascimentoCalendario = Calendario.getInstance();
        nascimentoCalendario.setTime(dataNascimentoPrm);
        int anoNascimento = nascimentoCalendario.get(Calendar.YEAR);
        String mesNascimentoStr = String.valueOf(nascimentoCalendario.get(Calendar.MONTH) + 1);
        if (mesNascimentoStr.length() == 1) {
            mesNascimentoStr = "0" + mesNascimentoStr;
        }
        String diaNascimentoStr = String.valueOf(nascimentoCalendario.get(Calendar.DAY_OF_MONTH));
        if (diaNascimentoStr.length() == 1) {
            diaNascimentoStr = "0" + diaNascimentoStr;
        }
        String mesDiaNascimento = mesNascimentoStr + diaNascimentoStr;

        // Desmembrando a data atual (passada como parametro)
        Calendar dataAtualCalendario = Calendario.getInstance();
        dataAtualCalendario.setTime(dataAtualPrm);

        int anoAtual = dataAtualCalendario.get(Calendar.YEAR);

        String mesAtualStr = String.valueOf(dataAtualCalendario.get(Calendar.MONTH) + 1);
        if (mesAtualStr.length() == 1) {
            mesAtualStr = "0" + mesAtualStr;
        }

        String diaAtualStr = String.valueOf(dataAtualCalendario.get(Calendar.DAY_OF_MONTH));
        if (diaAtualStr.length() == 1) {
            diaAtualStr = "0" + diaAtualStr;
        }

        String mesDiaAtual = mesAtualStr + diaAtualStr;

        int idade = anoAtual - anoNascimento;
        if (mesDiaAtual.compareTo(mesDiaNascimento) < 0) {
            idade--;
        }

        return idade;
    }

    public static String substituirTag(String tag, String valor, String texto) {
        int posicaoTag = texto.indexOf(tag);
        String parteTextoInicial = texto.substring(0, posicaoTag);
        String parteTextoFinal = texto.substring(posicaoTag + tag.length());
        return parteTextoInicial + valor + parteTextoFinal;
    }

    // Metodo para incrementar um numero a uma string Ex: 00001 apos execucao do metodo fica 00002
    public static String incrementarNumeroDoTipoString(String valor, int valorIncremento) {
        int tamanho = valor.length();
        int posicaoUltimoZero = 0;
        int cont = 0;
        boolean entrou = false;
        while (cont < tamanho) {
            if (!valor.substring(cont, cont + 1).equals("1") && !valor.substring(cont, cont + 1).equals("2")
                    && !valor.substring(cont, cont + 1).equals("3") && !valor.substring(cont, cont + 1).equals("4")
                    && !valor.substring(cont, cont + 1).equals("5") && !valor.substring(cont, cont + 1).equals("6")
                    && !valor.substring(cont, cont + 1).equals("7") && !valor.substring(cont, cont + 1).equals("8")
                    && !valor.substring(cont, cont + 1).equals("9")) {
                posicaoUltimoZero = cont;
                entrou = true;
            } else {
                cont = tamanho;
            }
            cont++;
        }
        String zeros = "";
        String valorParcial = "";
        if (entrou) {
            zeros = valor.substring(0, posicaoUltimoZero + 1);
            valorParcial = valor.substring(posicaoUltimoZero + 1);
        } else {
            zeros = "";
            valorParcial = valor.substring(0);
        }
        int valorParcialInt = Integer.parseInt(valorParcial);
        return zeros + String.valueOf(valorParcialInt + valorIncremento);
    }

    // Metodo para incrementar um numero a uma string Ex: 00001 apos execucao do metodo fica 00002
    public static String getIncrementarNumeroCheque(String valor) throws Exception {

        String aux = "";
        String zeros = "";
        Integer retorno = 0;
        int tamanho = valor.length();
        int i = 0;
        int posicao = 0;
        int j = 0;
        while (i != tamanho) {
            Character caracter = valor.charAt(i);
            if (Character.isDigit(caracter)) {
                posicao = i;
                break;
            }
            i++;
        }
        aux = valor.substring(0, posicao);
        try {
            retorno = Integer.parseInt(valor.substring(posicao, valor.length()));
            retorno++;
            String tam = retorno.toString();
            while (j != tamanho - tam.length() - aux.length() && tamanho > tam.length()) {
                zeros += '0';
                j++;
            }
            aux += zeros;
            aux += retorno.toString();
            return aux;
        } catch (NumberFormatException n) {
            throw new Exception("Número do cheque" + n.getMessage().substring(n.getMessage().indexOf(":") + 1) + " não é válido. Informe outro.");
        } catch (Exception e) {
            throw new Exception("Não foi possível incrementar o n�mero do cheque informe outro número.");
        }
    }

    // public static String getIncrementarNumeroCheque(String valor) throws Exception {
    // int i = 0;
    // boolean entrou = true;
    // String numeroAnterior = "";
    // int tamanho = valor.length();
    // int tamanhoMatricula = valor.length();
    // while (i < tamanhoMatricula) {
    // /*Esse se validar se o conteudo da String n�o e um numero a ser somado*/
    // if (valor.charAt(0) != '1' && valor.charAt(0) != '2' && valor.charAt(0) != '3' && valor.charAt(0) != '4' && valor.charAt(0) != '5' &&
    // valor.charAt(0) != '6' && valor.charAt(0) != '7' && valor.charAt(0) != '8' && valor.charAt(0) != '9') {
    // /*a variavel numeroAnteriro armazena se o conteudo da string for um caracter que nao pode ser somado*/
    // numeroAnterior = numeroAnterior + valor.substring(0, 1);
    // /*retiro o caracter da variavel valor para verificar o proximo caracter*/
    // valor = valor.substring(1, tamanho);
    // tamanho = tamanho - 1;
    // i++;
    // } else if (valor.charAt(0) == '9') {/* caso o caracter verificado seja o numero 9 vou validar se existe numero antes dele ou depois
    // dele para q seja feito o calculo */
    // if (valor.length() > 1) {
    // if (valor.charAt(valor.length() - 1) == '9' && valor.charAt(valor.length() - 2) == '9' && entrou) {/*rotina para fazer o calculo com
    // numero a esquerda do 9*/
    // if (numeroAnterior.charAt(numeroAnterior.length() - 1) == '0') {
    // numeroAnterior = numeroAnterior.substring(0, numeroAnterior.length() - 1);
    // entrou = false;
    // }
    // } else if (valor.charAt(valor.length() - 1) != '9') {
    // /**rotina para fazer o calculo com numero a direita do 9*/
    // numeroAnterior = numeroAnterior + valor.substring(0, 1);
    // valor = valor.substring(1, tamanho);
    // tamanho = tamanho - 1;
    // i++;
    // } else {
    // i++;
    // }
    // } else if (numeroAnterior.charAt(numeroAnterior.length() - 1) == '0' && entrou) {
    // numeroAnterior = numeroAnterior.substring(0, numeroAnterior.length() - 1);
    // entrou = false;
    // }
    // } else {
    // i++;
    // }
    // }
    // int incremento = 0;
    // try {
    // incremento = Integer.parseInt(valor);
    // incremento = incremento + 1;
    // } catch (Exception e) {
    // throw new Exception(email + "N�o foi poss�vel incrementar o n�mero do cheque informe outro n�mero.");
    // }
    // return numeroAnterior + String.valueOf(incremento);
    // }
    public static String gethoraHHMMSSAjustado(Date data) {
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        String hora = String.valueOf(cal.get(Calendar.HOUR_OF_DAY));
        String minuto = String.valueOf(cal.get(Calendar.MINUTE));
        String segundo = String.valueOf(cal.get(Calendar.SECOND));

        if (hora.length() == 1) {
            hora = "0" + hora;
        }
        if (minuto.length() == 1) {
            minuto = "0" + minuto;
        }
        if (segundo.length() == 1) {
            segundo = "0" + segundo;
        }

        String horaFormata = hora + ":" + minuto + ":" + segundo;

        return horaFormata;
    }

    public static String gethoraHHMMAjustado(Date data) {
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        String hora = String.valueOf(cal.get(Calendar.HOUR_OF_DAY));
        String minuto = String.valueOf(cal.get(Calendar.MINUTE));

        if (hora.length() == 1) {
            hora = "0" + hora;
        }
        if (minuto.length() == 1) {
            minuto = "0" + minuto;
        }

        String horaFormata = hora + ":" + minuto;

        return horaFormata;
    }

    public static Boolean getValidarStringSePossuiLetra(String texto) {
        for (int i = 0; i < texto.length(); i++) {
            if (Character.isLetter(texto.charAt(i))) {
                return true;
            }
        }
        return false;

    }

    public static void incluirLog(String key, String chavePrimariaEntidade, String chavePrimariaSubordinado,
                                  String descAnterior, String descAtual,
                                  String operacao, String descricaoOperacao,
                                  EntidadeLogEnum entidade,
                                  String descricaoEntidade, SessaoService sessaoService, LogDao logDao, boolean ia) {
        incluirLog(key, chavePrimariaEntidade, chavePrimariaSubordinado,
                descAnterior, descAtual,
                operacao, descricaoOperacao,
                entidade,
                descricaoEntidade, sessaoService.getUsuarioAtual().getUsername(), logDao, ia, null, null);
    }

    public static void incluirLog(String key, String chavePrimariaEntidade, String chavePrimariaSubordinado,
                                  String descAnterior, String descAtual,
                                  String operacao, String descricaoOperacao,
                                  EntidadeLogEnum entidade,
                                  String descricaoEntidade, SessaoService sessaoService, LogDao logDao) {
        incluirLog(key, chavePrimariaEntidade, chavePrimariaSubordinado,
                descAnterior, descAtual,
                operacao, descricaoOperacao,
                entidade,
                descricaoEntidade, sessaoService.getUsuarioAtual().getUsername(), logDao, null, null);
    }

    public static void incluirLog(String key, String chavePrimariaEntidade, String chavePrimariaSubordinado,
                                  String descAnterior, String descAtual,
                                  String operacao, String descricaoOperacao,
                                  EntidadeLogEnum entidade,
                                  String descricaoEntidade, SessaoService sessaoService, LogDao logDao, String nomeApp, String versaoApp) {
        incluirLog(key, chavePrimariaEntidade, chavePrimariaSubordinado,
                descAnterior, descAtual,
                operacao, descricaoOperacao,
                entidade,
                descricaoEntidade, sessaoService.getUsuarioAtual().getUsername(), logDao, nomeApp, versaoApp);
    }

    public static void incluirLog(String key, String chavePrimariaEntidade, String chavePrimariaSubordinado,
                                  String descAnterior, String descAtual,
                                  String operacao, String descricaoOperacao,
                                  EntidadeLogEnum entidade,
                                  String descricaoEntidade, String username, LogDao logDao) {
        incluirLog(key, chavePrimariaEntidade, chavePrimariaSubordinado,
                descAnterior, descAtual,
                operacao, descricaoOperacao,
                entidade,
                descricaoEntidade, username, logDao, false, null, null);
    }

    public static void incluirLog(String key, String chavePrimariaEntidade, String chavePrimariaSubordinado,
                                  String descAnterior, String descAtual,
                                  String operacao, String descricaoOperacao,
                                  EntidadeLogEnum entidade,
                                  String descricaoEntidade, String username, LogDao logDao, String nomeApp, String versaoApp) {
        incluirLog(key, chavePrimariaEntidade, chavePrimariaSubordinado,
                descAnterior, descAtual,
                operacao, descricaoOperacao,
                entidade,
                descricaoEntidade, username, logDao, false, nomeApp, versaoApp);
    }

    public static void incluirLog(String key, String chavePrimariaEntidade, String chavePrimariaSubordinado,
                                  String descAnterior, String descAtual,
                                  String operacao, String descricaoOperacao,
                                  EntidadeLogEnum entidade,
                                  String descricaoEntidade, String username, LogDao logDao, Boolean ia, String nomeApp, String versaoApp) {
        try{
            Log log = new Log();
            log.setOperacao(operacao);
            log.setDescricao(descricaoOperacao);
            log.setChavePrimaria(chavePrimariaEntidade);
            if(isNotBlank(chavePrimariaSubordinado)) {
                log.setChavePrimariaEntidadeSubordinada(chavePrimariaSubordinado);
            }
            log.setNomeEntidade(entidade.name());
            log.setNomeEntidadeDescricao(descricaoEntidade);
            log.setResponsavelAlteracao(username);
            log.setUserOAMD("");
            log.setNomeCampo("Campo(s)\n");
            log.setDataAlteracao(Calendario.hoje());
            log.setValorCampoAnterior(descAnterior);
            log.setValorCampoAlterado(descAtual);
            log.setPessoa(0);
            log.setIa(ia);
            log.setNomeApp(nomeApp);
            log.setVersaoApp(versaoApp);
            logDao.insert(key, log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param data1
     * @param data2
     * @return
     * @throws ParseException
     * @deprecated @see Calendario.java
     */
    public static Integer getCompareData(Date data1, Date data2) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
        String a = format.format(data1);
        String b = format.format(data2);
        data1 = format.parse(a);
        data2 = format.parse(b);
        if (data1.compareTo(data2) > 0) {
            return 1;
        } else if (data1.compareTo(data2) == 0) {
            return 0;
        } else if (data1.compareTo(data2) < 0) {
            return -1;
        }
        return null;
    }

    public static String getIntervaloHorasEntreDatas(Date dataHoraInicial, Date dataHoraFinal) throws Exception {
        StringBuilder ret = new StringBuilder();
        // se nao existe as datas retorna string vazia
        if (dataHoraInicial == null || dataHoraFinal == null) {
            return ret.toString();
        }
        // calcula os minutos entre essas datas e transforma para o padr�o hh:mi
        Long mins = Uteis.minutosEntreDatas(dataHoraInicial, dataHoraFinal);
        Long horas = mins / 60;
        Long minutos = mins % 60;

        String horasString = "";
        if (horas < 10) {
            horasString = "0" + horas.toString();
        } else {
            horasString = horas.toString();
        }
        String minutosString = "";
        if (minutos < 10) {
            minutosString = "0" + minutos.toString();
        } else {
            minutosString = minutos.toString();
        }
        ret.append(horasString);
        ret.append(":");
        ret.append(minutosString);
        return ret.toString();
    }

    public static Integer gerarDV(String numero, Integer soma) {
        int peso = 11;
        int dv = 0;
        int num = 0;

        for (int xi = numero.length() - 1; xi >= 0; --xi) {
            if (peso == 11) {
                peso = 2;
            }

            num = Integer.parseInt(String.valueOf(numero.charAt(xi)));
            soma = soma + (peso * num);
            peso++;
        }

        dv = soma % 11;
        dv = 11 - dv;

        if (dv == 11) {
            dv = 1;
        } else if (dv == 10) {
            dv = 0;
        }
        return new Integer(dv);
    }

    public static String retornaDescricaoDiaSemana(Calendar data) {

        if (data.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            return "Domingo";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY) {
            return "Segunda";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.TUESDAY) {
            return "Terça";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.WEDNESDAY) {
            return "Quarta";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.THURSDAY) {
            return "Quinta";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY) {
            return "Sexta";
        } else if (data.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
            return "Sábado";
        } else {
            return "";
        }
    }

    public static String retornaDescricaoMes(Date data) {
        Calendar calendario = Calendario.getInstance();
        calendario.setTime(data);
        if (calendario.get(Calendar.MONTH) == Calendar.JANUARY) {
            return "Janeiro";
        } else if (calendario.get(Calendar.MONTH) == Calendar.FEBRUARY) {
            return "Fevereiro";
        } else if (calendario.get(Calendar.MONTH) == Calendar.MARCH) {
            return "Março";
        } else if (calendario.get(Calendar.MONTH) == Calendar.APRIL) {
            return "Abril";
        } else if (calendario.get(Calendar.MONTH) == Calendar.MAY) {
            return "Maio";
        } else if (calendario.get(Calendar.MONTH) == Calendar.JUNE) {
            return "Junho";
        } else if (calendario.get(Calendar.MONTH) == Calendar.JULY) {
            return "Julho";
        } else if (calendario.get(Calendar.MONTH) == Calendar.AUGUST) {
            return "Agosto";
        } else if (calendario.get(Calendar.MONTH) == Calendar.SEPTEMBER) {
            return "Setembro";
        } else if (calendario.get(Calendar.MONTH) == Calendar.OCTOBER) {
            return "Outubro";
        } else if (calendario.get(Calendar.MONTH) == Calendar.NOVEMBER) {
            return "Novembro";
        } else if (calendario.get(Calendar.MONTH) == Calendar.DECEMBER) {
            return "Dezembro";
        } else {
            return "";
        }
    }

    public static Date obterPrimeiroEUltimoDiaSemana(Boolean primeiroDia) throws Exception {
        Date data = Calendario.hoje();
        Calendar c = Calendario.getInstance();
        c.setTime(data);
        if (Calendar.SUNDAY == c.get(Calendar.DAY_OF_WEEK)) {
            data = obterDataFutura2(data, 1);
            c.setTime(data);
        }
        if (primeiroDia) {
            c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            data = obterDataAnterior(c.getTime(), 1);

        } else {
            c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
            return c.getTime();
        }
        return data;
    }

    public static Date obterPrimeiroEUltimoDiaSemana(Boolean primeiroDia, Date dataConsideracao) throws Exception {
        Date data = dataConsideracao;
        Calendar c = Calendario.getInstance();
        c.setTime(data);
        if (Calendar.SUNDAY == c.get(Calendar.DAY_OF_WEEK)) {
            data = obterDataFutura2(data, 1);
            c.setTime(data);
        }
        if (primeiroDia) {
            c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            data = obterDataAnterior(c.getTime(), 1);

        } else {
            c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
            return c.getTime();
        }
        return data;
    }

    public static void liberarListaMemoria(List listaLiberar) {
        if (listaLiberar != null) {
            listaLiberar.clear();
        }
    }

    public synchronized static Date somarDias(Date data, int dias) {
        return somarDias(data, dias, null);
    }

    public synchronized static Date somarDias(Date data, int dias, String timeZone) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);
        if (timeZone != null) {
            cal.setTimeZone(TimeZone.getTimeZone(timeZone));
        }
        cal.add(Calendar.DAY_OF_MONTH, dias);

        return cal.getTime();
    }

    public static Date somarMeses(Date data, int meses) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);

        cal.add(Calendar.MONTH, meses);

        return cal.getTime();
    }

    public static String retiraTags(String textoFormatado) {
        // String[] textos = textoFormatado.split("<.*?>");
        // StringBuilder sb = new StringBuilder();
        // for (String s : textos) {
        // sb.append(s);
        // }
        // return sb.toString();
        Pattern p = Pattern.compile("<.*?>");
        Matcher m = p.matcher(textoFormatado);
        String resultado = m.replaceAll("");
        resultado = resultado.replace("Untitled document", "");
        resultado = resultado.replace("\r", "");
        resultado = resultado.replace("\n", "");
        return resultado;
    }

    public static String obterCaminhoWeb() {
        ServletContext servletContext = (ServletContext) FacesContext.getCurrentInstance().getExternalContext().getContext();
        File caminhoBase = new File(servletContext.getRealPath("/"));
        return caminhoBase.getAbsolutePath();
    }

    /**
     * @param word - String
     * @return palavra com a primeira letra min�scula
     * @Metodo: firstLetterLower
     * @Finalidade: primeira letra MIN�SCULA
     * <AUTHOR> Maciel
     * @date 28/02/2008
     */
    public static String firstLetterLower(String word) {
        return word.replaceFirst(String.valueOf(word.charAt(0)), String.valueOf(word.charAt(0)).toLowerCase());
    }

    /**
     * @param word - String
     * @return palavra com a primeira letra mai�scula
     * @Metodo: firstLetterUpper
     * @Finalidade: primeira letra MAI�SCULA
     * <AUTHOR> Maciel
     * @date 28/02/2008
     */
    public static String firstLetterUpper(String word) {
        return word.replaceFirst(String.valueOf(word.charAt(0)), String.valueOf(word.charAt(0)).toUpperCase());
    }

    public static String obterDiferencaEntreDatasPorExtenso(Date dataInicial, Date dataFinal) {
        Calendar calIni = Calendario.getInstance();
        Calendar calFim = Calendario.getInstance();
        calIni.setTime(dataInicial);
        calFim.setTime(dataFinal);

        int anos = (int) ((calFim.getTimeInMillis() - calIni.getTimeInMillis()) / UM_ANO);
        if (anos < 0) {
            anos = 0;
        }

        int mesInicial = calIni.get(DateFormat.MONTH_FIELD) + 1;
        int mesFinal = calFim.get(DateFormat.MONTH_FIELD) + 1;
        int difMeses = 0;
        boolean continua = true;
        while (continua) {
            if (mesInicial == mesFinal) {
                break;
            }

            if (mesInicial == 12) {
                mesInicial = 0;
            }

            mesInicial++;
            difMeses++;
        }
        int meses = difMeses;
        if (meses < 0) {
            meses = 0;
        }

        int diaInicial = calIni.get(Calendar.DAY_OF_MONTH);
        int diaFinal = calFim.get(Calendar.DAY_OF_MONTH);
        int dias = 0;
        if ((diaFinal < diaInicial) && (meses > 0)) {
            meses--;
            dias = 30 - Math.abs(diaFinal - diaInicial);
        } else {
            dias = Math.abs(diaFinal - diaInicial);
        }

        return Uteis.montarStringDataPorExtenso(anos, meses, dias);

    }

    private static String montarStringDataPorExtenso(Integer anos, Integer meses, Integer dias) {
        String sAnos = anos.intValue() <= 1 ? " ano" : " anos";
        String sMeses = meses.intValue() <= 1 ? " m�s" : " meses";
        String sDias = dias.intValue() <= 1 ? " dia" : " dias";

        String conteudoAnos = anos.intValue() != 0 ? anos.toString() + sAnos : "";
        String conteudoMeses = meses.intValue() != 0 ? meses.toString() + sMeses : "";
        String conteudoDias = dias.intValue() != 0 ? dias.toString() + sDias : "";

        if ((!conteudoAnos.isEmpty()) && (!conteudoMeses.isEmpty()) && (!conteudoDias.isEmpty())) {
            return conteudoAnos + ", " + conteudoMeses + " e " + conteudoDias;
        } else if ((!conteudoAnos.isEmpty()) && (!conteudoMeses.isEmpty())) {
            return conteudoAnos + " e " + conteudoMeses;

        } else if ((!conteudoMeses.isEmpty()) && (!conteudoDias.isEmpty())) {
            return conteudoMeses + " e " + conteudoDias;
        } else {
            return conteudoAnos + conteudoDias + conteudoMeses;
        }

    }

    public static Integer getMesesIdade(Integer anos) {
        Integer meses = 0;
        meses = anos * 12;
        return meses;
    }

    public static String getLocalhostIP() {
        InetAddress localHost;
        try {
            localHost = InetAddress.getLocalHost();
            // localHost.getHostName();
            return localHost.getHostAddress();
        } catch (UnknownHostException ex) {
            return "localhost";
        }
    }

    public static String convertStreamToString(InputStream is) throws IOException {

        if (is != null) {
            StringBuilder sb = new StringBuilder();
            String line = "";

            try {
                BufferedReader reader = new BufferedReader(new InputStreamReader(is, Charset.forName("LATIN1")));
                while ((line = reader.readLine()) != null) {
                    if (line.trim().length() > 0) {
                        sb.append(line).append("\n");
                    }
                }
            } finally {
                is.close();
            }
            return sb.toString();
        } else {
            return "";
        }
    }

    public static StringBuilder convertStreamToStringBuffer(InputStream is) throws IOException {

        if (is != null) {
            StringBuilder sb = new StringBuilder();
            String line = "";

            try {
                BufferedReader reader = new BufferedReader(new InputStreamReader(is, Charset.forName("LATIN1")));
                while ((line = reader.readLine()) != null) {
                    if (line.trim().length() > 0) {
                        sb.append(line).append("\n");
                    }
                }
            } finally {
                is.close();
            }
            return sb;
        } else {
            return new StringBuilder();
        }
    }

    public static java.util.Date diaInicioMes(int mes) {
        return null;
    }

    public static void retirarHoraDaData(Calendar calendar) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
        String data = sdf.format(calendar.getTime());
        calendar.setTime(sdf.parse(data));
    }

    public static Date retirarHoraDaData(Date dataHora) throws Exception {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataHora);
        retirarHoraDaData(calendar);
        return calendar.getTime();
    }

    public static void calculaTempoExecucaoDaFuncionalidade(Date dtInicial, String funcionalidade) {
        try {
//            Calendar dataInicial = Calendario.getInstance();
//            dataInicial.setTime(dtInicial);          
//            
//            long diferenca = System.currentTimeMillis() - dataInicial.getTimeInMillis();          
//            long diferencaSeg = diferenca /1000;    //DIFERENCA EM SEGUNDOS 
////            long diferencaMin = diferenca /(60*1000);    //DIFERENCA EM MINUTOS 
////            long diferencaHoras = diferenca/(60*60*1000);    // DIFERENCA EM HORAS          
//             
//            System.out.println("Tempo de excecucao de "+ funcionalidade +": " + diferencaSeg);

            Date dtFinal = Calendario.hoje();
            long diferenca = dtFinal.getTime() - dtInicial.getTime();
            long diferencaSeg = diferenca / 1000;    //DIFERENCA EM SEGUNDOS
            System.out.println("Tempo de excecucao de " + funcionalidade + ": " + diferencaSeg + " [dif millis: " + diferenca + "]");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String getXMLDocumentCFG(String file) throws Exception {
        EditorOC editor = new EditorOC();
        editor.montarEsqueleto(file);
        return editor.getText();
    }

    private static String getValor(String xml, String tagName) {
        try {
            String tagNameInicial = "<" + tagName + ">";
            int posIni = xml.indexOf(tagNameInicial) + tagNameInicial.length();
            String tagNameFinal = "</" + tagName + ">";
            int posFinal = xml.lastIndexOf(tagNameFinal);
            if (posFinal != -1 && posIni != -1) {
                return xml.substring(posIni, posFinal);
            } else {
                return "";
            }
        } catch (Exception e) {
            return "";
        }
    }

    public static String getValorTAG(String xml, String tagName) {
        try {
            return getValor(xml, tagName);
        } catch (Exception e) {
            return "";
        }
    }

    public static boolean getValorTAGBoolean(String xml, String tagName) {
        try {
            return Boolean.parseBoolean(getValor(xml, tagName));
        } catch (Exception e) {
            return false;
        }
    }

    public static void logarDebug(String mensagem) {
        String s = "[DEBUG] " + Calendario.hoje() + " - " + mensagem;
        System.out.println(s);
    }

    public static void logar(StringBuffer sb, String mensagem) {
        String s = "[DEBUG] " + Calendario.hoje() + " - " + mensagem;
        if (sb != null) {
            sb.append("<p>").append(s).append("</p>");
        }
        if (debug) {
            System.out.println(s);
        }
    }

    public static void logar(Boolean debug, StringBuffer sb, String mensagem) {
        String s = "[DEBUG] " + Calendario.hoje() + " - " + mensagem;
        if (sb != null) {
            sb.append("<p>").append(s).append("</p>");
        }
        if (debug) {
            System.out.println(s);
        }
    }

    public static void logar(final Exception e, final Class clazz) {
        Logger.getLogger(clazz.getName()).log(Level.SEVERE, e.getMessage(), e);
    }

    public static Map<String, String> obterMapFromString(String texto) {
        Map<String, String> mapa = new HashMap();
        if (texto == null) {
            return mapa;
        }
        texto = texto.replace("{", "");
        texto = texto.replace("}", "");
        for (String keyValue : texto.split(" *, *")) {
            String[] pairs = keyValue.split(" *= *", 2);
            String atributo = pairs[0];
            String vlr = pairs.length == 1 ? "" : pairs[1];
            mapa.put(atributo, vlr);
        }
        return mapa;
    }

    /**
     * Respons�vel por remover zeros a esquerda de uma string, util
     * principalmente na gera��o e edi��o dos c�digos de plano de contas e
     * centro de custos, e em qualquer outro tipo de c�digo que utilize um nr
     * fixo de caracteres num�ricos
     *
     * <AUTHOR> 23/11/2011
     */
    public static String removerZeroAEsquerda(String string, int quantidadeARemover) {
        int qtdZerosAEsquerda = 0;
        for (int i = 0; i < quantidadeARemover; i++) {
            if (string.charAt(i) != '0') {
                break;
            }
            qtdZerosAEsquerda++;
        }
        return string.substring(qtdZerosAEsquerda);
    }

    /**
     * Respons�vel por retornar o c�digo pai de uma entidade do financeiro,
     * baseado no c�digo pr�prio. Ex.: c�digo pai de '001.011.030' � '001.011'
     *
     * <AUTHOR> 24/11/2011
     */
    public static String obterCodigoPaiEntidadeFinanceiro(String codigo) {
        String codigoPai = "";
        //se o c�digo tiver um tamanho maior do que 3, indica que o mesmo possui pai
        if (codigo != null && codigo.length() > 3) {
            codigoPai = codigo.substring(0, codigo.length() - 4);
        }
        return codigoPai;
    }

    public static List<Date> getDiasEntreDatas(Date inicio, Date fim) throws Exception {
        List<Date> dias = new ArrayList<Date>();
        Date recipiente = Calendario.getDataComHoraZerada(inicio);
        fim = Calendario.getDataComHoraZerada(fim);
        //enquanto a data recipiente for menor ou igual ao ultimo dia
        while (Calendario.menorOuIgual(recipiente, fim)) {
            //verificar se a academia foi aberta neste dia
            dias.add(recipiente);
            //somar um dia na data recipiente
            recipiente = somarCampoData(recipiente, Calendar.DAY_OF_MONTH, 1);
        }
        return dias;
    }

    public static BufferedImage toBufferedImage(final Image image, final int type) {
        if (image instanceof BufferedImage) {
            return (BufferedImage) image;
        }
        if (image instanceof VolatileImage) {
            return ((VolatileImage) image).getSnapshot();
        }
        loadImage(image);
        final BufferedImage buffImg = new BufferedImage(image.getWidth(null), image.getHeight(null), type);
        final Graphics2D g2 = buffImg.createGraphics();
        g2.drawImage(image, null, null);
        g2.dispose();
        return buffImg;
    }

    private static void loadImage(final Image image) {
        class StatusObserver implements ImageObserver {

            boolean imageLoaded = false;

            @Override
            public boolean imageUpdate(final Image img, final int infoflags,
                                       final int x, final int y, final int width, final int height) {
                if (infoflags == ALLBITS) {
                    synchronized (this) {
                        imageLoaded = true;
                        notify();
                    }
                    return true;
                }
                return false;
            }
        }
        final StatusObserver imageStatus = new StatusObserver();
        synchronized (imageStatus) {
            if (image.getWidth(imageStatus) == -1 || image.getHeight(imageStatus) == -1) {
                while (!imageStatus.imageLoaded) {
                    try {
                        imageStatus.wait();
                    } catch (InterruptedException ex) {
                    }
                }
            }
        }
    }

    /**
     * Ordena um objeto Map por seus valores, utiliza o pattern Comparator para
     * isso.
     *
     * @param unsortMap
     * @return
     * <AUTHOR> Maciel
     */
    public static Map ordenarMapPorValores(Map unsortMap) {
        List list = new LinkedList(unsortMap.entrySet());

        //ordernar lista atrav�s de seu comparador implementado
        Collections.sort(list, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                return ((Comparable) ((Map.Entry) (o1)).getValue()).compareTo(((Map.Entry) (o2)).getValue());
            }
        });

        //adiciona a lista ordenada novamente no Map
        Map sortedMap = new LinkedHashMap();
        for (Iterator it = list.iterator(); it.hasNext(); ) {
            Map.Entry entry = (Map.Entry) it.next();
            sortedMap.put(entry.getKey(), entry.getValue());
        }
        return sortedMap;
    }

    public static Map ordenarMapPorKey(Map unsortMap) {
        List list = new LinkedList(unsortMap.keySet());

        //ordernar lista atrav�s de seu comparador implementado
        Collections.sort(list, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                return ((Comparable) (o1)).compareTo(((o2)));
            }
        });

        //adiciona a lista ordenada novamente no Map
        Map sortedMap = new LinkedHashMap();
        for (Iterator it = list.iterator(); it.hasNext(); ) {
            Object key = it.next();
            sortedMap.put(key, unsortMap.get(key));
        }
        return sortedMap;
    }

    public static String celulares(String telefones) {
        try {
            String[] split = telefones.split(";");
            String cels = "";
            for (String n : split) {
                if (validarTelefoneCelular(n)) {
                    cels += ";" + n;
                }
            }
            return cels.replaceFirst(";", "");
        } catch (Exception e) {
            return telefones;
        }
    }

    /**
     * Valida o telefone, se for celular (primeiro n�mero 7 ou 8 ou 9) retorna
     * true se n�o � considerado fixo e retorna falso. mascara do telefone
     * celular � (62)99999999
     *
     * @param celular
     * @return
     */
    public static boolean validarTelefoneCelular(String celular) {
        try {
            if (celular.length() == 13 || celular.length() == 12) {
                if (Integer.parseInt(celular.substring(4, 5)) > 6) {
                    return true;
                }
            }
        } catch (Exception e) {
            //igonre
        }
        return false;
    }

    public static String getPrimeiroNome(String nome) {
        String nomes[] = nome.split(" ");
        if (nomes.length <= 1) {
            return nome;
        }
        return nomes[0];
    }

    public static String encriptarCookie(String textoPlano) {
        return encriptar(textoPlano, CHAVE_CRIPTOGRAFIA);
    }

    public static String desencriptarCookie(String textoPlano) {
        return desencriptar(textoPlano, CHAVE_CRIPTOGRAFIA);
    }

    public static String encriptarRetornoZAWOffline(String texto) {
        return encriptar(texto, CHAVE_CRIPT_ZAW_OFFLINE);
    }

    public static String desencriptarRetornoZAWOffline(String texto) {
        return desencriptar(texto, CHAVE_CRIPT_ZAW_OFFLINE);
    }

    public static String encriptarAWS(String texto) throws Exception {
        return Criptografia.encrypt(texto, CHAVE_CRIPT_MIDIA, AlgoritmoCriptoEnum.ALGORITMO_AES);
    }

    public static String desencriptarAWS(String texto) throws Exception {
        return Criptografia.decrypt(texto, CHAVE_CRIPT_MIDIA, AlgoritmoCriptoEnum.ALGORITMO_AES);
    }

    public static String encriptarZWInternal(String texto) throws Exception {
        return Criptografia.encrypt(texto, CHAVE_CRIPT_MIDIA, AlgoritmoCriptoEnum.ALGORITMO_AES, false);
    }

    public static String desencriptarZWInternal(String texto) {
        return Criptografia.decrypt(texto, CHAVE_CRIPT_MIDIA, AlgoritmoCriptoEnum.ALGORITMO_AES, false);
    }

    public static String encryptUserData(String texto) throws Exception {
        return Criptografia.encrypt(texto, SECRET_AES_ALUNO, AlgoritmoCriptoEnum.ALGORITMO_AES);
    }

    public static String decryptUserData(String texto) throws Exception {
        return Criptografia.decrypt(texto, SECRET_AES_ALUNO, AlgoritmoCriptoEnum.ALGORITMO_AES);
    }

    public static String encriptar(String textoPlano, String chave) {
        int faixa = 256;
        int tamanhoChave = chave.length();
        int posChave = -1;
        int offset = new Random().nextInt(faixa);
        int posTextoPlano = 0;
        int codigo;

        if (tamanhoChave == 0) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        result.append(converterParaHexa(offset));

        for (; posTextoPlano < textoPlano.length(); posTextoPlano++) {
            codigo = ((int) textoPlano.charAt(posTextoPlano) + offset) % 255;

            if (posChave < (tamanhoChave - 1)) {
                posChave++;
            } else {
                posChave = 0;
            }

            codigo = codigo ^ ((int) chave.charAt(posChave));
            result.append(converterParaHexa(codigo));
            offset = codigo;
        }

        return result.toString();
    }

    private static boolean ehNumeroHexa(String texto) {
        if (texto.length() < 2) {
            return false;
        }
        String carecteres = "0123456789abcdefghABCDEFGH";
        return carecteres.indexOf(texto.charAt(0)) > -1 && carecteres.indexOf(texto.charAt(1)) > -1;
    }

    public static String desencriptar(String textoCifrado, String chave) {
        int tamanhoChave = chave.length();
        int posChave = -1;
        int offset;
        int posTextoCifrado;
        int codigoTemp;
        int codigoCaractereDecifrado;
        String textoHexa;
        StringBuilder result = new StringBuilder();

        if (tamanhoChave == 0) {
            return "";
        }

        textoHexa = textoCifrado.substring(0, 2);
        if (!ehNumeroHexa(textoHexa)) {
            return "";
        }

        offset = Integer.parseInt(textoHexa, 16);
        posTextoCifrado = 2;
        do {
            textoHexa = textoCifrado.substring(posTextoCifrado, posTextoCifrado + 2);
            if (!ehNumeroHexa(textoHexa)) {
                return "";
            }
            codigoTemp = Integer.parseInt(textoHexa, 16);
            if (posChave < (tamanhoChave - 1)) {
                posChave++;
            } else {
                posChave = 0;
            }
            codigoCaractereDecifrado = codigoTemp ^ (int) (chave.charAt(posChave));
            if (codigoCaractereDecifrado <= offset) {
                codigoCaractereDecifrado = 255 + codigoCaractereDecifrado - offset;
            } else {
                codigoCaractereDecifrado = codigoCaractereDecifrado - offset;
            }
            result.append((char) codigoCaractereDecifrado);
            offset = codigoTemp;
            posTextoCifrado = posTextoCifrado + 2;
        } while (posTextoCifrado < textoCifrado.length() - 1);

        return result.toString();
    }

    private static String converterParaHexa(int codigo) {
        String hexConvertido = Integer.toHexString(codigo);
        if (hexConvertido.length() == 1) {
            hexConvertido = '0' + hexConvertido;
        }
        return hexConvertido;
    }

    public static String retirarPontosGraficos(String texto) {
        texto = texto.replaceAll("\\?", "");
        texto = texto.replaceAll("\\!", "");
        return texto;
    }

    /**
     * Retorna um array do tipo: "1,2,3,4,5" ou "joana,maria,teste". De valores
     * de um atributo de uma lista. Utiliza-se Reflex�o
     *
     * @param nomeAtributo
     * @param listaObjetos
     * @return um array separado por v�rgulas ou uma String vazia.
     */
    public static String splitFromList(final String nomeAtributo, List listaObjetos, boolean scape) {
        String retorno = "";
        for (Object object : listaObjetos) {
            String sc = scape ? "'%s'," : "%s,";
            retorno += String.format(sc, new Object[]{
                    UtilReflection.getValor(object, nomeAtributo)});
        }
        int i = retorno.lastIndexOf(",");
        if (i != -1) {
            retorno = retorno.substring(0, i);
        }
        return retorno;
    }

    /**
     * Retorna um array do tipo: "1,2,3,4,5" ou "'joana','maria','teste'". De
     * valores de um atributo de uma lista. Utiliza-se Reflex�o
     *
     * @return um array separado por v�rgulas ou uma String vazia.
     */
    public static String splitFromArray(final Object[] array, boolean scape) {
        String retorno = "";
        for (int i = 0; i < array.length; i++) {
            String sc = scape ? "'%s'," : "%s,";
            retorno += String.format(sc, new Object[]{array[i]});
        }
        int i = retorno.lastIndexOf(",");
        if (i != -1) {
            retorno = retorno.substring(0, i);
        }
        return retorno;
    }

    public static List<String> getTituloAtributosFromStringBuffer(StringBuilder sb) {
        String regex = "\\[[^\\]]+\\]";
        Pattern pattern = Pattern.compile(regex);
        List<String> lista = new ArrayList<String>();

        Matcher matcher = pattern.matcher(sb.toString());
        // Procura as similaridades apenas do primeiro registro, o restante s�o repetidos
        if (matcher.find()) {
            String trecho = matcher.group();
            trecho = trecho.replace("[", "").replace("]", "");

            String[] v1 = trecho.split(",");
            for (int i = 0; i < v1.length; i++) {
                String o = v1[i];
                String nome = o.substring(0, o.indexOf("="));
                lista.add(nome);
            }
            return lista;
        }
        return null;
    }

    public static List<String> getValoresAtributosFromStringBuffer(StringBuilder sb) {
        String regex = "\\[[^\\]]+\\]";
        Pattern pattern = Pattern.compile(regex);
        List<String> lista = new ArrayList<String>();

        Matcher matcher = pattern.matcher(sb.toString());
        // Procura as similaridades
        while (matcher.find()) {
            String trecho = matcher.group();
            trecho = trecho.replace("[", "").replace("]", "");

            String[] v1 = trecho.split(",");
            for (int i = 0; i < v1.length; i++) {
                String o = v1[i];
                lista.add(o.substring(o.indexOf("=")));
            }
            return lista;
        }
        return null;
    }

    public static void subDivide(List list, int subCollectionSize, List resultList) {
        for (int i = 0; i < list.size() / subCollectionSize + 1; i++) {
            int maxLength;
            if (i * subCollectionSize + subCollectionSize > list.size()) {
                maxLength = list.size();
            } else {
                maxLength = i * subCollectionSize + subCollectionSize;
            }
            List sublist = new ArrayList();
            for (int j = i * subCollectionSize; j < maxLength; j++) {

                sublist.add(list.get(j));
            }
            resultList.add(sublist);
        }
    }

    public static ByteArrayOutputStream criarArray(File arquivo) throws Exception {
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        byte buffer[] = new byte[4096];
        int bytesRead = 0;
        FileInputStream fi = new FileInputStream(arquivo.getAbsolutePath());
        while ((bytesRead = fi.read(buffer)) != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
        }
        arrayOutputStream.close();
        fi.close();
        return arrayOutputStream;
    }

    public static int calcularDiasUtilizadosNoMes(Date data, int nrDiasMes) {
        int diaInicio = Uteis.getDiaMesData(data);
        int diaAtual = Uteis.getDiaMesData(Calendario.hoje());
        if (diaInicio <= diaAtual) {
            return diaAtual - diaInicio;
        } else {
            return (nrDiasMes - diaInicio) + diaAtual;
        }

    }

    public static String getNomeAbreviado(String nome) {
        String nomes[] = nome.split(" ");
        if (nomes.length <= 1) {
            return nome;
        }
        int tamanho = nomes.length;
        String abv = nomes[0];

        for (int i = 1; i < tamanho - 1; i++) {
            if (nomes[i].length() > 0) {
                abv += " " + nomes[i].charAt(0) + ".";
            }
        }

        abv += " " + nomes[tamanho - 1];

        return abv;
    }

    public static String getListaEscolhidos(List lista, String booleano, String atributo, boolean codigo, boolean todos) throws Exception {
        String valores = "";
        for (Object obj : lista) {
            Method getEscolhido = obj.getClass().getDeclaredMethod("get".concat(booleano), null);
            Boolean escolhido = (Boolean) getEscolhido.invoke(obj, null);
            if (escolhido || todos) {
                Method getCodigo = obj.getClass().getDeclaredMethod("get".concat(atributo), null);
                valores += codigo ? ("," + ((Integer) getCodigo.invoke(obj, null))) : ("," + ((String) getCodigo.invoke(obj, null)));
            }
        }
        return valores.replaceFirst(",", "");
    }

    public static boolean diferente(String str1, String str2) {
        return (str1 == null && str2 != null)
                || (str1 != null && str2 == null)
                || (str1 != null && str2 != null
                && !str1.equals(str2));
    }

    public static String retirarAcentuacaoRegex(String texto) {
        texto = texto.replaceAll("[ÂÀÁÄÃ]", "A");
        texto = texto.replaceAll("[âãàáä]", "a");
        texto = texto.replaceAll("[ÊÈÉË]", "E");
        texto = texto.replaceAll("[êèéë]", "e");
        texto = texto.replaceAll("[ÎÍÌÏ]", "I");
        texto = texto.replaceAll("[îíìï]", "i");
        texto = texto.replaceAll("[ÔÕÒÓÖ]", "O");
        texto = texto.replaceAll("[ôõòóö]", "o");
        texto = texto.replaceAll("[ÛÙÚÜ]", "U");
        texto = texto.replaceAll("[ûúùü]", "u");
        texto = texto.replaceAll("Ç", "C");
        texto = texto.replaceAll("ç", "c");
        texto = texto.replaceAll("[ýÿ]", "y");
        texto = texto.replaceAll("Ý", "Y");
        texto = texto.replaceAll("ñ", "n");
        texto = texto.replaceAll("Ñ", "N");
        texto = texto.replaceAll("['<>\\|/]", "");
        return texto;
    }

    public static String addZeroEsquerda(int number) {
        return number >= 10 ? String.valueOf(number) : "0" + number;
    }

    public static boolean containsIgnoreCaseAcentos(String contem, String contido) {
        return retirarAcentuacaoRegex(contem).toLowerCase().contains(
                retirarAcentuacaoRegex(contido).toLowerCase());
    }

    public static List filtrarListaValores(List objetos, String filtro, String[] nomesAtributos) throws Exception {
        List listaFiltrada = new ArrayList();
        OUT:
        for (Object obj : objetos) {
            for (String nomeAtributo : nomesAtributos) {
                Method getMethod = obj.getClass().getDeclaredMethod("get".concat(nomeAtributo.substring(0, 1).toUpperCase()).concat(nomeAtributo.substring(1)), null);
                if (containsIgnoreCaseAcentos((String) getMethod.invoke(obj, null), filtro)) {
                    listaFiltrada.add(obj);
                    continue OUT;
                }
            }

        }
        return listaFiltrada;
    }

    public static String getSobrenome(String nomeCompleto) {
        StringBuilder retorno = new StringBuilder();
        String nomes[] = nomeCompleto.split(" ");
        if (nomes.length <= 1) {
            retorno = new StringBuilder();
        } else {
            int i = 0;
            for (String nome : nomes) {
                if (i > 0) {
                    retorno.append(nome).append(" ");
                }
                i++;
            }
            retorno.deleteCharAt(retorno.length() - 1);
        }
        return retorno.toString();
    }

    public static String getNomeAbreviandoSomentePrimeiroSobrenome(String nome) {
        if (nome == null) {
            return "";
        }
        String nomes[] = nome.split(" ");
        if (nomes.length <= 1) {
            return nome;
        }
        int tamanho = nomes.length;
        String abv = nomes[0];

        for (int i = 1; i < tamanho - 1; i++) {
            if (nomes[i].length() > 3) {
                abv += " " + nomes[i].charAt(0) + ".";
                break;
            }
        }

        abv += " " + nomes[tamanho - 1];

        return abv;
    }

    public static String converterSegundosEmMinutos(Integer segundos) {
        segundos = segundos == null ? 0 : segundos;
        Integer newminutos = segundos / 60;
        Integer newsegundos = segundos - (newminutos * 60);
        return (newminutos < 10 ? "0" + newminutos : newminutos) + ":" + (newsegundos < 10 ? "0" + newsegundos : newsegundos);
    }

    public static String converterMinutosEmHoras(Integer segundos) {
        segundos = segundos == null ? 0 : segundos;
        Integer newminutos = segundos / 60;
        Integer newsegundos = segundos - (newminutos * 60);
        return (newminutos < 10 ? "0" + newminutos : newminutos) + ":" + (newsegundos < 10 ? "0" + newsegundos : newsegundos);
    }

    public static Integer converterMinutosEmSegundos(String minutos) {
        try {
            if (minutos.contains(":")) {
                String[] split = minutos.split(":");
                Integer mins = Integer.valueOf(split[0]);
                Integer segs = split[1] == null || split[1].isEmpty() ? 0 : Integer.valueOf(split[1]);
                return mins * 60 + segs;
            }
            return Integer.valueOf(minutos) * 60;
        } catch (Exception e) {
            return 0;
        }
    }

    public static void forceDirectory(final String pathDestino) {
        File fDestino = new File(pathDestino);
        if (!fDestino.exists()) {
            try {
                FileUtils.forceMkdir(fDestino);
            } catch (Exception e) {
                Logger.getLogger(Uteis.class.getName()).log(Level.SEVERE, e.getMessage(), e);
            }
        }
    }

    public static String tirarCaracteres(String texto, boolean somenteNrs) {
        String retorno = "";
        char[] c = Uteis.removerMascara(texto).replaceAll("\\(", "").replaceAll("\\)", "").replaceAll("\\-", "").toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (Character.isDigit(c[i]) && somenteNrs) {
                retorno = retorno + c[i];
            }

            if (!Character.isDigit(c[i]) && !somenteNrs) {
                retorno = retorno + c[i];
            }

        }
        return retorno;
    }

    public static void validarEmail(String email) throws Exception {
        if (email.matches("^((.)*(\\s)+(.)*)$")) {
            throw new Exception(email + ": O email não pode conter espaços em branco");
        }
        if (!email.matches("^(([a-zA-Z0-9]|\\.|\\_|\\-)*@(.)*)$")) {
            throw new Exception(email + ": O endereço de email não pode conter caracteres especiais além de ponto \'.\', hífen \'-\' e underline \'_\'");
        }
        if (!email.matches("^((.)*@([a-zA-Z0-9]|\\.|\\-)*)$")) {
            throw new Exception(email + ": O domínio do email não pode conter caracteres especiais além de ponto \'.\' e hífen \'-\'");
        }
        if (!email.matches("^((.){2,}@(.){2,})$")) {
            throw new Exception(email + ": O endereço e o domínio do email devem possuir ao menos dois caracteres cada e estarem separados por arroba \'@\'");
        }
        if (!email.matches("^([a-zA-Z0-9](.)*@(.)*)$")) {
            throw new Exception(email + ": O endereço de email deve começar com uma letra");
        }
        if (!email.matches("^((.)*@[a-zA-Z0-9](.)*)$")) {
            throw new Exception(email + ": O domínio do email deve começar com uma letra ou número");
        }
        if (!email.matches("^((.)*@(.)*[a-zA-Z0-9])$")) {
            throw new Exception(email + ": O domínio do email deve terminar com uma letra ou número");
        }
        if (!email.matches("^((.)*@[^\\.\\-]*([\\.|\\-][^\\.\\-]+)*)$")) {
            throw new Exception(email + ": O domínio do email não pode conter dois caracteres especiais seguidos");
        }
        if (!email.matches("^((.)*@[^\\.]{2,26}(\\.[^\\.]{2,26})+)$")) {
            throw new Exception(email + ": Cada domínio do email deve possuir no máximo vinte e seis caracteres e estarem separados por ponto \'.\'");
        }
        if (!email.matches("^((.)*@[^\\.]*[a-zA-Z][^\\.]*(\\.[^\\.]*[a-zA-Z][^\\.]*)*)$")) {
            throw new Exception(email + ": Cada domínio do email deve possuir ao menos uma letra");
        }
    }

    public static void validarEmailJson(String email) throws Exception {
        try {
            validarEmail(email);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public static String concatenarListaInteger(List lista) {
        StringBuilder retorno = new StringBuilder();
        for (Object i : lista) {
            retorno.append("|").append(i);
        }
        return retorno.toString().replaceFirst("\\|", "");
    }

    public static String encriptarSol(String texto) {
        return encriptar(texto, CHAVE_CRIPT_SOL);
    }

    public static java.util.Date getDate(String data, String padrao) throws Exception {
        SimpleDateFormat format = new SimpleDateFormat(padrao);
        return new java.util.Date(format.parse(data).getTime());
    }

    public static String getURLValidaProtocolo(HttpServletRequest request) {
        String urlLogin = getURL(request);
        try {
            URL url = new URL(urlLogin);
            if ((url.getPort() != -1)
                    && String.valueOf(url.getPort()).startsWith("90")
                    && "http".equalsIgnoreCase(url.getProtocol())
                    && !servidorLocal(urlLogin)) {
                urlLogin = urlLogin.replace("http", "https");
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        return urlLogin;
    }

    public static String getURL(HttpServletRequest request) {
        StringBuffer urlAplicacao = request.getRequestURL();
        String urlLogin = urlAplicacao.toString();
        urlLogin = urlLogin.substring(0, urlAplicacao.lastIndexOf("/"));
        return urlLogin;
    }

    public static Object iniciarMapa(Map mapa, Object chave, Object valor) {
        Object get = mapa.get(chave);
        if (get == null) {
            get = valor;
            mapa.put(chave, get);
        }
        return get;
    }

    public static List<String> stringToList(String str) {
        if (str == null || str.isEmpty()) {
            return new ArrayList<String>();
        }
        String[] split = str.split(";");
        return Arrays.asList(split);
    }

    public static String listToString(List<String> strs) {
        String str = "";
        for (String s : strs) {
            str += ";" + s;
        }
        return str.replaceFirst(";", "");
    }

    public static Integer mediana(List<Integer> m) {
        int middle = m.size() / 2;
        if (m.size() % 2 == 1) {
            return m.get(middle);
        } else {
            return (m.get(middle - 1) + m.get(middle)) / 2;
        }
    }

    public static String salvarArquivo(String nomeArquivo, String dados, String destinationFile) throws IOException {

        final String prefixo = destinationFile;
        String caminho = prefixo;
        String sufixo = nomeArquivo + ".txt";
        caminho += sufixo;

//        final String dir = caminho.substring(0, caminho.lastIndexOf(File.separator));
        forceDirectory(caminho);
        File arquivoDados = new File(caminho);
        if (arquivoDados.exists()) {
            arquivoDados.delete();
        }

        arquivoDados.createNewFile();
        FileWriter fw = new FileWriter(arquivoDados);
        BufferedWriter bw = new BufferedWriter(fw);
        String[] linhas = dados.split("#");
        for (String linha : linhas) {
            bw.write(linha);
            bw.newLine();
        }

        //fecha os recursos
        bw.close();
        fw.close();
        System.out.println("LOGIN -> gravei DADOS -> " + caminho);
        return arquivoDados.getAbsoluteFile().toString();
    }

    public static String obterStringArquivoTexto(File file) {
        try {
            FileReader fileReader = new FileReader(file);
            BufferedReader reader = new BufferedReader(fileReader);
            String data = null;
            StringBuilder txt = new StringBuilder();
            while ((data = reader.readLine()) != null) {
                txt.append(data);
            }
            fileReader.close();
            reader.close();
            return txt.toString();
        } catch (Exception ex) {
            return "";
        }
    }

    public static String removerEspacosInicioFimString(String texto) {
        if (!UteisValidacao.emptyString(texto)) {
            texto = rtrim(texto);
            texto = ltrim(texto);
        }
        return texto;
    }

    public static String rtrim(String toTrim) {
        char[] val = toTrim.toCharArray();
        int len = val.length;

        while (len > 0 && val[len - 1] <= ' ') {
            len--;
        }
        return len < val.length ? toTrim.substring(0, len) : toTrim;
    }

    public static String ltrim(String toTrim) {
        int st = 0;
        char[] val = toTrim.toCharArray();
        int len = val.length;

        while (st < len && val[st] <= ' ') {
            st++;
        }
        return st > 0 ? toTrim.substring(st, len) : toTrim;
    }

    public static String formatUrlRedirect(final String key, final String userOAMD,
                                           final String urlLogin,
                                           final Integer usuarioZW,
                                           final Integer empresaZW,
                                           final String uri,
                                           final String tokenNT) {
        return formatUrlRedirect(key, userOAMD, urlLogin, usuarioZW, empresaZW, uri, tokenNT, null, null);
    }

    public static String formatUrlRedirect(final String key, final String userOAMD,
                                           final String urlLogin,
                                           final Integer usuarioZW,
                                           final Integer empresaZW,
                                           final String uri,
                                           final String tokenNT,
                                           final String sessionId) {

        return formatUrlRedirect(key, userOAMD, urlLogin, usuarioZW, empresaZW, uri, tokenNT, sessionId, null);
    }

    public static String formatUrlRedirect(final String key, final String userOAMD,
                                           final String urlLogin,
                                           final Integer usuarioZW,
                                           final Integer empresaZW,
                                           final String uri,
                                           final String tokenNT,
                                           final String sessionId,
                                           final String tokenOamd) {
        try {
            JSONObject json = new JSONObject();
            json.put("login", "true");
            json.put("key", key);
            json.put("idUserZW", UteisValidacao.emptyNumber(usuarioZW) ? "0" : usuarioZW.toString());
            json.put("idUserTR", UteisValidacao.emptyNumber(usuarioZW) ? "0" : usuarioZW.toString());
            json.put("urlRedirect", uri);
            json.put("tokenNT", tokenNT);
            json.put("urlLogin", urlLogin);
            json.put("idEmpresa", empresaZW);
            json.put("userOamd", UteisValidacao.emptyString(userOAMD) ? "" : userOAMD);
            json.put("dataSistema", "");
            json.put("aprensentarTreino", "true");
            if (sessionId != null && !sessionId.isEmpty() && !"undefined".equals(sessionId)) {
                json.put("sessionId", sessionId);
            }
            if (tokenOamd != null && !tokenOamd.isEmpty() && !"undefined".equals(tokenOamd)) {
                json.put("tokenOamd", tokenOamd);
            }
            Calendar dataCalendar = Calendar.getInstance();
            dataCalendar.add(Calendar.MINUTE, 30);
            json.put("timevld", dataCalendar.getTimeInMillis());
            String params = "/oid?lgn=" + Uteis.encriptar(json.toString(), "chave_login_unificado");

            return params;
        } catch (JSONException ex) {

            return "";
        }
    }

    public static String formatUrlRedirectUCP(final String key, final Usuario usuarioZW) {
        try {
            JSONObject json = new JSONObject();
            json.put("key", key);
            json.put("userName", UteisValidacao.emptyString(usuarioZW.getUserName()) ? "0" : usuarioZW.getUserName().toString());
            json.put("nomeEmpresa", usuarioZW.getEmpresaLogada().getNome());
            json.put("nomeCompleto", usuarioZW.getNome());
            json.put("telefones", "");
            json.put("ranking", "true");
            Calendar dataCalendar = Calendar.getInstance();
            dataCalendar.add(Calendar.MINUTE, 30);
            json.put("timevld", dataCalendar.getTimeInMillis());
            String params = "/oid?lgn=" + Uteis.encriptar(json.toString(), "chave_login_unificado");

            return params;
        } catch (JSONException ex) {

            return "";
        }
    }

    public static double obterIndiceHora(Date diaHora) {
        return obterIndiceHora(diaHora, true);
    }

    public static double obterIndiceHora(Date diaHora, Boolean arredondar) {
        Calendar cal = Calendario.getInstance();
        cal.setTime(diaHora);
        int hora = cal.get(Calendar.HOUR_OF_DAY);
        int minutos = cal.get(Calendar.MINUTE);
        int quarto = minutos / 15;
        return (Double.valueOf(hora) + (arredondar ? (quarto * 0.25) : (minutos / 100)));
    }

    public static String formatarValorNumerico(final Double valor) {

        try {
            final NumberFormat numberf = NumberFormat.getInstance(BRASIL);
            return numberf.format(valor);
        } catch (Exception e) {
            return null;

        }
    }

    /**
     * Método para pegar um numero com muitas casas decimais e reduzir em duas casas decimais apenas
     *
     * @param valor - Valor a ser formatado em duas casas Decimais
     * @return valor formatado em 2 casas decimais
     */
    public static String reduzCasasDecimais(double valor) {
        // Deixando somente trás casas depois do ponto
        DecimalFormat formato = new DecimalFormat("0.00");
        String numeroFormatado = formato.format(valor);
        numeroFormatado = numeroFormatado.replaceAll(",", ".");
        return numeroFormatado;
    }


    /**
     * Método para calcular peso Muscular para os quatro tipos: Faulkner,
     * Guedes, Pollock3 e Pollock7
     *
     * @param peso         - Peso corporal
     * @param pesoGordura  - Peso de gordura
     * @param pesoOsseo    - Peso osseo
     * @param pesoResidual - Peso residual
     * @return Peso muscular
     */
    public static double calculaPesoMuscular(double peso, Double pesoGordura, Double pesoOsseo, Double pesoResidual) {

        // Peso muscular = [peso - (peso residual - peso osseo - peso gordura)]
        return peso - pesoResidual - pesoOsseo - pesoGordura;
    }

    /**
     * Método para calcular a Massa Magra Corporal por Guedes
     *
     * @param peso        - Peso corporal
     * @param pesoGordura - Peso de gordura
     * @return Massa Magra corporal
     */
    public static double calculaMassaMagraCorporal(double peso, Double pesoGordura) {
        // massa magra corporal = peso corporal - peso da gordura
        return peso - pesoGordura;
    }


    /**
     * Método para calcular o Peso da gordura por Faulkner, Guedes, Pollock3 e Pollock7
     *
     * @param peso              - Peso corporal
     * @param percentualGordura - Percentual de gordura
     * @return Peso Gordura
     */
    public static double calculaPesoGordura(double peso, Double percentualGordura) {
        // peso da gordura = (peso corporal x percentual de gordura)/ 100
        return (peso * percentualGordura) / 100;
    }

    public static Double calculaIMC(Double peso, Double altura) {
        if (!UteisValidacao.emptyNumber(altura) && !UteisValidacao.emptyNumber(peso)) {
            return (peso / (altura * altura));
        }
        return null;
    }

    // convert InputStream to String
    public static String getStringFromInputStream(InputStream is) {

        BufferedReader br = null;
        StringBuilder sb = new StringBuilder();

        String line;
        try {

            br = new BufferedReader(new InputStreamReader(is));
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return sb.toString();

    }

    public static Integer calcularFCMaxima(Integer fcRepouso, Integer fcMaxima, Integer nivelAtividadeFisica) {
        //FCtreino = FCrepouso + ((FCmax - FCrepouso) x (intensidade/100));
        return new Double(fcRepouso + ((fcMaxima - fcRepouso) * (nivelAtividadeFisica.doubleValue() / 100.0))).intValue();
    }


    public static String getDataRegistroApresentar(Date dataReferencia) {
        if (dataReferencia == null) {
            return "";
        }

        int minutes = difMinutes(dataReferencia.getTime(), Calendario.hoje().getTime());
        if (minutes <= 2) {
            return "Agora";
        } else if (minutes <= 15) {
            return "Agora à Pouco";
        } else if (minutes < 60) {
            return String.format("Há %s minutos", minutes);
        } else if (minutes < 1440) {
            return String.format("Há %s horas", truncarHora(minutes));
        } else {
            return String.format("Em %s às %s", Calendario.getData(dataReferencia, "dd/MM/yy"), Calendario.getData(dataReferencia, Calendario.MASC_HORA));
        }
    }

    public static String obterNomeComApenasPrimeiraLetraMaiuscula(String word) {
        StringBuilder wordNormalizada = new StringBuilder("");
        String[] nomes = word.toLowerCase().split(" ");
        for (String nome : nomes) {
            if (nome.length() > 0) {
                StringBuilder nomeNormalizado = new StringBuilder("");

                nomeNormalizado.append(nome.substring(0, 1).toUpperCase());
                if (nome.length() > 1) {
                    nomeNormalizado.append(nome.substring(1));
                }
                wordNormalizada.append(nomeNormalizado).append(" ");
            }
        }
        return wordNormalizada.toString().trim();
    }

    private static int difMinutes(Long inicio, Long fim) {
        return new Long(((fim - inicio) / 1000) / 60).intValue();
    }

    private static int truncarHora(int minutos) {
        return Math.abs(minutos / 60);
    }

    public static Map<String, Object> toMap(JSONObject object) throws JSONException {
        Map<String, Object> map = new HashMap<String, Object>();

        Iterator<String> keysItr = object.keys();
        while (keysItr.hasNext()) {
            String key = keysItr.next();
            Object value = object.get(key);

            if (value instanceof JSONArray) {
                value = toList((JSONArray) value);
            } else if (value instanceof JSONObject) {
                value = toMap((JSONObject) value);
            }
            map.put(key, value);
        }
        return map;
    }

    public static List<Object> toList(JSONArray array) throws JSONException {
        List<Object> list = new ArrayList<Object>();
        for (int i = 0; i < array.length(); i++) {
            Object value = array.get(i);
            if (value instanceof JSONArray) {
                value = toList((JSONArray) value);
            } else if (value instanceof JSONObject) {
                value = toMap((JSONObject) value);
            }
            list.add(value);
        }
        return list;
    }

    public static boolean valorVazioString(String s) {
        if (s == null || s.isEmpty() || s.trim().isEmpty() || s.trim().equals(".") || s.trim().equals("-")) {
            return true;
        }

        try {
            return UteisValidacao.emptyNumber(Double.valueOf(s));
        } catch (Exception e) {
        }

        try {
            return UteisValidacao.emptyNumber(Integer.valueOf(s));
        } catch (Exception e) {
        }

        return false;
    }

    public static Date dataHoraZeradaUTC(String dia) {
        try {
            return dataHoraZeradaUTC(getDate(dia).getTime());
        } catch (Exception e) {
            return new Date(dia);
        }

    }

    public static Date dataHoraZeradaUTC(String dia, String timeZone) {
        try {
            return dataHoraZeradaUTC(getDate(dia).getTime(), timeZone);
        } catch (Exception e) {
            return new Date(dia);
        }

    }

    public static Date dataHoraZeradaUTC(Long dia) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date(dia));
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            return Calendario.getDataComHoraZerada(getDate(sdf.format(calendar.getTime())));
        } catch (Exception e) {
            return new Date(dia);
        }
    }

    public static Date dataHoraZeradaUTC(Long dia, String timeZone) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date(dia));
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
            return Calendario.getDataComHoraZerada(getDate(sdf.format(calendar.getTime())));
        } catch (Exception e) {
            return new Date(dia);
        }
    }

    public static Date dataTZ(Long dia, String tz) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date(dia));
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            sdf.setTimeZone(TimeZone.getTimeZone(tz == null ? "UTC" : tz));
            return calendar.getTime();
        } catch (Exception e) {
            return new Date(dia);
        }

    }

    public static Date setHoraMinutoSegundo(Date data, int hora, int minu, int segundos) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        int ano = calendar.get(Calendar.YEAR);
        int mes = calendar.get(Calendar.MONTH);
        int dia = calendar.get(Calendar.DAY_OF_MONTH);
        calendar.set(ano, mes, dia, hora, minu, segundos);
        return calendar.getTime();
    }

    public static String getPaintFotoDaNuvem(final String fotokey) {
        return Aplicacao.obterUrlFotoDaNuvem(fotokey);
    }

    public static String retirarProtocoloDeSegurancaUrl(String urlService) throws MalformedURLException {
        URL url = new URL(urlService);
        if (url.getPort() != -1) {
            if (String.valueOf(url.getPort()).startsWith("90") && urlService.startsWith("https")) {
                if (!servidorLocal(urlService)) {
                    urlService = urlService.replace("90", "80");
                }
            }
        }
        urlService = urlService.replace("https", "http");
        return urlService;
    }

    private static boolean servidorLocal(final String url) {
        return (url.contains("10.1.1") || url.contains("192.168") || url.contains("dyndns.org") || url.contains("localhost"));
    }

    public static int getNumeroDiasEntreDatas(Date inicio, Date fim) {
        //método para somar os dias entre duas datas, soma o dia inicial também.
        Date recipiente = Calendario.getDataComHoraZerada(inicio);
        fim = Calendario.getDataComHoraZerada(fim);
        int numeroDias = 0;
        while (Calendario.menorOuIgual(recipiente, fim)) {
            numeroDias++;
            recipiente = somarCampoData(recipiente, Calendar.DAY_OF_MONTH, 1);
        }
        return numeroDias;
    }

    public static String getTrimNomeFicha(String nome) {
        if (nome == null) {
            return "";
        }
        String[] nomes = nome.split(" ");
        int length = nomes.length;
        StringBuilder abv = new StringBuilder();
        if (nomes.length <= 2) {
            return nome;
        }
        boolean append = true;
        for (int i = 0; i < length - 1; i++) {
            if (nomes[i].equals("#@")) {
                append = false;
            }
            if (append) {
                abv.append(nomes[i]).append(" ");
            }
        }
        return abv.toString();
    }

    public static String removerTudoMenosNumero(String texto) {
        if (!UteisValidacao.emptyString(texto)) {
            texto = texto.replaceAll("\\D*", "");
        }
        return texto;
    }

    public static String readLineByLineJava8(String filePath) {
        StringBuilder contentBuilder = new StringBuilder();

        try (Stream<String> stream = Files.lines(Paths.get(filePath), StandardCharsets.UTF_8)) {
            stream.forEach(s -> contentBuilder.append(s));
        } catch (IOException e) {
            e.printStackTrace();
        }

        return contentBuilder.toString();
    }

    public static String encodeValue(String value) {
        try {
            return URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException ex) {
            throw new RuntimeException(ex.getCause());
        }
    }

    public static String getPath(HttpServletRequest request) throws Exception {
        if (request == null) {
            request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        }
        StringBuffer url = request.getRequestURL();
        URL u = new URL(url.toString());
        String path;
        if (UteisValidacao.emptyNumber(u.getPort()) || u.getPort() < 0) {
            path = u.getProtocol() + "://" + u.getHost() + request.getContextPath();
        } else {
            path = u.getProtocol() + "://" + u.getHost() + ":" + u.getPort() + request.getContextPath();
        }
        return path;
    }

    public static JSONObject getJSON(String url) throws Exception {
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Content-type", "application/json");
        CloseableHttpResponse response = client.execute(httpGet);
        String responseJSON = EntityUtils.toString(response.getEntity());
        return new JSONObject(responseJSON);
    }

    public static List<String> stringToList(String string, String separador) {
        if (string == null || string.isEmpty()) {
            return new ArrayList<String>();
        } else {
            List<String> lista = new ArrayList<String>();
            String[] emails = string.split(separador);
            for (String str : emails) {
                if (str != null && !str.isEmpty()) {
                    lista.add(str.replaceAll("\\{", "").replaceAll("\\}", ""));
                }

            }
            return lista;
        }
    }


    public static String concatenarListaVirgula(List lista) {
        StringBuilder retorno = new StringBuilder();
        for (Object i : lista) {
            retorno.append(",").append(i);
        }
        return retorno.toString().replaceFirst(",", "");
    }

    public static Map<String, String> montarMapa(ResultSet rs, String ... columns) throws Exception{
        return new HashMap(){{
            for(String c : columns){
                try {
                    if(c.contains("data")){
                        put(c, Uteis.getData(rs.getDate(c)));
                    } else {
                        put(c, String.valueOf(rs.getObject(c)));
                    }
                }catch (Exception e){
                    put(c, String.valueOf(rs.getObject(c)));
                }
            }
        }};
    }

    public static List<AlteracoesTO> compararMapas(Map<String, String> valoresAnteriores, Map<String, String> valoresAlterados){
        List<AlteracoesTO> alteracoes = new ArrayList<>();
        if(valoresAnteriores == null){
            for(String key : valoresAlterados.keySet()){
                if(!UteisValidacao.emptyString(valoresAlterados.get(key))){
                    alteracoes.add(new AlteracoesTO(key, "", valoresAlterados.get(key)));
                }
            }
        } else {
            for(String key : valoresAlterados.keySet()){
                String valor = valoresAnteriores.get(key);
                String valorAlterado = valoresAlterados.get(key);
                valor = valor == null || valor.equals("0") || valor.trim().isEmpty() || valor.equals("null") ? "" : valor;
                valorAlterado = valorAlterado == null || valorAlterado.equals("0") || valorAlterado.trim().isEmpty() || valorAlterado.equals("null") ? "" : valorAlterado;
                if(!valor.equals(valorAlterado)){
                    alteracoes.add(new AlteracoesTO(key, valor, valoresAlterados.get(key)));
                }
            }
        }
        return alteracoes;
    }

    public static String arrayToString(String[] strs, String separador) {
        String str = "";
        for (String s : strs) {
            str += separador + s;
        }
        return str.replaceFirst(separador, "");
    }

    public static Map<String, String> resultSetToMap(ResultSet rs, String key, String label) throws Exception{
        return new HashMap(){{
            while (rs.next()){
                put(rs.getString(key), rs.getString(label));
            }
        }};
    }

    public static String getIp(HttpServletRequest request){
        String ip = request.getHeader("X-FORWARDED-FOR");

        if( ip == null){
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if( ip == null){
            ip = request.getHeader("HTTP_CLIENT_IP");
        }

        if (ip == null) {
            ip = request.getRemoteAddr();
        }

        return ip;
    }

    public static String chamadaZW(String ctx,
                                    String endpoint,
                                 List<NameValuePair> params) throws Exception{

        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        return handler.handleResponse(response);
    }

    public static String calculaIdadeComMeses(String dataNasc) {

        Calendar dataHoje = GregorianCalendar.getInstance();
        int diaHoje = dataHoje.get(Calendar.DAY_OF_MONTH);
        int mesHoje = dataHoje.get(Calendar.MONTH) + 1;
        int anoHoje = dataHoje.get(Calendar.YEAR);


        String[] quebraDN = dataNasc.split("-");
        int diaNascimento = Integer.valueOf(quebraDN[2]);
        int mesNascimento = Integer.valueOf(quebraDN[1]);
        int anoNascimento = Integer.valueOf(quebraDN[0]);

        String strNiver = anoHoje+"-"+mesNascimento+"-"+diaNascimento;
        Calendar calNiver = Calendar.getInstance();
        try {
            calNiver.setTime(new SimpleDateFormat("yyyy-MM-dd").parse(strNiver));
        } catch (ParseException ex) {
            Logger.getLogger(Uteis.class.getName()).log(Level.SEVERE, null, ex);
        }

        int anos = (dataHoje.getTimeInMillis() < calNiver.getTimeInMillis()) ? (anoHoje - anoNascimento - 1):anoHoje - anoNascimento;
        int meses;
        int dias;

        meses = mesHoje - mesNascimento;
        if (meses > 0) {
            if (diaHoje < diaNascimento) {
                meses--;
            }
        } else if (meses < 0) {
            meses = 12 + meses;

            if (diaHoje < diaNascimento) {
                meses--;
            }
        } else {
            if (diaHoje < diaNascimento) {
                meses = 11;
            }
        }
        return anos + "-" + meses;
    }

    public static List diferencaEntreListas(List listaMaior, List listaMenor) throws Exception {
        List listaRetorno = new ArrayList();
        for (Object o : listaMaior) {
            if (!listaMenor.contains(o)) {
                listaRetorno.add(o);
            }
        }
        return listaRetorno;
    }

    public static String encontrarFaixaHorario(List<String> horariosBusca, String horarioProcurado) {
        List<String> horarios = new ArrayList<>(horariosBusca);
        horarios.add("23:59");
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        try {
            Date horario = sdf.parse(horarioProcurado);
            for (int i = 0; i < horarios.size(); i++) {
                Date inicioFaixa = sdf.parse(horarios.get(i));
                Date fimFaixa = sdf.parse(horarios.get(i + 1));

                if (horario.equals(inicioFaixa) || (horario.after(inicioFaixa) && horario.before(fimFaixa))) {
                    return horarios.get(i);
                }
            }
        } catch (ParseException e) {
            e.printStackTrace();
        } catch (Exception ex) {
            System.out.println("##### erro ao encontrar horario:############  "  + horarioProcurado);
        }
        return "00:00";
    }


    public static boolean resultSetContemColuna(ResultSet resultSet, String nomeColuna) throws SQLException {
        ResultSetMetaData rsMetaData = resultSet.getMetaData();
        int numberOfColumns = rsMetaData.getColumnCount();
        for (int i = 1; i <= numberOfColumns; i++) {
            String columnName = rsMetaData.getColumnName(i);
            if (nomeColuna.equalsIgnoreCase(columnName)) {
                return true;
            }
        }
        return false;
    }

    public static String obterCodigosOrdenadosSeparadosPorVirgula(List<Integer> codigos) {
        if (!UteisValidacao.emptyList(codigos)) {
            Collections.sort(codigos);
            return codigos.stream().map(Objects::toString).collect(Collectors.joining(","));
        } else {
            return "";
        }
    }

    private static long calcularMillisPorDia() {
        return 1000L * 60 * 60 * 24;
    }

    public static long calcularMillisPorSemana() {
        return calcularMillisPorDia() * 7;
    }

    public static String removerPrefixo(String valor, String prefixo) {
        if (valor != null && prefixo != null && valor.startsWith(prefixo)) {
            return valor.substring(prefixo.length());
        }
        return valor;
    }

    public static String encriptarZWUI(String texto) throws Exception {
        return Criptografia.encryptZWUI(texto, Aplicacao.getProp(Aplicacao.CHAVE_CRIPTO_ZW_UI));
    }

    public static String desencriptarZWUI(String texto) throws Exception {
        return Criptografia.decryptZWUI(texto, Aplicacao.getProp(Aplicacao.CHAVE_CRIPTO_ZW_UI));
    }

    public static Map<String, String> obtemNomeEVersaoApp(HttpServletRequest request) {
        Map<String, String> appInfo = new HashMap<>();
        try {
            String userAgent = request.getHeader("User-Agent");
            Pattern pattern = Pattern.compile("^(\\S+)\\s*/\\s*v:([\\d.]+)");
            Matcher matcher = pattern.matcher(userAgent);

            if (matcher.find()) {
                String appName = matcher.group(1);     // Ex: TREINO_IOS
                String appVersion = matcher.group(2);  // Ex: 6.13.1
                appInfo.put("nomeApp", appName);
                appInfo.put("versaoApp", appVersion);
                return appInfo;
            }
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
        return appInfo;
    }


    public static AppInfo getAppInfo(String userAgent) {
        if (UteisValidacao.emptyString(userAgent)) {
            return new AppInfo(null, null);
        }

        Matcher matcher = USER_AGENT_PATTERN_APP.matcher(userAgent);
        if (matcher.matches()) {
            String nomeApp = matcher.group(1);
            String versao = matcher.group(2);
            return new AppInfo(nomeApp, versao);
        }
        return new AppInfo(null, null);
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.atividade.FiltroAtivoEnum;
import br.com.pacto.controller.json.atividade.FiltroAtividadeCrossfitJSON;
import br.com.pacto.controller.json.atividade.FiltroAtividadeJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AtividadeDao extends DaoGenerico<Atividade, Integer>{


    List<Atividade> listarAtividades(final String ctx, FiltroAtividadeJSON filtros, String tipo, PaginadorDTO paginadorDTO,
                                     boolean crossfit, boolean buscarTodas, List<AtividadeGrupoMuscular> atvGrupoMusc, Integer empresaId)throws ServiceException;

    List<Atividade> listarAtividadeCrossfit(String ctx, FiltroAtividadeCrossfitJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    Atividade obterPorId(String ctx, Integer codigoAtividade) throws ServiceException;

    List<Atividade> listarAtividadeSelect(String ctx, FiltroAtividadeCrossfitJSON filtros) throws Exception;

    Atividade obterAlgunsCamposPorCodigo(String ctx, Integer atividadeCodigo) throws Exception;

    Atividade obterPorNomeExato(String ctx, String nome) throws Exception;

    Atividade obterPorIDIA(String ctx, String nome) throws Exception;

    Atividade obterPorIdIAVersaoDois(String ctx, int idIA) throws Exception;

    Atividade buscarPorNomeOriginalIA(String ctx, String nomeOriginalIA) throws Exception;

    Atividade buscarPorNomeAtividade(String ctx, String nomeAtividade) throws Exception;

    boolean existeAtividadeIA(String ctx);

    List<Atividade> listarAtividadesComIdia2NaoNulo(String ctx) throws ServiceException;

    void excluirRelacionamentosPorCodigos(String ctx, List<Integer> atividadeCodigos) throws ServiceException;

    void excluirAtividadesPorCodigos(String ctx, List<Integer> atividadeCodigos) throws ServiceException;

    /**
     * Busca uma atividade por ID carregando todos os relacionamentos @OneToMany
     * em uma única consulta para evitar LazyInitializationException.
     *
     * @param ctx contexto de acesso ao banco
     * @param codigo código da atividade a ser buscada
     * @return Atividade com todos os relacionamentos carregados ou null se não encontrada
     * @throws ServiceException em caso de erro na consulta
     */
    Atividade findByIdWithAllRelationships(String ctx, Integer codigo) throws ServiceException;

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.atividade;

import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AtividadeAparelhoDao extends DaoGenerico<AtividadeAparelho, Integer> {

    List<AtividadeAparelho> obterAparelhosPorAtividade(String ctx, Integer codigo) throws ServiceException;
}

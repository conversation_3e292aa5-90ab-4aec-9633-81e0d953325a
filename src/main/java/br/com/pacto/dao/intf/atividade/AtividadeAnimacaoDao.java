/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.atividade;

import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AtividadeAnimacaoDao extends DaoGenerico<AtividadeAnimacao, Integer> {

    List<AtividadeAnimacao> obterImagens(String ctx, Integer codigoAtividade, Boolean recarregaAtividade) throws ServiceException;

    void deleteFotoKeyAtividade(String ctx, Integer codigoAtividade) throws ServiceException;
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.atividade;

import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.AtividadeAparelhoDao;
import br.com.pacto.service.exception.ServiceException;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AtividadeAparelhoDaoImpl extends DaoGenericoImpl<AtividadeAparelho, Integer> implements
        AtividadeAparelhoDao {

    @Override
    public List<AtividadeAparelho> obterAparelhosPorAtividade(String ctx, Integer codigo) throws ServiceException {
        List<AtividadeAparelho> aparelhos = new ArrayList<>();
        final String sql =  "SELECT aa.codigo, aa.aparelho_codigo " +
                "FROM AtividadeAparelho aa " +
                "WHERE aa.atividade_codigo = ? " +
                "ORDER BY aa.codigo";

        try (PreparedStatement stm = getConnection(ctx).prepareStatement(sql)) {
            stm.setInt(1, codigo);
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    AtividadeAparelho aparelho = new AtividadeAparelho();
                    aparelho.setCodigo(rs.getInt("codigo"));
                    aparelhos.add(aparelho);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("Erro ao obter aparelhos por atividade: " + e.getMessage(), e);
        }
        return aparelhos;
    }
}
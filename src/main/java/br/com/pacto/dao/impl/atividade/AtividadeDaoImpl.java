/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeAlternativa;
import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.bean.atividade.AtividadeCategoriaAtividade;
import br.com.pacto.bean.atividade.AtividadeEmpresa;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.atividade.AtividadeMusculo;
import br.com.pacto.bean.atividade.AtividadeNivel;
import br.com.pacto.bean.atividade.AtividadeVideo;
import br.com.pacto.bean.atividade.CategoriaAtividade;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.musculo.GrupoMuscular;
import br.com.pacto.controller.json.atividade.FiltroAtividadeCrossfitJSON;
import br.com.pacto.controller.json.atividade.FiltroAtividadeJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public class AtividadeDaoImpl extends DaoGenericoImpl<Atividade, Integer> implements
        AtividadeDao {

    private static final int MAXIMO_ATIVIDADES_LISTAR = 50;

    @Override
    public List<Atividade> listarAtividades(String ctx, FiltroAtividadeJSON filtros, String tipo,
                                            PaginadorDTO paginadorDTO, boolean crossfit, boolean buscarTodas,
                                            List<AtividadeGrupoMuscular> atvGrupoMusc, Integer empresaId) throws ServiceException {
        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_ATIVIDADES_LISTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_ATIVIDADES_LISTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<>();
        hql.append("SELECT obj FROM Atividade obj ");

        if (filtros != null && filtros.getEmpresasIds().isEmpty()) {
            hql.append(" left join fetch obj.empresasHabilitadas ae ");
        }

        where.append("where 1=1 ");
        where.append(" AND obj.nome NOT LIKE ' %'");

        if (filtros != null && !filtros.getEmpresasIds().isEmpty()) {
            where.append(" and obj.todasEmpresas = :buscarTodas");
            param.put("buscarTodas", false);
        }

        if (filtros != null && filtros.getAtividades() != null && !filtros.getAtividades().isEmpty()) {
            if (filtros.getAtividades().contains("IA") && filtros.getAtividades().contains("CONVENCIONAL")) {
                where.append(" AND ((obj.idIA2 is not null OR obj.idIA is not null) OR (obj.idIA is null AND obj.idIA2 is null)) ");
            } else if (filtros.getAtividades().contains("IA")) {
                where.append(" AND (obj.idIA2 is not null OR obj.idIA is not null) ");
            } else if (filtros.getAtividades().contains("CONVENCIONAL")) {
                where.append(" AND obj.idIA is null AND obj.idIA2 is null ");
            }
        }

        where.append(" and obj.crossfit = :crossfit");
        param.put("crossfit", crossfit);

        if (filtros != null && (filtros.getNome()) && (!filtros.getParametro().trim().equals(""))) {
            where.append(" AND upper(unaccent(obj.nome)) like CONCAT('%',:nome,'%')");
            param.put("nome", Uteis.retirarAcentuacaoRegex(filtros.getParametro().toUpperCase()));
        }

        if (filtros != null && (filtros.getTipo()) != null && !filtros.getTipo().isEmpty()) {
            where.append(" AND obj.tipo in (");
            String tipoId = filtros.getTipo().stream()
                    .map(TipoAtividadeEnum::getId)
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
            where.append(tipoId).append(")");
        }

        if (filtros != null && filtros.getAtivo() != null) {
            where.append(" AND obj.ativo = :ativo");
            param.put("ativo", Boolean.parseBoolean(filtros.getAtivo()));
        }

        if (!UteisValidacao.emptyList(atvGrupoMusc)) {
            where.append(" AND obj.codigo in (");
            String selecionados = atvGrupoMusc.stream()
                    .map(atividadeGrupoMuscular -> atividadeGrupoMuscular.getAtividade().getCodigo())
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
            where.append(selecionados).append(")");
        }

        List<Integer> empresasIds = (filtros != null) ? filtros.getEmpresasIds() : null;
        if (filtros != null && !filtros.getEmpresasIds().isEmpty()) {
            where.append(" AND obj.codigo IN (SELECT atividade.codigo from AtividadeEmpresa atve WHERE atve.empresa.codigo in (");
            String selecionados = empresasIds.stream()
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
            where.append(selecionados).append("))");
        } else if (filtros != null) {
            EmpresaService es = UtilContext.getBean(EmpresaService.class);
            Empresa e = es.obterPorIdZW(ctx, empresaId);
            where.append(" AND (ae.empresa.codigo IS NULL OR ae.empresa.codigo = ").append(e.getCodigo()).append(")");
        }

        List<Integer> aparelhosIds = (filtros != null) ? filtros.getAparelhosIds() : null;
        if (!UteisValidacao.emptyList(aparelhosIds)) {
            where.append(" AND obj.codigo IN (SELECT sub.atividade.codigo FROM AtividadeAparelho sub WHERE sub.aparelho.codigo IN (");
            String selecionados = aparelhosIds.stream()
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
            where.append(selecionados).append("))");
        }

        hql.append(where.toString());

        List<Atividade> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParamHqlFullFromSizeList(ctx, hql.toString(), param).longValue());
            }
            if (paginadorDTO != null && paginadorDTO.getSort().contains("codigo")) {
                paginadorDTO.setSort(paginadorDTO.getSort().replace("codigo", "obj.codigo"));
            }
            if (paginadorDTO != null) {
                hql.append(paginadorDTO.getSQLOrderByUse());
            }
            if (filtros == null && paginadorDTO == null) {
                maxResults = 0;
                indiceInicial = 0;
            }
            lista = findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }
        if (paginadorDTO != null) {
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }
        return lista;
    }

    @Override
    public List<Atividade> listarAtividadeCrossfit(String ctx, FiltroAtividadeCrossfitJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        int maxResults = MAXIMO_ATIVIDADES_LISTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_ATIVIDADES_LISTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        HashMap<String, Object> params = new HashMap<>();
        hql.append("SELECT obj FROM Atividade obj ");
        where.append("WHERE obj.crossfit = true ");
        if (filtros.getNome()) {
            where.append("and upper(obj.nome) like CONCAT('%',:nome,'%')");
            params.put("nome", filtros.getParametro().trim().toUpperCase());
        }
        hql.append(where);
        hql.append(paginadorDTO.getSQLOrderBy());
        List<Atividade> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, params).longValue());
            }
            lista = findByParam(ctx, hql.toString(), params, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;
    }

    @Override
    public Atividade obterPorId(String ctx, Integer codigoAtividade) throws ServiceException {
        try {
            getCurrentSession(ctx).clear();
            return findById(ctx, codigoAtividade);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<Atividade> listarAtividadeSelect(String ctx, FiltroAtividadeCrossfitJSON filtros) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();

        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE obj.crossfit = :crossfit ");
        if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametro())) {
            hql.append("AND upper(obj.nome) like :nome ");
        }
        hql.append("ORDER BY nome ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("crossfit", filtros.isCrossfit());
        if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametro())) {
            params.put("nome", "%" + filtros.getParametro().toUpperCase().trim() + "%");
        }
        if (filtros.getIndex() != null && !UteisValidacao.emptyNumber(filtros.getMaxResults())) {
            listaReturn = findByParam(ctx, hql.toString(), params, filtros.getMaxResults(), filtros.getIndex());
        } else {
            listaReturn = findByParam(ctx, hql.toString(), params);
        }

        return listaReturn;
    }

    @Override
    public Atividade obterPorNomeExato(String ctx, String nome) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE trim(upper(obj.nome)) = trim(:nome) ");
        hql.append("ORDER BY codigo ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("nome", nome.toUpperCase());
        listaReturn = findByParam(ctx, hql.toString(), params);
        return listaReturn == null || listaReturn.isEmpty() ? null : listaReturn.get(0);
    }

    @Override
    public Atividade obterPorIDIA(String ctx, String idIA) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE obj.idIA = :idIA ");
        hql.append("ORDER BY codigo ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("idIA", idIA);
        listaReturn = findByParam(ctx, hql.toString(), params);
        return listaReturn == null || listaReturn.isEmpty() ? null : listaReturn.get(0);
    }

    @Override
    public Atividade obterPorIdIAVersaoDois(String ctx, int idIA) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE obj.idIA2 = :idIA ");
        hql.append("ORDER BY codigo ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("idIA", idIA);
        listaReturn = findByParam(ctx, hql.toString(), params);
        return listaReturn == null || listaReturn.isEmpty() ? null : listaReturn.get(0);
    }

    @Override
    public Atividade buscarPorNomeOriginalIA(String ctx, String nomeOriginalIA) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE lower(obj.nomeOriginalIA) = lower(:nome_ia) ");
        hql.append("ORDER BY codigo ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("nome_ia", nomeOriginalIA);
        listaReturn = findByParam(ctx, hql.toString(), params);
        return listaReturn == null || listaReturn.isEmpty() ? null : listaReturn.get(0);
    }

    @Override
    public Atividade buscarPorNomeAtividade(String ctx, String nomeAtividade) throws Exception {
        getCurrentSession(ctx).clear();
        List<Atividade> listaReturn;
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Atividade obj ");
        hql.append("WHERE lower(obj.nome) = lower(:nome_ia) ");
        hql.append("ORDER BY codigo ASC");
        HashMap<String, Object> params = new HashMap<>();
        params.put("nome_ia", nomeAtividade);
        listaReturn = findByParam(ctx, hql.toString(), params);
        return listaReturn == null || listaReturn.isEmpty() ? null : listaReturn.get(0);
    }


    @Override
    public Atividade obterAlgunsCamposPorCodigo(String ctx, Integer atividadeCodigo) throws Exception {
        String sql = "SELECT codigo, nome FROM Atividade WHERE codigo = " + atividadeCodigo;

        Atividade atividade;
        try (ResultSet rs = createStatement(ctx, sql)) {
            atividade = new Atividade();
            if (rs.next()) {
                atividade.setCodigo(rs.getInt("codigo"));
                atividade.setNome(rs.getString("nome"));
            }
        }

        return atividade;
    }

    @Override
    public boolean existeAtividadeIA(String ctx) {
        String sql = "SELECT EXISTS (SELECT * FROM atividade a WHERE a.idia2 IS NOT NULL)";
        try (ResultSet rs = createStatement(ctx, sql)) {
            if (rs.next()) {
                return rs.getBoolean(1);
            }
        } catch (Exception e) {
            throw new RuntimeException("Erro ao verificar existência de Atividade IA", e);
        }
        return false;
    }

    @Override
    public List<Atividade> listarAtividadesComIdia2NaoNulo(String ctx) throws ServiceException {
        try {
            String hql = "SELECT obj FROM Atividade obj WHERE obj.idIA2 IS NOT NULL";
            return findByParam(ctx, hql, new HashMap<>());
        } catch (Exception e) {
            throw new ServiceException("Erro ao buscar atividades com idIA2 não nulo.", e);
        }
    }

    @Override
    public void excluirRelacionamentosPorCodigos(String ctx, List<Integer> atividadeCodigos) throws ServiceException {
        if (atividadeCodigos == null || atividadeCodigos.isEmpty()) {
            return;
        }
        try {

            String hqlDeleteGrupoMuscular = "DELETE FROM AtividadeGrupoMuscular WHERE atividade.codigo IN (:codigos)";
            getCurrentSession(ctx).createQuery(hqlDeleteGrupoMuscular).setParameterList("codigos", atividadeCodigos).executeUpdate();

            String hqlDeleteAnimacao = "DELETE FROM AtividadeAnimacao WHERE atividade.codigo IN (:codigos)";
            getCurrentSession(ctx).createQuery(hqlDeleteAnimacao).setParameterList("codigos", atividadeCodigos).executeUpdate();

            String hqlDeleteAlternativa = "DELETE FROM AtividadeAlternativa WHERE atividade.codigo IN (:codigos)";
            getCurrentSession(ctx).createQuery(hqlDeleteAlternativa).setParameterList("codigos", atividadeCodigos).executeUpdate();

        } catch (Exception e) {
            throw new ServiceException("Erro ao excluir relacionamentos de atividades.", e);
        }
    }

    @Override
    public void excluirAtividadesPorCodigos(String ctx, List<Integer> atividadeCodigos) throws ServiceException {
        if (atividadeCodigos == null || atividadeCodigos.isEmpty()) {
            return;
        }
        try {
            String hql = "DELETE FROM Atividade WHERE codigo IN (:codigos)";
            getCurrentSession(ctx).createQuery(hql).setParameterList("codigos", atividadeCodigos).executeUpdate();
        } catch (Exception e) {
            throw new ServiceException("Erro ao excluir atividades.", e);
        }
    }

    /**
     * Busca uma atividade por ID carregando todos os relacionamentos @OneToMany
     * usando 100% SQL nativo para máxima performance e evitar LazyInitializationException.
     *
     * Este métod0 é especialmente útil para carregar uma atividade completa
     * durante a geração de logs de auditoria, onde todos os relacionamentos
     * podem ser necessários.
     *
     * @param ctx contexto de acesso ao banco
     * @param codigo código da atividade a ser buscada
     * @return Atividade com todos os relacionamentos carregados ou null se não encontrada
     * @throws ServiceException em caso de erro na consulta
     */
    @Override
    public Atividade findByIdWithAllRelationships(String ctx, Integer codigo) throws ServiceException {
        try {
            getCurrentSession(ctx).clear();

            // SQL nativo para buscar atividade com todos os relacionamentos - formatado e corrigido
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT\n");
            sql.append("    a.codigo as atividade_codigo,\n");
            sql.append("    a.nome as atividade_nome,\n");
            sql.append("    a.descricao as atividade_descricao,\n");
            sql.append("    a.ativo as atividade_ativo,\n");
            sql.append("    a.seriesapenasduracao as atividade_series_apenas_duracao,\n");
            sql.append("    a.tipo as atividade_tipo,\n");
            sql.append("    a.linkvideo as atividade_link_video,\n");
            sql.append("    a.crossfit as atividade_crossfit,\n");
            sql.append("    a.todasempresas as atividade_todas_empresas,\n");
            sql.append("    a.usarnaprescricao as atividade_usar_na_prescricao,\n");
            sql.append("    a.idia as atividade_id_ia,\n");
            sql.append("    a.idia2 as atividade_id_ia2,\n");
            sql.append("    a.nomeoriginalia as atividade_nome_original_ia,\n");
            sql.append("    aa.codigo as animacao_codigo,\n");
            sql.append("    aa.fotokey as animacao_foto_key,\n");
            sql.append("    aa.fotokeypequena as animacao_foto_key_pequena,\n");
            sql.append("    aa.fotokeyminiatura as animacao_foto_key_miniatura,\n");
            sql.append("    aa.animacao_codigo as animacao_animacao_codigo,\n");
            sql.append("    ae.codigo as empresa_codigo,\n");
            sql.append("    ae.empresa_codigo as empresa_empresa_codigo,\n");
            sql.append("    ap.codigo as aparelho_codigo,\n");
            sql.append("    ap.aparelho_codigo as aparelho_aparelho_codigo,\n");
            sql.append("    apa.nome as aparelho_nome,\n");
            sql.append("    am.codigo as musculo_codigo,\n");
            sql.append("    am.musculo_codigo as musculo_musculo_codigo,\n");
            sql.append("    agm.codigo as grupo_muscular_codigo,\n");
            sql.append("    agm.grupomuscular_codigo as grupo_muscular_grupo_muscular_codigo,\n");
            sql.append("    gm.nome as grupomuscular_nome,\n");
            sql.append("    aca.codigo as categoria_codigo,\n");
            sql.append("    aca.categoriaatividade_codigo as categoria_categoria_atividade_codigo,\n");
            sql.append("    ca.nome as categoria_nome,\n");
            sql.append("    an.codigo as nivel_codigo,\n");
            sql.append("    an.nivel_codigo as nivel_nivel_codigo,\n");
            sql.append("    ni.nome as nivel_nome,\n");
            sql.append("    af.codigo as ficha_codigo,\n");
            sql.append("    af.ficha_codigo as ficha_ficha_codigo,\n");
            sql.append("    aal.codigo as alternativa_codigo,\n");
            sql.append("    aal.atividadealternativa as alternativa_atividade_alternativa_codigo,\n");
            sql.append("    av.codigo as video_codigo,\n");
            sql.append("    av.linkvideo as video_link,\n");
            sql.append("    av.professor as linkvideo_professor\n");
            sql.append("FROM atividade a\n");
            sql.append("LEFT JOIN atividadeanimacao aa ON aa.atividade_codigo = a.codigo\n");
            sql.append("LEFT JOIN atividadeempresa ae ON ae.atividade_codigo = a.codigo\n");
            sql.append("LEFT JOIN atividadeaparelho ap ON ap.atividade_codigo = a.codigo\n");
            sql.append("LEFT JOIN aparelho apa ON apa.codigo = ap.aparelho_codigo\n");
            sql.append("LEFT JOIN atividademusculo am ON am.atividade_codigo = a.codigo\n");
            sql.append("LEFT JOIN atividadegrupomuscular agm ON agm.atividade_codigo = a.codigo\n");
            sql.append("LEFT JOIN grupomuscular gm ON gm.codigo = agm.grupomuscular_codigo \n");
            sql.append("LEFT JOIN atividadecategoriaatividade aca ON aca.atividade_codigo = a.codigo\n");
            sql.append("LEFT JOIN categoriaatividade ca ON ca.codigo = aca.categoriaatividade_codigo\n");
            sql.append("LEFT JOIN atividadenivel an ON an.atividade_codigo = a.codigo\n");
            sql.append("LEFT JOIN nivel ni ON ni.codigo = an.nivel_codigo\n");
            sql.append("LEFT JOIN atividadeficha af ON af.atividade_codigo = a.codigo\n");
            sql.append("LEFT JOIN atividadealternativa aal ON aal.atividade_codigo = a.codigo\n");
            sql.append("LEFT JOIN atividadevideo av ON av.atividade_codigo = a.codigo\n");
            sql.append("WHERE a.codigo = ?\n");
            sql.append("ORDER BY\n");
            sql.append("    a.codigo,\n");
            sql.append("    aa.codigo,\n");
            sql.append("    ae.codigo,\n");
            sql.append("    ap.codigo,\n");
            sql.append("    am.codigo,\n");
            sql.append("    agm.codigo,\n");
            sql.append("    aca.codigo,\n");
            sql.append("    an.codigo,\n");
            sql.append("    af.codigo,\n");
            sql.append("    aal.codigo,\n");
            sql.append("    av.codigo");

            return montarAtividadeCompletaSegura(ctx, sql.toString(), codigo);

        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADE, e);
        }
    }

    /**
     * Monta uma atividade completa a partir do ResultSet do SQL nativo usando PreparedStatement seguro
     */
    private Atividade montarAtividadeCompletaSegura(String ctx, String sql, Integer codigo) throws Exception {
        try (Connection conn = getConnection(ctx);
             PreparedStatement ps = conn.prepareStatement(sql)) {

            // Definir parâmetro de forma segura para evitar SQL injection
            ps.setInt(1, codigo);

            try (ResultSet rs = ps.executeQuery()) {
            Atividade atividade = null;
            Map<Integer, AtividadeAnimacao> animacoesMap = new HashMap<>();
            Map<Integer, AtividadeEmpresa> empresasMap = new HashMap<>();
            Map<Integer, AtividadeAparelho> aparelhosMap = new HashMap<>();
            Map<Integer, AtividadeMusculo> musculosMap = new HashMap<>();
            Map<Integer, AtividadeGrupoMuscular> gruposMap = new HashMap<>();
            Map<Integer, AtividadeCategoriaAtividade> categoriasMap = new HashMap<>();
            Map<Integer, AtividadeNivel> niveisMap = new HashMap<>();
            Map<Integer, AtividadeFicha> fichasMap = new HashMap<>();
            Map<Integer, AtividadeAlternativa> alternativasMap = new HashMap<>();
            Map<Integer, AtividadeVideo> videosMap = new HashMap<>();

            while (rs.next()) {
                // Criar atividade apenas uma vez
                if (atividade == null) {
                    atividade = new Atividade();
                    atividade.setCodigo(rs.getInt("atividade_codigo"));
                    atividade.setNome(rs.getString("atividade_nome"));
                    atividade.setDescricao(rs.getString("atividade_descricao"));
                    atividade.setAtivo(rs.getBoolean("atividade_ativo"));
                    atividade.setSeriesApenasDuracao(rs.getBoolean("atividade_series_apenas_duracao"));
                    atividade.setTipo(TipoAtividadeEnum.values()[rs.getInt("atividade_tipo")]);
                    atividade.setLinkVideo(rs.getString("atividade_link_video"));
                    atividade.setCrossfit(rs.getBoolean("atividade_crossfit"));
                    atividade.setTodasEmpresas(rs.getBoolean("atividade_todas_empresas"));
                    atividade.setUsarNaPrescricao(rs.getBoolean("atividade_usar_na_prescricao"));
                    atividade.setIdIA(rs.getString("atividade_id_ia"));
                    atividade.setIdIA2(rs.getInt("atividade_id_ia2"));
                    atividade.setNomeOriginalIA(rs.getString("atividade_nome_original_ia"));
                }

                // Processar AtividadeAnimacao
                Integer animacaoCodigo = rs.getInt("animacao_codigo");
                if (!rs.wasNull() && animacaoCodigo > 0 && !animacoesMap.containsKey(animacaoCodigo)) {
                    AtividadeAnimacao animacao = new AtividadeAnimacao();
                    animacao.setCodigo(animacaoCodigo);
                    animacao.setFotoKey(rs.getString("animacao_foto_key"));
                    animacao.setFotoKeyPequena(rs.getString("animacao_foto_key_pequena"));
                    animacao.setFotoKeyMiniatura(rs.getString("animacao_foto_key_miniatura"));
                    animacao.setAtividade(atividade);
                    // Carregar animacao se necessário
                    Integer animacaoAnimacaoCodigo = rs.getInt("animacao_animacao_codigo");
                    if (!rs.wasNull() && animacaoAnimacaoCodigo > 0) {
                        // Criar objeto básico da animação para evitar nova consulta
                        br.com.pacto.bean.animacao.Animacao anim = new br.com.pacto.bean.animacao.Animacao();
                        anim.setCodigo(animacaoAnimacaoCodigo);
                        animacao.setAnimacao(anim);
                    }
                    animacoesMap.put(animacaoCodigo, animacao);
                }

                // Processar AtividadeEmpresa
                Integer empresaCodigo = rs.getInt("empresa_codigo");
                if (!rs.wasNull() && empresaCodigo > 0 && !empresasMap.containsKey(empresaCodigo)) {
                    AtividadeEmpresa empresa = new AtividadeEmpresa();
                    empresa.setCodigo(empresaCodigo);
                    empresa.setAtividade(atividade);
                    // Criar objeto básico da empresa
                    Integer empresaEmpresaCodigo = rs.getInt("empresa_empresa_codigo");
                    if (!rs.wasNull() && empresaEmpresaCodigo > 0) {
                        Empresa emp = new Empresa();
                        emp.setCodigo(empresaEmpresaCodigo);
                        empresa.setEmpresa(emp);
                    }
                    empresasMap.put(empresaCodigo, empresa);
                }

                // Processar AtividadeAparelho
                Integer aparelhoCodigo = rs.getInt("aparelho_codigo");
                if (!rs.wasNull() && aparelhoCodigo > 0 && !aparelhosMap.containsKey(aparelhoCodigo)) {
                    AtividadeAparelho aparelho = new AtividadeAparelho();
                    aparelho.setCodigo(aparelhoCodigo);
                    aparelho.setAtividade(atividade);
                    // Criar objeto básico do aparelho
                    Integer aparelhoAparelhoCodigo = rs.getInt("aparelho_aparelho_codigo");
                    if (!rs.wasNull() && aparelhoAparelhoCodigo > 0) {
                        Aparelho ap = new Aparelho();
                        ap.setCodigo(aparelhoAparelhoCodigo);
                        ap.setNome(rs.getString("aparelho_nome"));
                        aparelho.setAparelho(ap);
                    }
                    aparelhosMap.put(aparelhoCodigo, aparelho);
                }

                // Processar AtividadeMusculo
                Integer musculoCodigo = rs.getInt("musculo_codigo");
                if (!rs.wasNull() && musculoCodigo > 0 && !musculosMap.containsKey(musculoCodigo)) {
                    AtividadeMusculo musculo = new AtividadeMusculo();
                    musculo.setCodigo(musculoCodigo);
                    musculo.setAtividade(atividade);
                    // Criar objeto básico do músculo
                    Integer musculoMusculoCodigo = rs.getInt("musculo_musculo_codigo");
                    if (!rs.wasNull() && musculoMusculoCodigo > 0) {
                        br.com.pacto.bean.musculo.Musculo musc = new br.com.pacto.bean.musculo.Musculo();
                        musc.setCodigo(musculoMusculoCodigo);
                        musculo.setMusculo(musc);
                    }
                    musculosMap.put(musculoCodigo, musculo);
                }

                // Processar AtividadeGrupoMuscular
                Integer grupoMuscularCodigo = rs.getInt("grupo_muscular_codigo");
                if (!rs.wasNull() && grupoMuscularCodigo > 0 && !gruposMap.containsKey(grupoMuscularCodigo)) {
                    AtividadeGrupoMuscular grupo = new AtividadeGrupoMuscular();
                    grupo.setCodigo(grupoMuscularCodigo);
                    grupo.setAtividade(atividade);
                    // Criar objeto básico do grupo muscular
                    Integer grupoMuscularGrupoMuscularCodigo = rs.getInt("grupo_muscular_grupo_muscular_codigo");
                    if (!rs.wasNull() && grupoMuscularGrupoMuscularCodigo > 0) {
                        GrupoMuscular gm = new GrupoMuscular();
                        gm.setCodigo(grupoMuscularGrupoMuscularCodigo);
                        gm.setNome(rs.getString("grupomuscular_nome"));
                        grupo.setGrupoMuscular(gm);
                    }
                    gruposMap.put(grupoMuscularCodigo, grupo);
                }

                // Processar AtividadeCategoriaAtividade
                Integer categoriaCodigo = rs.getInt("categoria_codigo");
                if (!rs.wasNull() && categoriaCodigo > 0 && !categoriasMap.containsKey(categoriaCodigo)) {
                    AtividadeCategoriaAtividade categoria = new AtividadeCategoriaAtividade();
                    categoria.setCodigo(categoriaCodigo);
                    categoria.setAtividade(atividade);
                    // Criar objeto básico da categoria
                    Integer categoriaCategoriaAtividadeCodigo = rs.getInt("categoria_categoria_atividade_codigo");
                    if (!rs.wasNull() && categoriaCategoriaAtividadeCodigo > 0) {
                        CategoriaAtividade cat = new CategoriaAtividade();
                        cat.setCodigo(categoriaCategoriaAtividadeCodigo);
                        cat.setNome(rs.getString("categoria_nome"));
                        categoria.setCategoriaAtividade(cat);
                    }
                    categoriasMap.put(categoriaCodigo, categoria);
                }

                // Processar AtividadeNivel
                Integer nivelCodigo = rs.getInt("nivel_codigo");
                if (!rs.wasNull() && nivelCodigo > 0 && !niveisMap.containsKey(nivelCodigo)) {
                    AtividadeNivel nivel = new AtividadeNivel();
                    nivel.setCodigo(nivelCodigo);
                    nivel.setAtividade(atividade);
                    // Criar objeto básico do nível
                    Integer nivelNivelCodigo = rs.getInt("nivel_nivel_codigo");
                    if (!rs.wasNull() && nivelNivelCodigo > 0) {
                        br.com.pacto.bean.nivel.Nivel niv = new br.com.pacto.bean.nivel.Nivel();
                        niv.setCodigo(nivelNivelCodigo);
                        niv.setNome(rs.getString("nivel_nome"));
                        nivel.setNivel(niv);
                    }
                    niveisMap.put(nivelCodigo, nivel);
                }

                // Processar AtividadeFicha
                Integer fichaCodigo = rs.getInt("ficha_codigo");
                if (!rs.wasNull() && fichaCodigo > 0 && !fichasMap.containsKey(fichaCodigo)) {
                    AtividadeFicha ficha = new AtividadeFicha();
                    ficha.setCodigo(fichaCodigo);
                    ficha.setAtividade(atividade);
                    // Criar objeto básico da ficha
                    Integer fichaFichaCodigo = rs.getInt("ficha_ficha_codigo");
                    if (!rs.wasNull() && fichaFichaCodigo > 0) {
                        br.com.pacto.bean.ficha.Ficha fic = new br.com.pacto.bean.ficha.Ficha();
                        fic.setCodigo(fichaFichaCodigo);
                        ficha.setFicha(fic);
                    }
                    fichasMap.put(fichaCodigo, ficha);
                }

                // Processar AtividadeAlternativa
                Integer alternativaCodigo = rs.getInt("alternativa_codigo");
                if (!rs.wasNull() && alternativaCodigo > 0 && !alternativasMap.containsKey(alternativaCodigo)) {
                    AtividadeAlternativa alternativa = new AtividadeAlternativa();
                    alternativa.setCodigo(alternativaCodigo);
                    alternativa.setAtividade(atividade);
                    // Criar objeto básico da atividade alternativa
                    Integer alternativaAtividadeCodigo = rs.getInt("alternativa_atividade_alternativa_codigo");
                    if (!rs.wasNull() && alternativaAtividadeCodigo > 0) {
                        Atividade atvAlt = new Atividade();
                        atvAlt.setCodigo(alternativaAtividadeCodigo);
                        alternativa.setAtividadeAlternativa(alternativaAtividadeCodigo);
                    }
                    alternativasMap.put(alternativaCodigo, alternativa);
                }

                // Processar AtividadeVideo
                Integer videoCodigo = rs.getInt("video_codigo");
                if (!rs.wasNull() && videoCodigo > 0 && !videosMap.containsKey(videoCodigo)) {
                    AtividadeVideo video = new AtividadeVideo();
                    video.setCodigo(videoCodigo);
                    video.setLinkvideo(rs.getString("video_link"));
                    video.setAtividade(atividade);
                    video.setProfessor(rs.getBoolean("linkvideo_professor"));
                    videosMap.put(videoCodigo, video);
                }
            }

            // Montar as listas ordenadas por código
            if (atividade != null) {
                // Converter Maps para Lists ordenadas
                atividade.setAnimacoes(new ArrayList<>(animacoesMap.values()));
                atividade.setEmpresasHabilitadas(new ArrayList<>(empresasMap.values()));
                atividade.setAparelhos(new ArrayList<>(aparelhosMap.values()));
                atividade.setMusculos(new ArrayList<>(musculosMap.values()));
                atividade.setGruposMusculares(new ArrayList<>(gruposMap.values()));
                atividade.setCategorias(new ArrayList<>(categoriasMap.values()));
                atividade.setNiveis(new ArrayList<>(niveisMap.values()));
                atividade.setFichas(new ArrayList<>(fichasMap.values()));
                atividade.setAtividadesAlternativas(new ArrayList<>(alternativasMap.values()));
                atividade.setLinkVideos(new ArrayList<>(videosMap.values()));

                // Ordenar todas as listas por código
                atividade.getAnimacoes().sort((a, b) -> a.getCodigo().compareTo(b.getCodigo()));
                atividade.getEmpresasHabilitadas().sort((a, b) -> a.getCodigo().compareTo(b.getCodigo()));
                atividade.getAparelhos().sort((a, b) -> a.getCodigo().compareTo(b.getCodigo()));
                atividade.getMusculos().sort((a, b) -> a.getCodigo().compareTo(b.getCodigo()));
                atividade.getGruposMusculares().sort((a, b) -> a.getCodigo().compareTo(b.getCodigo()));
                atividade.getCategorias().sort((a, b) -> a.getCodigo().compareTo(b.getCodigo()));
                atividade.getNiveis().sort((a, b) -> a.getCodigo().compareTo(b.getCodigo()));
                atividade.getFichas().sort((a, b) -> a.getCodigo().compareTo(b.getCodigo()));
                atividade.getAtividadesAlternativas().sort((a, b) -> a.getCodigo().compareTo(b.getCodigo()));
                atividade.getLinkVideos().sort((a, b) -> a.getCodigo().compareTo(b.getCodigo()));
            }

            return atividade;
            }
        }

    }
}

package br.com.pacto.dao.impl.turma;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.controller.json.aulaDia.AulaColetivaResponseDTO;
import br.com.pacto.controller.json.empresa.EmpresaDTO;
import br.com.pacto.controller.json.modalidade.ModalidadeTO;
import br.com.pacto.controller.json.turma.*;
import br.com.pacto.dao.intf.turma.TurmaDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.util.UteisValidacao;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Repository
public class TurmaDaoImpl implements TurmaDao {

    @Autowired
    private final ConexaoZWService conexaoZWService;

    @Autowired
    public TurmaDaoImpl(ConexaoZWService conexaoZWService) {
        this.conexaoZWService = conexaoZWService;
    }

    @Override
    public TurmaResponseDTO save(String ctx, TurmaResponseDTO turmaDTO) throws Exception {
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO TURMA (");
            sql.append("descricao, empresa, cor, identificador, modalidade, idadeMinima, idadeMinimaMeses, idadeMaxima, ");
            sql.append("idadeMaximaMeses, dataInicialVigencia, dataFinalVigencia, minutosAntecedenciaDesmarcarAula, ");
            sql.append("minutosAposInicioApp, tipoAntecedenciaMarcarAula, minutosAntecedenciaMarcarAula, qtdeNivelOcupacao, ");
            sql.append("percDescOcupacaoNivel1, percDescOcupacaoNivel2, percDescOcupacaoNivel3, percDescOcupacaoNivel4, ");
            sql.append("percDescOcupacaoNivel5, bloquearMatriculasAcimaLimite, permitirDesmarcarReposicoes, permiteAlunoOutraEmpresa, ");
            sql.append("monitorada, bloquearReposicaoAcimaLimite, permitirAulaExperimental, bloquearLotacaoFutura) ");
            sql.append(" VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )");
            sql.append(" RETURNING codigo");

        int i = 1;
        try(Connection connection = conexaoZWService.conexaoZw(ctx)){
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setString(i++, turmaDTO.getNome());
            stm.setInt(i++, turmaDTO.getEmpresa().getCodigo());
            stm.setString(i++, turmaDTO.getCor());
            stm.setString(i++, turmaDTO.getIdentificador());
            stm.setInt(i++, turmaDTO.getModalidade().getId());
            stm.setInt(i++, turmaDTO.getIdadeMinima());
            stm.setInt(i++, turmaDTO.getIdadeMinimaMeses());
            stm.setInt(i++, turmaDTO.getIdadeMaxima());
            stm.setInt(i++, turmaDTO.getIdadeMaximaMeses());
            stm.setDate(i++, new Date(Long.parseLong(turmaDTO.getDataInicial())));
            stm.setDate(i++, new Date(Long.parseLong(turmaDTO.getDataFinal())));
            stm.setInt(i++, turmaDTO.getMinutosAntecedenciaDesmarcarAula());
            stm.setInt(i++, turmaDTO.getMinutosAposInicioApp());
            stm.setInt(i++, turmaDTO.getTipoAntecedenciaMarcarAula());
            stm.setInt(i++, turmaDTO.getMinutosAntecedenciaMarcarAula());
            stm.setInt(i++, turmaDTO.getQtdeNivelOcupacao());
            stm.setDouble(i++, turmaDTO.getPercDescOcupacaoNivel1() != null ? turmaDTO.getPercDescOcupacaoNivel1() : 0);
            stm.setDouble(i++, turmaDTO.getPercDescOcupacaoNivel2() != null ? turmaDTO.getPercDescOcupacaoNivel2() : 0);
            stm.setDouble(i++, turmaDTO.getPercDescOcupacaoNivel3() != null ? turmaDTO.getPercDescOcupacaoNivel3() : 0);
            stm.setDouble(i++, turmaDTO.getPercDescOcupacaoNivel4() != null ? turmaDTO.getPercDescOcupacaoNivel4() : 0);
            stm.setDouble(i++, turmaDTO.getPercDescOcupacaoNivel5() != null ? turmaDTO.getPercDescOcupacaoNivel5() : 0);
            stm.setBoolean(i++, turmaDTO.isBloquearMatriculasAcimaLimite());
            stm.setBoolean(i++, turmaDTO.isPermitirDesmarcarReposicoes());
            stm.setBoolean(i++, turmaDTO.isPermiteAlunoOutraEmpresa());
            stm.setBoolean(i++, turmaDTO.isMonitorada());
            stm.setBoolean(i++, turmaDTO.isBloquearReposicaoAcimaLimite());
            stm.setBoolean(i++, turmaDTO.isPermitirAulaExperimental());
            stm.setBoolean(i++, turmaDTO.isBloquearLotacaoFutura());
            try (ResultSet resultSet = stm.executeQuery()) {
                if (resultSet.next()) {
                    turmaDTO.setId(resultSet.getInt("codigo"));
                }
            }
            connection.close();
            return turmaDTO;
        }

    }

    @Override
    public AulaColetivaResponseDTO saveAulaColetiva(String ctx, AulaColetivaResponseDTO aulaDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO TURMA (");
        sql.append("aulacoletiva, descricao, identificador, cor, empresa, meta, pontosbonus, bonificacao, mensagem, ocupacao, modalidade, ");
        sql.append("datainicialvigencia, datafinalvigencia, tolerancia, tipotolerancia, validarRestricoesMarcacao, ");
        sql.append("naoValidarModalidadeContrato, produtoGymPass, idClasseGymPass, urlTurmaVirtual, permiteFixar, ");
        sql.append("aulaIntegracaoSelfloops, visualizarProdutosGympass, visualizarProdutosTotalpass, niveis, idademinima, ");
        sql.append("idademinimameses, idademaxima, idademaximameses,  tipoReservaEquipamento, mapaEquipamentos, bloquearmatriculasacimalimite ");
        sql.append(") ");
        sql.append(" VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )");
        sql.append(" RETURNING codigo");

        int i = 1;
        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setBoolean(i++, true);
            stm.setString(i++, aulaDTO.getDescricao());
            stm.setString(i++, aulaDTO.getIdentificador());
            stm.setString(i++, aulaDTO.getCor());
            stm.setInt(i++, aulaDTO.getEmpresa());
            stm.setDouble(i++, aulaDTO.getMeta() != null ? aulaDTO.getMeta() : Types.NULL);
            stm.setInt(i++, aulaDTO.getPontuacaoBonus() != null ? aulaDTO.getPontuacaoBonus() : Types.NULL);
            stm.setDouble(i++, aulaDTO.getBonificacao() != null ? aulaDTO.getBonificacao() : Types.NULL);
            stm.setString(i++, aulaDTO.getMensagem());
            stm.setInt(i++, aulaDTO.getOcupacao() != null ? aulaDTO.getOcupacao() : Types.NULL);
            stm.setInt(i++, aulaDTO.getModalidadeId());
            stm.setDate(i++, new Date(aulaDTO.getDataInicio()));
            stm.setDate(i++, new Date(aulaDTO.getDataFinal()));
            stm.setInt(i++, aulaDTO.getToleranciaMin());
            stm.setInt(i++, aulaDTO.getTipoTolerancia());
            stm.setBoolean(i++, aulaDTO.getValidarRestricoesMarcacao());
            stm.setBoolean(i++, aulaDTO.getNaoValidarModalidadeContrato());
            stm.setInt(i++, aulaDTO.getProdutoGymPass() != null ? aulaDTO.getProdutoGymPass() : Types.NULL);
            stm.setInt(i++, aulaDTO.getIdClasseGymPass() != null ? aulaDTO.getIdClasseGymPass() : Types.NULL);
            stm.setString(i++, aulaDTO.getUrlTurmaVirtual());
            stm.setBoolean(i++, aulaDTO.getPermiteFixar());
            stm.setBoolean(i++, aulaDTO.getAulaIntegracaoSelfloops());
            stm.setBoolean(i++, aulaDTO.getVisualizarProdutosGympass());
            stm.setBoolean(i++, aulaDTO.getVisualizarProdutosTotalpass());

            String idsNivel = "";
            if (aulaDTO.getNiveis() != null && !aulaDTO.getNiveis().isEmpty()) {
                for (NivelTO n : aulaDTO.getNiveis()) {
                    idsNivel += "," + n.getId();
                }
                idsNivel = idsNivel.replaceFirst(",", "");
            }
            stm.setString(i++, idsNivel);

            stm.setInt(i++, aulaDTO.getIdadeMinimaAnos() != null ? aulaDTO.getIdadeMinimaAnos() : 0);
            stm.setInt(i++, aulaDTO.getIdadeMinimaMeses() != null ? aulaDTO.getIdadeMinimaMeses() : 0);
            stm.setInt(i++, aulaDTO.getIdadeMaximaAnos() != null ? aulaDTO.getIdadeMaximaAnos() : 0);
            stm.setInt(i++, aulaDTO.getIdadeMaximaMeses() != null ? aulaDTO.getIdadeMaximaMeses() : 0);
            stm.setString(i++, aulaDTO.getTipoReservaEquipamento());
            stm.setString(i++, aulaDTO.getMapaEquipamentos());
            stm.setBoolean(i++, false); // bloquearmatriculasacimalimite

            try (ResultSet resultSet = stm.executeQuery()) {
                if (resultSet.next()) {
                    aulaDTO.setCodigo(resultSet.getInt("codigo"));
                }
            }
            connection.close();
            return aulaDTO;
        }

    }

    @Override
    public AulaColetivaResponseDTO updateAulaColetiva(String ctx, AulaColetivaResponseDTO aulaDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE TURMA  SET ");
        sql.append("descricao = ?, identificador = ?, cor = ?, empresa = ?, meta = ?, pontosbonus = ?, bonificacao = ?, mensagem = ?, ");
        sql.append("ocupacao = ?, modalidade = ?, datainicialvigencia = ?, datafinalvigencia = ?, tolerancia = ?, tipotolerancia = ?, ");
        sql.append("validarrestricoesmarcacao = ?, naovalidarmodalidadecontrato = ?, produtogympass = ?, idclassegympass = ?, urlturmavirtual = ?, ");
        sql.append("permitefixar = ?, aulaintegracaoselfloops = ?, visualizarprodutosgympass = ?, visualizarprodutostotalpass = ?, ");
        sql.append("niveis = ?, idademinima = ?, idademinimameses = ?, idademaxima = ?, idademaximameses = ?, tiporeservaequipamento = ?, ");
        sql.append("mapaequipamentos = ? ");
        sql.append("WHERE codigo = ? ");

        int i = 1;
        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setString(i++, aulaDTO.getDescricao());
            stm.setString(i++, aulaDTO.getIdentificador());
            stm.setString(i++, aulaDTO.getCor());
            stm.setInt(i++, aulaDTO.getEmpresa());
            stm.setDouble(i++, aulaDTO.getMeta() != null ? aulaDTO.getMeta() : Types.NULL);
            stm.setInt(i++, aulaDTO.getPontuacaoBonus() != null ? aulaDTO.getPontuacaoBonus() : Types.NULL);
            stm.setDouble(i++, aulaDTO.getBonificacao() != null ? aulaDTO.getBonificacao() : Types.NULL);
            stm.setString(i++, aulaDTO.getMensagem());
            stm.setInt(i++, aulaDTO.getOcupacao() != null ? aulaDTO.getOcupacao() : Types.NULL);
            stm.setInt(i++, aulaDTO.getModalidadeId());
            stm.setDate(i++, new Date(aulaDTO.getDataInicio()));
            stm.setDate(i++, new Date(aulaDTO.getDataFinal()));
            stm.setInt(i++, aulaDTO.getToleranciaMin());
            stm.setInt(i++, aulaDTO.getTipoTolerancia());
            stm.setBoolean(i++, aulaDTO.getValidarRestricoesMarcacao());
            stm.setBoolean(i++, aulaDTO.getNaoValidarModalidadeContrato());
            stm.setInt(i++, aulaDTO.getProdutoGymPass() != null ? aulaDTO.getProdutoGymPass() : Types.NULL);
            stm.setInt(i++, aulaDTO.getIdClasseGymPass() != null ? aulaDTO.getIdClasseGymPass() : Types.NULL);
            stm.setString(i++, aulaDTO.getUrlTurmaVirtual());
            stm.setBoolean(i++, aulaDTO.getPermiteFixar());
            stm.setBoolean(i++, aulaDTO.getAulaIntegracaoSelfloops());
            stm.setBoolean(i++, aulaDTO.getVisualizarProdutosGympass());
            stm.setBoolean(i++, aulaDTO.getVisualizarProdutosTotalpass());

            String idsNivel = "";
            if (aulaDTO.getNiveis() != null && !aulaDTO.getNiveis().isEmpty()) {
                for (NivelTO n : aulaDTO.getNiveis()) {
                    idsNivel += "," + n.getId();
                }
                idsNivel = idsNivel.replaceFirst(",", "");
            }
            stm.setString(i++, idsNivel);

            stm.setInt(i++, aulaDTO.getIdadeMinimaAnos() != null ? aulaDTO.getIdadeMinimaAnos() : 0);
            stm.setInt(i++, aulaDTO.getIdadeMinimaMeses() != null ? aulaDTO.getIdadeMinimaMeses() : 0);
            stm.setInt(i++, aulaDTO.getIdadeMaximaAnos() != null ? aulaDTO.getIdadeMaximaAnos() : 0);
            stm.setInt(i++, aulaDTO.getIdadeMaximaMeses() != null ? aulaDTO.getIdadeMaximaMeses() : 0);
            stm.setString(i++, aulaDTO.getTipoReservaEquipamento());
            stm.setString(i++, aulaDTO.getMapaEquipamentos());
            stm.setInt(i++, aulaDTO.getCodigo());

            stm.execute();
        }
        return aulaDTO;
    }

    @Override
    public TurmaResponseDTO update(String ctx, TurmaResponseDTO turmaDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE TURMA  SET ");
        sql.append("descricao = ?, empresa = ?, cor = ?, identificador = ?, modalidade = ?, idadeMinima = ?, idadeMinimaMeses = ?, idadeMaxima = ?, ");
        sql.append("idadeMaximaMeses = ?, dataInicialVigencia = ?, dataFinalVigencia = ?, minutosAntecedenciaDesmarcarAula = ?, ");
        sql.append("minutosAposInicioApp = ?, tipoAntecedenciaMarcarAula = ?, minutosAntecedenciaMarcarAula = ?, qtdeNivelOcupacao = ?, ");
        sql.append("percDescOcupacaoNivel1 = ?, percDescOcupacaoNivel2 = ?, percDescOcupacaoNivel3 = ?, percDescOcupacaoNivel4 = ?, ");
        sql.append("percDescOcupacaoNivel5 = ?, bloquearMatriculasAcimaLimite = ?, permitirDesmarcarReposicoes = ?, permiteAlunoOutraEmpresa = ?, ");
        sql.append("monitorada = ?, bloquearReposicaoAcimaLimite = ?, permitirAulaExperimental = ?, bloquearLotacaoFutura = ?, ");
        sql.append("urlvideoyoutube = ? ");
        sql.append(" WHERE codigo = ? ");

        int i = 1;
        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setString(i++, turmaDTO.getNome());
            stm.setInt(i++, turmaDTO.getEmpresa().getCodigo());
            stm.setString(i++, turmaDTO.getCor());
            stm.setString(i++, turmaDTO.getIdentificador());
            stm.setInt(i++, turmaDTO.getModalidade().getId());
            stm.setInt(i++, turmaDTO.getIdadeMinima());
            stm.setInt(i++, turmaDTO.getIdadeMinimaMeses());
            stm.setInt(i++, turmaDTO.getIdadeMaxima());
            stm.setInt(i++, turmaDTO.getIdadeMaximaMeses());
            stm.setDate(i++, new Date(Long.parseLong(turmaDTO.getDataInicial())));
            stm.setDate(i++, new Date(Long.parseLong(turmaDTO.getDataFinal())));
            stm.setInt(i++, turmaDTO.getMinutosAntecedenciaDesmarcarAula());
            stm.setInt(i++, turmaDTO.getMinutosAposInicioApp());
            stm.setInt(i++, turmaDTO.getTipoAntecedenciaMarcarAula());
            stm.setInt(i++, turmaDTO.getMinutosAntecedenciaMarcarAula());
            stm.setInt(i++, turmaDTO.getQtdeNivelOcupacao());
            stm.setDouble(i++, turmaDTO.getPercDescOcupacaoNivel1() != null ? turmaDTO.getPercDescOcupacaoNivel1() : 0);
            stm.setDouble(i++, turmaDTO.getPercDescOcupacaoNivel2() != null ? turmaDTO.getPercDescOcupacaoNivel2() : 0);
            stm.setDouble(i++, turmaDTO.getPercDescOcupacaoNivel3() != null ? turmaDTO.getPercDescOcupacaoNivel3() : 0);
            stm.setDouble(i++, turmaDTO.getPercDescOcupacaoNivel4() != null ? turmaDTO.getPercDescOcupacaoNivel4() : 0);
            stm.setDouble(i++, turmaDTO.getPercDescOcupacaoNivel5() != null ? turmaDTO.getPercDescOcupacaoNivel5() : 0);
            stm.setBoolean(i++, turmaDTO.isBloquearMatriculasAcimaLimite());
            stm.setBoolean(i++, turmaDTO.isPermitirDesmarcarReposicoes());
            stm.setBoolean(i++, turmaDTO.isPermiteAlunoOutraEmpresa());
            stm.setBoolean(i++, turmaDTO.isMonitorada());
            stm.setBoolean(i++, turmaDTO.isBloquearReposicaoAcimaLimite());
            stm.setBoolean(i++, turmaDTO.isPermitirAulaExperimental());
            stm.setBoolean(i++, turmaDTO.isBloquearLotacaoFutura());
            stm.setString(i++, turmaDTO.getUrlVideoYoutube());
            stm.setInt(i++, turmaDTO.getId());


            stm.execute();
        }
        return turmaDTO;
    }

    @Override
    public void updateFotoKey(String ctx, Integer codigoTurma, String fotoKey) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE TURMA  SET fotokey = ? WHERE codigo = ? ");

        int i = 1;
        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());
            stm.setString(i++, fotoKey);
            stm.setInt(i++, codigoTurma);
            stm.execute();
        }
    }

    @Override
    public HorarioTurmaResponseDTO saveHorario(String ctx, HorarioTurmaResponseDTO horarioDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO HORARIOTURMA (");
        sql.append("nrmaximoaluno, identificadorturma, situacao, diasemana, diasemananumero, nivelturma, ambiente, professor, horainicial, ");
        sql.append("horafinal, turma, toleranciaentradaminutos, toleranciaentradaaposminutos, liberadomarcacaoapp, ");
        sql.append("ativo, dataentrouturma, limitevagasagregados, qtdeMaximaAlunoExperimental) ");
        sql.append(" VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )");
        sql.append(" RETURNING codigo");

        int i = 1;
        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setInt(i++, horarioDTO.getMaxAlunos());
            stm.setString(i++, horarioDTO.getIdentificadorTurma());
            stm.setString(i++, horarioDTO.getSituacao());
            stm.setString(i++, horarioDTO.getDia());
            stm.setInt(i++, horarioDTO.getDiaSemanaNumero() != null ? horarioDTO.getDiaSemanaNumero() : Types.NULL);
            stm.setInt(i++, horarioDTO.getNivelTurmaId());
            stm.setInt(i++, horarioDTO.getAmbienteId());
            stm.setInt(i++, horarioDTO.getProfessorId());
            stm.setString(i++, horarioDTO.getHoraInicial());
            stm.setString(i++, horarioDTO.getHoraFinal());
            stm.setInt(i++, horarioDTO.getTurma());
            stm.setInt(i++, horarioDTO.getToleranciaMin());
            stm.setInt(i++, horarioDTO.getToleranciaAposMin() != null ? horarioDTO.getToleranciaAposMin() : 0);
            stm.setBoolean(i++, horarioDTO.getLiberadoMarcacaoApp());
            stm.setBoolean(i++, horarioDTO.getHorarioDisponivelVenda());
            stm.setDate(i++, Uteis.getDataJDBC(Calendario.hoje()));
            stm.setInt(i++, horarioDTO.getLimiteVagasAgregados() != null ? horarioDTO.getLimiteVagasAgregados() : 0);
            stm.setInt(i++, horarioDTO.getQtdeMaximaAlunoExperimental() != null ? horarioDTO.getQtdeMaximaAlunoExperimental() : 0);

            try (ResultSet resultSet = stm.executeQuery()) {
                if (resultSet.next()) {
                    horarioDTO.setCodigo(resultSet.getInt("codigo"));
                }
            }
        }
        return horarioDTO;
    }

    @Override
    public void saveHorarioCapacidadeCategoria(String ctx, HorarioTurmaResponseDTO horarioDTO) throws Exception {
        if (!UteisValidacao.emptyNumber(horarioDTO.getCodigo())) {
            excluirHorarioCapacidadeCategoria(ctx, horarioDTO.getCodigo());

            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO HORARIOCAPACIDADECATEGORIA (");
            sql.append("horarioturma, tipoCategoria, capacidade) ");
            sql.append(" VALUES ( ?, ?, ?)");
            sql.append(" RETURNING codigo");

            if (!UteisValidacao.emptyList(horarioDTO.getHorarioCapacidadeCategoria())) {
                try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
                    horarioDTO.getHorarioCapacidadeCategoria().forEach(hCC -> {
                        try {
                            int i = 1;
                            PreparedStatement stm = connection.prepareStatement(sql.toString());

                            stm.setInt(i++, horarioDTO.getCodigo());
                            stm.setString(i++, hCC.getTipoCategoria());
                            stm.setInt(i++, hCC.getCapacidade());

                            try (ResultSet resultSet = stm.executeQuery()) {
                                if (resultSet.next()) {
                                    hCC.setCodigo(resultSet.getInt("codigo"));
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    });
                }
            }
        }
    }

    @Override
    public HorarioTurmaResponseDTO updateHorario(String ctx, HorarioTurmaResponseDTO horarioDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE HORARIOTURMA SET ");
        sql.append("nrmaximoaluno = ?, identificadorturma = ?, situacao = ?, diasemana = ?, nivelturma = ?, ambiente = ?, professor = ?, horainicial = ?, ");
        sql.append("horafinal = ?, turma = ?, toleranciaentradaminutos = ?, toleranciaentradaaposminutos = ?, liberadomarcacaoapp = ?, ");
        sql.append("ativo = ?, dataentrouturma = ?, datasaiuturma = ? ");
        sql.append(" WHERE codigo = ?");

        int i = 1;
        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setInt(i++, horarioDTO.getMaxAlunos());
            stm.setString(i++, horarioDTO.getIdentificadorTurma());
            stm.setString(i++, horarioDTO.getSituacao());
            stm.setString(i++, horarioDTO.getDia());
            stm.setInt(i++, horarioDTO.getNivelTurmaId());
            stm.setInt(i++, horarioDTO.getAmbienteId());
            stm.setInt(i++, horarioDTO.getProfessorId());
            stm.setString(i++, horarioDTO.getHoraInicial());
            stm.setString(i++, horarioDTO.getHoraFinal());
            stm.setInt(i++, horarioDTO.getTurma());
            stm.setInt(i++, horarioDTO.getToleranciaMin());
            stm.setInt(i++, horarioDTO.getToleranciaAposMin());
            stm.setBoolean(i++, horarioDTO.getLiberadoMarcacaoApp());
            stm.setBoolean(i++, horarioDTO.getHorarioDisponivelVenda());
            if (isNotBlank(horarioDTO.getDataEntrouTurma())) {
                stm.setDate(i++, Uteis.getDataJDBC(Calendario.getDate("MM/dd/yyyy HH:mm:ss", horarioDTO.getDataEntrouTurma())));
            } else {
                stm.setDate(i++, null);
            }
            if (Objects.equals(horarioDTO.getSituacao(), "AT")) {
                stm.setDate(i++, null);
            } else {
                stm.setDate(i++, Uteis.getDataJDBC(Calendario.getDate("MM/dd/yyyy HH:mm:ss", horarioDTO.getDataSaiuTurma())));
            }

            stm.setInt(i++, horarioDTO.getCodigo());

            stm.execute();
        }
        return horarioDTO;
    }

    @Override
    public HorarioTurmaResponseDTO updateHorarioAulaColetiva(String ctx, HorarioTurmaResponseDTO horarioDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE HORARIOTURMA SET ");
        sql.append("nrmaximoaluno = ?, identificadorturma = ?, situacao = ?, diasemana = ?, nivelturma = ?, ambiente = ?, professor = ?, horainicial = ?, ");
        sql.append("horafinal = ?, turma = ?, toleranciaentradaminutos = ?, toleranciaentradaaposminutos = ?, liberadomarcacaoapp = ?, ");
        sql.append("ativo = ?, dataentrouturma = ?, datasaiuturma = ?, limitevagasagregados = ?, qtdeMaximaAlunoExperimental = ? ");
        sql.append(" WHERE codigo = ?");

        int i = 1;
        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setInt(i++, horarioDTO.getMaxAlunos());
            stm.setString(i++, horarioDTO.getIdentificadorTurma());
            stm.setString(i++, horarioDTO.getSituacao());
            stm.setString(i++, horarioDTO.getDia());
            stm.setInt(i++, horarioDTO.getNivelTurmaId());
            stm.setInt(i++, horarioDTO.getAmbienteId());
            stm.setInt(i++, horarioDTO.getProfessorId());
            stm.setString(i++, horarioDTO.getHoraInicial());
            stm.setString(i++, horarioDTO.getHoraFinal());
            stm.setInt(i++, horarioDTO.getTurma());
            stm.setInt(i++, horarioDTO.getToleranciaMin());
            stm.setInt(i++, horarioDTO.getToleranciaAposMin());
            stm.setBoolean(i++, horarioDTO.getLiberadoMarcacaoApp());
            stm.setBoolean(i++, horarioDTO.getHorarioDisponivelVenda());
            if (isNotBlank(horarioDTO.getDataEntrouTurma())) {
                stm.setDate(i++, Uteis.getDataJDBC(Calendario.getDate("dd/MM/yyyy HH:mm:ss", horarioDTO.getDataEntrouTurma())));
            } else {
                stm.setDate(i++, null);
            }
            if (Objects.equals(horarioDTO.getSituacao(), "AT")) {
                stm.setDate(i++, null);
            } else {
                stm.setDate(i++, Uteis.getDataJDBC(Calendario.getDate("dd/MM/yyyy HH:mm:ss", horarioDTO.getDataSaiuTurma())));
            }
            stm.setInt(i++, horarioDTO.getLimiteVagasAgregados() != null ? horarioDTO.getLimiteVagasAgregados() : 0);
            stm.setInt(i++, horarioDTO.getQtdeMaximaAlunoExperimental() != null ? horarioDTO.getQtdeMaximaAlunoExperimental() : 0);

            stm.setInt(i++, horarioDTO.getCodigo());

            stm.execute();
        }
        return horarioDTO;
    }

    @Override
    public AmbienteDTO saveAmbiente(String ctx, AmbienteDTO ambienteDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO AMBIENTE (");
        sql.append("descricao, tipoAmbiente, capacidadeMaximaConvidados, situacao, capacidade, tipoModulo, coletor )");
        sql.append(" VALUES ( ?, ?, ?, ?, ?, ?, ? )");
        sql.append(" RETURNING codigo");

        int i = 1;
        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setString(i++, ambienteDTO.getNome());
            stm.setInt(i++, ambienteDTO.getTipoAmbiente());
            stm.setInt(i++, ambienteDTO.getCapacidadeMaximaConvidados() == null ? 0 : ambienteDTO.getCapacidadeMaximaConvidados());
            stm.setInt(i++, ambienteDTO.getSituacao());
            stm.setInt(i++, ambienteDTO.getCapacidade() == null ? 0 : ambienteDTO.getCapacidade());
            stm.setString(i++, ambienteDTO.getTipoModulo());
            if (!UteisValidacao.emptyNumber(ambienteDTO.getColetor())) {
                stm.setInt(i++, ambienteDTO.getColetor());
            } else {
                stm.setNull(i++, 0);
            }

            try (ResultSet resultSet = stm.executeQuery()) {
                if (resultSet.next()) {
                    ambienteDTO.setId(resultSet.getInt("codigo"));
                }
            }
        }
        return ambienteDTO;
    }

    public Boolean existsNivelTurmaByDescricao(String ctx, String descricao) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(" SELECT nT.codigo FROM NivelTurma nT where nT.descricao ilike '"
                    + descricao + "'", conZW)) {
                return rs.next();
            }
        }
    }

    public Boolean existsAmbienteByDescricao(String ctx, String descricao) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(" SELECT a.codigo FROM Ambiente a where a.descricao ilike '" +
                    descricao + "'", conZW)) {
                return rs.next();
            }
        }
    }

    @Override
    public NivelTurmaDTO saveNivelTurma(String ctx, NivelTurmaDTO nivelTurmaDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO NIVELTURMA (");
        sql.append("descricao, codigoMgb )");
        sql.append(" VALUES ( ?, ? )");
        sql.append(" RETURNING codigo");

        int i = 1;
        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setString(i++, nivelTurmaDTO.getNome());
            stm.setString(i++, nivelTurmaDTO.getCodigoMgb());

            try (ResultSet resultSet = stm.executeQuery()) {
                if (resultSet.next()) {
                    nivelTurmaDTO.setId(resultSet.getInt("codigo"));
                }
            }
        }
        return nivelTurmaDTO;
    }

    @Override
    public List<TurmaResponseDTO> obterTurmas(String ctx, Integer codTurmaEdit, Integer empresa, PaginadorDTO paginadorDTO, JSONObject filtros) throws Exception {
        filtros = codTurmaEdit == null ? filtros : new JSONObject().put("codigo", codTurmaEdit);
        String orderBy = "";
        boolean orderByDesc = false;
        if(!UteisValidacao.emptyString(paginadorDTO.getSort())){
            String[] split = paginadorDTO.getSort().split(",");
            orderBy = split[0];
            orderByDesc = split[1].equals("DESC");
        }
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {

            StringBuilder sql = retornaSelectConsultaBasica();
            sql.append(retornaFromConsultaBasica(empresa, filtros));

            if (!UteisValidacao.emptyString(orderBy)) {
                sql.append(" order by  ").append(getSqlOrderBy(orderBy));
                sql.append(orderByDesc ? " desc" : " asc");
            }
            int limit = (paginadorDTO.getSize() == null ? 10000 : Integer.parseInt(paginadorDTO.getSize().toString()));
            sql.append(" LIMIT ").append(limit);
            sql.append(" OFFSET ").append((paginadorDTO.getPage() == null ? 0 : Integer.parseInt(paginadorDTO.getPage().toString())) * limit);

            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(" SELECT COUNT(*) " +
                    retornaFromConsultaBasica(empresa, filtros), conZW)) {
                if (rs.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rs.getLong("count"));
                }
            }
            try (ResultSet rsC = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {

                return montarListaTurmasAula(rsC);
            }

        }
    }

    @Override
    public AulaColetivaResponseDTO obterAulaColetiva(String ctx, Integer codigo) throws Exception {
        AulaColetivaResponseDTO aulaColetivaResponseDTO = null;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t.codigo, t.descricao, t.cor, t.fotokey, t.identificador, t.empresa, t.meta, t.pontosbonus, t.bonificacao, t.mensagem, ");
        sql.append("t.ocupacao, t.modalidade, t.datainicialvigencia, t.datafinalvigencia, t.tolerancia, t.tipotolerancia, ");
        sql.append("t.validarRestricoesMarcacao, t.naoValidarModalidadeContrato, t.produtoGymPass, t.idClasseGymPass, ");
        sql.append("t.urlTurmaVirtual, t.permiteFixar, t.aulaIntegracaoSelfloops, t.visualizarProdutosGympass, ");
        sql.append("t.visualizarProdutosTotalpass, t.niveis, t.idademaxima, t.idademaximameses, t.idademinima, ");
        sql.append("t.idademinimameses, t.tipoReservaEquipamento, t.mapaEquipamentos ");
        sql.append("FROM turma t ");
        sql.append("WHERE t.codigo = ").append(codigo);

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                if (rs.next()) {
                    aulaColetivaResponseDTO = montarDadosAulaColetiva(rs);
                }
            }
        }

        return aulaColetivaResponseDTO;
    }

    @Override
    public TurmaResponseDTO obterTurma(String ctx, Integer codigo) throws Exception {
        TurmaResponseDTO turmaResponseDTO = null;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t.codigo as codigoTurma, t.descricao as nomeTurma, ");
        sql.append("t.cor, m.codigo as codigoModalidade, m.nome as nomeModalidade, ");
        sql.append("t.identificador, t.idademinima, t.idademinimameses, t.idademaxima, ");
        sql.append("t.idademaximameses, t.integracaospivi, t.datainicialvigencia, t.datafinalvigencia, ");
        sql.append("t.minutosAntecedenciaDesmarcarAula, t.minutosAposInicioApp, t.tipoAntecedenciaMarcarAula, ");
        sql.append("t.minutosAntecedenciaMarcarAula, t.qtdeNivelOcupacao, t.percDescOcupacaoNivel1, ");
        sql.append("t.percDescOcupacaoNivel2, t.percDescOcupacaoNivel3, t.percDescOcupacaoNivel4, ");
        sql.append("t.percDescOcupacaoNivel5, t.bloquearMatriculasAcimaLimite, t.permitirDesmarcarReposicoes, ");
        sql.append("t.permiteAlunoOutraEmpresa, t.monitorada, t.bloquearReposicaoAcimaLimite, ");
        sql.append("t.permitirAulaExperimental, t.bloquearLotacaoFutura, ");
        sql.append("t.urlvideoyoutube ");
        sql.append("FROM turma t ");
        sql.append("inner join modalidade m on m.codigo = t.modalidade ");
        sql.append("where t.codigo = ").append(codigo);
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                if (rs.next()) {
                    turmaResponseDTO = montarDadosTurma(rs);
                }
            }
        }
        return turmaResponseDTO;
    }

    @Override
    public HorarioTurmaResponseDTO obterHorarioTurma(String ctx, Integer codigo) throws Exception {
        HorarioTurmaResponseDTO horarioTurmaDTO = null;
        StringBuilder sql = new StringBuilder();
        sql.append("select ht.codigo, ht.turma, p.nome as professor, c.codigo as professorId, a.descricao as ambiente, a.codigo as ambienteId, ");
        sql.append("concat(concat(ht.horainicial, ' às '), ht.horafinal) as horarioTurma, ");
        sql.append("ht.horainicial, ht.horafinal, ht.diasemana as dia, ht.nrmaximoaluno as maxAlunos, ht.toleranciaentradaminutos, ht.toleranciaentradaaposminutos, ");
        sql.append("nt.descricao as nivelTurma, nt.codigo as nivelTurmaId, ");
        sql.append("ht.liberadoMarcacaoApp, ht.ativo as horarioDisponivelVenda, ht.dataEntrouTurma, ht.dataSaiuTurma, ht.limitevagasagregados, ht.qtdemaximaalunoexperimental ");
        sql.append("from horarioturma ht ");
        sql.append("inner join colaborador c on c.codigo = ht.professor ");
        sql.append("inner join pessoa p on p.codigo = c.pessoa ");
        sql.append("inner join ambiente a on a.codigo = ht.ambiente ");
        sql.append("inner join nivelturma nt on nt.codigo = ht.nivelturma ");
        sql.append("where ht.codigo = ").append(codigo);
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                if (rs.next()) {
                    horarioTurmaDTO = montarDadosHorarioTurma(ctx, rs);
                }
            }
        }
        return horarioTurmaDTO;
    }

    @Override
    public List<HorarioTurmaResponseDTO> listarTodosHorariosAtivosAulaColetiva(String ctx, Integer codigoAulaColetiva) throws Exception {
        List<HorarioTurmaResponseDTO> horarioTurmaResponseDTO = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select ht.codigo, ht.turma, p.nome as professor, c.codigo as professorId, a.descricao as ambiente, a.codigo as ambienteId, ");
        sql.append("concat(concat(ht.horainicial, ' às '), ht.horafinal) as horarioTurma, ");
        sql.append("ht.horainicial, ht.horafinal, ht.diasemana, ht.nrmaximoaluno, ht.toleranciaentradaminutos, ht.toleranciaentradaaposminutos, ");
        sql.append("nt.descricao as nivelTurma, nt.codigo as nivelTurmaId, ");
        sql.append("ht.liberadoMarcacaoApp, ht.ativo as horarioDisponivelVenda, ht.dataEntrouTurma, ht.dataSaiuTurma, ht.limitevagasagregados ");
        sql.append("from horarioturma ht ");
        sql.append("inner join colaborador c on c.codigo = ht.professor ");
        sql.append("inner join pessoa p on p.codigo = c.pessoa ");
        sql.append("inner join ambiente a on a.codigo = ht.ambiente ");
        sql.append("inner join nivelturma nt on nt.codigo = ht.nivelturma ");
        sql.append("where ht.turma = ").append(codigoAulaColetiva);
        sql.append("and ht.datasaiuturma is null");
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rsC = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                while (rsC.next()) {
                    horarioTurmaResponseDTO.add(montarDadosHorarioTurma(ctx, rsC));
                }
            }
        }
        return horarioTurmaResponseDTO;
    }

    @Override
    public List<HorarioTurmaResponseDTO> listarHorariosTurma(String ctx, JSONObject filtros, PaginadorDTO paginadorDTO, Integer turma) throws Exception {
        if (StringUtils.isBlank(paginadorDTO.getSort())) {
            paginadorDTO.setSort("professor,ASC");
        }

        String orderBy = "";
        boolean orderByDesc = false;
        if (!UteisValidacao.emptyString(paginadorDTO.getSort())) {
            String[] split = paginadorDTO.getSort().split(",");
            orderBy = split[0];
            orderByDesc = split[1].equals("DESC");
        }


        List<HorarioTurmaResponseDTO> horarioTurmaResponseDTO = new ArrayList<>();
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(montarConsultaHorarioTurma(filtros, paginadorDTO, turma, orderBy, orderByDesc, true).toString(), conZW)) {
                if (rs.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rs.getLong("count"));
                }
            }
            try (ResultSet rsC = ConexaoZWServiceImpl.criarConsulta(montarConsultaHorarioTurma(filtros, paginadorDTO, turma, orderBy, orderByDesc, false).toString(), conZW)) {
                while (rsC.next()) {
                    horarioTurmaResponseDTO.add(montarDadosHorarioTurma(ctx, rsC));
                }
            }
        }
        return horarioTurmaResponseDTO;
    }

    private static StringBuilder montarConsultaHorarioTurma(JSONObject filtros, PaginadorDTO paginadorDTO, Integer turma, String orderBy, boolean orderByDesc, boolean contar) {
        StringBuilder sql = new StringBuilder();
        if (contar) {
            sql.append("select count(*) ");
        } else {
            sql.append("select ht.codigo, ht.turma, p.nome as professor, c.codigo as professorId, a.descricao as ambiente, a.codigo as ambienteId, ");
            sql.append("concat(concat(ht.horainicial, ' às '), ht.horafinal) as horarioTurma, ");
            sql.append("ht.horainicial, ht.horafinal, ht.diasemana as dia, ht.nrmaximoaluno as maxAlunos, ht.toleranciaentradaminutos, ht.toleranciaentradaaposminutos, ");
            sql.append("nt.descricao as nivelTurma, nt.codigo as nivelTurmaId, ");
            sql.append("ht.liberadoMarcacaoApp, ht.ativo as horarioDisponivelVenda, ht.dataEntrouTurma, ht.dataSaiuTurma, ht.limitevagasagregados, ht.qtdeMaximaAlunoExperimental ");
        }
        sql.append("from horarioturma ht ");
        sql.append("inner join colaborador c on c.codigo = ht.professor ");
        sql.append("inner join pessoa p on p.codigo = c.pessoa ");
        sql.append("inner join ambiente a on a.codigo = ht.ambiente ");
        sql.append("inner join nivelturma nt on nt.codigo = ht.nivelturma ");
        sql.append("where ht.turma = ").append(turma);

        if (filtros.has("professor") && filtros.getJSONArray("professor").length() > 0) {
            sql.append(" AND c.codigo in ( \n");
            sql.append(Uteis.arrayJsonToString(filtros.getJSONArray("professor"), ","));
            sql.append(" ) \n");
        }

        if (filtros.has("ambiente") && filtros.getJSONArray("ambiente").length() > 0) {
            sql.append(" AND a.codigo in ( \n");
            sql.append(Uteis.arrayJsonToString(filtros.getJSONArray("ambiente"), ","));
            sql.append(" ) \n");
        }

        if (filtros.has("nivelTurma") && filtros.getJSONArray("nivelTurma").length() > 0) {
            sql.append(" AND nt.codigo in ( \n");
            sql.append(Uteis.arrayJsonToString(filtros.getJSONArray("nivelTurma"), ","));
            sql.append(" ) \n");
        }

        if (filtros.has("dias") && filtros.getJSONArray("dias").length() > 0) {
            sql.append(" AND ht.diasemana in ( \n'");
            sql.append(Uteis.arrayJsonToString(filtros.getJSONArray("dias"), "','"));
            sql.append("' ) \n");
        }

        if(filtros.has("situacao") && filtros.getJSONArray("situacao").length() > 0){
            List<String> vigencia = Uteis.arrayJsonToList(filtros.getJSONArray("situacao"));
            sql.append(" AND ( \n");
            if(vigencia.contains("INATIVO")){
                sql.append(" ht.situacao = 'IN' ");
            }
            if(vigencia.contains("INATIVO") && vigencia.contains("ATIVO")){
                sql.append(" OR ");
            }
            if(vigencia.contains("ATIVO")){
                sql.append(" ht.situacao = 'AT' ");
            }
            sql.append(" ) \n");
        }

        if (filtros.has("quicksearchValue") && !UteisValidacao.emptyString(filtros.getString("quicksearchValue"))) {
            sql.append(" and (remove_acento_upper(ht.horainicial) like remove_acento_upper('%").append(filtros.getString("quicksearchValue").toUpperCase()).append("%')");
            sql.append(" or remove_acento_upper(ht.horafinal) like remove_acento_upper('%").append(filtros.getString("quicksearchValue").toUpperCase()).append("%'))");
        }


        if (!contar && !UteisValidacao.emptyString(orderBy)) {
            sql.append(" order by  ").append(orderBy);
            sql.append(orderByDesc ? " desc" : " asc");

            int limit = (paginadorDTO.getSize() == null ? 10000 : Integer.parseInt(paginadorDTO.getSize().toString()));
            sql.append(" LIMIT ").append(limit);
            sql.append(" OFFSET ").append((paginadorDTO.getPage() == null ? 0 : Integer.parseInt(paginadorDTO.getPage().toString())) * limit);
        }
        return sql;
    }

    private TurmaResponseDTO montarDadosTurma(ResultSet resultSet)throws Exception{
        TurmaResponseDTO turma = new TurmaResponseDTO();
        turma.setId(resultSet.getInt("codigoTurma"));
        turma.setNome(resultSet.getString("nomeTurma"));
        turma.setCor(resultSet.getString("cor"));
        turma.setIdentificador(resultSet.getString("identificador"));
        turma.setModalidade(new ModalidadeTO());
        turma.getModalidade().setId(resultSet.getInt("codigoModalidade"));
        turma.getModalidade().setNome(resultSet.getString("nomeModalidade"));
        turma.setIdadeMinima(resultSet.getInt("idademinima"));
        turma.setIdadeMinimaMeses(resultSet.getInt("idademinimameses"));
        turma.setIdadeMaxima(resultSet.getInt("idademaxima"));
        turma.setIdadeMaximaMeses(resultSet.getInt("idademaximameses"));
        turma.setIntegracaoSpiv(resultSet.getBoolean("integracaospivi"));
        turma.setDataInicial(Uteis.getDataAplicandoFormatacao(resultSet.getDate("datainicialvigencia"), "MM/dd/yyyy"));
        turma.setDataFinal(Uteis.getDataAplicandoFormatacao(resultSet.getDate("datafinalvigencia"), "MM/dd/yyyy"));
        turma.setMinutosAntecedenciaDesmarcarAula(resultSet.getInt("minutosAntecedenciaDesmarcarAula"));
        turma.setMinutosAposInicioApp(resultSet.getInt("minutosAposInicioApp"));
        turma.setTipoAntecedenciaMarcarAula(resultSet.getInt("tipoAntecedenciaMarcarAula"));
        turma.setMinutosAntecedenciaMarcarAula(resultSet.getInt("minutosAntecedenciaMarcarAula"));
        turma.setQtdeNivelOcupacao(resultSet.getInt("qtdeNivelOcupacao"));
        turma.setPercDescOcupacaoNivel1(resultSet.getDouble("percDescOcupacaoNivel1"));
        turma.setPercDescOcupacaoNivel2(resultSet.getDouble("percDescOcupacaoNivel2"));
        turma.setPercDescOcupacaoNivel3(resultSet.getDouble("percDescOcupacaoNivel3"));
        turma.setPercDescOcupacaoNivel4(resultSet.getDouble("percDescOcupacaoNivel4"));
        turma.setPercDescOcupacaoNivel5(resultSet.getDouble("percDescOcupacaoNivel5"));
        turma.setBloquearMatriculasAcimaLimite(resultSet.getBoolean("bloquearMatriculasAcimaLimite"));
        turma.setPermitirDesmarcarReposicoes(resultSet.getBoolean("permitirDesmarcarReposicoes"));
        turma.setPermiteAlunoOutraEmpresa(resultSet.getBoolean("permiteAlunoOutraEmpresa"));
        turma.setMonitorada(resultSet.getBoolean("monitorada"));
        turma.setBloquearReposicaoAcimaLimite(resultSet.getBoolean("bloquearReposicaoAcimaLimite"));
        turma.setPermitirAulaExperimental(resultSet.getBoolean("permitirAulaExperimental"));
        turma.setBloquearLotacaoFutura(resultSet.getBoolean("bloquearLotacaoFutura"));
        turma.setUrlVideoYoutube(resultSet.getString("urlvideoyoutube"));
        return  turma;
    }

    private AulaColetivaResponseDTO montarDadosAulaColetiva(ResultSet resultSet)throws Exception{
        AulaColetivaResponseDTO aula = new AulaColetivaResponseDTO();

        aula.setCodigo(resultSet.getInt("codigo"));
        aula.setDescricao(resultSet.getString("descricao"));
        aula.setIdentificador(resultSet.getString("identificador"));
        aula.setCor(resultSet.getString("cor"));
        aula.setImageUrl(resultSet.getString("fotokey"));
        aula.setEmpresa(resultSet.getInt("empresa"));
        aula.setMeta(resultSet.getDouble("meta"));
        aula.setPontuacaoBonus(resultSet.getInt("pontosbonus"));
        aula.setBonificacao(resultSet.getDouble("bonificacao"));
        aula.setMensagem(resultSet.getString("mensagem"));
        aula.setOcupacao(resultSet.getInt("ocupacao"));
        aula.setModalidadeId(resultSet.getInt("modalidade"));
        aula.setDataInicio(resultSet.getDate("datainicialvigencia") != null ? resultSet.getDate("datainicialvigencia").getTime() : null);
        aula.setDataFinal(resultSet.getDate("datafinalvigencia") != null ? resultSet.getDate("datafinalvigencia").getTime() : null);
        aula.setToleranciaMin(resultSet.getInt("tolerancia"));
        aula.setTipoTolerancia(resultSet.getInt("tipotolerancia"));
        aula.setValidarRestricoesMarcacao(resultSet.getBoolean("validarRestricoesMarcacao"));
        aula.setNaoValidarModalidadeContrato(resultSet.getBoolean("naoValidarModalidadeContrato"));
        aula.setProdutoGymPass(resultSet.getInt("produtoGymPass"));
        aula.setIdClasseGymPass(resultSet.getInt("idClasseGymPass"));
        aula.setUrlTurmaVirtual(resultSet.getString("urlTurmaVirtual"));
        aula.setPermiteFixar(resultSet.getBoolean("permiteFixar"));
        aula.setAulaIntegracaoSelfloops(resultSet.getBoolean("aulaIntegracaoSelfloops"));
        aula.setVisualizarProdutosGympass(resultSet.getBoolean("visualizarProdutosGympass"));
        aula.setVisualizarProdutosTotalpass(resultSet.getBoolean("visualizarProdutosTotalpass"));
        List<NivelTO> listNiveisTO = new ArrayList<>();
        if (!UteisValidacao.emptyString(resultSet.getString("niveis"))) {
            String[] niveis = resultSet.getString("niveis").split(",");
            for (String nivel : niveis) {
                try {
                    NivelTO nivelTO = new NivelTO();
                    nivelTO.setId(Integer.parseInt(nivel));
                    listNiveisTO.add(nivelTO);
                } catch (Exception e) {
                    e.printStackTrace();
                    Uteis.logarDebug("Erro ao montar níveis da aula coletiva: " + e.getMessage());
                }
            }
        }
        aula.setNiveis(listNiveisTO);
        aula.setIdadeMinimaAnos(resultSet.getInt("idademinima"));
        aula.setIdadeMinimaMeses(resultSet.getInt("idademinimameses"));
        aula.setIdadeMaximaAnos(resultSet.getInt("idademaxima"));
        aula.setIdadeMaximaMeses(resultSet.getInt("idademaximameses"));
        aula.setTipoReservaEquipamento(resultSet.getString("tipoReservaEquipamento"));
        aula.setMapaEquipamentos(resultSet.getString("mapaEquipamentos"));

        return  aula;
    }

    private HorarioTurmaResponseDTO montarDadosHorarioTurma(String ctx, ResultSet resultSet) throws Exception {
        HorarioTurmaResponseDTO horarioTurmaResponseDTO = new HorarioTurmaResponseDTO();
        horarioTurmaResponseDTO.setCodigo(resultSet.getInt("codigo"));
        horarioTurmaResponseDTO.setTurma(resultSet.getInt("turma"));
        horarioTurmaResponseDTO.setProfessor(resultSet.getString("professor"));
        horarioTurmaResponseDTO.setProfessorId(resultSet.getInt("professorId"));
        horarioTurmaResponseDTO.setAmbiente(resultSet.getString("ambiente"));
        horarioTurmaResponseDTO.setAmbienteId(resultSet.getInt("ambienteId"));
        horarioTurmaResponseDTO.setHorarioTurma(resultSet.getString("horarioTurma"));
        horarioTurmaResponseDTO.setHoraInicial(resultSet.getString("horaInicial"));
        horarioTurmaResponseDTO.setHoraFinal(resultSet.getString("horafinal"));
        horarioTurmaResponseDTO.setDuracao(Uteis.getIntervaloHorasEntreDatas(Calendario.getDataComHora(Calendario.hoje(),
                resultSet.getString("horaInicial")), Calendario.getDataComHora(Calendario.hoje(),
                resultSet.getString("horafinal"))));
        horarioTurmaResponseDTO.setDia(resultSet.getString("dia"));
        horarioTurmaResponseDTO.setMaxAlunos(resultSet.getInt("maxAlunos"));
        horarioTurmaResponseDTO.setToleranciaMin(resultSet.getInt("toleranciaEntradaMinutos"));
        horarioTurmaResponseDTO.setToleranciaAposMin(resultSet.getInt("toleranciaEntradaAposMinutos"));
        horarioTurmaResponseDTO.setNivelTurma(resultSet.getString("nivelTurma"));
        horarioTurmaResponseDTO.setNivelTurmaId(resultSet.getInt("nivelTurmaId"));
        horarioTurmaResponseDTO.setHorarioDisponivelVenda(resultSet.getBoolean("horarioDisponivelVenda"));
        horarioTurmaResponseDTO.setLiberadoMarcacaoApp(resultSet.getBoolean("liberadoMarcacaoApp"));
        horarioTurmaResponseDTO.setDataEntrouTurma(resultSet.getDate("dataEntrouTurma") != null ?
                Uteis.getDataAplicandoFormatacao(resultSet.getDate("dataEntrouTurma"), "dd/MM/yyyy HH:mm:ss") : null);
        horarioTurmaResponseDTO.setDataSaiuTurma(resultSet.getDate("dataSaiuTurma") != null ?
                Uteis.getDataAplicandoFormatacao(resultSet.getDate("dataSaiuTurma"), "dd/MM/yyyy HH:mm:ss") : null);
        horarioTurmaResponseDTO.setLimiteVagasAgregados(resultSet.getInt("limitevagasagregados"));
        horarioTurmaResponseDTO.setQtdeMaximaAlunoExperimental(resultSet.getInt("qtdeMaximaAlunoExperimental"));

        horarioTurmaResponseDTO.setHorarioCapacidadeCategoria(montarDadosHorarioCapacidadeCategoria(ctx, horarioTurmaResponseDTO.getCodigo()));
        return horarioTurmaResponseDTO;
    }

    private List<HorarioCapacidadeCategoriaDTO> montarDadosHorarioCapacidadeCategoria(String ctx, Integer horarioTurma) throws Exception {
        List<HorarioCapacidadeCategoriaDTO> horarioCapacidadeCategoriasDTO = new ArrayList<>();
        String sqlStr = "SELECT hcc.codigo, hcc.horarioTurma, hcc.tipoCategoria, hcc.capacidade \n"
                + "FROM HorarioCapacidadeCategoria hcc \n"
                + "WHERE hcc.horarioTurma = " + horarioTurma;
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet tabelaResultado = ConexaoZWServiceImpl.criarConsulta(sqlStr, conZW)) {
                while (tabelaResultado.next()) {
                    HorarioCapacidadeCategoriaDTO horarioCapacidadeCategoriaDTO = new HorarioCapacidadeCategoriaDTO();
                    horarioCapacidadeCategoriaDTO.setCodigo(tabelaResultado.getInt("codigo"));
                    horarioCapacidadeCategoriaDTO.setHorarioTurma(tabelaResultado.getInt("horarioTurma"));
                    horarioCapacidadeCategoriaDTO.setTipoCategoria(tabelaResultado.getString("tipoCategoria"));
                    horarioCapacidadeCategoriaDTO.setCapacidade(tabelaResultado.getInt("capacidade"));
                    horarioCapacidadeCategoriasDTO.add(horarioCapacidadeCategoriaDTO);
                }
            }
        }
        return horarioCapacidadeCategoriasDTO;
    }

    private TurmaResponseDTO montarDadosListaTurma(ResultSet resultSet)throws Exception{
        TurmaResponseDTO turma = new TurmaResponseDTO();
        turma.setId(resultSet.getInt("turma"));
        turma.setNome(resultSet.getString("nome"));
        turma.setIdentificador(resultSet.getString("identificador"));
        turma.setModalidade(new ModalidadeTO());
        turma.getModalidade().setNome(resultSet.getString("modalidade"));
        turma.setEmpresa(new EmpresaDTO());
        turma.getEmpresa().setCodigo(resultSet.getInt("codigoEmpresa"));
        turma.getEmpresa().setNome(resultSet.getString("empresa"));
        turma.setIdadeMinima(resultSet.getInt("idademinima"));
        turma.setIdadeMaxima(resultSet.getInt("idademaxima"));
        turma.setIntegracaoSpiv(resultSet.getBoolean("integracaospiv"));
        turma.setDataFinal(Uteis.getDataAplicandoFormatacao(resultSet.getDate("datafinal"), "MM/dd/yyyy"));
        return  turma;
    }

    private List<TurmaResponseDTO> montarListaTurmasAula(ResultSet rs) throws Exception {
        List<TurmaResponseDTO> aulas = new ArrayList<>();
        while (rs.next()) {
            aulas.add(montarDadosListaTurma(rs));
        }
        return aulas;
    }

    private String getSqlOrderBy(String orderBy) {
        switch (orderBy) {
            case "id":
                return "t.codigo";
            case "nome":
                return "t.descricao";
            case "identificador":
                return "t.identificador";
            case "modalidade.nome":
                return "m.nome";
            case "empresa.nome":
                return "e.nome";
            case "idadeMinima":
                return "t.idademinima";
            case "idadeMaxima":
                return "t.idademaxima";
            case "integracaospiv":
                return "t.integracaospivi";
        }
        return "";
    }

    private StringBuilder retornaSelectConsultaBasica(){
        StringBuilder sqlSelect = new StringBuilder();
        sqlSelect.append("SELECT t.codigo as turma, t.descricao  as nome, \n");
        sqlSelect.append("t.identificador, m.nome as modalidade,e.nome as empresa, e.codigo as codigoEmpresa, \n");
        sqlSelect.append("t.idademinima, t.idademaxima, t.integracaospivi as integracaospiv, \n");
        sqlSelect.append("t.datafinalvigencia as datafinal \n");
        return sqlSelect;
    }

    private StringBuilder retornaFromConsultaBasica(Integer empresa, JSONObject filtros) throws Exception {
        StringBuilder sqlFrom = retornaFromConsultaBasica(filtros);
        if(empresa>0)
            sqlFrom.append(" AND t.empresa = "+empresa );
        return sqlFrom;
    }

    private StringBuilder retornaFromConsultaBasica(JSONObject filtros) throws Exception {
        StringBuilder sqlFrom = new StringBuilder();
        sqlFrom.append(" FROM turma t \n");
        sqlFrom.append(" INNER JOIN modalidade m ON m.codigo = t.modalidade \n");
        sqlFrom.append(" INNER JOIN empresa e ON e.codigo = t.empresa \n");
        sqlFrom.append(" WHERE t.aulacoletiva is false\n");
        if(filtros == null){
            sqlFrom.append(" AND t.usuariodesativou is null \n");
            return sqlFrom;
        }

        if(filtros.has("codigo") && filtros.getInt("codigo") > 0){
            sqlFrom.append(" AND t.codigo = ").append(filtros.getInt("codigo"));
            return sqlFrom;
        }

        if(filtros.has("vigencia") && filtros.getJSONArray("vigencia").length() > 0){
            List<String> vigencia = Uteis.arrayJsonToList(filtros.getJSONArray("vigencia"));
            sqlFrom.append(" AND ( \n");
            if(vigencia.contains("NAO_VIGENTE")){
                sqlFrom.append(" t.datafinalvigencia < '").append(Uteis.getDataJDBC(Calendario.hoje())).append("' ");
            }
            if(vigencia.contains("NAO_VIGENTE") && vigencia.contains("VIGENTE")){
                sqlFrom.append(" OR ");
            }
            if(vigencia.contains("VIGENTE")){
                sqlFrom.append(" t.datafinalvigencia >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("' ");
            }
            sqlFrom.append(" ) \n");
        }

        if(filtros.has("modalidadeIds") && filtros.getJSONArray("modalidadeIds").length() > 0){
            sqlFrom.append(" AND t.modalidade in ( \n");
            sqlFrom.append(Uteis.arrayJsonToString(filtros.getJSONArray("modalidadeIds"), ","));
            sqlFrom.append(" ) \n");
        }

        if(filtros.has("quicksearchValue") && !UteisValidacao.emptyString(filtros.getString("quicksearchValue"))){
            if(Uteis.isNumeroValido(filtros.getString("quicksearchValue"))){
                sqlFrom.append(" and t.codigo = ").append(filtros.getString("quicksearchValue"));
            } else {
                sqlFrom.append(" and (remove_acento_upper(t.descricao) like remove_acento_upper('%").append(filtros.getString("quicksearchValue").toUpperCase()).append("%')");
                sqlFrom.append(" or remove_acento_upper(t.identificador) like remove_acento_upper('%").append(filtros.getString("quicksearchValue").toUpperCase()).append("%'))");
            }
        }

        return sqlFrom;
    }


    @Override
    public Long consultarPorHorarioTurmaPorPeriodoCount(String ctx, int valorConsulta, java.util.Date dataInicial, java.util.Date dataFinal) throws Exception {
        String sqlStr = "SELECT count(codigo) FROM MatriculaAlunoHorarioTurma "
                + "WHERE horarioTurma = " + valorConsulta + " "
                + sqlPeriodo(dataInicial, dataFinal);
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ResultSet tabelaResultado = ConexaoZWServiceImpl.criarConsulta(sqlStr, conZW);
            if (!tabelaResultado.next()) {
                return new Long(0);
            }
            return (new Long(tabelaResultado.getInt(1)));
        }
    }

    private String sqlPeriodo(java.util.Date inicio, java.util.Date fim) throws Exception {
        if (inicio == null && fim == null) {
            return "";
        }
        if (fim == null) {
            return " AND datainicio >= '" + Uteis.getDataJDBC(inicio) + "' ";
        }
        return "AND ((datafim >= '" + Uteis.getDataJDBC(inicio) + "' AND datafim <= '" + Uteis.getDataJDBC(fim) + "') OR "
                + "(datainicio >= '" + Uteis.getDataJDBC(inicio) + "' AND datainicio <= '" + Uteis.getDataJDBC(fim) + "') OR "
                + "(datainicio < '" + Uteis.getDataJDBC(inicio) + "' AND datafim > '" + Uteis.getDataJDBC(fim) + "')) ";
    }

    @Override
    public Long existemAlunosEmHorarioFuturo(String ctx, Integer codigoHorario) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(codigo) FROM alunohorarioturma a \n");
        sql.append("WHERE a.horarioTurma = ").append(codigoHorario).append(" \n");
        sql.append("AND a.dia > '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'");

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ResultSet tabelaResultado = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
            if (!tabelaResultado.next()) {
                return new Long(0);
            }
            return (new Long(tabelaResultado.getInt(1)));
        }
    }

    @Override
    public void nrAlunosReposicao(String ctx, HorarioTurmaResponseDTO dto, final String periodo) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rsEntraram = ConexaoZWServiceImpl.criarConsulta(
                    String.format(
                            "SELECT COUNT(r.codigo) AS qtde FROM reposicao r "
                                    + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                                    + "WHERE r.horarioturma = %s AND "
                                    + "datareposicao between %s ",
                            new Object[]{
                                    dto.getCodigo(),
                                    periodo
                            }), conZW)) {
                if (rsEntraram.next()) {
                    dto.setNrAlunoEntraramPorReposicao(rsEntraram.getInt("qtde"));
                }
            }
            try (ResultSet rsSairam = ConexaoZWServiceImpl.criarConsulta(String.format(
                    "SELECT COUNT(r.codigo) AS qtde FROM reposicao r "
                            + "inner JOIN horarioturma ht ON ht.codigo = r.horarioturma "
                            + "WHERE r.horarioturmaorigem = %s AND "
                            + "dataorigem between %s ",
                    new Object[]{
                            dto.getCodigo(),
                            periodo
                    }), conZW)) {
                if (rsSairam.next()) {
                    dto.setNrAlunoSairamPorReposicao(rsSairam.getInt("qtde"));
                }
            }
        }
    }

    @Override
    public Long consultarPorHorarioTurmaCount(String ctx, int horarioTurma) throws Exception {
        String sqlStr = "SELECT count(codigo) FROM MatriculaAlunoHorarioTurma "
                + "WHERE horarioTurma = " + horarioTurma;
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ResultSet tabelaResultado = ConexaoZWServiceImpl.criarConsulta(sqlStr, conZW);
            if (!tabelaResultado.next()) {
                return new Long(0);
            }
            return (new Long(tabelaResultado.getInt(1)));
        }
    }

    @Override
    public boolean existeControleCredito(String ctx, Integer codigo) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {

            return ConexaoZWServiceImpl.existe("SELECT codigo FROM controlecreditotreino WHERE horarioturmafalta = " + codigo, conZW);
        }
    }

    @Override
    public boolean existemReposicoesParaHorarioTurma(String ctx, HorarioTurmaResponseDTO dto, boolean reposicoesFuturas) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT codigo FROM reposicao r\n");
            sql.append("WHERE 1 = 1\n");
            sql.append("AND (horarioturma = %s or horarioturmaorigem = %s)\n");
            if (reposicoesFuturas) {
                sql.append("AND datareposicao > now() ");
            }
            return ConexaoZWServiceImpl.existe(String.format(sql.toString(), dto.getCodigo(), dto.getCodigo()), conZW);
        }
    }

    @Override
    public boolean existeContratoModalidadeHorarioTurma(String ctx, Integer codigo) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            StringBuilder sql = new StringBuilder("select count(*) reg from contratomodalidadehorarioturma where horarioturma = ").append(codigo);
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                if (rs.next()) {
                    return rs.getInt("reg") > 0;
                }
                return false;
            }
        }
    }

    @Override
    public boolean existeAulaDesmarcadaSemReporParaHorarioTurma(String ctx, Integer codigo) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {

            StringBuilder sql;
            try {
                sql = new StringBuilder("select * from auladesmarcada where horarioturma = %s");
                return ConexaoZWServiceImpl.existe(String.format(sql.toString(), codigo), conZW);
            } catch (Exception e) {
                throw e;
            } finally {
                sql = null;
            }
        }
    }


    @Override
    public void excluirHorarioTurma(String ctx, HorarioTurmaResponseDTO dto) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {

            String sql = "DELETE FROM HorarioTurma WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = conZW.prepareStatement(sql)) {
                sqlExcluir.setInt(1, dto.getCodigo());
                sqlExcluir.execute();
            }
        }
    }

    private void excluirHorarioCapacidadeCategoria(String ctx, Integer horarioTurma) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {

            String sql = "DELETE FROM horariocapacidadecategoria WHERE horarioturma = ?";
            try (PreparedStatement sqlExcluir = conZW.prepareStatement(sql)) {
                sqlExcluir.setInt(1, horarioTurma);
                sqlExcluir.execute();
            }
        }
    }

    @Override
    public void desativarHorarioTurma(String ctx, HorarioTurmaResponseDTO dto) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {

            String sql = "UPDATE HorarioTurma SET situacao=?, dataSaiuTurma=?, ativo=? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = conZW.prepareStatement(sql)) {
                sqlAlterar.setString(1, dto.getSituacao());
                if (dto.getDataSaiuTurma() != null) {
                    sqlAlterar.setDate(2, new Date(Calendario.getDate("MM/dd/yyyy HH:mm:ss", dto.getDataSaiuTurma()).getTime()));
                } else {
                    sqlAlterar.setNull(2, 0);
                }
                sqlAlterar.setBoolean(3, dto.getHorarioDisponivelVenda());
                sqlAlterar.setInt(4, dto.getCodigo());
                sqlAlterar.execute();
            }
        }
    }

    @Override
    public void desativarHorarioAula(String ctx, HorarioTurmaResponseDTO dto) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {

            String sql = "UPDATE HorarioTurma SET situacao=?, dataSaiuTurma=?, ativo=? WHERE codigo = ?";
            try (PreparedStatement sqlAlterar = conZW.prepareStatement(sql)) {
                sqlAlterar.setString(1, dto.getSituacao());
                if (dto.getDataSaiuTurma() != null) {
                    sqlAlterar.setDate(2, new Date(Calendario.getDate("dd/MM/yyyy HH:mm:ss", dto.getDataSaiuTurma()).getTime()));
                } else {
                    sqlAlterar.setNull(2, 0);
                }
                sqlAlterar.setBoolean(3, dto.getHorarioDisponivelVenda());
                sqlAlterar.setInt(4, dto.getCodigo());
                sqlAlterar.execute();
            }
        }
    }

    @Override
    public boolean existemAlunosNaTurma(String ctx, TurmaResponseDTO turma) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String sql = "SELECT\n" +
                    "  count(*) as qtdAlunos\n" +
                    "FROM matriculaalunohorarioturma maht\n" +
                    "  INNER JOIN horarioturma ht\n" +
                    "    ON maht.horarioturma = ht.codigo\n" +
                    "  INNER JOIN turma t\n" +
                    "    ON ht.turma = t.codigo\n" +
                    "WHERE t.codigo = " + turma.getId() + "\n" +
                    "      AND datafim > '" + Uteis.getDataJDBC(Calendario.hoje()) + "'";
            try (Statement stm = conZW.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                    return tabelaResultado.next() && tabelaResultado.getInt("qtdAlunos") > 0;
                }
            }
        }
    }

    @Override
    public List<TurmaVideoDTO> obterListaTurmaVideo(String ctx, Integer codigo) throws Exception {
        TurmaVideoDTO turmaVideoDTO = null;
        StringBuilder sql = new StringBuilder();
        sql.append("select codigo, turma_codigo , linkvideo , professor ");
        sql.append("from turmavideo ");
        sql.append("where turma_codigo = ").append(codigo);

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                return  montarDadosListaTurmaVideo(rs);
            }
        }
    }

    @Override
    public TurmaVideoDTO obterTurmaVideo(String ctx, Integer codigo) throws Exception {
        TurmaVideoDTO turmaVideoDTO =  null;
        StringBuilder sql = new StringBuilder();
        sql.append("select codigo, turma_codigo , linkvideo , professor ");
        sql.append("from turmavideo ");
        sql.append("where codigo = ").append(codigo);

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                if (rs.next()) {
                    turmaVideoDTO = montarDadosTurmaVideo(rs);
                }
            }
        }
        return turmaVideoDTO;
    }

    @Override
    public void excluirTurmaVideo(String ctx, TurmaVideoDTO dto) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String sql = "DELETE FROM turmavideo WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = conZW.prepareStatement(sql)) {
                sqlExcluir.setInt(1, dto.getId());
                sqlExcluir.execute();
            }
        }
    }

    @Override
    public TurmaVideoDTO saveTurmaVideo(String ctx, TurmaVideoDTO turmaVideoDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO turmavideo (");
        sql.append("turma_codigo, linkvideo, professor )");
        sql.append(" VALUES ( ?, ?, ? )");
        sql.append(" RETURNING codigo");

        int i = 1;
        try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement stm = connection.prepareStatement(sql.toString());

            stm.setInt(i++, turmaVideoDTO.getTurma_codigo());
            stm.setString(i++, turmaVideoDTO.getLinkVideo());
            stm.setBoolean(i++, turmaVideoDTO.getProfessor());

            try (ResultSet resultSet = stm.executeQuery()) {
                if (resultSet.next()) {
                    turmaVideoDTO.setId(resultSet.getInt("codigo"));
                }
            }
        }
        return turmaVideoDTO;
    }

    @Override
    public void updateTurmaVideo(String ctx, TurmaVideoDTO turmaVideoDTO) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {

            String sql = "UPDATE turmavideo SET professor= ? WHERE codigo = ?";
            int i = 1;
            try (PreparedStatement sqlAlterar = conZW.prepareStatement(sql)) {
                sqlAlterar.setBoolean(i++, turmaVideoDTO.getProfessor());
                sqlAlterar.setInt(i++, turmaVideoDTO.getId());
                sqlAlterar.execute();
            }
        }
    }

    @Override
    public Integer consultarUltimaTurmaSalva(String ctx) throws Exception {
        String sqlStr = "SELECT codigo FROM turma ORDER BY codigo DESC LIMIT 1; ";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ResultSet tabelaResultado = ConexaoZWServiceImpl.criarConsulta(sqlStr, conZW);
            if (!tabelaResultado.next()) {
                return new Integer(0);
            }
            return (new Integer(tabelaResultado.getInt(1)));
        }
    }


    private List<TurmaVideoDTO> montarDadosListaTurmaVideo(ResultSet resultSet) throws Exception {
        List<TurmaVideoDTO> linkVideos = new ArrayList<>();
        while (resultSet.next()) {
            TurmaVideoDTO turmaVideoDTO = new TurmaVideoDTO();
            turmaVideoDTO.setId(resultSet.getInt("codigo"));
            turmaVideoDTO.setLinkVideo(resultSet.getString("linkvideo"));
            turmaVideoDTO.setProfessor(resultSet.getBoolean("professor"));
            linkVideos.add(turmaVideoDTO);
        }
        return linkVideos;
    }

    private TurmaVideoDTO montarDadosTurmaVideo(ResultSet resultSet) throws Exception {
        TurmaVideoDTO turmaVideoDTO = new TurmaVideoDTO();
        turmaVideoDTO.setId(resultSet.getInt("codigo"));
        turmaVideoDTO.setLinkVideo(resultSet.getString("linkvideo"));
        turmaVideoDTO.setProfessor(resultSet.getBoolean("professor"));
        return turmaVideoDTO;
    }

    @Override
    public String obterNomeModalidadePorCodigo(String ctx, Integer codigo) {
        String nomeModalidade = "";
        try {
            String sqlStr = "select nome from modalidade m where m.codigo = " + codigo;
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlStr, conZW);
                if (rs.next()) {
                    nomeModalidade = rs.getString("nome");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao obter nomeModalidadePorCodigo: " + e.getMessage());
        }
        return nomeModalidade;
    }

    @Override
    public Integer obterCodigoNivelTurmaSN(String ctx) {
        Integer codigoSN = null;
        try {
            String sql = "SELECT n.codigo FROM nivelturma n WHERE n.descricao ILIKE 'SN'";
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW);
                if (rs.next()) {
                    codigoSN = rs.getInt("codigo");
                }
            }

            if (codigoSN == null) {
                StringBuilder sqlInsert = new StringBuilder();
                sqlInsert.append("INSERT INTO NIVELTURMA (descricao, codigomgb) VALUES ('SN', null) RETURNING codigo;");
                try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
                    PreparedStatement stm = connection.prepareStatement(sqlInsert.toString());
                    try (ResultSet rs = stm.executeQuery()) {
                        if (rs.next()) {
                            codigoSN = rs.getInt("codigo");
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao obter nomeModalidadePorCodigo: " + e.getMessage());
        }
        return codigoSN;
    }

    @Override
    public String obterDiasSemanaHorarioPorTurma(String ctx, Integer codigoTurma) throws Exception {
        String diasSemana = "";
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT DISTINCT(h.diasemana), h.diasemananumero \n ");
            sql.append("FROM horarioturma h \n ");
            sql.append("WHERE (h.datasaiuturma IS NULL OR h.situacao = 'AT') \n");
            sql.append("AND h.turma = ").append(codigoTurma).append(" \n ");
            sql.append("ORDER BY h.diasemananumero ASC");
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
                while (rs.next()) {
                    diasSemana += "," + rs.getString("diasemana");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao obter nomeModalidadePorCodigo: " + e.getMessage());
        }

        if (!UteisValidacao.emptyString(diasSemana)) {
            diasSemana = diasSemana.replaceFirst(",", "");
        }

        return diasSemana;
    }

    @Override
    public boolean isTurmaPermiteAulaExperimental(String ctx, Integer codigoHorarioTurma) {
        boolean permiteAulaExperimentao = false;
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT permitiraulaexperimental \n ");
            sql.append("FROM turma t \n ");
            sql.append("INNER JOIN horarioturma ht on ht.turma = t.codigo \n ");
            sql.append("WHERE ht.codigo =").append(codigoHorarioTurma);

            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
                while (rs.next()) {
                    permiteAulaExperimentao = rs.getBoolean("permitiraulaexperimental");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao obter permitiraulaexperimental: " + e.getMessage());
        }

        return permiteAulaExperimentao;
    }

    @Override
    public boolean alunoJaPossuiCompromissoReposicao(String ctx, Integer codigoHorarioTurma, Integer codigoCliente) {
        boolean possuiCompromisso = false;
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT COUNT(*) as total \n");
            sql.append("FROM reposicao r \n");
            sql.append("INNER JOIN horarioturma h ON h.codigo = r.horarioturma \n");
            sql.append("INNER JOIN horarioturma h_verificar ON h_verificar.codigo = ").append(codigoHorarioTurma).append(" \n");
            sql.append("WHERE r.cliente = ").append(codigoCliente).append(" \n");
            sql.append("AND h.diasemana = h_verificar.diasemana \n");
            sql.append("AND ( \n");
            sql.append("    (h.horainicial <= h_verificar.horainicial AND h.horafinal > h_verificar.horainicial) \n");
            sql.append("    OR (h.horainicial < h_verificar.horafinal AND h.horafinal >= h_verificar.horafinal) \n");
            sql.append("    OR (h.horainicial >= h_verificar.horainicial AND h.horafinal <= h_verificar.horafinal) \n");
            sql.append(") \n");

            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
                while (rs.next()) {
                    possuiCompromisso = rs.getInt("total") > 0;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao verificar compromisso de reposição: " + e.getMessage());
        }

        return possuiCompromisso;
    }

    @Override
    public void validarClienteIdadeEstaValidaParaHorarioTurma(String ctx, Integer codigoHorarioTurma, Integer codigoCliente) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT t.idademinima, t.idademinimameses, t.idademaxima, t.idademaximameses, p.datanasc \n");
            sql.append("FROM turma t \n");
            sql.append("INNER JOIN horarioturma h ON h.turma = t.codigo \n");
            sql.append("INNER JOIN cliente c ON c.codigo = ").append(codigoCliente).append(" \n");
            sql.append("INNER JOIN pessoa p ON p.codigo = c.pessoa \n");
            sql.append("WHERE h.codigo = ").append(codigoHorarioTurma);

            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
                while (rs.next()) {
                    Date dataNasc = rs.getDate("datanasc");
                    if (dataNasc != null) {
                        String dataNascCalcular = Uteis.getDataAplicandoFormatacao(dataNasc, "yyyy-MM-dd");
                        String validaIdade = Uteis.calculaIdadeComMeses(dataNascCalcular);

                        String[] split = validaIdade.split("-");
                        int idadeAlunoAno = Integer.parseInt(split[0]);
                        int idadeAlunoMes = Integer.parseInt(split[1]);

                        int idadeMinAno = rs.getInt("idademinima");
                        int idadeMinMes = rs.getInt("idademinimameses");
                        int idadeMaxAno = rs.getInt("idademaxima");
                        int idadeMaxMes = rs.getInt("idademaximameses");

                        validaIdadeAluno(
                                idadeAlunoAno,
                                idadeAlunoMes,
                                idadeMinAno,
                                idadeMinMes,
                                idadeMaxAno,
                                idadeMaxMes
                        );
                    }
                }
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao validar idade do cliente para horário da turma: " + e.getMessage());
            throw new ServiceException("Erro ao validar idade do cliente para horário da turma");
        }
    }

    private void validaIdadeAluno(int idadeAnoAluno, int idadeMesAluno, int idadeAnoMinima, int idadeMesMinima, int idadeAnoMaxima, int idadeMesMaxima) throws ServiceException {
        boolean temIdadeMinima = idadeAnoMinima > 0 || idadeMesMinima > 0;
        boolean temIdadeMaxima = idadeAnoMaxima > 0 || idadeMesMaxima > 0;

        boolean menorQueMin = temIdadeMinima && ((idadeAnoAluno < idadeAnoMinima) || (idadeAnoAluno == idadeAnoMinima && idadeMesAluno < idadeMesMinima));
        boolean maiorQueMax = temIdadeMaxima && ((idadeAnoAluno > idadeAnoMaxima) || (idadeAnoAluno == idadeAnoMaxima && idadeMesAluno > idadeMesMaxima));

        if (menorQueMin || maiorQueMax) {
            throw new ServiceException(
                    String.format(
                            "A idade do aluno não é permitida para participar desta aula, a idade mínima é de %s e %s e a idade máxima é de %s e %s",
                            formatarAnos(idadeAnoMinima), formatarMeses(idadeMesMinima),
                            formatarAnos(idadeAnoMaxima), formatarMeses(idadeMesMaxima)
                    )
            );
        }

    }

    private String formatarAnos(int anos) {
        return anos == 1 ? "1 ano" : anos + " anos";
    }

    private String formatarMeses(int meses) {
        return meses == 1 ? "1 mês" : meses + " meses";
    }

}

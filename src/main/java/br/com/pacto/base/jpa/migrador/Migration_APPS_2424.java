package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "25/04/2025",
        descricao = "Adiciona colunas nomeapp e versaoapp na tabela log",
        motivacao = "APPS-2424")
public class Migration_APPS_2424 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE log ADD COLUMN nomeapp VARCHAR(255)");
            dao.executeNativeSQL(ctx,"ALTER TABLE log ADD COLUMN versaoapp VARCHAR(255)");
        } catch (Exception e) {
            Uteis.logar(e, AA_FirstMigration.class);
        }
    }
}

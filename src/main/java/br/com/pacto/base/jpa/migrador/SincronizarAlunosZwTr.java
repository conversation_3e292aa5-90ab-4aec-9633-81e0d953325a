package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.processo.ProcessosService;
import br.com.pacto.util.UtilContext;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "04/08/2025",
        descricao = "Sincronizar alunos entre bancos zw e tr",
        motivacao = "TW-2694 Alunos com vínculos e situações diferentes no treino devido problema gestão carteira")
public class SincronizarAlunosZwTr implements MigracaoVersaoInterface {

    // Processo criado para executar automaticamente e sincronizar situação e vinculo com professor TW devido problema do gestão de carteiras que não estava mandando os alunos para o treino;
    // Importar alunos que o ZW não enviou para o Treino;

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {

        List<Integer> codEmpresasZw = new ArrayList<>();
        try {
            // Obter o codigoZw de todas empresas
            String sqlObtemCodigosEmpresaZW = "select codzw from empresa";
            try (ResultSet rsEmpresa = dao.createStatement(ctx, sqlObtemCodigosEmpresaZW)) {
                while (rsEmpresa.next()) {
                    codEmpresasZw.add(rsEmpresa.getInt("codzw"));
                }
            }

            // A utilização de thread foi necessária pois mesmo o processo estando otimizado se tiver muitos alunos para
            // importar pode demorar algum tempo para ser concluído
            // Localmente na minha máquina a importação de alunos gastou 1min para importar uma média de 100 alunos;
            if (codEmpresasZw != null && !codEmpresasZw.isEmpty()) {
                new Thread(() -> {
                    Uteis.logarDebug("#### [SincronizarAlunosZwTr] Iniciando execucao automatica do processo " +
                            "SincronizarAlunosZwTr para sincronizar situacao, vinculo e importar alunos do ZW para o TR");
                    try {
                        ProcessosService processoService = UtilContext.getBean(ProcessosService.class);
                        for (Integer codzw : codEmpresasZw) {
                            // Neste fluxo desconsiderar a importação de visitantes do ZW para o TR devido o banco da corpo e saúde
                            // que identifiquei mais de 30k de visitantes não importados, o que faria consumir horas para o processo ser concluído
                            processoService.validarSincronizacaoTodosAlunosZwTr(ctx, codzw, false);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    Uteis.logarDebug("#### [SincronizarAlunosZwTr] Finalizando execucao automatica do processo " +
                            "SincronizarAlunosZwTr para sincronizar situacao, vinculo e importar alunos do ZW para o TR");
                }).start();
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("#### [SincronizarAlunosZwTr] Erro ao executar processo SincronizarAlunosZwTr: "
                    + e.getMessage());
        }
    }
}

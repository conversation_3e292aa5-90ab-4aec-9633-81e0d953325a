/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.ficha;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.AtividadeFichaAjuste;
import br.com.pacto.bean.ficha.*;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.controller.json.atividade.FiltroFichaPredefinidaJSON;
import br.com.pacto.controller.json.ficha.write.FichaWriteJSON;
import br.com.pacto.objeto.to.AtividadeFichaTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface FichaService {

    public static final String SERVICE_NAME = "FichaService";

    public Ficha inserir(final String ctx, Ficha object) throws ServiceException, ValidacaoException;

    Ficha cadastrar(final String ctx, Ficha ficha) throws ServiceException;

    public Ficha obterPorId(final String ctx, Integer id) throws ServiceException;

    public FichaWriteJSON obterPorIdApp(final String ctx, Integer id) throws ServiceException;

    public Ficha alterar(final String ctx, Ficha object, boolean persistirAtividades) throws ServiceException, ValidacaoException;

    public void excluir(final String ctx, Ficha object) throws ServiceException;

    public List<Ficha> obterTodos(final String ctx) throws ServiceException;

    public List<Ficha> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Ficha> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Ficha obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Ficha> obterPorPrograma(final String ctx, Integer codigo) throws ServiceException;

    public AtividadeFicha obterAtividadeFicha(final String ctx, final Integer Atividade, final Integer ficha)
            throws ServiceException;

    public List<AtividadeFicha> obterAtividadesFicha(final String ctx, final Integer ficha)
            throws ServiceException;

    public AtividadeFicha alterarAtividadeFicha(final String ctx, AtividadeFicha object) throws ServiceException;

    public void excluirAtividadeFicha(final String ctx, AtividadeFicha object) throws ServiceException;

    public AtividadeFicha inserirAtividadeFicha(final String ctx, AtividadeFicha object) throws ServiceException;

    public List<Ficha> obterFichasPredefinidas(String ctx, boolean somenteAtivas) throws ServiceException;

    public FichaResponseTO obterFichaPredefinidaById(Integer id) throws ServiceException;

    public List<Ficha> obterFichasComCategoria(String ctx, CategoriaFicha categoria) throws ServiceException;

    public List<ProgramaTreinoFicha> obterProgramasFicha(final String ctx, final Integer ficha) throws ServiceException;

    public List<Ficha> obterPorNome(final String ctx, boolean preDefinidas, String nome, Integer codigo) throws ServiceException;

    public Boolean reordenar(String ctx, AtividadeFicha atividade, Ficha ficha, boolean up) throws ServiceException;

    public void tornarPreDefinida(String key, Ficha ficha) throws ValidacaoException, ServiceException;

    public void atualizarVersaoFicha(String ctx, Integer ficha) throws ServiceException;

    public AtividadeFicha salvarAtividadeFicha(final String ctx, AtividadeFicha atividadeFicha) throws ValidacaoException, ServiceException;

    public void removerAtividade(final String ctx, final AtividadeFicha atividade, final Ficha ficha) throws ValidacaoException, ServiceException;

    public Ficha salvarAtividadesDaFicha(final String ctx, Ficha ficha) throws ValidacaoException, ServiceException;

    public Ficha gerarSeries(Ficha ficha);

    public Ficha prepararPersistenciaJSON(final String ctx, final FichaWriteJSON fw)
            throws ServiceException;

    public Ficha prepararPersistenciaAppJSON(final String ctx, final FichaWriteJSON fw) throws ServiceException;

    public Ficha efetuarExclusoesJSON(final String ctx, final FichaWriteJSON fw,
            Ficha fichaPreparada) throws ServiceException;
    
    public AtividadeFicha obterAtividadeFichaPorId(final String ctx, final Integer id) throws ServiceException;
    
    public void limparAssociadas(final String ctx, final AtividadeFicha atv ) throws Exception;

    public void ordenarAtividadesAssociadas(final String ctx, final AtividadeFicha itemDeslocado,
                                            final Integer posFinal,
                                            final Ficha ficha) throws Exception;

    public void montarOrdenacaoIndice(final String ctx, List<AtividadeFicha> atividades) throws Exception ;

    public void trocarFichaDaAtividade(String ctx, AtividadeFichaTO atividadeTO, Map<String, Ficha> mapaFichas, Integer ordem) throws ServiceException;

    public void alterarOrdemForm(final String ctx, Ficha ficha, int itemInicio, int itemFinal) throws Exception;

    public List<AtividadeFicha> alterarOrdemForm(final String ctx, List<AtividadeFicha> atividades, int itemInicio, int itemFinal, boolean alterarAssociado) throws Exception;

    public ProgramaTreino obterProgramaPorFicha(final String ctx, final Integer ficha) throws ServiceException;

    Ficha atualizarUltimaExecucao(final String ctx, Date dataUltimaExecucao, Integer idFicha) throws ServiceException;
    
    public void refresh(String ctx, Ficha object) throws Exception;

    public Ficha alterarSimples(final String ctx, Ficha object, ProgramaTreinoFicha ptf) throws ServiceException;

    public String sugerirNomeFicha(List fichas);

    public void aplicarPadraoSeries(final String ctx,
                                                    Ficha ficha,
                                                    List<AtividadeFicha> atvs,
                                                    Integer nrSeries,
                                                    Serie serie,
                                    boolean alternar) throws Exception;

    public Map<String, Integer> mapAtividadesSugeridas(final String ctx,
                                                    Integer professor,
                                                    String nomeFicha,
                                                    Integer idade,
                                                    String sexo,
                                                    boolean loop) throws Exception;

    public List<String> atividadesSugeridas(final String ctx,
                                                    Integer professor,
                                                    String nomeFicha,
                                                    Integer idade,
                                                    String sexo) throws Exception;

    List<FichaResponseTO> consultarFichasPreDefinidas(FiltroFichaPredefinidaJSON filtroFichaPredefinidaJSON, Integer categoriaId, String nome, PaginadorDTO paginadorDTO) throws ServiceException;

    FichaResponseTO criarFicha(Integer preDefinidoId, Integer programaId, HttpServletRequest request, String chaveOrigem, String nomeFicha) throws ServiceException;

    FichaResponseTO editarFicha(FichaDTO fichaDTO, HttpServletRequest request) throws ServiceException;

    FichaResponseTO criarPredefinida(FichaDTO fichaDTO, HttpServletRequest request) throws ServiceException;

    List<AtividadeFichaResponseTO> reordenarAtividades(Integer fichaId, List<Integer> atividadesIds) throws ServiceException;

    List<AtividadeFichaResponseTO> aplicarPadraoSeries(Integer fichaId, AtividadeFichaPadraoSeriesTO padraoSeriesTO) throws ServiceException;

    List<String> obterMensagensRecomendadas() throws ServiceException;

    void excluirPorId(Integer id, HttpServletRequest request) throws ServiceException;

    List<CategoriaFichaResponseTO> carregarCategoriaFichas() throws ServiceException;

    void criarFichaPredefinida(Integer fichaId) throws ServiceException;

    public ProgramaTreinoFicha persistirProgramaTreinoFicha(final String ctx, ProgramaTreinoFicha ptf) throws ServiceException;

    Ficha tornarPreDefinidaApp(String key, Ficha ficha) throws ValidacaoException, ServiceException;

    List<Ficha> obterFichasPredefinidasApp(String ctx, boolean somenteAtivas, Integer index, Integer maxResult, String nomeFicha) throws ServiceException;

    Ficha cadastrarApp(final String ctx, Ficha ficha) throws ServiceException;

    void atualizarSituacaoFichaPredefinida(FichaDTO fichaTO, Integer id) throws ServiceException;

    List<AtividadeFichaAjuste> obterAtividadeFichaAjustePorAtividadeFicha(final String ctx, Integer codAtividadeFicha) throws Exception;

    List<String> consultarGruposMuscularesPorFichaTreino(Integer id) throws Exception;

    List<FichaDTO> obterFichasExecutadasPorClienteZW(Integer clienteZW) throws ServiceException;
}

package br.com.pacto.service.intf.agenda;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.agenda.ConfigAgenda;
import br.com.pacto.bean.aula.AulaAluno;
import br.com.pacto.bean.aula.AulaHorario;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.bean.gympass.TipoThreadGympassEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.*;
import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.aulaDia.AulaAlunoJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.gympass.json.TurmaGymPassJSON;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import br.com.pacto.util.json.AgendaTotalJSON;
import org.json.JSONArray;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AgendaService {
    public static final String SERVICE_NAME = "AgendaService";

    void substituirProfessor(SubstituirProfessorDTO dados, Date dia, Integer horarioAula) throws ServiceException;

    AulaAlunoJSON adicionarAluno(Integer alunoid, Date dia, Integer horarioAula) throws ServiceException;

    AulaAlunoJSON adicionarAluno(String ctx, Integer alunoid, Date dia, Integer horarioAula) throws ServiceException;

    String incluirHorarioEquipamentoAluno(String ctx, Integer empresaId, Integer horarioId, Integer alunoId, String equipamento, Integer usuarioId, String dia, Boolean app) throws ServiceException;

    void deletarHorarioEquipamentoAluno(String ctx, Integer empresaId, Integer horarioId, Integer alunoId, Date dia) throws ServiceException;

    String horarioEquipamento(String ctx, Integer aulaId, Integer empresa) throws ServiceException;

    String horarioEquipamentoOcupado(String ctx, Date dia,
                                     Integer horarioTurma) throws ServiceException;

    void removerAluno(Integer alunoid, Date dia, Integer horarioAula) throws ServiceException;

    void removerAluno(String ctx, Integer alunoid, Date dia, Integer horarioAula) throws ServiceException;

    void confirmarPresenca(Integer alunoid, Date dia, Integer horarioAula) throws ServiceException;

    void removerPresenca(Integer alunoid, Date dia, Integer horarioAula) throws ServiceException;

    void removerAula(Date dia, Integer horarioAula, String justificativa, Integer empresa) throws ServiceException;

    List<ProfessorAgendamentosDTO> agendamentos(Date dia) throws Exception;

    List<AgendamentosDTO> agendamentosMes(Integer mes, Integer ano, Integer empresaID) throws Exception;

    List<EventoAulaDTO> agendaDia(Date dia, String ctx) throws Exception;

    List<AulaAluno> aulasAluno(String ctx, Integer matricula, Date inicio, Date fim ) throws Exception;

    EventoAulaDTO aulaHorarioEvento(Date dia, Integer codigoHorario, String ctx) throws Exception;

    Integer nrAlunosHorarioDia(String ctx, Integer horario, Date dia) throws Exception;

    List<AlunoResponseTO> alunosHorarioDia(String ctx, AulaHorario aulaHorario, Date dia) throws Exception;

    void gerarOcupacao(String ctx, Date dia) throws Exception;

    public String incluirAtualizarConfigAgenda(String config, boolean mes) throws Exception;

    public ConfigAgenda obterConfigAgenda() throws Exception;

    Map<String, List<TurmaResponseDTO>> obterTodasTurmas(String ctx, HttpServletRequest request, Integer empresaId, Date dia, PeriodoFiltrarEnum periodo, FiltroTurmaDTO filtro) throws ServiceException;

    TurmaResponseDTO alterarSituacaoAlunoAula(Integer empresaId,
                                              Integer aulaHorario,
                                              Date dia,
                                              Integer matriculaAluno,
                                              Integer codigoPassivo,
                                              Integer codigoIndicado,
                                              AlunoVinculoAulaEnum alunoVinculoAula,
                                              Boolean desmarcar,
                                              HttpServletRequest request,
                                              OrigemSistemaEnum origem,
                                              Boolean autorizadoGestaoRede,
                                              String codAcessoAutorizado,
                                              Integer matriculaAutorizado) throws ServiceException;

    TurmaResponseDTO confirmarTodasPresencas(Integer empresaId, Integer aulaHorarioId, Date dia, HttpServletRequest request,
                                             OrigemSistemaEnum origem) throws ServiceException;

    AdicionarAlunoTurmaResponseDTO marcarAlunoHorarioConversasIA(String ctx,
                                                                Usuario usuario,
                                                                Integer empresaId,
                                                                Integer codigoHorarioTurma,
                                                                Integer matriculaAluno,
                                                                Date dia,
                                                                AcaoAulaTurmaEnum acao,
                                                                String origem,
                                                                HttpServletRequest request) throws ServiceException;

    AdicionarAlunoTurmaResponseDTO adicionarAlunoTurma(String ctx, Usuario usuario, Integer empresaId, Integer aulaHorario, Integer matriculaAluno, Date dia, Integer produtoId,
                                                       Integer aulaDesmarcarId, Date diaAulaDesmarcar, AcaoAulaTurmaEnum acao,  Boolean autorizado,
                                                       HttpServletRequest request) throws ServiceException;

    AdicionarAlunoTurmaResponseDTO adicionarAlunoTurma(String ctx, Usuario usuario, Integer empresaId, Integer aulaHorario, Integer matriculaAluno, Date dia, Integer produtoId,
                                                       Integer aulaDesmarcarId, Date diaAulaDesmarcar, AcaoAulaTurmaEnum acao,
                                                       String bookingId,
                                                       Boolean autorizado,
                                                       HttpServletRequest request) throws ServiceException;

    List<Map<String, Object>> turmasAlunoDia(Integer matriculaAluno, Date dia) throws ServiceException;

    List<ProdutoZWDTO> produtosZW(Integer empresaId) throws ServiceException;

    TurmaResponseDTO removerAlunoAula(String ctx, Usuario usuario, Integer empresaId, Integer matriculaAluno, Integer codigoPassivo, Integer aulaHorario,
                                      Date diaAulaHorario,
                                      AlunoVinculoAulaEnum alunoVinculoAula,
                                      String justificativa,
                                      HttpServletRequest request) throws ServiceException;

    TurmaResponseDTO removerAlunoAula(String ctx, Usuario usuario, Integer empresaId, Integer matriculaAluno, Integer codigoPassivo, Integer aulaHorario,
                                      Date diaAulaHorario,
                                      AlunoVinculoAulaEnum alunoVinculoAula,
                                      String justificativa,
                                      HttpServletRequest request,
                                      Boolean autorizadoGestaoRede,
                                      String codAcessoAutorizado,
                                      Integer matriculaAutorizado) throws ServiceException;

    Map<String, List<ServicoAgendamentoDTO>> servicosAgendado(Integer empresaId, HttpServletRequest request, Date diaReferencia, PeriodoFiltrarEnum periodo, FiltrosAgendamentosDTO filtros) throws ServiceException;

    String booking(String ctx, String json, Boolean validando) throws Exception;

    void iniciaThreadGympassRemoverSlot(String chave, Integer codigoTurma, Integer codigoHorario, Integer empresaZW, Integer empresaTR);

    void iniciaThreadGympass(String chave, Integer turmaID, Integer empresaZW, Integer empresaTR, TipoThreadGympassEnum tipoThreadGympassEnum);

    void sincronizarTurmasGympassBooking(String ctx, Integer empresaZW, Integer empresaTW) throws Exception;

    List<Map<String, Object>> horariosSincronizados(String ctx) throws Exception;

    String sincronizarTurmaGympassBooking(String ctx, Integer empresaZW, Integer empresaTW, Integer turmaId) throws Exception;

    String sincronizarTurmaGympassBooked(String ctx, Integer empresaZW, Date dia, Integer horarioAulaId, ConfigGymPass configGymPass) throws Exception;

    TurmaGymPassJSON obterDadosTurmaTurmaGymPassJSON(String ctx, Integer turma, Integer produtoGymPass) throws Exception;

    String excluirHorariosGympassBooking(String ctx, Integer empresaZW, Integer empresaTW,
                                         Integer turmaId, boolean excluirTodas) throws Exception;

    String excluirHorarioGympassBooking(String ctx, Integer empresaZW, Integer empresaTW, Integer codigoHorario, Integer codigoTurma) throws Exception;

    TurmaResponseDTO aulaDetalhada(String key, Integer empresaId,
                                          Integer aulaHorarioId,
                                          Date dia, Boolean consultarClientes,
                                   HttpServletRequest request) throws ServiceException;

    public void verificarBloqueados(String ctx,
                                    Long inicio,
                                    Long fim,
                                    Integer empresaId,
                                    List<AgendaTotalJSON> agendamentos);

    public List<Map<String, Object>> listarAlunosFaltosos(PaginadorDTO paginadorDTO, Integer empresaId, FiltroAlunosFaltososDTO filtro) throws ServiceException;
    void validarHorarioEquipamento(String ctx, Integer empresaId, Integer aulaHorarioId, String equipamento, Usuario u, String dia) throws Exception;
    void deletarHorarioEquipamentoAlunoSeExistir(String ctx, Integer empresaId, Integer horarioId, Integer alunoId, Date dia, String equipamento) throws ServiceException;

    void obterAtividadesCourseDoDiaIntegracaoSelfloops(String ctx, Integer empresaId, Integer horarioId, String dia) throws ServiceException;

    JSONArray obterAgendamentoAvaliacaoDia(String ctx, String dia, Boolean confirmado, Integer empresaZw) throws Exception;

    JSONArray obterAgendamentoAvaliacaoDiaConfirmadoUmaVezAno(String ctx, String dia, Integer empresaZw) throws Exception;
}

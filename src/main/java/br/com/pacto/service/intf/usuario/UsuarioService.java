package br.com.pacto.service.intf.usuario;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuario.UsuarioColaboradorResponseTO;
import br.com.pacto.bean.usuario.UsuarioColaboradorTO;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.controller.json.colaborador.UsuarioResponseTO;
import br.com.pacto.controller.json.usuario.UsuarioAppBasicoJSON;
import br.com.pacto.controller.json.usuario.UsuarioDependenteDTO;
import br.com.pacto.controller.json.usuario.UsuarioEmpresaApp;
import br.com.pacto.controller.json.usuario.UsuarioJSON;
import br.com.pacto.security.dto.UsuarioAppDTO;
import br.com.pacto.security.dto.UsuarioLoginV2DTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.agenda.ColaboradorVO;
import br.com.pacto.service.impl.agenda.PessoaVO;
import br.com.pacto.service.login.TokenDTO;
import br.com.pacto.util.AppInfo;
import org.json.JSONObject;
import servicos.integracao.zw.beans.UsuarioZW;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface UsuarioService {
    
    public static final String SERVICE_NAME = "usuarioService";

    Map<Integer, String> usernamesClientes(final String ctx, List<ClienteSintetico> clientes) throws ServiceException;

    public Usuario inserir(final String ctx, Usuario object) throws ServiceException;

    public Usuario obterPorId(final String ctx, Integer id, boolean aluno) throws ServiceException;

    public Usuario obterPorId(final String ctx, Integer id) throws ServiceException;

    public Usuario obterPorId(final String ctx, Integer id, Integer nivelmontardados) throws ServiceException;

    public Usuario alterar(final String ctx, Usuario object) throws ServiceException;
    
    public Usuario alterar(final String ctx, Usuario usuarioTreino, UsuarioZW usuarioZW) throws ServiceException;

    public void excluir(final String ctx, Usuario object) throws ServiceException;

    public List<Usuario> obterTodos(final String ctx, final Integer empresa) throws ServiceException;

    public List<Usuario> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Usuario> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Usuario obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    Usuario validarUsuario(final String chave, final String userName, final String password, final Boolean... isEncript)
            throws ServiceException;

    Usuario validarUsuario(final String chave, final String userName, final String password, final boolean aluno, Boolean isEncript)
            throws ServiceException;

    public Usuario validarUsuarioApp(final String chave, final String userName, final String password, final Boolean... isEncript)
            throws ServiceException;

    List<Usuario> validarUsuarioV2(final String chave, final String userName, final String password) throws ServiceException;

    public Usuario validarUserSemPreparar(final String chave, final String userName, final String password, final boolean aluno, Boolean... isEncript) throws Exception;

    List<Usuario> validarUserSemPrepararV2(final String chave, final String userName, final String password) throws Exception;

    void validarUsuarioColaborador(String ctx, UsuarioColaboradorTO usuarioColaboradorTO, Integer empresaId) throws Exception;

    public Usuario obterPorAtributo(final String ctx, final String atributo, final Object valor, final boolean cliente)
            throws ServiceException;

    public Usuario obterPorAtributo(final String ctx, final String atributo, final Object valor)
            throws ServiceException;
    
    public List<Usuario> obterUsuariosProfessores(String ctx, final Integer empresa) throws ServiceException;
    
    public void gravarAlteracaoUsuario(final String ctx, final Usuario usuario) throws ValidacaoException, ServiceException;
    
     public Usuario prepararUsuario(Usuario u, String chave, String password) throws Exception;
     
     public Usuario verificarUsoModulos(final String ctx, Usuario usuario);
     
     public List<Usuario> obterListaPorAtributo(final String ctx, final String atributo, Object valor)
            throws ServiceException;
     
     public Usuario gerarUsuario(final String ctx, final String userName, 
            final String senha, final ProfessorSintetico professor,
            final TipoUsuarioEnum tipo,
            final Perfil perfil) throws ServiceException;
     
     public Usuario consultarPorUserName(final String chave, final String userName) throws Exception;

    Usuario consultarPorUserNameSimples(final String chave, final String userName) throws Exception;

    Usuario consultarPorUserName(final String chave, final String userName, final Boolean verificarUsuarioZW, final Boolean aluno) throws Exception;

    Usuario criarOuConsultarSeExisteRecorrencia(final String chave) throws Exception;

    public Usuario consultarPorCliente(final String chave, final Integer cliente) throws Exception;
     
     public Usuario consultarPorProfessor(final String chave, final Integer professor) throws Exception;
     
     public List<String> consultarEmails(final String ctx) throws ServiceException;

    public Usuario consultarUsuarioSimples(final String ctx, final String userName) throws Exception;
    
    public Usuario consultarPorMatricula(final String chave, final Integer matricula) throws Exception;

    void removerUsuarioDaCache(final String chave, final Integer matricula);

    Usuario consultarPorCpf(final String chave, final String cpf) throws Exception;

    public Usuario validarUsuarioEmail(final String chave, final String email, final Boolean verificarUsuarioZW)
            throws ServiceException ;

    public List<Usuario> consultarPorNome(final String chave, final String nome, final Integer limit) throws Exception;

    List<UsuarioResponseTO> listaUsuarioColaborador(FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException;

    UsuarioColaboradorResponseTO obterUsuarioColaborador(Integer id, HttpServletRequest request) throws ServiceException;

    Usuario obterUsuarioPorColaborador(Integer colaboradorId) throws ServiceException;

    Usuario obterUsuarioPorColaborador(String ctx, Integer colaboradorId) throws ServiceException;

    UsuarioColaboradorResponseTO cadastrarUsuarioColaborador(String ctx, UsuarioColaboradorTO usuarioColaboradorTO, Integer empresaId, HttpServletRequest request) throws ServiceException;

    UsuarioColaboradorResponseTO alterarUsuarioColaborador(HttpServletRequest request, Integer id, UsuarioColaboradorTO usuarioColaboradorTO, Integer empresaId) throws ServiceException;

    Usuario consultarProColaborador(String contexto, Integer codigoColaborador) throws Exception;

    List<Usuario> consultarPorNomeColaborador(final String contexto, final String nomeColaborador) throws Exception;

    public Usuario recuperarSenhaPorEmail(String key, String email) throws Exception;

    void adicionarUsuarioServicoDescobrir(String ctx, String email) throws IOException;

    String urlZW(String usuarioOamd, String urlLogin, Integer empresa, String sessionId, String tokenOamd) throws ServiceException;

    String obterUsernameAlunoPorFicha(String ctx, Integer fichaId) throws ServiceException;

    String registrarZillyonWebLoginTreinoWeb(String ctx, Usuario usuario);

    String urlSolicitarAtendimento(Integer empresaId, HttpServletRequest request) throws ServiceException;
    List<UsuarioAppBasicoJSON> obterIdentidadeDadosBasicosCelularApp(String key, String ddi, String ddd, String telefone) throws ServiceException;

    Usuario consultarColaboradorPorUsername(final String contexto, final String username) throws ServiceException;

    Usuario gerarUsuarioApp(String ctx, String userName,
                            String senha, ProfessorSintetico professor,
                            ClienteSintetico cliente,
                            TipoUsuarioEnum tipo) throws ServiceException;

    Usuario validarUsuarioMovelApp(String chave, String userName, boolean aluno)
            throws ServiceException;

    Integer consultarCodZWPorCodCliente(String ctx, Integer codUsuario) throws ServiceException;

    Integer consultarCodProfessorPorCodigoExterno(String ctx, String codigoExterno, Integer codigoEmpresaZw) throws ServiceException;

    void naoApresentarHoje(String ctx, Integer usuario) throws Exception;

    Boolean podeApresentarNotificacaoFimTreino(String ctx, Integer usuario) throws ServiceException;

    List<UsuarioEmpresaApp> empresasApp(String ctx, Usuario u);

    Usuario consultarPorCodigoExternoAndEmpresaZW(final String ctx, final String codigoExterno, final Integer empresaZW) throws ServiceException;

    Usuario validarUsuarioLoginV2(final UsuarioLoginV2DTO dto) throws ServiceException;

    Usuario consultarPorUsuarioZWAndEmpresaZW(final String ctx, final Integer usuarioZW, final Integer empresaZW) throws ServiceException;

    TokenDTO solicitarCodigoVerficacaoEmail(String chave, Integer idUsuario, String email, HttpServletRequest request) throws ServiceException;

    void processarToken(String chave, Integer idUsuario, TokenDTO tokenDTO, HttpServletRequest request) throws ServiceException;

    void recuperarSenhaNovoLogin(String chave, Integer idUsuario, HttpServletRequest request) throws ServiceException;

    Usuario criarOuConsultarSeExisteImportacao(final String chave, ColaboradorTO colaboradorTO, Integer empresaId,
                                               HttpServletRequest request) throws Exception;


    void desvincularUsuarioNovoLogin(String chave, Integer idUsuario, HttpServletRequest request) throws ServiceException;

    void registrarUsoApp(String chave, UsuarioAppDTO usuarioAppDTO) throws Exception;

    Usuario userIA(String chave,
                   Integer empresaId,
                   JSONObject tokenDecode) throws Exception;

    Boolean temAcessoAcademia(UsuarioJSON uJSON, String ctx) throws Exception;

    void alterarFotoUsuarioColaborador(String key, String url, int valorVersao, Integer codUsuarioZW, Boolean atualizaFotoZW) throws Exception;
    String nomeEmpresaClienteSintetico(String chave, Usuario u) throws ServiceException;

    void preencheFotoEVersaoFotoApp(String ctx, Usuario u, Boolean atualizaSessao) throws Exception;

    void alterarFotoCliente(String key, String fotoKeyApp, Integer versaoFotoApp, Integer codigo, String matricula, Boolean atualizaFotoZW) throws Exception;

    String urlFotoAppTreino(String ctx, Integer codUsuarioZW) throws Exception;

    void preencheFotoEVersaoFotoAppColaborador(String key, Usuario obj) throws Exception;

    void alterarPerfilUsuario(String ctx, Integer usuarioZW, Integer perfilCodigo) throws Exception;

    Integer consultarUsuarioMovelPorEmail(String ctx, String email, Boolean cliente) throws Exception;

    void alterarTipoTw(String ctx, Integer usuarioZW, Integer tipoTw) throws Exception;

    List<UsuarioAppBasicoJSON> obterIdentidadeDadosBasicosEmailApp(String ctx, String email) throws ServiceException;
    String carregaFotoPessoaZW(Integer matricula, String ctx);

    Usuario consultaPorId(String chave, Integer codUsuario) throws Exception;

    String obterUsernameUsuarioZW(Integer usuarioZW, String chave) throws Exception;

    void consultarUsernameEEmailZW(String ctx, UsuarioJSON uJSON) throws Exception;

    void criarUsuarioTreinoIA(String ctx) throws Exception;

    List<Usuario> consultarUsuarioZillyon(String ctx, String username) throws Exception;

    List<PessoaVO> consultarPessoaZillyon(String ctx, String nome) throws Exception;

    List<ColaboradorVO> consultarColaboradorZillyon(String ctx, Integer pessoaCodigo, Integer empresa) throws Exception;

    Integer consultarCodigoColaboradorZillyon(String ctx, Integer pessoaCodigo, Integer empresa) throws Exception;

    Usuario consultarUsuarioPorUserNameEEmpresa(final String ctx, final String userName, final Integer codZW) throws ServiceException;

    void inserirPessoaZW(String ctx, PessoaVO pessoa) throws Exception;

    void inserirColaboradorZW(String ctx, Integer pessoaCodigo, Integer empresaId) throws Exception;

    void inserirTipoColaboradorZW(String ctx, Integer colaboradorCodigo) throws Exception;

    void inserirUsuarioZW(String ctx, Usuario usuario) throws Exception;

    void inserirUsuarioPerfilAcessoZW(String ctx, Integer empresa, Integer usuario, Integer perfilacesso) throws Exception;

    void inserirUsuarioEmailZW(String ctx, Integer usuarioCodigo, String email, Boolean verificado) throws Exception;

    void inserirDadosUsuarioZW(String ctx, Integer usuarioCodigo, Integer colaboradorCodigo, Integer perfilCoordenador) throws Exception;

    UsuarioEmail consultarUsuarioEmailZW(String ctx, String email) throws Exception;

    List<UsuarioDependenteDTO> obterDependentes(String ctx, Usuario u);

    Usuario consultarPorCodigoColaborador(String ctx, Integer codigoColaborador) throws ServiceException;

    String consultaCPFClienteZW(Integer matricula, String ctx);

    String consultaCPFColaboradorZW(String ctx, Integer codigoColaborador);

    void registrarUsoAppNoLogin(String ctx, Usuario u, String idClienteApp, AppInfo appInfo);
}

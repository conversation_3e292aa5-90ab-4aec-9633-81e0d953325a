package br.com.pacto.service.intf.agendatotal;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.agendamento.AgendaDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.FiltroTurmaDTO;
import br.com.pacto.controller.json.agendamento.PeriodoFiltrarEnum;
import br.com.pacto.service.exception.ServiceException;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

public interface AgendaCardsService {

    String sqlAgendaAulas = "select a.descricao as ambiente, h.horainicial, h.codigo, \n" +
            "h.dia<PERSON><PERSON><PERSON>, nrmaxi<PERSON>al<PERSON>,\n" +
            "(select count(codigo) from matriculaalunohorarioturma m " +
            " where current_date between datainicio and datafim and \n" +
            " horarioturma = h.codigo) as ocupacao,\n" +
            "h.horafinal, t.modalidade, t.identificador as turma, p.nome as professor\n" +
            "from horarioturma h \n" +
            "inner join colaborador c on c.codigo = h.professor \n" +
            "inner join pessoa p on p.codigo = c.pessoa \n" +
            "inner join turma t on t.codigo = h.turma \n" +
            "inner join ambiente a on h.ambiente = a.codigo \n" +
            "where t.usuariodesativou is null\n" +
            "and h.situacao = 'AT' and h.diasemananumero in(:diasemana)\n" +
            "order by h.horainicial ";

    AgendaCardsDTO montarAgenda(Date inicio, Date fim) throws ServiceException;

    AgendaCardsDTO montarCards(HttpServletRequest request, Integer empresaId, Date dia,
                               PeriodoFiltrarEnum periodo, FiltroTurmaDTO filtro) throws ServiceException;

    List<AgendaDisponibilidadeDTO> disponibilidades(Integer empresaId,
                                                    Date dia,
                                                    String horaSelecionada,
                                                    String horaSelecionadaFinal,
                                                    String tipo, PaginadorDTO paginadorDTO) throws ServiceException;

    List<AgendaDisponibilidadeDTO> disponibilidadesV2(Integer empresaId,
                                                    Date dia,
                                                    String horaSelecionada,
                                                    String horaSelecionadaFinal,
                                                    String tipo, PaginadorDTO paginadorDTO) throws ServiceException;

    Integer obterIdAula(Integer empresa, Integer horarioTurma);
}

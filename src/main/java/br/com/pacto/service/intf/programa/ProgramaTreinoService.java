/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.programa;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.avaliacao.AnamneseTreinoPorIADTO;
import br.com.pacto.bean.avaliacao.evolucao.EvolucaoFisicaDTO;
import br.com.pacto.bean.badge.Badge;
import br.com.pacto.bean.cliente.ClienteAcompanhamentoResponseTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.ficha.AtividadeFichaResponseTO;
import br.com.pacto.bean.ficha.CategoriaFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.Compromisso;
import br.com.pacto.bean.programa.ObjetivoPrograma;
import br.com.pacto.bean.programa.OrigemExecucaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamento;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.programa.ProgramaTreinoResponseTO;
import br.com.pacto.bean.programa.ProgramaTreinoResumo;
import br.com.pacto.bean.programa.ProgramaTreinoTO;
import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.aluno.DetalheTreinoAlunoDTO;
import br.com.pacto.controller.json.aluno.ProgramaTreinoAlunoResponseDTO;
import br.com.pacto.controller.json.aulaDia.AulaAlunoDTO;
import br.com.pacto.controller.json.aulaDia.HistoricoTreinosVO;
import br.com.pacto.controller.json.programa.ConsultaTreinoDTO;
import br.com.pacto.controller.json.programa.FiltroProgramaTreinoJSON;
import br.com.pacto.controller.json.programa.ProgramaDeTreinoGeradoPorIADTO;
import br.com.pacto.controller.json.programa.ProgramaFichaJSON;
import br.com.pacto.controller.json.programa.read.ProgramaVersaoJSON;
import br.com.pacto.controller.json.programa.write.ProgramaWriteAppJSON;
import br.com.pacto.controller.json.programa.write.ProgramaWriteJSON;
import br.com.pacto.objeto.to.AtividadeFichaEndpointTO;
import br.com.pacto.objeto.to.AtividadeFichaTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.cliente.perfil.HorariosQueTreinouProgramaAtual;
import br.com.pacto.service.intf.cliente.HistoricoPresencasVO;
import org.json.JSONArray;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface ProgramaTreinoService {

    static final String SERVICE_NAME = "ProgramaTreinoService";

    Compromisso addCompromisso(ProgramaTreino programa, int diaSemana, final String horario);

    List<Compromisso> addCompromisso(ProgramaTreino programa, int[] diaSemana, final String horario);

    boolean remove(Compromisso compromisso, final ProgramaTreino programa);

    ObjetivoPrograma addObjetivo(final String ctx, ProgramaTreino programa, final String nome) throws ServiceException;

    ProgramaTreinoFicha addProgramaFicha(final String ctx, ProgramaTreino programa,
                                         Ficha ficha,
                                         TipoExecucaoEnum tipoExecucao,
                                         String versao,
                                         List<String> diaSemana) throws ServiceException;

    AtividadeFicha addAtividadeFicha(final String ctx, ProgramaTreinoFicha programaTreinoFicha,
                                     final String atividade, Integer ordem) throws ServiceException;

    CategoriaFicha addCategoriaFicha(final String ctx, Ficha ficha, final String nome) throws ServiceException;

    void validarSituacao(final String ctx, ClienteSintetico cliente) throws ServiceException;

    ProgramaTreino inserir(final String ctx, ProgramaTreino object) throws ServiceException, ValidacaoException;

    ProgramaTreino obterPorId(final String ctx, Integer id) throws ServiceException;

    ProgramaTreino alterar(final String ctx, ProgramaTreino object, final ProfessorSintetico professor) throws ServiceException, ValidacaoException;

    void excluir(final String ctx, ProgramaTreino object, final String username, boolean registrarLog) throws ServiceException;

    List<ProgramaTreino> obterTodos(final String ctx) throws ServiceException;

    List<ProgramaTreino> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    List<ProgramaTreino> obterPorParam(final String ctx, String query,
                                       Map<String, Object> params, int max, int index)
            throws ServiceException;

    ProgramaTreino obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    ProgramaTreino obterProgramaVigente(final String ctx, final ClienteSintetico cliente)
            throws ServiceException;

    ProgramaTreino obterUltimoProgramaVigente(final String ctx, final ClienteSintetico cliente)
            throws ServiceException;

    ProgramaTreino obterUltimoProgramaVigenteComOuSemTreinoRealizado(String ctx, Integer cliente)
            throws ServiceException;

    ProgramaVersaoJSON obterVersaoUltimoProgramaVigente(final String ctx,
                                                        final Integer codCliente, final Date diaAtual) throws ServiceException;

    Integer obterCodigoProgramaVigente(final String ctx, final Integer codCliente)
            throws ServiceException;

    ProgramaTreinoFicha obterProgramaTreinoFicha(final String ctx, final Integer programa, final Integer ficha)
            throws ServiceException;

    List<Number> obterCodigoProgramaTreinoFicha(final String ctx, final Integer programa, final Integer ficha) throws ServiceException;

    List<ProgramaTreinoFicha> obterFichaPorProgramaTreino(final String ctx, final Integer programa) throws ServiceException;

    Serie obterSerie(final String ctx, final Integer idSerie) throws ServiceException;

    SerieRealizada obterSerieRealizadaHoje(final String ctx, final Integer idSerie) throws ServiceException;

    List<TreinoRealizado> obterUltimosTreinosRealizados(final String ctx, final Integer idPrograma,
                                                        final int maxResults) throws ServiceException;

    List<TreinoRealizado> obterUltimosTreinosRealizadosCliente(final String ctx, final Integer matricula,
                                                               final int maxResults) throws ServiceException;

    List<SerieRealizada> obterSeriesDoTreino(final String ctx, final Integer idTreinoRealizado) throws ServiceException;

    List<SerieRealizada> obterSeriesDaFicha(final String ctx, final Integer idFicha) throws ServiceException;

    List<Date> obterDatasSeriesAgrupadasDaFicha(final String ctx, final Integer idFicha, final int maxResults) throws ServiceException;

    List<SerieRealizada> obterSeriesDaFichaPorData(final String ctx, final Integer idFicha, final Date data) throws ServiceException;

    List<ProgramaTreino> obterProgramasPorCliente(final String ctx, final Integer codigoCliente, final Date inicio, final Date fim, final Integer codigoProgramaTreino, Integer maxResults) throws ServiceException;

    List<ProgramaTreino> obterProgramasPorClienteZW(final String ctx, final Integer codigoCliente, final Date inicio, final Date fim, final Integer codigoProgramaTreino, Integer maxResults) throws ServiceException;

    ProgramaTreinoFicha obterProgramasFicha(final String ctx, final Integer programa, final Integer ficha)
            throws ServiceException;

    List<ProgramaTreinoFicha> obterProgramasFichasPorPrograma(String ctx, Integer programaId) throws ServiceException;

    ProgramaTreinoResumo obterResumo(final String ctx, final ClienteSintetico cliente) throws ServiceException;

    void inserirSerieRealizada(final String ctx, SerieRealizada serieRealizada) throws ServiceException;

    void atualizarSerieRealizada(final String ctx, final Integer idSerie,
                                 final String valor1, final String valor2, final Integer duracao, boolean forcarCargaPraFicha) throws ServiceException;

    SerieRealizada  inserirTreinoRealizado(final String ctx, final String username,
                                           ProgramaTreino programa, final String idFicha, final String inicio,
                                           final String fim, final String idAtividade, Serie serie,
                                           AtividadeFicha atividadeFicha, OrigemExecucaoEnum origem,
                                           ProgramaTreinoFicha programaTreinoFicha)
            throws ServiceException;

    TreinoRealizado inserirTreinoRealizadoSemSerie(final String ctx, ProgramaTreino programa,
                                                   final String idFicha, final String inicio,
                                                   final String fim, OrigemExecucaoEnum origem,
                                                   ProgramaTreinoFicha programaTreinoFicha,
                                                   final String chaveExecucao,
                                                   final String unidadeExecucao, HttpServletRequest request)
            throws ServiceException;

    void resetarCampoSerieRealizadaDaSerie(final String ctx, final Integer idFicha) throws ServiceException;

    ProgramaTreinoAndamento ************************(final String ctx, final String idPrograma,
                                                     final String idFicha, final Date dataBase,
                                                     boolean fichaConcluida,String origem,
                                                     final String chaveExecucao,
                                                     final String unidadeExecucao, HttpServletRequest request) throws ServiceException;

    Map<Integer, String> obterUltimosTreinosPrograma(final String ctx, final Integer codigoPrograma) throws Exception;

    ProgramaTreinoAndamento concluirTreinoEmAndamento(final String ctx, final String username, final String idPrograma, final int idFicha,
                                                      final int dia, final String nota, final int tempo, final String comentario, Date diaDoTreino) throws ServiceException;

    ProgramaTreinoFicha alterarProgramaFicha(final String ctx, ProgramaTreinoFicha object) throws ServiceException;

    void excluirProgramaFicha(final String ctx, ProgramaTreinoFicha object) throws ServiceException;

    ProgramaTreinoFicha inserirProgramaFicha(final String ctx, ProgramaTreinoFicha object) throws ServiceException;

    void atualizar(final String ctx, ProgramaTreino object) throws ServiceException;

    List<ProgramaTreinoFicha> obterFichaPorPrograma(final String ctx, Integer codigo,
                                                    Integer codProgramaFicha, boolean todos) throws ServiceException;

    Integer obterFichaAtual(String ctx, Integer codigoPrograma, TreinoRealizado ultimoTreino) throws ServiceException;

    Integer obterProximaFicha(String ctx, Integer codigoPrograma, Ficha fichaAtual) throws ServiceException;

    Double calcularAndamentoFicha(final String ctx, Integer ficha, Integer treinoRealizado) throws ServiceException;

    List<Badge> calcularBadges(final String ctx, ProgramaTreino programa) throws ServiceException;

    TreinoRealizado obterTreinoEmAndamento(final String ctx, final Integer idCliente,
                                           final int idProgramaFicha,
                                           final String inicio) throws Exception;

    List<TreinoRealizado> obterTreinosRealizado(final String ctx,
                                                final Integer codigoPrograma, Date inicio, Date fim,
                                                final Integer codigoCliente, final Integer maxResults, final Integer index)
            throws ServiceException;

    String marcarTreinoRealizadoFichaAvulsa(final String ctx, final Integer ficha, final String usuario,final String dia) throws ServiceException;

    List<TreinoRealizado> obterUltimosTreinosRealizadosFicha(final String ctx, final Integer idProgramaFicha,
                                                             final int maxResults) throws ServiceException;

    TreinoRealizado obterUltimoTreinoRealizadoFicha(final String ctx, final Integer idFicha) throws ServiceException;

    List<TreinoRealizado> obterTodosTreinosRealizadoPrograma(final String ctx,
                                                             final Integer codigoPrograma, Date inicio, Date fim,
                                                             final Integer codigoCliente, final Integer maxResults, final Integer index)
            throws ServiceException;

    void povoarAndamentoTreino(String ctx) throws ServiceException;

    void verificarSeriesRealizadas(String ctx, ProgramaTreinoFicha ficha, TreinoRealizado treino) throws ServiceException;

    ProgramaTreinoFicha obterProgramaTreinoFichaPorFicha(final String ctx, final Integer ficha) throws ServiceException;

    ProgramaTreinoAndamento obterAndamento(final String ctx, final ProgramaTreino programa) throws ServiceException;

    void obterObjetivosDoPrograma(String ctx, ProgramaTreino programa) throws ServiceException;

    void obterRestricoesPrograma(String ctx, ProgramaTreino programa) throws ServiceException;

    void prepararPersistirRestricoes(String ctx, ProgramaTreino programa) throws Exception;

    void atualizarVersaoPrograma(String ctx, ProgramaTreino programa) throws Exception;

    void atualizarVersaoProgramaSemGerarLog(String ctx, ProgramaTreino programa) throws Exception;

    void atualizarVersaoProgramas(String ctx, List<Integer> codsPrograma) throws Exception;

    Ficha gravarFichaSemTratarExcecao(String ctx, boolean cadastroPrograma,
                                      TipoExecucaoEnum tipoExecucaoEnum, List<String> diasSemana, ProgramaTreino programa, Ficha ficha, Integer codigoCategoria,
                                      ProgramaTreinoFicha fichaAntesAlteracao, boolean adicionandoFichaPreDefinida,
                                      final ProfessorSintetico professor, boolean isMobile) throws Exception;

    ProgramaTreino gravarProgramaSemTratarExcecao(final String ctx,
                                                  ProgramaTreino programa, final ProfessorSintetico professor)
            throws ServiceException, ValidacaoException;

    void calcularAulasPrevistas(final ProgramaTreino programa) throws ServiceException;

    String importarProgramasJson(String ctx, List<ProgramaFichaJSON> programasJSON) throws ServiceException;

    Long obterTempoUtil(final String ctx, final Integer treinoRealizado) throws ServiceException;

    void atualizarTempoUtilTreinoRealizado(final String ctx, final Integer treinoRealizado) throws ServiceException;

    void desabilitarNotificacoesTreinoVencido(final String ctx) throws ServiceException;

    void desabilitarNotificacoesTreinoVencidoCliente(final String ctx,final Integer codigoCliente) throws ServiceException;

    List<ProgramaTreino> consultarPrevistosRenovarNosProximosDias(final String ctx, final Date dataBase,
                                                                  Integer professor, Integer aluno,
                                                                  TipoLembreteEnum tipoLembrete) throws ServiceException;

    void ordenarFichas(final String ctx, ProgramaTreino programa) throws ServiceException;

    boolean clienteTemProgramaVigente(final String ctx, final ClienteSintetico cliente) throws ServiceException;

    void calcularTerminoPrevisto(final String ctx, final ProgramaTreino programa) throws ServiceException;

    List<ProgramaTreino> consultarAtrasados(final String ctx, final Date dataBase,
                                            Integer professor, Integer aluno,
                                            TipoNotificacaoEnum tipoNotificacao,
                                            TipoLembreteEnum tipoLembrete) throws ServiceException;

    ProgramaTreino renovarProgramaTreino(final String ctx, final ProgramaTreino programa, final ProfessorSintetico progessorMontou) throws ServiceException;

    void ordenarProgramaTreinoFichas(final String ctx, final Integer codigoPrograma, List<ProgramaTreinoFicha> listaFichas) throws ServiceException;

    ProgramaTreino consultarUltimoTreinoDataBaseAluno(final String ctx, final ClienteSintetico cliente, final Date data) throws ServiceException;

    ProgramaTreino consultarSomenteDataTreinoAtual(final String ctx, final ClienteSintetico cliente) throws ServiceException;

    void gravarHistoricoRevisoes(final String ctx, final ProgramaTreino programa,
                                 final String justificativa,
                                 final Date proximaRevisao,
                                 final ProfessorSintetico professor) throws Exception;

    void carregarHistoricoRevisoes(
            final String ctx, ProgramaTreino programa) throws Exception;

    boolean clienteTemProgramaVencido(final String ctx, final ClienteSintetico cliente) throws ServiceException;

    String atualizarAndamentoAluno(final String ctx, final String matricula) throws ServiceException;

    void atualizarNrTreinosRealizados(final String ctx, final Integer codPrograma,
                                      final Integer nrTreinos) throws ServiceException;

    ProgramaTreino prepararPersistenciaJSON(final String ctx, final ProgramaWriteJSON pw)
            throws ServiceException;

    ProgramaTreino gerarProgramaDefault(String ctx, ClienteSintetico cliente, final Usuario usuario, Integer codigoColaborador, ProgramaTreinoTO programaTreinoTO) throws ServiceException;

    ProgramaTreino alterar(final String ctx, ProgramaTreino object) throws ServiceException, ValidacaoException;

    AtividadeFichaTO salvarAtividadeFichaRapida(String ctx, Atividade atividadeSelecionada,
                                                Serie serie, Integer nrSeries, String fichaSelecionada,
                                                Ficha ficha, Integer ordem, boolean alternar) throws ServiceException;

    AtividadeFicha salvarAtividadeFicha(String ctx, Atividade atividadeSelecionada,
                                        Serie serie, Integer nrSeries, String fichaSelecionada,
                                        Ficha ficha, Integer ordem, boolean alternar) throws ServiceException;

    Integer atualizarNrTreinosRealizados(String ctx, Integer idPrograma) throws ServiceException;

    void carregarHistoricoRevisoesSincronizacao(final String ctx, ProgramaTreino programa) throws Exception;

    void excluirHistoricoRevisaoSincronizacao(String ctx, ProgramaTreino object) throws Exception;

    void notificarOuvintes(String ctx, final ProgramaTreino programa);

    void notificarOuvintes(String ctx, final Integer codigoCliente, final Integer codigoColaborador, HttpServletRequest request);

    ProgramaTreinoAndamento consultarAndamentoSimples(String ctx, Integer programa) throws ServiceException;

    List<TreinoRealizado> obterTreinosRealizados(final String ctx,
                                                 final Integer codigoPrograma, Date inicio, Date fim,
                                                 final Integer codigoCliente) throws Exception;

    void atualizarSerieComBaseNaSerieRealizada(final String ctx, SerieRealizada serieRealizada);

    String pesquisarTreinoRelaizadoPorClientes(final String ctx,String codigoClientes,Date data) throws ServiceException;

    void refresh(String ctx, ProgramaTreino object) throws ServiceException;

    void refresh(String ctx, List<ProgramaTreino> object) throws ServiceException;

    ProgramaTreinoFicha obterProgramaTreinoFicha(final String ctx, final Integer ficha) throws ServiceException;

    ProgramaTreinoFicha novaFicha(final String ctx, final List<ProgramaTreinoFicha> fichas,
                                  ProgramaTreino programa, Ficha predefinida) throws Exception;


    void removerFichaPredefinida(final String ctx, List<ProgramaTreinoFicha> fichas,
                                 Ficha predefinida) throws Exception;

    List<ProgramaTreino> programasPredefinidos(String ctx) throws Exception;

    void tornarPreDefinido(String key, ProgramaTreino programa) throws ServiceException;

    ProgramaTreino escolherPreDefinido(String key, ClienteSintetico cliente, ProgramaTreino predefinido,
                                       Usuario usuario, Boolean verificarConfiguracaoSistema, Integer colaborador, ProgramaTreinoTO escolherPreDefinido) throws Exception;

    List<ProgramaTreinoResponseTO> obterProgramasPreDefinidos(PaginadorDTO paginadorDTO, FiltroProgramaTreinoJSON filtros, Boolean app) throws ServiceException;

    List<ProgramaTreinoResponseTO> obterProgramasPreDefinidosPorChave(String ctx) throws ServiceException;

    ProgramaTreinoResponseTO consultarProgramaTreino(Integer programaTreinoId, String chaveFranqueadora) throws ServiceException;
    Integer consultarProgramaTreinoAnterior(Integer clienteCodigo, Integer programaTreinoId, String chaveFranqueadora) throws ServiceException;

    ProgramaTreinoResponseTO criarProgramaTreino(Integer empresa,
                                                 ProgramaTreinoTO programaTreinoTO,
                                                 Integer preDefinidoId,
                                                 String chaveFranqueadora,
                                                 Boolean renovarAtual, HttpServletRequest request, Integer origemProgramaTreino) throws ServiceException;

    ProgramaTreino criarProximoProgramaTreinoJSON(String ctx, Integer empresa,
                                                      ClienteSintetico clienteSintetico, ProgramaTreino base) throws Exception;

    String alterarProgramaTreinoJSON(String ctx, Integer empresa,
                                     ProgramaTreino base) throws ServiceException;

    ProgramaTreinoResponseTO alterarProgramaTreino(Integer empresa,
                                                   ProgramaTreinoTO programaTreinoTO, HttpServletRequest request) throws ServiceException;

    void alterarEmRevisaoProfessor(Integer codigoPrograma, Boolean emRevisaoProfessor) throws ServiceException;

    void tornarProgramaPreDefinido(Integer programaTreinoId) throws ServiceException;

    void excluirProgramaTreino(Integer programaTreinoId, HttpServletRequest request) throws ServiceException;
    void excluirProgramaTreino(Integer programaTreinoId, String ctx) throws ServiceException;

    AtividadeFichaResponseTO criarAtividadeFicha(AtividadeFichaEndpointTO atividadeFichaTO) throws ServiceException;

    AtividadeFichaResponseTO atualizarAtividadeFicha(AtividadeFichaEndpointTO atividadeFichaTO) throws ServiceException;

    void removerAtividadeFicha(Integer atividadeFichaId) throws ServiceException;

    void padronizarSeries(Integer fichaId, AtividadeFichaEndpointTO atividadeFichaEndpointTO) throws ServiceException;

    DetalheTreinoAlunoDTO detalheTreinamentoCliente(Integer alunoId) throws ServiceException;

    JSONArray obterProgramasPorClienteJson(final String ctx)
            throws ServiceException;

    ProgramaTreinoResponseTO updateCalculosAulasPrevistas(Integer programaId, String campoAlterado, String value, Long inicio, Long termino, Integer totalTreinos, Integer qtdDiasSemana) throws ServiceException;

    ProgramaTreino prepararPersistenciaAppJSON(final String ctx, final ProgramaWriteAppJSON pw, Integer origem)
            throws ServiceException;

    Integer obterNumeroAtividadesRealizadas(final String ctx, final Integer codTreinoRealizado)
            throws ServiceException;

    void atualizarProfessorCarteiraProgramaVirgente(String ctx, String matricula) throws ServiceException;

    ProgramaTreinoResponseTO programaConflitante(ProgramaTreinoTO programaTreinoTO) throws ServiceException;

    void atualizarSituacaoProgramaPredefinido(Integer id, Integer situacao, HttpServletRequest request) throws ServiceException;

    ProgramaTreinoResponseTO criarProgramaPreDefinido(ProgramaTreinoTO programaTreinoTO, HttpServletRequest request) throws ServiceException;

    String importarProgramaPreDefinido(String ctxOrigem, String ctxDestino, HttpServletRequest request) throws ServiceException;

    List<ProgramaTreinoResponseTO> obterProgramasPreDefinidosSlim(Boolean rede, String nome, String chaveRede,
                                                                  Boolean consultaFranqueadora) throws ServiceException;

    AtividadeFichaResponseTO addAtividadeFicha(Integer fichaId, Integer atividadeId) throws ServiceException;

    List<ProgramaTreinoAlunoResponseDTO> obterProgramasAluno(FiltroProgramaTreinoJSON filtros,
                                                             PaginadorDTO paginadorDTO,
                                                             Integer matricula) throws ServiceException;

    void acao(final ProgramaTreino programa, TipoRevisaoEnum tipo);

    void leaveAcao();


    Integer obterQuantidadeExecucoesTreinoRealizados(final String ctx, final Integer codigoPrograma) throws ServiceException;

    ClienteAcompanhamentoResponseTO consultarAcompanhamento(Integer codigoPessoa, HttpServletRequest request) throws ServiceException;

    Integer obterQuantidadeFichasPorPrograma(String ctx, Integer codPrograma) throws Exception;

    Integer obterQuantidadeExecucoesFichaAluno(String ctx, Integer codFicha) throws Exception;

    HorariosQueTreinouProgramaAtual obterHorariosQueTreinouProgramaAtual(String ctx, Integer matricula) throws ServiceException;

    EvolucaoFisicaDTO gruposMuscularesTrabalhadosPeriodo(String ctx, String dataInicial, String dataFinal, Integer codigoCliente) throws Exception;

    void enviarProgramaTreino(Integer empresa,
                              Integer codigoPrograma,
                              List<Integer> clientes) throws ServiceException;

    boolean temProgramaVigenteByMatricula(String chave, String matricula) throws ServiceException;

    List<AulaAlunoDTO> consultarAulasAgendadasPorAluno(Integer matricula, String dataInicio, String dataFim, String ctx, Integer contrato) throws ParseException, Exception;

    String criaProgramaTreinoGeradoPorIA(String ctx, ProgramaDeTreinoGeradoPorIADTO programaDeTreinoGeradoPorIADTO, String origem) throws Exception;

    String preparaProgramaTreinoPorIA(AnamneseTreinoPorIADTO anamneseTreinoPorIADTO, String ctx) throws Exception;

    Integer obterQuantidadeTreinosRealizadosPorAluno(String ctx, Integer id, Date dataInicio, Date dataAtual);

    List<AulaAlunoDTO> consultarAulasAgendadasPorAlunoV2(Integer matricula, String dataInicio, String dataFim, String ctx, Integer contrato) throws Exception;

    List<ConsultaTreinoDTO> obterProgramaTreinoGeradoPorIA(String ctx) throws ServiceException;

    String atualizarBancoAtividadesIA(String ctx) throws Exception;

    HistoricoPresencasVO consultarAulasAgendadasPorAlunoTotaisEMesAtualEQtdAulasPorSemanaConsecutiva(Integer matricula, String ctx, Integer empresa);

    void registrarWorkoutTreinoIa(String ctx, Integer nivelIniciante,Integer nivelIntermediario,Integer nivelAvancado) throws Exception;

    HistoricoTreinosVO dadosTreinos(String ctx, Integer codigoCliente, String periodoInicio, String periodoFinal) throws Exception;
}

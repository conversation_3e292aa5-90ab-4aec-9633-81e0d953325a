/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.empresa;

import br.com.pacto.bean.atividade.EmpresaBasicaResponseTO;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.controller.json.empresa.CreateClientDTO;
import br.com.pacto.controller.json.empresa.EmpresaDTO;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.controller.json.empresa.ResponseActiveEmpresaDTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
import servicos.integracao.adm.client.EmpresaWS;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * <AUTHOR>
 */
public interface EmpresaService {

    public static final String SERVICE_NAME = "empresaService";

    public EmpresaJSON validarChave(final String ctx) throws ServiceException;

    public void persistirEmpresasZW(final String ctx, List<EmpresaWS> empresas) throws ServiceException;

    public Empresa inserir(final String ctx, Empresa object) throws ServiceException;

    public Empresa alterar(final String ctx, Empresa object) throws ServiceException;

    public EmpresaDTO alterarEmpDTO(final String ctx, EmpresaDTO object) throws ServiceException;
    
    public void excluir(final String ctx, Empresa object) throws ServiceException;

    public Empresa obterPorId(final String ctx, Integer id) throws ServiceException;

    public EmpresaDTO obterPorIdEmpDTO(final String ctx, Integer id) throws ServiceException;

    public Empresa obterPorIdZW(final String ctx, Integer id) throws ServiceException;

    public List<Empresa> obterTodos(final String ctx) throws ServiceException;

    public Empresa persistirEmpresasZW(final String ctx, final Integer idEmpresaWS,
            final Integer idUsuarioZW) throws ServiceException;
    
    public Empresa obterEmpresaTreinoIndependente(final String ctx) throws ServiceException;

    List<EmpresaBasicaResponseTO> listarEmpresas() throws ServiceException;

    void inicializarEmpresa(String ctx, String nomeEmpresa, ColaboradorTO colaborador, HttpServletRequest request) throws ServiceException;

    ResponseActiveEmpresaDTO ativarEmpresa(String ctx, CreateClientDTO colaborador, HttpServletRequest request) throws ServiceException;


    String health(String chave) throws Exception;

    Empresa obterPorCodFinanceiro(final String ctx, Integer codFinanceiro) throws ServiceException;

    String obterFusoHorarioEmpresa(String ctx, Integer empresa) throws ServiceException;

    void reverterAlteracoesProjetoUsuario(String ctx) throws Exception;

    Empresa obterPorAributo(String ctx, String atributo, Object valor) throws ServiceException;

    List<String> obterTotens(String ctx, Integer empresa) throws Exception;

    ConfiguracaoConvenioEmpresaVO obterConfiguracaoConvenioEmpresa(String ctx, Integer codEmpresa) throws Exception;

    public List<EmpresaBasicaResponseTO> obterUnidadesAtivas() throws Exception;

    String obterSiglaEstado(String chave, int empresa);

    List<Empresa> obterTodosComCodZW(String ctx) throws ServiceException;
}

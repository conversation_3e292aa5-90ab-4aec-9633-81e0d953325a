package br.com.pacto.service.intf.processo;

import br.com.pacto.dto.SincronizacaoAlunosResultadoDTO;

import java.util.List;

public interface ProcessosService {

    public static final String SERVICE_NAME = "ProcessosService";

    String createUnaccent(final String ctx) throws Exception;

    List<String> replicarAssinaturaContratoParaParq(final String ctx, Integer empresaZW, Boolean apenasValidar) throws Exception;

    String forcarSincronizacaoFotoKeyPessoaZwTr(final String ctx) throws Exception;

    SincronizacaoAlunosResultadoDTO validarSincronizacaoTodosAlunosZwTr(String ctx, Integer empresaIdZw, boolean considerarVisitante) throws Exception;
}

package br.com.pacto.service.impl.agendatotal;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.bean.aula.TipoAulaCheiaOrigemEnum;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.controller.json.agendamento.*;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.ambiente.FiltroAmbienteJSON;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.dao.intf.aula.AmbienteDao;
import br.com.pacto.dao.intf.aula.ModalidadeDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import br.com.pacto.service.intf.agendatotal.AgendaCardsDTO;
import br.com.pacto.service.intf.agendatotal.AgendaCardsService;
import br.com.pacto.service.intf.agendatotal.AgendaHorarioDTO;
import br.com.pacto.service.intf.agendatotal.HorarioItemAgendaDTO;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.locacao.LocacaoService;
import br.com.pacto.service.intf.modalidade.ModalidadeService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import br.com.pacto.util.impl.Ordenacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

import static br.com.pacto.controller.json.base.SuperControle.COMPONENT_NAME_VIEWUTILS;

@Service
public class AgendaCardsServiceImpl implements AgendaCardsService {

    @Autowired
    private ConexaoZWService conexaoZWService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ModalidadeDao modalidadeDao;
    @Autowired
    private ModalidadeService modalidadeService;
    @Autowired
    private DisponibilidadeService disponibilidadeService;
    @Autowired
    private LocacaoService locacaoService;
    @Autowired
    private AgendaService agendaService;
    @Autowired
    private AmbienteDao ambienteDao;
    @Autowired
    private TipoEventoService tipoEventoService;
    @Autowired
    private AgendamentoService agendamentoService;


    public AgendaCardsDTO montarCards(HttpServletRequest request, Integer empresaId, Date dia,
                                      PeriodoFiltrarEnum periodo, FiltroTurmaDTO filtro) throws ServiceException {
        String chave = sessaoService.getUsuarioAtual().getChave();
        AgendaCardsDTO agendaCardsDTO = new AgendaCardsDTO();
        Date inicio = Calendario.getDataComHoraZerada(dia);
        Date fim = Calendario.getDataComHora(dia, "23:59:59");
        if (periodo.equals(PeriodoFiltrarEnum.SEMANA)) {
            inicio = Calendario.inicioSemanaSegunda(dia);
            fim = Calendario.fimSemanaDomingo(dia);
        } else if (periodo.equals(PeriodoFiltrarEnum.MES)) {
            inicio = Calendario.inicioMes(dia);
            fim = Calendario.fimMes(dia);
        }
        Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda = new HashMap<>();
        try {
            List<Date> dias = Uteis.getDiasEntreDatas(inicio, fim);
            List<String> listaHorarios = gerarListaHorarios();
            Map<String, List<ServicoAgendamentoDTO>> servicos = servicosPeriodo(chave, inicio, fim, empresaId, periodo, filtro);
            switch (periodo){
                case SEMANA:
                case DIA:
                    visaoDiaSemana(request, empresaId, dia, periodo, filtro, agendaCardsDTO, dias, chave, listaHorarios, agenda, servicos);
                    break;
                case SERVICO:
                    visaoServico(empresaId, filtro, servicos, inicio, listaHorarios, agenda, chave, agendaCardsDTO);
                    break;
                case MODALIDADE:
                    visaoModalidade(request, empresaId, dia, periodo, filtro, agendaCardsDTO,
                            chave, listaHorarios,
                            agenda, servicos);
                    break;
                case AMBIENTE:
                    visaoAmbiente(request, empresaId, dia, periodo, filtro, agendaCardsDTO,
                            chave, listaHorarios,
                            agenda, servicos);
                    break;
            }

        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException(e);
        }
        return getAgendaCardsDTO(agendaCardsDTO, agenda, filtro);
    }

    private void visaoDiaSemana(HttpServletRequest request, Integer empresaId, Date dia,
                                PeriodoFiltrarEnum periodo,
                                FiltroTurmaDTO filtro, AgendaCardsDTO agendaCardsDTO,
                                List<Date> dias, String chave, List<String> listaHorarios, Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda, Map<String, List<ServicoAgendamentoDTO>> servicos) throws Exception {
        agendaCardsDTO.setDias(montarDias(dias));
        Map<String, List<TurmaResponseDTO>> turmas = exibirAgendaAulas(filtro) ?
                agendaService.obterTodasTurmas(chave, request, empresaId, dia, periodo, filtro) : new HashMap<>();
        for(Date diaMes : dias){
            cardsAulasDia(diaMes, turmas.get(Uteis.getDataAplicandoFormatacao(diaMes, "yyyyMMdd")), listaHorarios, agenda, filtro, Uteis.getDataAplicandoFormatacao(diaMes, "yyyyMMdd"));
            cardsAgendamentos(servicos, diaMes, Uteis.getDataAplicandoFormatacao(diaMes, "yyyyMMdd"), listaHorarios, agenda, filtro);
            cardsDisponibilidades(Uteis.getDataAplicandoFormatacao(diaMes, "yyyyMMdd"), diaMes,
                    listaHorarios, empresaId, agenda, null, filtro);
            locacaoService.cardsLocacoesDisponiveis(chave, Uteis.getDataAplicandoFormatacao(diaMes, "yyyyMMdd"), diaMes,
                    listaHorarios, empresaId, agenda, filtro);
            locacaoService.cardsLocacoesAgendadas(chave, Uteis.getDataAplicandoFormatacao(diaMes, "yyyyMMdd"), diaMes,
                    listaHorarios, empresaId, agenda, filtro);

        }
    }

    private void visaoAmbiente(HttpServletRequest request,
                               Integer empresaId, Date dia,
                               PeriodoFiltrarEnum periodo,
                               FiltroTurmaDTO filtro, AgendaCardsDTO agendaCardsDTO,
                               String chave, List<String> listaHorarios,
                               Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda,
                               Map<String, List<ServicoAgendamentoDTO>> servicos) throws Exception {
        Set<String> ambientes = new HashSet<>();
        List<AmbienteResponseTO> ambienteResponseTOS = ambienteDao.obterTodosAtivo(chave, new FiltroAmbienteJSON(null));
        Map<String, List<TurmaResponseDTO>> turmas = agendaService.obterTodasTurmas(chave, request, empresaId, dia, periodo, filtro);
        for(AmbienteResponseTO ambiente : ambienteResponseTOS){
            List<TurmaResponseDTO> aulasDiaFiltrada = aulasDiaFiltrada(turmas.get(Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd")), ambiente.getId(), null);
            if(!aulasDiaFiltrada.isEmpty()){
                cardsAulasDia(dia, aulasDiaFiltrada,
                        listaHorarios, agenda, filtro, ambiente.getNome());
                ambientes.add(ambiente.getNome());
            }

        }
        agendaCardsDTO.setDias(new ArrayList<>(ambientes));
        Collections.sort(agendaCardsDTO.getDias());
    }

    private void visaoModalidade(HttpServletRequest request,
                               Integer empresaId, Date dia,
                               PeriodoFiltrarEnum periodo,
                               FiltroTurmaDTO filtro, AgendaCardsDTO agendaCardsDTO,
                               String chave, List<String> listaHorarios,
                               Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda,
                               Map<String, List<ServicoAgendamentoDTO>> servicos) throws Exception {
        Set<String> modalidades = new HashSet<>();
        List<Modalidade> modalidadesEntidade = modalidadeDao.findAll(chave);
        Map<String, List<TurmaResponseDTO>> turmas = exibirAgendaAulas(filtro) ?
                agendaService.obterTodasTurmas(chave, request, empresaId, dia, periodo, filtro) : new HashMap<>();
        for(Modalidade modalidade : modalidadesEntidade){
            List<TurmaResponseDTO> aulasDiaFiltrada = aulasDiaFiltrada(turmas.get(Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd")),
                    null, modalidade.getCodigoZW());
            if(!aulasDiaFiltrada.isEmpty()){
                cardsAulasDia(dia, aulasDiaFiltrada,
                        listaHorarios, agenda, filtro, modalidade.getNome());
                modalidades.add(modalidade.getNome());
            }

        }
        agendaCardsDTO.setDias(new ArrayList<>(modalidades));
        Collections.sort(agendaCardsDTO.getDias());
    }

    private List<TurmaResponseDTO> aulasDiaFiltrada(List<TurmaResponseDTO> aulasDia, Integer ambiente, Integer modalidade){
        List<TurmaResponseDTO> resultado = new ArrayList<>();

        for (TurmaResponseDTO aula : aulasDia) {
            if ((ambiente == null || aula.getAmbiente().getId().equals(ambiente))
                && (modalidade == null || aula.getModalidade().getId().equals(modalidade))){
                resultado.add(aula);
            }
        }

        return resultado;
    }

    private void visaoServico(Integer empresaId, FiltroTurmaDTO filtro, Map<String, List<ServicoAgendamentoDTO>> servicos, Date inicio, List<String> listaHorarios, Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda, String chave, AgendaCardsDTO agendaCardsDTO) throws Exception {
        for(String k : servicos.keySet()) {
            cardsAgendamentos(servicos, inicio, k, listaHorarios, agenda, filtro);
        }
        List<TipoEvento> tipoEventos = tipoEventoService.obterTodos(chave, true);
        for(TipoEvento t : tipoEventos){
            cardsDisponibilidades(t.getNome(), inicio, listaHorarios, empresaId, agenda, t, filtro);
        }
        Set<String> tipos = new HashSet<>();
        for(String h : agenda.keySet()){
            Map<String, List<HorarioItemAgendaDTO>> eventos = agenda.get(h);
            tipos.addAll(eventos.keySet());
        }
        agendaCardsDTO.setDias(new ArrayList<>(tipos));
        Collections.sort(agendaCardsDTO.getDias());
    }

    public Map<String, List<ServicoAgendamentoDTO>> servicosPeriodo(String chave,
                                                                    Date inicio, Date fim, Integer empresaId,
                                                                    PeriodoFiltrarEnum tipo, FiltroTurmaDTO filtro) throws Exception{

        String professores = "";
        for (Integer prof : filtro.getProfessoresIds()){
            professores += "," + prof;
        }

        List<Agendamento> agendamentos = agendamentoService.consultarPorData(chave,
                Calendario.getDataComHoraZerada(inicio),
                Calendario.getDataComHora(fim, "23:59:59"),
                empresaId,
                professores.replaceFirst(",", ""),
                null,
                null,
                null,
                false,
                true,
                null,
                null,
                null, filtro.getSearch());
        Map<String, List<ServicoAgendamentoDTO>> ret = new HashMap<>();
        for (Agendamento agendamento : agendamentos) {
            String chaveAgrupadora = tipo.equals(PeriodoFiltrarEnum.SERVICO) ?
                    agendamento.getTipoEvento().getNome() : Calendario.getData(agendamento.getInicio(), "yyyyMMdd");
            List<ServicoAgendamentoDTO> servicos = ret.computeIfAbsent(chaveAgrupadora,
                    k -> new ArrayList<>());
            servicos.add(new ServicoAgendamentoDTO(agendamento, SuperControle.independente(chave)));
        }
        return ret;
    }

    private boolean exibirAgendaServicos(FiltroTurmaDTO filtro){
        if(filtro == null){
            return true;
        }
        if(filtro.getTipo() != null
                && !filtro.getTipo().equals(TipoAulaCheiaOrigemEnum.SERVICO)
                && !filtro.getTipo().equals(TipoAulaCheiaOrigemEnum.TODAS)){
            return false;
        }

        if(!UteisValidacao.emptyList(filtro.getAmbientesIds())
                || !UteisValidacao.emptyList(filtro.getModalidadesIds())){
            return false;
        }

        return true;
    }

    private boolean exibirAgendaAulas(FiltroTurmaDTO filtro){
        if(filtro == null){
            return true;
        }
        if(filtro.getTipo() != null
                && filtro.getTipo().equals(TipoAulaCheiaOrigemEnum.SERVICO)){
            return false;
        }
        if (filtro.getTipo() != null && filtro.getTipo().equals(TipoAulaCheiaOrigemEnum.LOCACAO)) {
            return false;
        }

        return true;
    }

    private void cardsAgendamentos(Map<String, List<ServicoAgendamentoDTO>> servicos,
                                   Date diaMes,
                                   String chaveAgrupadora,
                                   List<String> listaHorarios,
                                   Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda, FiltroTurmaDTO filtro) throws Exception{
        if(!exibirAgendaServicos(filtro)){
            return;
        }

        List<ServicoAgendamentoDTO> servicosDia = servicos.computeIfAbsent(chaveAgrupadora, k -> new ArrayList<>());
        servicosDia = Ordenacao.ordenarLista(servicosDia, "horarioInicial");
        for(ServicoAgendamentoDTO t : servicosDia){
            HorarioItemAgendaDTO servicoDia = new HorarioItemAgendaDTO(diaMes, t);
            String faixaHorario = Uteis.encontrarFaixaHorario(listaHorarios, servicoDia.getInicio());
            Map<String, List<HorarioItemAgendaDTO>> aulasNoHorario = agenda.computeIfAbsent(faixaHorario, k -> new HashMap<>());
            List<HorarioItemAgendaDTO> aulas = aulasNoHorario.computeIfAbsent(
                    chaveAgrupadora, k -> new ArrayList<>());
            aulas.add(servicoDia);
        }
    }

    private void cardsDisponibilidades(String chaveAgrupadora,
                                       Date diaMes, List<String> listaHorarios,
                                       Integer empresa,
                                       Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda, TipoEvento tipoEvento,
                                       FiltroTurmaDTO filtro) throws Exception{

        if(!exibirAgendaServicos(filtro)){
            return;
        }

        String professores = "";
        for (Integer prof : filtro.getProfessoresIds()){
            professores += "," + prof;
        }

        Date inicioDia = Calendario.getDataComHora(diaMes, "00:00");
        Date fimDia = Calendario.getDataComHora(diaMes, "23:59");
        List<TipoAgendamentoDTO> tipos = disponibilidadeService.consultarDisponibilidadesHorario(inicioDia, fimDia,
                empresa, tipoEvento, filtro.getSearch(), professores.replaceFirst(",", ""));

        for(String hora : listaHorarios){
            try {
                Date inicio = Calendario.getDataComHora(diaMes, hora);
                Date fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, 29);

                List<TipoAgendamentoDTO> tiposHoras = new ArrayList<>();
                for (TipoAgendamentoDTO t : tipos) {
                    if (isDisponibilidadeVisivelGradeAgenda(hora, t)) {
                        tiposHoras.add(t);
                    }
                }

                if(!tiposHoras.isEmpty()){
                    String horaFim = Uteis.getDataAplicandoFormatacao(fim, "HH:mm");
                    HorarioItemAgendaDTO aulaDia = new HorarioItemAgendaDTO(String.valueOf(diaMes.getTime()),
                            hora,
                            horaFim,
                            tiposHoras);
                    setTipoPeriodo(tipoEvento, aulaDia);
                    Map<String, List<HorarioItemAgendaDTO>> aulasNoHorario = agenda.computeIfAbsent(hora, k -> new HashMap<>());
                    List<HorarioItemAgendaDTO> aulas = aulasNoHorario.computeIfAbsent(
                            chaveAgrupadora, k -> new ArrayList<>());
                    aulas.add(aulaDia);
                }
            }catch (Exception e){
                Uteis.logar(e, AgendaCardsServiceImpl.class);
            }

        }
    }

    private boolean isDisponibilidadeVisivelGradeAgenda(String horaMapaAgenda, TipoAgendamentoDTO tipeAgendamentoDTO) {
        int horaGradeInicio = Integer.parseInt(horaMapaAgenda.replace(":", ""));
        // Adiciona 29 minutos ao horário e converte para formato numérico, pois a agenda apresenta grade com intervalo de 30min;
        int horaGradeFim = adiciona29Minutos(horaMapaAgenda);
        int inicioDisponibilidade = Integer.parseInt(tipeAgendamentoDTO.getInicio().replace(":", ""));
        int fimDisponibilidade = Integer.parseInt(tipeAgendamentoDTO.getFim().replace(":", ""));

        // Regra 1: Se horaGradeInicio e horaGradeFim estiverem dentro do intervalo de inicioDisponibilidade e fimDisponibilidade
        if (horaGradeInicio >= inicioDisponibilidade && horaGradeFim <= fimDisponibilidade) {
            return true;
        }

        // Regra 2: Se inicioDisponibilidade e fimDisponibilidade estiverem dentro do intervalo de horaGradeInicio e horaGradeFim
        if (inicioDisponibilidade >= horaGradeInicio && fimDisponibilidade <= horaGradeFim) {
            return true;
        }

        // Regra 3: Se inicioDisponibilidade ou fimDisponibilidade estiver dentro do intervalo de horaGradeInicio e horaGradeFim
        if ((inicioDisponibilidade >= horaGradeInicio && inicioDisponibilidade < horaGradeFim) ||
            (fimDisponibilidade > horaGradeInicio && fimDisponibilidade <= horaGradeFim)) {
            return true;
        }

        // Qualquer coisa além disso retorna false
        return false;
    }

    private int adiciona29Minutos(String hora) {
        int horaDoDiaFim;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
            Date horaDate = sdf.parse(hora);
            Calendar cal = Calendar.getInstance();
            cal.setTime(horaDate);
            cal.add(Calendar.MINUTE, 29);
            String horaFimFormatada = sdf.format(cal.getTime());
            horaDoDiaFim = Integer.parseInt(horaFimFormatada.replace(":", ""));
        } catch (Exception e) {
            // Em caso de erro no parsing, usa o valor original
            horaDoDiaFim = Integer.parseInt(hora.replace(":", ""));
        }
        return horaDoDiaFim;
    }

    private static void setTipoPeriodo(TipoEvento tipoEvento, HorarioItemAgendaDTO aulaDia) {
        if(tipoEvento == null){
            return;
        }
        ViewUtils view = ((ViewUtils) UtilContext.getBean(COMPONENT_NAME_VIEWUTILS));
        String descPeriodo = view.getLabel(tipoEvento.getDuracao().name());
        switch (tipoEvento.getDuracao()){
            case DURACAO_PREDEFINIDA:
                descPeriodo = tipoEvento.getDuracaoMinutosMin() + " minutos";
                break;
            case INTERVALO_DE_TEMPO:
                descPeriodo = "De " + tipoEvento.getDuracaoMinutosMin()
                        + " à "
                        + tipoEvento.getDuracaoMinutosMax()
                        + " minutos";
                break;
        }
        aulaDia.setTipoPeriodo(descPeriodo);
    }

    private void cardsAulasDia(Date diaMes,
                               List<TurmaResponseDTO> aulasDia,
                               List<String> listaHorarios,
                               Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda,
                               FiltroTurmaDTO filtro, String agrupador) {

        if(aulasDia == null){
            return;
        }
        aulasDia = Ordenacao.ordenarLista(aulasDia, "periodo");
        for(TurmaResponseDTO t : aulasDia){
            HorarioItemAgendaDTO aulaDia = new HorarioItemAgendaDTO(t);
            String faixaHorario = Uteis.encontrarFaixaHorario(listaHorarios, aulaDia.getInicio());
            Map<String, List<HorarioItemAgendaDTO>> aulasNoHorario = agenda.computeIfAbsent(faixaHorario, k -> new HashMap<>());

            List<HorarioItemAgendaDTO> aulas = aulasNoHorario.computeIfAbsent(
                    agrupador, k -> new ArrayList<>());
            aulas.add(aulaDia);
        }
    }

    private AgendaCardsDTO getAgendaCardsDTO(AgendaCardsDTO agendaCardsDTO,
                                             Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda,
                                             FiltroTurmaDTO filtro) {
        List<AgendaHorarioDTO> agendaList = new ArrayList<>();
        String chave = sessaoService.getUsuarioAtual().getChave();
        String[] horarioTurno = {"",""};
        if(filtro.getTurno()!=null){
            horarioTurno = agendamentoService.buscaHorarioTurno(chave, filtro.getTurno().name());
        }
        for (String horario : agenda.keySet()) {
            if(filtro == null || filtro.getTurno()==null){
                AgendaHorarioDTO agendaTurmaDTO = new AgendaHorarioDTO();
                agendaTurmaDTO.setHorario(horario);
                agendaTurmaDTO.setDias(agenda.get(horario));
                agendaList.add(agendaTurmaDTO);
            }else if(filtro.getTurno()!=null){
                String[] finalHorarioTurno = horarioTurno;
                List<HorarioItemAgendaDTO> novoHorarioItemAgenda = new ArrayList<>();
                Map<String, List<HorarioItemAgendaDTO>> diasa = new HashMap<>();
                agenda.get(horario).forEach((key, value) ->{
                    value.forEach(a -> {
                        if(verificaEstaNoTurno(finalHorarioTurno, a.getInicio(), filtro.getTurno())){
                            novoHorarioItemAgenda.add(a);
                        }
                    });
                    if(novoHorarioItemAgenda.size() > 0){
                        diasa.put(key,novoHorarioItemAgenda);
                        AgendaHorarioDTO agendaTurmaDTO = new AgendaHorarioDTO();
                        agendaTurmaDTO.setHorario(horario);
                        agendaTurmaDTO.setDias(diasa);
                        agendaList.add(agendaTurmaDTO);
                    }
                });
            }
        }
        agendaCardsDTO.setItens(Ordenacao.ordenarLista(agendaList, "horario"));
        return agendaCardsDTO;
    }

    public AgendaCardsDTO montarAgenda(Date inicio, Date fim) throws ServiceException {
        AgendaCardsDTO agendaCardsDTO = new AgendaCardsDTO();

        Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda = new HashMap<>();
        String chave = sessaoService.getUsuarioAtual().getChave();

        try (Connection connection = conexaoZWService.conexaoZw(chave)) {

            List<Date> dias = Uteis.getDiasEntreDatas(inicio, fim);
            List<String> listaHorarios = gerarListaHorarios();
            agendaCardsDTO.setDias(montarDias(dias));

            StringBuilder diasSemanaFiltro = new StringBuilder();
            dias.forEach(dia -> {
                diasSemanaFiltro.append(",").append(Uteis.getDiaDaSemanaNumero(dia));
            });
            PreparedStatement pst = connection.prepareStatement(
                    sqlAgendaAulas.replace(":diasemana", diasSemanaFiltro.toString().replaceFirst(",", "")));
            try (ResultSet rs = pst.executeQuery()) {
                Map<Integer, String> coresModalidades = mapCoresModalidades(chave);
                while (rs.next()) {
                    String horainicial = rs.getString("horainicial");
                    String faixaHorario = Uteis.encontrarFaixaHorario(listaHorarios, horainicial);
                    Map<String, List<HorarioItemAgendaDTO>> aulasNoHorario = agenda.computeIfAbsent(faixaHorario, k -> new HashMap<>());
                    DiasSemana diaSemana = DiasSemana.getDiaSemana(rs.getString("diasemana"));

                    for (Date diaMes : dias) {
                        DiasSemana diaSemanaData = DiasSemana.getDiaSemanaNumeral(Uteis.getDiaDaSemanaNumero(diaMes));
                        if (diaSemanaData == diaSemana) {
                            List<HorarioItemAgendaDTO> aulas = aulasNoHorario.computeIfAbsent(
                                    Uteis.getDataAplicandoFormatacao(diaMes, "yyyyMMdd"), k -> new ArrayList<>());
                            HorarioItemAgendaDTO horario = montarCardAula(rs, coresModalidades, Uteis.getDataAplicandoFormatacao(diaMes, "yyyyMMdd"));
                            aulas.add(horario);
                        }
                    }
                }
            }

        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return getAgendaCardsDTO(agendaCardsDTO, agenda, null);
    }

    public Integer obterIdAula(Integer empresa, Integer horarioTurma) {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            try (Connection connection = conexaoZWService.conexaoZw(chave)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                        "select t.codigo from turma t\n" +
                                "inner join horarioturma ht on ht.turma = t.codigo\n" +
                                "where ht.codigo = " + horarioTurma + " and t.empresa = " + empresa, connection)) {
                    if (rs.next()) {
                        return rs.getInt("codigo");
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }


    private List<String> montarDias(List<Date> diasEntreDatas) throws ServiceException {
        try {
            return new ArrayList<String>(){{
                for (Date dia : diasEntreDatas) {
                    add(Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd"));
                }
            }};
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    private static HorarioItemAgendaDTO montarCardAula(ResultSet rs, Map<Integer, String> coresModalidades, String diaMes) throws SQLException {
        HorarioItemAgendaDTO horario = new HorarioItemAgendaDTO();
        horario.setDiaMes(diaMes);
        horario.setCodigo(rs.getInt("codigo"));
        horario.setInicio(rs.getString("horainicial"));
        horario.setFim(rs.getString("horafinal"));
        horario.setCapacidade(rs.getInt("nrmaximoaluno"));
        horario.setOcupacao(rs.getInt("ocupacao"));
        horario.setTurma(rs.getString("turma"));
        horario.setAmbiente(rs.getString("ambiente"));
        horario.setProfessor(rs.getString("professor"));
        horario.setCor(coresModalidades.get(rs.getInt("modalidade")));
        return horario;
    }

    private Map<Integer, String> mapCoresModalidades(String chave){

        Map<Integer, String> corModalidadesMap = new HashMap<>();

        try {
            List<Modalidade> modalidades = modalidadeDao.findAll(chave);
            for (Modalidade modalidade : modalidades) {
                if(modalidade.getCor() == null){
                    corModalidadesMap.put(modalidade.getCodigoZW(), PaletaCoresEnum.AZUL_A.getCor());
                } else {
                    corModalidadesMap.put(modalidade.getCodigoZW(), modalidade.getCor().getCor());
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, AgendaCardsService.class);
        }

        return corModalidadesMap;
    }

    public List<String> gerarListaHorarios() {
        List<String> horarios = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);

        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        horarios.add("00:00");
        while (calendar.get(Calendar.HOUR_OF_DAY) != 23 || calendar.get(Calendar.MINUTE) != 30) {
            calendar.add(Calendar.MINUTE, 30);
            Date hora = calendar.getTime();
            horarios.add(sdf.format(hora));
        }

        return horarios;
    }

    public List<AgendaDisponibilidadeDTO> disponibilidades(Integer empresaId,
                                                           Date dia,
                                                           String horaSelecionada,
                                                           String horaSelecionadaFinal,
                                                           String tipo, PaginadorDTO paginadorDTO) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<AgendaDisponibilidadeDTO> ret = new ArrayList<>();
            Date dataComHora = Calendario.getDataComHora(dia, horaSelecionada.replace("h", ":"));
            Date dataComHoraFinal = Calendario.getDataComHora(dia, horaSelecionadaFinal.replace("h", ":"));
            List<Agendamento> agendamentos = agendamentoService.consultarPorData(ctx,
                    dataComHoraFinal,
                    Calendario.getDataComHora(dia, "23:59:59"),
                    empresaId,
                    null,
                    tipo.toString(),
                    null,
                    null,
                    true,
                    false,
                    null,
                    null,
                    null, true, null, false);
            agendamentos.addAll(agendamentoService.consultarPorData(ctx,
                    dataComHora,
                    Calendario.getDataComHora(dia, "23:59:59"),
                    empresaId,
                    null,
                    tipo.toString(),
                    null,
                    null,
                    true,
                    false,
                    null,
                    null,
                    null, true, null, true));
            for (Agendamento disponibilidade : agendamentos) {
                AgendaDisponibilidadeDTO disponibilidadeOriginal = new AgendaDisponibilidadeDTO(disponibilidade);
                List<Agendamento> agendamentosProfessor = agendamentoService.consultarAgendamentoDataHoraProfessor(ctx,
                        Uteis.getDate(disponibilidadeOriginal.getDia(), "yyyyMMdd"),
                        disponibilidadeOriginal.getHorarioInicial(),
                        disponibilidadeOriginal.getHorarioFinal(),
                        disponibilidadeOriginal.getProfessor().getId());
                if (disponibilidade.getHorarioDisponibilidade() != null) {
                    disponibilidadeOriginal.setAmbiente(disponibilidade.getHorarioDisponibilidade().getAmbiente().getNome());
                    disponibilidadeOriginal.setAmbienteId(disponibilidade.getHorarioDisponibilidade().getAmbiente().getCodigo());
                }
                List<AgendaDisponibilidadeDTO> disponibilidadeGeradas = new ArrayList(){{
                    add(disponibilidadeOriginal);
                }};
                for(Agendamento agendamento : agendamentosProfessor){
                    for(AgendaDisponibilidadeDTO disponibilidadeDTO : new ArrayList<>(disponibilidadeGeradas)){
                        //se o agendamento começa e termina na mesma hora que a disponibilidade, remove a disponibilidade
                        if(disponibilidadeDTO.getHorarioInicial().equals(agendamento.getHoraInicioApresentar())
                                && disponibilidadeDTO.getHorarioFinal().equals(agendamento.getHoraFimApresentar())){
                            disponibilidadeGeradas.remove(disponibilidadeDTO);
                            continue;
                        }
                        //se o agendamento começa na mesma hora que a disponibilidade
                        if(disponibilidadeDTO.getHorarioInicial().equals(agendamento.getHoraInicioApresentar())
                                && Calendario.maiorComHora(disponibilidadeDTO.getFimDate(), agendamento.getFim())){
                            disponibilidadeDTO.setHorarioInicial(agendamento.getHoraFimApresentar());
                            disponibilidadeDTO.setInicioDate(agendamento.getFim());
                            continue;
                        }
                        //se o agendamento termina na mesma hora que a disponibilidade
                        if(disponibilidadeDTO.getHorarioFinal().equals(agendamento.getHoraFimApresentar())
                                && Calendario.menorComHora(disponibilidadeDTO.getInicioDate(), agendamento.getInicio())){
                            disponibilidadeDTO.setHorarioFinal(agendamento.getHoraInicioApresentar());
                            disponibilidadeDTO.setFimDate(agendamento.getInicio());
                            //sistema tem um comportamento de iniciar um evento 1 minuto depois de algum já criado para evitar conflito
                            //isso gera problema de disponibilidade de 1 minuto
                            //não exibir essa disponibilidade na agenda
                            if(Uteis.minutosEntreDatas(disponibilidadeDTO.getInicioDate(), disponibilidadeDTO.getFimDate()) == 1){
                                disponibilidadeGeradas.remove(disponibilidadeDTO);
                            }
                            continue;
                        }
                        //se o agendamento está dentro da disponibilidade, quebrar a disponibilidade
                        //se o agendamento termina na mesma hora que a disponibilidade
                        if(Calendario.menorComHora(disponibilidadeDTO.getInicioDate(), agendamento.getInicio()) &&
                                Calendario.maiorComHora(disponibilidadeDTO.getFimDate(), agendamento.getFim())){
                            //gerar outro
                            AgendaDisponibilidadeDTO clone = disponibilidadeDTO.clone();
                            disponibilidadeDTO.setHorarioFinal(agendamento.getHoraInicioApresentar());
                            disponibilidadeDTO.setFimDate(agendamento.getInicio());
                            clone.setHorarioInicial(agendamento.getHoraFimApresentar());
                            clone.setInicioDate(agendamento.getFim());
                            disponibilidadeGeradas.add(clone);
                        }
                    }
                }
                ret.addAll(disponibilidadeGeradas);
            }

            for(AgendaDisponibilidadeDTO agenda : new ArrayList<>(ret)){
                if(!Calendario.entre(dataComHoraFinal, agenda.getInicioDate(), agenda.getFimDate())
                    && !Calendario.igualComHora(dataComHoraFinal, agenda.getInicioDate())
                    && !Calendario.igualComHora(dataComHoraFinal, agenda.getFimDate())
                ){
                    ret.remove(agenda);
                }
            }
            return paginar(ret, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_LISTAR_AGENDAMENTOS, e);
        }
    }

    public List<AgendaDisponibilidadeDTO> disponibilidadesV2(Integer empresaId,
                                                           Date dia,
                                                           String horaSelecionada,
                                                           String horaSelecionadaFinal,
                                                           String tipo, PaginadorDTO paginadorDTO) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<AgendaDisponibilidadeDTO> ret = new ArrayList<>();
            Date dataComHora = Calendario.getDataComHora(dia, horaSelecionada.replace("h", ":"));
            Date dataComHoraFinal = Calendario.getDataComHora(dia, horaSelecionadaFinal.replace("h", ":"));
            List<Agendamento> agendamentosFinal = new ArrayList<>();

            List<Agendamento> agendamentos = agendamentoService.consultarPorDataV2(ctx,
                    dataComHora,
                    dataComHoraFinal,
                    empresaId,
                    null,
                    tipo.toString(),
                    null,
                    null,
                    true,
                    false,
                    null,
                    null,
                    null, true, null, false);
            agendamentos.addAll(agendamentoService.consultarPorDataV2(ctx,
                    dataComHora,
                    dataComHoraFinal,
                    empresaId,
                    null,
                    tipo.toString(),
                    null,
                    null,
                    true,
                    false,
                    null,
                    null,
                    null, true, null, true));
            Set<String> combinacoesVistas = new HashSet<>();
            for (Agendamento disponibilidade : agendamentos) {
                if(disponibilidade.getTipoEvento()!= null){
                    String chave = disponibilidade.getTipoEvento().getCodigo() + "-" + disponibilidade.getProfessor().getCodigo() + " - "+disponibilidade.getInicio();
                    if (combinacoesVistas.add(chave)) {
                        agendamentosFinal.add(disponibilidade);
                    }
                }else{
                    String chave = disponibilidade.getHorarioDisponibilidade().getCodigoDisponibilidade() + "-" + disponibilidade.getProfessor().getCodigo()+ " - "+disponibilidade.getInicio();
                    if (combinacoesVistas.add(chave)) {
                        agendamentosFinal.add(disponibilidade);
                    }
                }
            }

            for (Agendamento disponibilidade : agendamentosFinal) {
                AgendaDisponibilidadeDTO disponibilidadeOriginal = new AgendaDisponibilidadeDTO(disponibilidade);
                List<Agendamento> agendamentosProfessor = agendamentoService.consultarAgendamentoDataHoraProfessor(ctx,
                        Uteis.getDate(disponibilidadeOriginal.getDia(), "yyyyMMdd"),
                        disponibilidadeOriginal.getHorarioInicial(),
                        disponibilidadeOriginal.getHorarioFinal(),
                        disponibilidadeOriginal.getProfessor().getId());
                if (disponibilidade.getHorarioDisponibilidade() != null) {
                    disponibilidadeOriginal.setAmbiente(disponibilidade.getHorarioDisponibilidade().getAmbiente().getNome());
                    disponibilidadeOriginal.setAmbienteId(disponibilidade.getHorarioDisponibilidade().getAmbiente().getCodigo());
                }
                List<AgendaDisponibilidadeDTO> disponibilidadeGeradas = new ArrayList(){{
                    add(disponibilidadeOriginal);
                }};
                for(Agendamento agendamento : agendamentosProfessor){
                    for(AgendaDisponibilidadeDTO disponibilidadeDTO : new ArrayList<>(disponibilidadeGeradas)){
                        //se o agendamento começa e termina na mesma hora que a disponibilidade, remove a disponibilidade
                        if(disponibilidadeDTO.getHorarioInicial().equals(agendamento.getHoraInicioApresentar())
                                && disponibilidadeDTO.getHorarioFinal().equals(agendamento.getHoraFimApresentar())){
                            disponibilidadeGeradas.remove(disponibilidadeDTO);
                            continue;
                        }
                        //se o agendamento começa na mesma hora que a disponibilidade
                        if(disponibilidadeDTO.getHorarioInicial().equals(agendamento.getHoraInicioApresentar())
                                && Calendario.maiorComHora(disponibilidadeDTO.getFimDate(), agendamento.getFim())){
                            disponibilidadeDTO.setHorarioInicial(agendamento.getHoraFimApresentar());
                            disponibilidadeDTO.setInicioDate(agendamento.getFim());
                            continue;
                        }
                        //se o agendamento termina na mesma hora que a disponibilidade
                        if(disponibilidadeDTO.getHorarioFinal().equals(agendamento.getHoraFimApresentar())
                                && Calendario.menorComHora(disponibilidadeDTO.getInicioDate(), agendamento.getInicio())){
                            disponibilidadeDTO.setHorarioFinal(agendamento.getHoraInicioApresentar());
                            disponibilidadeDTO.setFimDate(agendamento.getInicio());
                            //sistema tem um comportamento de iniciar um evento 1 minuto depois de algum já criado para evitar conflito
                            //isso gera problema de disponibilidade de 1 minuto
                            //não exibir essa disponibilidade na agenda
                            if(Uteis.minutosEntreDatas(disponibilidadeDTO.getInicioDate(), disponibilidadeDTO.getFimDate()) == 1){
                                disponibilidadeGeradas.remove(disponibilidadeDTO);
                            }
                            continue;
                        }
                        //se o agendamento está dentro da disponibilidade, quebrar a disponibilidade
                        //se o agendamento termina na mesma hora que a disponibilidade
                        if(Calendario.menorComHora(disponibilidadeDTO.getInicioDate(), agendamento.getInicio()) &&
                                Calendario.maiorComHora(disponibilidadeDTO.getFimDate(), agendamento.getFim())){
                            //gerar outro
                            AgendaDisponibilidadeDTO clone = disponibilidadeDTO.clone();
                            disponibilidadeDTO.setHorarioFinal(agendamento.getHoraInicioApresentar());
                            disponibilidadeDTO.setFimDate(agendamento.getInicio());
                            clone.setHorarioInicial(agendamento.getHoraFimApresentar());
                            clone.setInicioDate(agendamento.getFim());
                            disponibilidadeGeradas.add(clone);
                        }
                    }
                }
                ret.addAll(disponibilidadeGeradas);
            }

            return paginar(ret, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_LISTAR_AGENDAMENTOS, e);
        }
    }

    public static List<AgendaDisponibilidadeDTO> paginar(List<AgendaDisponibilidadeDTO> alunos, PaginadorDTO paginadorDTO){
        paginadorDTO.setQuantidadeTotalElementos(new Long(alunos.size()));
        if(paginadorDTO.getSize() > paginadorDTO.getQuantidadeTotalElementos()){
            return alunos;
        }
        Long index = paginadorDTO.getPage() * paginadorDTO.getSize();
        long last = (index + paginadorDTO.getSize() > (alunos.size() - 1) ?
                (alunos.size()) : index + paginadorDTO.getSize());
        return alunos.subList(index.intValue(), (int) last);
    }

    public void buscaHorarioTurno(String chave, String turno){

        String[] horario = agendamentoService.buscaHorarioTurno(chave, turno);
    }

    public static boolean verificaEstaNoTurno(String[] horarioTurno, String horario, TurnoEnum turno){
        if(turno == null){
            return true;
        }
        Integer horarioInt = Integer.valueOf(horario.replace(":", ""));
        Integer horarioInicialInt = Integer.valueOf(horarioTurno[0].replace(":", ""));
        Integer horarioFinalInt = Integer.valueOf(horarioTurno[1].replace(":", ""));
        return horarioInt >= horarioInicialInt && horarioInt <= horarioFinalInt;
    }
}


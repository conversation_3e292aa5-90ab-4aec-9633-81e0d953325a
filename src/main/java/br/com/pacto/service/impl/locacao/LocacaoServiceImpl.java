package br.com.pacto.service.impl.locacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.aula.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.locacao.Locacao;
import br.com.pacto.bean.locacao.LocacaoHorario;
import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AgendaDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.FiltroTurmaDTO;
import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.aluno.TelefoneDTO;
import br.com.pacto.controller.json.ambiente.AmbienteHorarioLocacaoTO;
import br.com.pacto.controller.json.locacao.*;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.turma.AmbienteDTO;
import br.com.pacto.dao.intf.locacao.*;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.agenda.AgendaServiceImpl;
import br.com.pacto.service.impl.agenda.AmbienteAgendadoTO;
import br.com.pacto.service.impl.agendatotal.AgendaCardsServiceImpl;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.configuracoes.ConfiguracaoSistemaServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.LocacaoExcecoes;
import br.com.pacto.service.intf.agendatotal.HorarioItemAgendaDTO;
import br.com.pacto.service.intf.agendatotal.TipoItemAgendaEnum;
import br.com.pacto.service.intf.ambiente.AmbienteService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.locacao.LocacaoService;
import br.com.pacto.service.intf.locacao.LocacaoVO;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.venda.ItemVendaAvulsaDTO;
import br.com.pacto.service.intf.venda.VendaAvulsaDTO;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import javax.xml.bind.DatatypeConverter;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static java.util.Arrays.asList;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class LocacaoServiceImpl implements LocacaoService {

    private final LocacaoDao locacaoDao;
    private final LocacaoHorarioDao locacaoHorarioDao;
    private final UsuarioService usuarioService;
    private final AgendamentoLocacaoDao agendamentoLocacaoDao;
    private final AgendamentoLocacaoProdutoDao agendamentoLocacaoProdutoDao;
    private final SessaoService sessaoService;
    private final ConexaoZWServiceImpl conexaoZWService;
    private final AmbienteService ambienteService;
    private final ClienteSinteticoService clienteSinteticoService;
    private final LogDao logDao;
    private final ConfiguracaoSistemaService configuracaoSistemaService;
    private final NotificacaoService notificacaoService;

    @Autowired
    public LocacaoServiceImpl(LocacaoDao locacaoDao,
                              LocacaoHorarioDao locacaoHorarioDao, UsuarioService usuarioService, SessaoService sessaoService,
                              AgendamentoLocacaoProdutoDao agendamentoLocacaoProdutoDao,
                              AgendamentoLocacaoDao agendamentoLocacaoDao,
                              ConexaoZWServiceImpl conexaoZWService,
                              AmbienteService ambienteService,
                              ClienteSinteticoService clienteSinteticoService,
                              LogDao logDao, ConfiguracaoSistemaService configuracaoSistemaService,
                              NotificacaoService notificacaoService) {
        this.locacaoDao = locacaoDao;
        this.locacaoHorarioDao = locacaoHorarioDao;
        this.usuarioService = usuarioService;
        this.sessaoService = sessaoService;
        this.agendamentoLocacaoDao = agendamentoLocacaoDao;
        this.conexaoZWService = conexaoZWService;
        this.ambienteService = ambienteService;
        this.clienteSinteticoService = clienteSinteticoService;
        this.agendamentoLocacaoProdutoDao = agendamentoLocacaoProdutoDao;
        this.logDao = logDao;
        this.configuracaoSistemaService = configuracaoSistemaService;
        this.notificacaoService = notificacaoService;
    }

    @Override
    public List<LocacaoTO> consultar(FiltrosLocacaoJSON filtros, Integer empresaId, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<LocacaoTO> locacaoTOS = new ArrayList<>();
            List<Locacao> locacao = locacaoDao.consultarLocacoes(ctx, filtros, empresaId, paginadorDTO);
            if (!UteisValidacao.emptyList(locacao)) {
                locacao.forEach(l -> {
                    LocacaoTO locacaoTO = new LocacaoTO(l, true);
                    locacaoTOS.add(locacaoTO);
                });
                locacaoTOS.forEach(l -> {
                    montarUsuario(ctx, l);
                });
            }
            if (isNotBlank(paginadorDTO.getSort()) && paginadorDTO.getSort().startsWith("diaSemana")) {
                if (paginadorDTO.getSort().contains(",ASC")) {
                    Ordenacao.ordenarLista(locacaoTOS, "dias");
                } else {
                    Ordenacao.ordenarListaReverse(locacaoTOS, "dias");
                }
            }
            return locacaoTOS;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public LocacaoTO consultarPorCodigo(Integer codigo) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            locacaoDao.getCurrentSession(ctx).clear();
            Locacao locacaoEntity = locacaoDao.findById(ctx, codigo);
            LocacaoTO locacaoTO = new LocacaoTO(locacaoEntity, false);
            montarUsuario(ctx, locacaoTO);
            montarProduto(ctx, locacaoTO);
            montarDescricaoProdutosSugeridos(ctx, locacaoTO);
            verificarAgendamentosHorarios(locacaoTO.getHorarios());
            return locacaoTO;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private void montarUsuario(String ctx, LocacaoTO locacaoTO) {
        locacaoTO.getHorarios().forEach(h -> {
            try {
                h.setResponsavel(new UsuarioTO(usuarioService.obterPorId(ctx, h.getResponsavel().getCodigo())));
            } catch (ServiceException e) {
                throw new RuntimeException(e);
            }
        });
    }

    private void montarProduto(String ctx, LocacaoTO locacaoTO) {
        if(locacaoTO.getProduto() != null && UteisValidacao.notEmptyNumber(locacaoTO.getProduto().getCodigo())){
            ProdutoTO prod = consultarProduto(ctx, locacaoTO.getProduto().getCodigo());
            if (prod != null) {
                prod.setValorFinal(Uteis.arredondarForcando2CasasDecimais(prod.getValorFinal()));
                locacaoTO.setProduto(prod);
            }
        }
    }

    private void verificarAgendamentosHorarios(List<LocacaoHorarioTO> horarios) {
        horarios.forEach(h -> {
            // TODO implementar consultar para verificar se o horario possui algum agendamento/locacao efetivada
            h.setPossuiAgendamentos(false);
        });
    }

    private void montarDescricaoProdutosSugeridos(String ctx, LocacaoTO locacaoTO) {
        if(locacaoTO.getProdutosSugeridos() != null) {
            locacaoTO.getProdutosSugeridos().forEach(p -> {
                p.setProduto(consultarProduto(ctx, p.getProduto().getCodigo()));
            });
        }
    }

    private ProdutoTO consultarProduto(String chave, Integer codigoProduto) {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(chave)) {
                String sql = "SELECT p.codigo, p.descricao, p.valorfinal FROM produto p where p.codigo = " + codigoProduto;
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW);
                if (rs.next()) {
                    ProdutoTO produtoTO = new ProdutoTO();
                    produtoTO.setCodigo(rs.getInt("codigo"));
                    produtoTO.setDescricao(rs.getString("descricao"));
                    produtoTO.setValorFinal(rs.getDouble("valorfinal"));
                    return produtoTO;
                }
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private PlanoTO consultarPlano(String chave, Integer codigoPlano, Connection conZW) {
        try {
            String sql = "SELECT p.codigo, p.descricao FROM plano p where p.codigo = " + codigoPlano;
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {
                if (rs.next()) {
                    PlanoTO planoTO = new PlanoTO();
                    planoTO.setCodigo(rs.getInt("codigo"));
                    planoTO.setDescricao(rs.getString("descricao"));
                    return planoTO;
                }
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public LocacaoTO cadastrar(LocacaoTO locacaoTO, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        validacoes(ctx, empresaId, locacaoTO);
        locacaoDao.getCurrentSession(ctx).clear();
        try {
            Locacao locacaoEntity = new Locacao(null, locacaoTO);
            locacaoEntity.setEmpresa(empresaId);

            if (TipoHorarioLocacaoEnum.PRE_DEFINIDO.name().equalsIgnoreCase(Objects.requireNonNull(TipoHorarioLocacaoEnum.getByCodigo(locacaoTO.getTipoHorario())).toString())) {
                locacaoEntity.setHorarios(calcularHoraFim(locacaoEntity.getHorarios(), locacaoEntity.getDuracaoMinutos()));
            }

            if (TipoHorarioLocacaoEnum.LIVRE.name().equalsIgnoreCase(Objects.requireNonNull(TipoHorarioLocacaoEnum.getByCodigo(locacaoTO.getTipoHorario())).toString())) {
                locacaoEntity.setHorarios(locacaoEntity.getHorarios().stream().peek(it -> {
                    it.setTempoMinimoMinutos(locacaoEntity.getTempoMinimoMinutos());
                }).collect(Collectors.toList()));
            }

            povoarImagem(locacaoTO, ctx, locacaoEntity);
            locacaoDao.insert(ctx, locacaoEntity);
            locacaoTO = consultarPorCodigo(locacaoEntity.getCodigo());
            gerarLogInclusaoLocacao(locacaoTO, ctx);

            return locacaoTO;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private List<LocacaoHorario> calcularHoraFim(List<LocacaoHorario> horarios, Integer duracaoMinutos) {
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");

        horarios.forEach(h -> {
            try {
                // Converte a horaInicio para Date, adiciona minutos e formata para String
                String novaHoraFim = Calendario.somaMinutos(
                        timeFormat.parse(h.getHoraInicio()),
                        duracaoMinutos,
                        "HH:mm"
                );
                h.setHoraFim(novaHoraFim);
            } catch (ParseException e) {
                // Lança uma exceção personalizada ou com mensagem
                throw new RuntimeException("Erro ao processar o horário: " + h.getHoraInicio(), e);
            }
        });

        return horarios;
    }

    private void povoarImagem(LocacaoTO locacaoTO, String ctx, Locacao locacao) throws ServiceException {
        try {
            if (isNotBlank(locacao.getIdentificadorFotoKey()) && isNotBlank(locacao.getFotoKey()) && isBlank(locacaoTO.getUrlImagem())) {
                MidiaService.getInstance().deleteObject(locacao.getFotoKey());
                locacao.setIdentificadorFotoKey(null);
                locacao.setFotoKey(null);
            }

            if (isNotBlank(locacaoTO.getBase64Imagem())) {
                String identificador = Calendario.getData(Calendario.hoje(), "ddMMyyyyhhMMss") + "-FotoLocacao";
                MidiaEntidadeEnum tmidia = MidiaEntidadeEnum.obterPorExtensao("." + locacaoTO.getFormControlNomeImagem().split("\\.")[1]);
                locacao.setIdentificadorFotoKey(identificador);
                locacao.setFotoKey(MidiaService.getInstance().uploadObjectFromByteArray(ctx, tmidia, identificador, Base64.decodeBase64(locacaoTO.getBase64Imagem().split(",")[1])));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public List<LocacaoHorarioTO> saveOrUpdateLocacaoHorarios(Integer codigoLocacao, List<LocacaoHorarioTO> horarioTOS) throws ServiceException {
        validacoes(horarioTOS);
        String ctx = sessaoService.getUsuarioAtual().getChave();
        locacaoHorarioDao.getCurrentSession(ctx).clear();
        for (LocacaoHorarioTO locacaoHorarioTO : horarioTOS) {
            try {
                Locacao locacaoEntity = locacaoDao.findById(ctx, codigoLocacao);
                LocacaoHorario locacaoHorarioEntity = new LocacaoHorario(locacaoHorarioTO);
                locacaoHorarioEntity.setLocacao(locacaoEntity);
                if (UteisValidacao.emptyNumber(locacaoHorarioTO.getCodigo())) {
                    locacaoHorarioEntity.setAtivo(true);
                    locacaoHorarioEntity = locacaoHorarioDao.insert(ctx, locacaoHorarioEntity);
                    incluirLog(ctx, locacaoHorarioEntity.getCodigo().toString(), locacaoEntity.getCodigo().toString(), "", getDescricaoLocacaoHorarioParaLog(locacaoHorarioTO, null), "INCLUSÃO", "INCLUSÃO DE LOCAÇÃO HORÁRIO", EntidadeLogEnum.LOCACAO_HORARIO, "Locação Horário", sessaoService, logDao, null, null);
                } else {
                    LocacaoHorarioTO locacaoHorarioTOAntes = new LocacaoHorarioTO(locacaoHorarioDao.findById(ctx, locacaoHorarioTO.getCodigo()));
                    locacaoHorarioEntity = locacaoHorarioDao.update(ctx, locacaoHorarioEntity);
                    String descricaoLogAntes = getDescricaoLocacaoHorarioParaLog(locacaoHorarioTOAntes, locacaoHorarioTO);
                    String descricaoLogAlteracoes = getDescricaoLocacaoHorarioParaLog(locacaoHorarioTO, locacaoHorarioTOAntes);
                    if (!UteisValidacao.emptyString(descricaoLogAntes.trim()) || !UteisValidacao.emptyString(descricaoLogAlteracoes.trim())) {
                        incluirLog(ctx, locacaoHorarioEntity.getCodigo().toString(), locacaoEntity.getCodigo().toString(), descricaoLogAntes, descricaoLogAlteracoes, "ALTERAÇÃO", "ALTERAÇÃO DE LOCAÇÃO HORÁRIO", EntidadeLogEnum.LOCACAO_HORARIO, "Locação Horário", sessaoService, logDao, null, null);
                    }
                }
                locacaoHorarioEntity.setResponsavel(locacaoHorarioEntity.getResponsavel());
                locacaoHorarioTO = new LocacaoHorarioTO(locacaoHorarioEntity);
            } catch (Exception ex) {
                throw new ServiceException("Falha ao salvar Locação horário:" + ex.getMessage());
            }
        }
        return horarioTOS;
    }

    private void gerarLogInclusaoLocacao(LocacaoTO locacaoTO, String ctx) {
        StringBuilder logLocacao = new StringBuilder();
        logLocacao.append(getDescricaoLocacaoParaLog(locacaoTO, null));

        locacaoTO.getHorarios().forEach(horarioTO -> {
            incluirLog(ctx, horarioTO.getCodigo().toString(), locacaoTO.getCodigo().toString(), "", getDescricaoLocacaoHorarioParaLog(horarioTO, null), "INCLUSÃO", "INCLUSÃO DE LOCAÇÃO HORÁRIO", EntidadeLogEnum.LOCACAO_HORARIO, "Locação Horário", sessaoService, logDao, null, null);
        });
        locacaoTO.getProdutosSugeridos().forEach(locacaoProdutoSugeridoTO -> {
            incluirLog(ctx, locacaoProdutoSugeridoTO.getCodigo().toString(), locacaoProdutoSugeridoTO.getCodigo().toString(), "", getDescricaoLocacaoProdutoSugeridoParaLog(locacaoProdutoSugeridoTO, null), "INCLUSÃO", "INCLUSÃO DE LOCAÇÃO PRODUTO SUGERIDO", EntidadeLogEnum.LOCACAO_PRODUTO_SUGERIDO, "Locação Produto Sugerido", sessaoService, logDao, null, null);
        });

        if (!locacaoTO.getItensValidacao().isEmpty()) {
            logLocacao.append("\nITENSVALIDACAO: [");
            logLocacao.append(locacaoTO.getItensValidacao().stream().map(p -> p.getCodigo().toString()).collect(Collectors.joining(",")));
            logLocacao.append("];");
        }

        incluirLog(ctx, locacaoTO.getCodigo().toString(), "", "", logLocacao.toString(), "INCLUSÃO", "INCLUSÃO DE LOCAÇÃO", EntidadeLogEnum.LOCACAO, "Locação", sessaoService, logDao, null, null);
    }

    @Override
    public LocacaoTO alterar(LocacaoTO locacaoTO, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        validacoes(ctx, empresaId, locacaoTO);
        locacaoDao.getCurrentSession(ctx).clear();
        try {
            Locacao locacaoAnterior = locacaoDao.findById(ctx, locacaoTO.getCodigo());
            Locacao locacaoEntity = new Locacao(locacaoAnterior, locacaoTO);
            locacaoEntity.setEmpresa(empresaId);

            if (TipoHorarioLocacaoEnum.PRE_DEFINIDO.name().equalsIgnoreCase(Objects.requireNonNull(TipoHorarioLocacaoEnum.getByCodigo(locacaoTO.getTipoHorario())).toString())) {
                locacaoEntity.setHorarios(calcularHoraFim(locacaoEntity.getHorarios(), locacaoEntity.getDuracaoMinutos()));
            }

            if (TipoHorarioLocacaoEnum.LIVRE.name().equalsIgnoreCase(Objects.requireNonNull(TipoHorarioLocacaoEnum.getByCodigo(locacaoTO.getTipoHorario())).toString())) {
                locacaoEntity.setHorarios(locacaoEntity.getHorarios().stream().peek(it -> {
                    it.setTempoMinimoMinutos(locacaoEntity.getTempoMinimoMinutos());
                }).collect(Collectors.toList()));
            }

            povoarImagem(locacaoTO, ctx, locacaoEntity);
            locacaoTO = new LocacaoTO(locacaoDao.update(ctx, locacaoEntity), false);
            gerarLogAlteracaoLocacao(locacaoTO, new LocacaoTO(locacaoAnterior, false), ctx);
            return locacaoTO;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void gerarLogAlteracaoLocacao(LocacaoTO locacaoTO, LocacaoTO locacaoTOAnterior, String ctx) {
        StringBuilder descricaoLogAnterior = new StringBuilder(getDescricaoLocacaoParaLog(locacaoTOAnterior, locacaoTO));
        StringBuilder descricaoLogAlteracao = new StringBuilder(getDescricaoLocacaoParaLog(locacaoTO, locacaoTOAnterior));

        StringBuilder descricaoLogLpsAnterior = new StringBuilder();
        StringBuilder descricaoLogLpsAlteracao = new StringBuilder();
        locacaoTO.getProdutosSugeridos().forEach(lps -> {
             Optional<LocacaoProdutoSugeridoTO> lpsAnterior = locacaoTOAnterior.getProdutosSugeridos().stream()
                    .filter(lpsold -> lpsold.getCodigo().equals(lps.getCodigo()))
                    .findFirst();
             if (lpsAnterior.isPresent()) {
                 descricaoLogLpsAnterior.append(getDescricaoLocacaoProdutoSugeridoParaLog(lpsAnterior.get(), lps));
                 descricaoLogLpsAlteracao.append(getDescricaoLocacaoProdutoSugeridoParaLog(lps, lpsAnterior.get()));
             } else {
                 descricaoLogLpsAlteracao.append("INCLUSÃO").append(getDescricaoLocacaoProdutoSugeridoParaLog(lps, null)).append(";");
             }
        });

        if (!UteisValidacao.emptyString(descricaoLogLpsAnterior.toString().trim()) || !UteisValidacao.emptyString(descricaoLogLpsAlteracao.toString().trim())) {
            descricaoLogAnterior.append(descricaoLogLpsAnterior.toString().trim());
            descricaoLogAlteracao.append(descricaoLogLpsAlteracao.toString().trim());
        }

        String codigosPlanosAnterior = Uteis.obterCodigosOrdenadosSeparadosPorVirgula(locacaoTOAnterior.getItensValidacao().stream().map(p -> p.getCodigo()).collect(Collectors.toList()));
        String codigosPlanos = Uteis.obterCodigosOrdenadosSeparadosPorVirgula(locacaoTO.getItensValidacao().stream().map(p -> p.getCodigo()).collect(Collectors.toList()));
        if (!codigosPlanosAnterior.equals(codigosPlanos)) {
            descricaoLogAnterior.append("\nITENSVALIDACAO: [");
            descricaoLogAnterior.append(codigosPlanosAnterior);
            descricaoLogAnterior.append("];");
            descricaoLogAlteracao.append("\nITENSVALIDACAO: [");
            descricaoLogAlteracao.append(codigosPlanos);
            descricaoLogAlteracao.append("];");
        }

        if (!UteisValidacao.emptyString(descricaoLogAnterior.toString()) || !UteisValidacao.emptyString(descricaoLogAlteracao.toString())) {
            incluirLog(ctx, locacaoTO.getCodigo().toString(), "",
                    descricaoLogAnterior.toString(), descricaoLogAlteracao.toString(),
                    "ALTERAÇÃO", "ALTERAÇÃO DE LOCAÇÃO", EntidadeLogEnum.LOCACAO, "Locação", sessaoService, logDao, null, null);
        }
    }

    @Override
    public void deletar(Integer codigo) throws ServiceException {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            Locacao locacao = locacaoDao.findById(chave, codigo);
            locacao.setDataFinal(Calendario.ontem());
            locacaoDao.update(chave, locacao);
            incluirLog(chave, locacao.getCodigo().toString(), "",
                    null, getDescricaoLocacaoParaLog(new LocacaoTO(locacao, false), null),
                    "EXCLUSÃO", "EXCLUSÃO DE LOCAÇÃO", EntidadeLogEnum.LOCACAO, "LOCAÇÃO", sessaoService, logDao, null, null);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void deletarLocacaoHorario(Integer codigo) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            locacaoHorarioDao.getCurrentSession(ctx).clear();
            LocacaoHorario locacaoHorario = locacaoHorarioDao.findById(ctx, codigo);
            locacaoHorario.setAtivo(false);
            incluirLog(ctx, locacaoHorario.getCodigo().toString(), "",
                    null, getDescricaoLocacaoHorarioParaLog(new LocacaoHorarioTO(locacaoHorario), null),
                    "EXCLUSÃO", "EXCLUSÃO DE LOCAÇÃO HORÁRIO", EntidadeLogEnum.LOCACAO_HORARIO, "LOCAÇÃO HORÁRIO", sessaoService, logDao, null, null);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void validacoes(List<LocacaoHorarioTO> horarioTOS) throws ServiceException {
        for (LocacaoHorarioTO lc: horarioTOS) {
            if (lc.getAmbientes() == null || lc.getAmbientes().isEmpty()) {
                throw new ServiceException("O campo \"Ambiente\" deve ser informado.");
            }
            if (lc.getResponsavel() == null || UteisValidacao.emptyNumber(lc.getResponsavel().getCodigo())) {
                throw new ServiceException("O campo \"Responsável\" deve ser informado.");
            }
            if (UteisValidacao.emptyString(lc.getHoraInicio())) {
                throw new ServiceException("O campo \"Hora inicio\" deve ser informado.");
            }
            if (UteisValidacao.emptyString(lc.getHoraFim())) {
                throw new ServiceException("O campo \"Hora final\" deve ser informado.");
            }
            if (UteisValidacao.emptyString(lc.getDiaSemana())) {
                throw new ServiceException("O campo \"Dia\" deve ser informado.");
            }
        }
    }

    public void validacoes(String ctx, Integer empresaID, LocacaoTO locacaoTO) throws ServiceException {
        if (UteisValidacao.emptyString(locacaoTO.getNome())) {
            throw new ServiceException("O campo \"Nome\" deve ser informado.");
        }
        if (UteisValidacao.emptyString(locacaoTO.getDescricao())) {
            throw new ServiceException("O campo \"Descrição\" deve ser informado.");
        }
        if (UteisValidacao.emptyString(locacaoTO.getCor())) {
            throw new ServiceException("O campo \"Cor\" deve ser informado.");
        }
        if (locacaoTO.getTipoHorario() == null || TipoHorarioLocacaoEnum.getByCodigo(locacaoTO.getTipoHorario()) == null) {
            throw new ServiceException("O campo \"Tipo horário\" deve ser informado.");
        }

        if (locacaoTO.getTipoHorario().equals(TipoHorarioLocacaoEnum.PLAY.getCodigo())) {
            if (!TipoValidacaoLocacaoEnum.LIVRE.getCodigo().equals(locacaoTO.getTipoValidacao())) {
                if (locacaoTO.getTipoValidacao() == null || TipoValidacaoLocacaoEnum.getByCodigo(locacaoTO.getTipoValidacao()) == null) {
                    throw new ServiceException("O campo \"Tipo validação\" deve ser informado.");
                }
                if (UteisValidacao.emptyList(locacaoTO.getItensValidacao())
                        && UteisValidacao.emptyList(locacaoTO.getItensValidacao())) {
                    throw new ServiceException("O campo \"Itens de validação\" deve ser informado.");
                }
            }
        } else {
            if (locacaoTO.getTipoHorario().equals(TipoHorarioLocacaoEnum.LIVRE.getCodigo())
                && UteisValidacao.emptyNumber(locacaoTO.getTempoMinimoMinutos())) {
                throw new ServiceException("O campo \"Tempo mínimo\" deve ser informado.");
            }
            if (locacaoTO.getTipoHorario().equals(TipoHorarioLocacaoEnum.PRE_DEFINIDO.getCodigo())
                    && UteisValidacao.emptyNumber(locacaoTO.getDuracaoMinutos())) {
                throw new ServiceException("O campo \"Duração\" deve ser informado.");
            }
            if (locacaoTO.getProduto() == null || UteisValidacao.emptyNumber(locacaoTO.getProduto().getCodigo())) {
                throw new ServiceException("O campo \"Produto\" deve ser informado.");
            }
        }
        if (locacaoTO.getDataInicial() == null) {
            throw new ServiceException("O campo \"Data inicial\" deve ser informado.");
        }
        if (locacaoTO.getDataFinal() == null) {
            throw new ServiceException("O campo \"Data final\" deve ser informado.");
        }
        if (!UteisValidacao.emptyString(locacaoTO.getBase64Imagem())) {
            String stringImagem = locacaoTO.getBase64Imagem().split(",")[1];
            byte[] imagem = DatatypeConverter.parseBase64Binary(stringImagem);
            double megabytes = (double) imagem.length / (1024 * 1024);
            if (megabytes > 2.0) {
                throw new ServiceException("O tamanho máximo da imagem deve ser de 2 megabytes.");
            }
        }
        if (existeLocacaoComNome(ctx, empresaID, locacaoTO.getNome(), locacaoTO.getCodigo())) {
            throw new ServiceException("Já existe uma locação cadastrada com o nome \"" + locacaoTO.getNome() + "\", deve ser informado outro nome.");
        }
    }

    private boolean existeLocacaoComNome(String ctx, Integer empresaId, String nomeLocacao, Integer codLocacao) {
        try {
            StringBuilder where = new StringBuilder();
            where.append(" nome ilike '").append(nomeLocacao).append("' and empresa = ").append(empresaId);

            if (codLocacao != null) {
                where.append(" and codigo <> ").append(codLocacao);
            }

            return locacaoDao.existsWithParam(ctx, where);
        } catch (Exception e) {
            Uteis.logar(e, LocacaoServiceImpl.class);
        }
        return false;
    }

    public void cardsLocacoesDisponiveis(String chave, String chaveAgrupadora,
                                       Date diaMes, List<String> listaHorarios,
                                       Integer empresa,
                                       Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda, FiltroTurmaDTO filtro) throws Exception {
        if (!exibirAgendaLocacao(filtro)) {
            return;
        }

        String diaSemana = Uteis.obterDiaSemanaData(diaMes);
        try (ResultSet rs = locacaoDao.createStatement(chave,
                " select distinct lh.horainicio, lh.horafim from locacaohorario lh  \n" +
                        " inner join locacao l on l.codigo = lh.locacao  \n" +
                        " where lh.ativo = true and diasemana = '" + diaSemana + "' \n" +
                        " and l.empresa = " + empresa + " \n" +
                        " and '" + Uteis.getDataAplicandoFormatacao(diaMes, "yyyy-MM-dd") + "' \n" +
                        " between DATE(l.datainicial) AND DATE(l.datafinal) ")) {
            while (rs.next()) {
                for (String hora : listaHorarios) {
                    if (agenda.getOrDefault(hora, null) != null && !agenda.get(hora).getOrDefault(chaveAgrupadora, Collections.emptyList()).isEmpty()) {
                        continue;
                    }

                    int horaNumero = Integer.parseInt(hora.replace(":", ""));
                    int menorHoraNumero = Integer.parseInt(rs.getString("horainicio").replace(":", ""));
                    int maiorHoraNumero = Integer.parseInt(rs.getString("horafim").replace(":", ""));
                    if (horaNumero >= menorHoraNumero && horaNumero <= maiorHoraNumero) {
                        Date inicio = Calendario.getDataComHora(diaMes, hora);
                        Date fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, 29);

                        String horaInicio = Uteis.getDataAplicandoFormatacao(inicio, "HH:mm");
                        String horaFim = Uteis.getDataAplicandoFormatacao(fim, "HH:mm");

                        HorarioItemAgendaDTO servicoDia = new HorarioItemAgendaDTO();
                        servicoDia.setTipo(TipoItemAgendaEnum.locacao);
                        servicoDia.setDiaMes(String.valueOf(diaMes.getTime()));
                        servicoDia.setInicio(horaInicio);
                        servicoDia.setFim(horaFim);

                        Map<String, List<HorarioItemAgendaDTO>> aulasNoHorario = agenda.computeIfAbsent(hora, k -> new HashMap<>());
                        List<HorarioItemAgendaDTO> aulas = aulasNoHorario.computeIfAbsent(
                                chaveAgrupadora, k -> new ArrayList<>());
                        aulas.add(servicoDia);
                    }
                }
            }
        }
    }

    public List<AgendaDisponibilidadeDTO> disponibilidades(Integer empresaId,
                                                           String ctx,
                                                           Date dia,
                                                           String horaSelecionada,
                                                           Integer locacaoCodigo,
                                                           PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<AgendaDisponibilidadeDTO> disponibilidades = new ArrayList<>();
            String diaSemana = Uteis.obterDiaSemanaData(dia);
            String dataFormatada = Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd");
            String horaSelecionadaFormatada = horaSelecionada.trim().isEmpty() ? "" : horaSelecionada.replace("h", ":");

            String query = getQuery(dataFormatada, diaSemana, horaSelecionadaFormatada, locacaoCodigo);

            try (ResultSet rs = locacaoDao.createStatement(ctx, query)) {
                while (rs.next()) {
                    AgendaDisponibilidadeDTO disponibilidade = new AgendaDisponibilidadeDTO();
                    disponibilidade.setId(rs.getInt("id_disponibilidade"));
                    disponibilidade.setDia(Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd"));
                    disponibilidade.setAmbienteId(rs.getInt("ambienteId"));
                    disponibilidade.setAmbiente(rs.getString("nomeAmbiente"));

                    String horaInicio = rs.getString("start_time");
                    String horaFim = rs.getString("end_time");

                    // Garantir que os horários de início e fim estão corretamente formatados
                    disponibilidade.setHorarioInicial(horaInicio != null ? horaInicio : "00:00");
                    disponibilidade.setHorarioFinal(horaFim != null ? horaFim : "00:00");

                    disponibilidade.setDescricao(rs.getString("nome"));

                    TipoHorarioLocacaoEnum tipohorario = TipoHorarioLocacaoEnum.getByCodigo(rs.getInt("tipohorario"));
                    disponibilidade.setTipoHorario(tipohorario == null ? "" : tipohorario.getDescricao());
                    disponibilidade.setCodigoZwAmbiente(rs.getInt("codZwAmbiente"));

                    disponibilidades.add(disponibilidade);
                }
            }

            ConfiguracaoSistema configuracaoSistema = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_MESMO_AMBIENTE);
            if (configuracaoSistema.getValorAsBoolean()) {
                verificarBloqueadosLocacao(ctx, dia, dia, empresaId, disponibilidades);
            }
            return AgendaCardsServiceImpl.paginar(disponibilidades, paginadorDTO);
        } catch (Exception e) {
            Uteis.logar(e, LocacaoServiceImpl.class);
            throw new ServiceException(e);
        }
    }

    private static String getQuery(String dataFormatada, String diaSemana, String horaSelecionadaFormatada, Integer codigoLocacao) {
        return "WITH ocupados AS ("
                + "    SELECT al.locacaohorario, al.inicio::time AS hora, l.nome AS nome, "
                + "           l.tipohorario AS tipohorario, a.nome AS nomeAmbiente, a.codigozw AS codZwAmbiente, a.codigo as ambienteId "
                + "    FROM agendamentolocacao al "
                + "    JOIN ambiente a ON a.codigo = al.ambiente "
                + "    JOIN locacaohorario lh ON lh.codigo = al.locacaohorario "
                + "    JOIN locacao l ON l.codigo = lh.locacao "
                + "    WHERE al.inicio::date = '" + dataFormatada + "' AND al.iscancelado = false "
                + "    UNION ALL "
                + "    SELECT al.locacaohorario, al.fim::time AS hora, l.nome AS nome, "
                + "           l.tipohorario AS tipohorario, a.nome AS nomeAmbiente, a.codigozw AS codZwAmbiente, a.codigo as ambienteId "
                + "    FROM agendamentolocacao al "
                + "    JOIN ambiente a ON a.codigo = al.ambiente "
                + "    JOIN locacaohorario lh ON lh.codigo = al.locacaohorario "
                + "    JOIN locacao l ON l.codigo = lh.locacao "
                + "    WHERE al.inicio::date = '" + dataFormatada + "' AND al.iscancelado = false "
                + "), "
                + "disponibilidades AS ("
                + "    SELECT lh.codigo AS id_disponibilidade, lh.horaInicio::time AS hora, "
                + "           l.nome, l.tipohorario, a.nome AS nomeAmbiente, a.codigozw AS codZwAmbiente, a.codigo as ambienteId "
                + "    FROM locacaohorario lh "
                + "    INNER JOIN locacao l ON l.codigo = lh.locacao "
                + "    INNER JOIN ambientehorariolocacao ahl ON ahl.locacaohorario = lh.codigo "
                + "    INNER JOIN ambiente a ON a.codigo = ahl.ambiente "
                + "    WHERE lh.diasemana = '" + diaSemana + "' AND lh.ativo = TRUE "
                + "          AND '" + dataFormatada + "' BETWEEN DATE(l.datainicial) AND DATE(l.datafinal) "
                + (codigoLocacao > 0 ? " AND lh.locacao = " + codigoLocacao + " " : "")
                + "    UNION ALL "
                + "    SELECT lh.codigo AS id_disponibilidade, lh.horaFim::time AS hora, "
                + "           l.nome, l.tipohorario, a.nome AS nomeAmbiente, a.codigozw AS codZwAmbiente, a.codigo as ambienteId "
                + "    FROM locacaohorario lh "
                + "    INNER JOIN locacao l ON l.codigo = lh.locacao "
                + "    INNER JOIN ambientehorariolocacao ahl ON ahl.locacaohorario = lh.codigo "
                + "    INNER JOIN ambiente a ON a.codigo = ahl.ambiente "
                + "    WHERE lh.diasemana = '" + diaSemana + "' AND lh.ativo = TRUE "
                + "          AND '" + dataFormatada + "' BETWEEN DATE(l.datainicial) AND DATE(l.datafinal) "
                + (codigoLocacao > 0 ? " AND lh.locacao = " + codigoLocacao + " " : "")
                + "), "
                + "time_points AS ("
                + "    SELECT id_disponibilidade, hora, nome, tipohorario, nomeAmbiente, codZwAmbiente, ambienteId FROM disponibilidades "
                + "    UNION "
                + "    SELECT locacaohorario, hora, nome, tipohorario, nomeAmbiente, codZwAmbiente, ambienteId FROM ocupados "
                + "), "
                + "sorted_times AS ("
                + "    SELECT id_disponibilidade, hora, "
                + "           LEAD(hora) OVER (ORDER BY id_disponibilidade, hora) AS next_hora, "
                + "           nome, tipohorario, nomeAmbiente, codZwAmbiente, ambienteId "
                + "    FROM time_points "
                + "), "
                + "available_slots AS ("
                + "    SELECT st.id_disponibilidade, TO_CHAR(st.hora, 'HH24:MI') AS start_time, TO_CHAR(st.next_hora, 'HH24:MI') AS end_time, "
                + "           st.nome, st.tipohorario, st.nomeAmbiente, st.codZwAmbiente, st.ambienteId "
                + "    FROM sorted_times st "
                + "where not exists(SELECT 1 FROM agendamentolocacao al WHERE st.id_disponibilidade = locacaohorario AND "
                + "TO_TIMESTAMP('" + dataFormatada + "' || ' ' || st.hora, 'YYYY-MM-DD HH24:MI:SS') = al.inicio AND TO_TIMESTAMP('" + dataFormatada + "' || ' ' || st.next_hora, 'YYYY-MM-DD HH24:MI:SS') = al.fim) "
                + "AND exists(SELECT 1 FROM locacaohorario lh where lh.codigo = st.id_disponibilidade and st.hora::time >= lh.horainicio::time and st.next_hora::time <= lh.horafim::time)) "
                + "SELECT DISTINCT ON (start_time, end_time, id_disponibilidade) * "
                + "FROM available_slots "
                + "WHERE start_time < end_time "
                + (horaSelecionadaFormatada.isEmpty() ? "" : "AND CAST('"+horaSelecionadaFormatada+"' AS time) BETWEEN CAST(start_time AS time) AND CAST(end_time AS time) ")
                + "ORDER BY start_time, end_time;";
    }

    public void verificarBloqueadosLocacao(String ctx,
                                            Date inicio,
                                            Date fim,
                                            Integer empresaId,
                                            List<AgendaDisponibilidadeDTO> locacoes) {
        try {
            Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendados = ambientesAgendados(ctx, inicio, fim, empresaId);
            Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendadosLocacao = ambientesAgendadosLocacao(ctx, inicio, fim, empresaId);
            if (ambientesAgendados.isEmpty() && ambientesAgendadosLocacao.isEmpty()) {
                return;
            }

            for (AgendaDisponibilidadeDTO locacao : locacoes) {
                List<AmbienteAgendadoTO> ambienteAgendadoTOS = ambientesAgendados.get(locacao.getCodigoZwAmbiente());
                if (UteisValidacao.emptyList(ambienteAgendadoTOS)) {
                    continue;
                }

                Date horarioInicial = Uteis.getDate(locacao.getDia() + locacao.getHorarioInicial(), "yyyyMMddHH:mm");
                Date horarioFim = Uteis.getDate(locacao.getDia() + locacao.getHorarioFinal(), "yyyyMMddHH:mm");
                for (AmbienteAgendadoTO aa : ambienteAgendadoTOS) {
                    boolean bloquear =
                            (locacoes.size() == 1)
                            && ((horarioInicial.getTime() == aa.getInicio().getTime() || horarioFim.getTime() == aa.getFim().getTime())
                            || Calendario.entreENaoIgual(horarioInicial, aa.getInicio(), aa.getFim())
                            || Calendario.entreENaoIgual(horarioFim, aa.getInicio(), aa.getFim())
                            || Calendario.entreENaoIgual( aa.getInicio(), horarioInicial, horarioFim));
                    if (bloquear) {
                        locacao.setBloqueado(true);
                    }
                }
            }

            for (AgendaDisponibilidadeDTO locacao : locacoes) {
                if (!locacao.getBloqueado()) {
                    List<AmbienteAgendadoTO> ambienteAgendadoLocacaoTOS = ambientesAgendadosLocacao.get(locacao.getCodigoZwAmbiente());
                    if (UteisValidacao.emptyList(ambienteAgendadoLocacaoTOS)) {
                        continue;
                    }
                Date horarioInicial = Uteis.getDate(locacao.getDia() + locacao.getHorarioInicial(), "yyyyMMddHH:mm");
                Date horarioFim = Uteis.getDate(locacao.getDia() + locacao.getHorarioFinal(), "yyyyMMddHH:mm");
                    for (AmbienteAgendadoTO aa : ambienteAgendadoLocacaoTOS) {
                        boolean bloquear = !locacao.getId().equals(aa.getHorarioTurma())
                                && locacoes.size() == 1
                                && ((horarioInicial.getTime() == aa.getInicio().getTime() || horarioFim.getTime() == aa.getFim().getTime())
                                || Calendario.entreENaoIgual(horarioInicial, aa.getInicio(), aa.getFim())
                                || Calendario.entreENaoIgual(horarioFim, aa.getInicio(), aa.getFim())
                                || Calendario.entreENaoIgual(aa.getInicio(), horarioInicial, horarioFim));
                        if (bloquear) {
                            locacao.setBloqueado(true);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, AgendaServiceImpl.class);
        }
    }

    private Map<Integer, List<AmbienteAgendadoTO>>  ambientesAgendadosLocacao(String ctx, Date inicio, Date fim, Integer empresaId) throws Exception {
        JSONArray content = new JSONArray();
        StringBuilder sql = new StringBuilder();
        sql.append("select a.codigozw as codigoZwAmbiente, al.codigo as codigoLocacao, al.inicio, al.fim \n");
        sql.append("from agendamentolocacao al \n");
        sql.append("inner join ambiente a on a.codigo = al.ambiente \n");
        sql.append("where al.inicio between '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd")).append("' ");
        sql.append("and '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd") + " 23:59:59' ");

        try (ResultSet rs =  agendamentoLocacaoDao.createStatement(ctx, sql.toString())) {
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("dia", inicio.getTime());
                json.put("ambiente", rs.getInt("codigoZwAmbiente"));
                json.put("codigoLocacao", rs.getInt("codigoLocacao"));
                json.put("inicio", rs.getString("inicio"));
                json.put("fim", rs.getString("fim"));
                content.put(json);
            }
        }

        return new HashMap() {{
            for (int i = 0; i < content.length(); i++) {
                AmbienteAgendadoTO ambienteAgendadoTO = new AmbienteAgendadoTO(content.getJSONObject(i), true);
                List<AmbienteAgendadoTO> agendados = (ArrayList) get(ambienteAgendadoTO.getAmbiente());
                if (agendados == null) {
                    agendados = new ArrayList<>();
                    put(ambienteAgendadoTO.getAmbiente(), agendados);
                }
                agendados.add(ambienteAgendadoTO);
            }
        }};
    }

    private Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendados(String ctx,
                                                                      Date inicio,
                                                                      Date fim,
                                                                      Integer empresaId) throws Exception{
        JSONObject retorno = chamadaZW(ctx, "/prest/aulacheia/ambientes-agendados", empresaId, inicio.getTime(), fim.getTime(), null);
        JSONArray content = retorno.optJSONArray("content");
        if (content == null || content.length() == 0) {
            return new HashMap<>();
        }
        return new HashMap() {{
            for (int i = 0; i < content.length(); i++) {
                AmbienteAgendadoTO ambienteAgendadoTO = new AmbienteAgendadoTO(content.getJSONObject(i));
                List<AmbienteAgendadoTO> agendados = (ArrayList) get(ambienteAgendadoTO.getAmbiente());
                if (agendados == null) {
                    agendados = new ArrayList<>();
                    put(ambienteAgendadoTO.getAmbiente(), agendados);
                }
                agendados.add(ambienteAgendadoTO);
            }
        }};
    }

    private JSONObject chamadaZW(String ctx,
                                 String endpoint,
                                 Integer empresa,
                                 Long inicio,
                                 Long fim,
                                 JSONObject dados) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        if (dados != null) {
            params.add(new BasicNameValuePair("dados", dados.toString()));
        }
        if (inicio != null) {
            params.add(new BasicNameValuePair("inicio", inicio.toString()));
        }
        if (fim != null) {
            params.add(new BasicNameValuePair("fim", fim.toString()));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    public List<AgendamentoLocacaoProduto> produtosLocados(String chave, Integer agendamento) throws Exception{
        String hql = "SELECT obj FROM AgendamentoLocacaoProduto" +
                " obj WHERE obj.agendamento.codigo = :t";
        Map<String, Object> params = new HashMap<>();
        params.put("t", agendamento);
        return agendamentoLocacaoProdutoDao.findByParam(chave, hql, params);
    }

    public ConfigAgendamentoLocacaoDTO editAgendamento(Integer agendamentoId, String data, String ctx) throws ServiceException {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            AgendamentoLocacao agendamento = agendamentoLocacaoDao.findById(chave, agendamentoId);
            ConfigAgendamentoLocacaoDTO config = configAgendamento(agendamento.getLocacaoHorario().getCodigo(), data, ctx);
            config.setNomeLocacao(agendamento.getLocacaoHorario().getLocacao().getNome());
            config.setData(Uteis.getDataAplicandoFormatacao(agendamento.getInicio(), "dd/MM/yyyy"));

            if (agendamento.getCancelado()) {
                config.setCancelado(agendamento.getCancelado());
                config.setJustificativa(agendamento.getJustificativa());
                config.setDataCancelamento(Uteis.getDataAplicandoFormatacao(agendamento.getDataCancelamento(), "dd/MM/yyyy HH:mm"));
                Usuario usuarioCancelou = usuarioService.obterPorId(chave, agendamento.getUsuarioCancelou());
                config.setUsuarioCancelamento(usuarioCancelou != null ? usuarioCancelou.getUserName() : " - ");
            }

            if (agendamento.getFinalizado()) {
                config.setFinalizado(agendamento.getFinalizado());
            }

            Set<String> horariosAdicionados = new HashSet<>();
            config.setHorariosAdicionados(new ArrayList<>());
            LocacaoHorarioTO horarioTO = new LocacaoHorarioTO();
            horarioTO.setCodigo(agendamento.getLocacaoHorario().getCodigo());
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
            horarioTO.setHoraInicio(sdf.format(agendamento.getInicio()));
            horarioTO.setHoraFim(sdf.format(agendamento.getFim()));
            horarioTO.setFinalizado(config.isFinalizado());

            LocalTime inicio = LocalTime.parse(horarioTO.getHoraInicio());
            LocalTime fim = LocalTime.parse(horarioTO.getHoraFim());

            horarioTO.setValorLocacao((config.getValorHora() / 60) * (double) Duration.between(inicio, fim).toMinutes());
            horarioTO.setValorExtrasObrigatorios(Optional.ofNullable(config.getValorExtrasObrigatorios()).orElse(0D));
            horarioTO.setValorExtrasAdicionais(Optional.ofNullable(config.getValorExtrasAdicionais()).orElse(0D));
            horarioTO.setValorTotal(horarioTO.getValorLocacao() + horarioTO.getValorExtrasObrigatorios() + horarioTO.getValorExtrasAdicionais());
            horarioTO.setVendaAvulsaCodigo(agendamento.getVendaAvulsa());

            config.getHorariosAdicionados().add(horarioTO);
            horariosAdicionados.add(horarioTO.getInicioFim());

            LocacaoHorario locacaoHorario = locacaoHorarioDao.findById(chave, horarioTO.getCodigo());
            if (locacaoHorario == null) {
                throw new ServiceException("Horário de locação não encontrado.");
            }

            Locacao locacao = locacaoHorario.getLocacao();
            if (locacao == null) {
                throw new ServiceException("Locação não encontrada para o horário fornecido.");
            }

            Locacao locacaoEncontrada = locacaoDao.findById(chave, locacao.getCodigo());
            if (locacaoEncontrada == null) {
                throw new ServiceException("A locação não foi encontrada no banco de dados.");
            }

            config.setTempoMinimoMinutos(locacaoEncontrada.getTempoMinimoMinutos());

            config.getHorarios().removeIf(lo -> horariosAdicionados.contains(lo.getInicioFim()));

            List<AgendamentoLocacaoProduto> agendamentoLocacaoProdutos = produtosLocados(chave, agendamento.getCodigo());
            for (AgendamentoLocacaoProduto al : agendamentoLocacaoProdutos) {
                if (config.getProdutosObrigatorios().stream().noneMatch(it -> it.getProduto().getCodigo().equals(al.getProduto()))) {
                    LocacaoProdutoSugeridoTO locp = new LocacaoProdutoSugeridoTO(al);
                    locp.setQuantidade(new QuantidadeDTO(al.getQuantidade(), al.getQuantidade().toString()));
                    locp.setProduto(consultarProduto(chave, locp.getProduto().getCodigo()));
                    config.getProdutosObrigatorios().add(locp);
                }
            }
            config.setAluno(alunoLocacao(chave, agendamento.getCodigo()));
            config.setCodigoPessoaClienteZW(codigoPessoaClienteZW(chave, config.getAluno().getMatriculaZW()));
            if (!UteisValidacao.emptyNumber(agendamento.getAmbiente())) {
                Ambiente ambiente = ambienteService.consultarPorAmbiente(chave, agendamento.getAmbiente());
                if (ambiente != null) {
                    AmbienteDTO ambienteDTO = new AmbienteDTO();
                    ambienteDTO.setNome(ambiente.getNome());
                    ambienteDTO.setId(ambiente.getCodigo());
                    config.setAmbiente(ambienteDTO);
                }
            }
            return config;
        } catch (Exception e) {
            Uteis.logar(e, LocacaoServiceImpl.class);
            throw new ServiceException(e);
        }
    }

    private Integer codigoPessoaClienteZW(String ctx, Integer codigoMatricula) throws Exception {
        Integer codigoCliente = null;
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                        "select pessoa from cliente where codigomatricula = " + codigoMatricula + " limit 1", conZW)) {
                    while (rs.next()) {
                        codigoCliente = rs.getInt("pessoa");
                    }
                }
            } catch (Exception e) {
                Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
            }
        }

        return codigoCliente;
    }

    private AlunoResponseTO alunoLocacao(String ctx, Integer codigoAgendamento) throws Exception{
        AlunoResponseTO aluno;
        try (ResultSet rs = agendamentoLocacaoDao.createStatement(ctx,
                "select cli.nome, cli.codigo, cli.matricula, cli.codigocliente, cli.telefones, cli.situacao, pes.fotoKey " +
                    "from clientesintetico cli " +
                    "inner join agendamentolocacao a on a.cliente_codigo = cli.codigo " +
                    "inner join pessoa pes on pes.codigo = cli.pessoa_codigo " +
                    "where a.codigo = " + codigoAgendamento)) {
            aluno = new AlunoResponseTO();
            if (rs.next()) {
                aluno.setId(rs.getInt("codigo"));
                aluno.setCodigoCliente(rs.getInt("codigocliente"));
                aluno.setMatriculaZW(rs.getInt("matricula"));
                aluno.setNome(rs.getString("nome"));
                aluno.setSituacaoAluno(SituacaoAlunoEnum.getInstance(rs.getString("situacao")));
                aluno.setFones(new ArrayList<>());
                for (String telefone : Uteis.stringToList(rs.getString("telefones"), ";")) {
                    TelefoneDTO telefoneDTO = new TelefoneDTO();
                    telefoneDTO.setNumero(telefone);
                    if (telefone.length() == 13) {
                        telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                    } else {
                        telefoneDTO.setTipo(TipoTelefoneEnum.FIXO);
                    }
                    aluno.getFones().add(telefoneDTO);
                }
                if (aluno.getFones().size() == 0) {
                    TelefoneDTO telefoneDTO = new TelefoneDTO();
                    telefoneDTO.setNumero("-");
                    aluno.getFones().add(telefoneDTO);
                }
                if (!UteisValidacao.emptyString(rs.getString("fotoKey")) && !"fotoPadrao.jpg".equals(rs.getString("fotoKey"))) {
                    aluno.setImageUri(Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotoKey")));
                } else {
                    aluno.setImageUri(null);
                }
            }
        }
        return aluno;
    }

    public ConfigAgendamentoLocacaoDTO configAgendamento(Integer codigoHorarioLocacao, String data, String ctx) throws ServiceException {
        try {
            ConfigAgendamentoLocacaoDTO config = new ConfigAgendamentoLocacaoDTO();
            LocacaoHorario locacaoHorario = locacaoHorarioDao.findById(ctx, codigoHorarioLocacao);
            LocacaoTO locacaoTO = new LocacaoTO(locacaoHorario.getLocacao(), false);
            montarUsuario(ctx, locacaoTO);
            montarProduto(ctx, locacaoTO);
            if (locacaoTO.getProduto() != null && UteisValidacao.notEmptyNumber(locacaoTO.getProduto().getCodigo())) {
                config.setProduto(Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(locacaoTO.getProduto().getDescricao()));
                config.setCodigoProduto(locacaoTO.getProduto().getCodigo());
                config.setValorHora(locacaoTO.getProduto().getValorFinal());
                if (locacaoTO.getTipoHorarioDescricao().equalsIgnoreCase(TipoHorarioLocacaoEnum.PRE_DEFINIDO.getDescricao())) {
                    config.setValorLocacao(config.getValorHora() / 60 * locacaoTO.getDuracaoMinutos());
                } else {
                    config.setValorLocacao(config.getValorHora() / 60 * locacaoHorario.getTempoMinimoMinutos());
                }
            }

            LocacaoHorarioTO horarioAddTO = new LocacaoHorarioTO();
            for (LocacaoHorarioTO horario : locacaoTO.getHorarios()) {
                if (!horario.getAtivo()) {
                    continue;
                }

                if (horario.getDiaSemana().equalsIgnoreCase(locacaoHorario.getDiaSemana())) {
                    LocacaoHorarioTO hr = new LocacaoHorarioTO();
                    String[] split = horario.getInicioFim().split(" - ");
                    hr.setHoraInicio(split[0]);
                    hr.setHoraFim(split[1]);
                    hr.setCodigo(horario.getCodigo());
                    hr.setFinalizado(config.isFinalizado());
                    hr.setTempoMinimoMinutos(horario.getTempoMinimoMinutos());

                    hr.setValorLocacao(Optional.ofNullable(config.getValorLocacao()).orElse(0D));
                    hr.setValorExtrasObrigatorios(Optional.ofNullable(config.getValorExtrasObrigatorios()).orElse(0D));
                    hr.setValorExtrasAdicionais(Optional.ofNullable(config.getValorExtrasAdicionais()).orElse(0D));
                    hr.setValorTotal(hr.getValorLocacao() + hr.getValorExtrasObrigatorios() + hr.getValorExtrasAdicionais());

                    String query = "SELECT al FROM AgendamentoLocacao al\n" +
                            "WHERE al.locacaoHorario.codigo = :locacaohorario\n" +
                            (!data.isEmpty() ? "AND al.inicio = :dataInicio\n" : "") +
                            "ORDER BY al.codigo";
                    Map<String, Object> params = new HashMap<>();
                    params.put("locacaohorario", hr.getCodigo());
                    if (!data.isEmpty()) {
                        String formattedDate = data.substring(0, 4) + "-" + data.substring(4, 6) + "-" + data.substring(6, 8) + " " + hr.getHoraInicio() + ":00";
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        params.put("dataInicio", formatter.parse(formattedDate));
                    }

                    hr.setPossuiAgendamentos(agendamentoLocacaoDao.findObjectByParam(ctx, query, params) != null);

                    if (!data.isEmpty()) {
                        config.getHorarios().add(hr);
                    }
                }

                if (horario.getCodigo().equals(locacaoHorario.getCodigo())) {
                    config.setResponsavel(horario.getResponsavel().getNome());
                    for (AmbienteHorarioLocacaoTO ambienteHorarioLocacaoTO : horario.getAmbientes()) {
                        AmbienteDTO ambienteDTO = new AmbienteDTO();
                        ambienteDTO.setId(ambienteHorarioLocacaoTO.getAmbiente().getCodigo());
                        ambienteDTO.setNome(ambienteHorarioLocacaoTO.getAmbiente().getNome());
                        ambienteDTO.setCapacidade(ambienteHorarioLocacaoTO.getAmbiente().getCapacidade());

                        config.getAmbientes().add(ambienteDTO);
                    }
                    horarioAddTO = horario;
                }
            }

            config.setNomeLocacao(locacaoTO.getNome());
            config.getHorariosAdicionados().add(horarioAddTO);
            for (LocacaoProdutoSugeridoTO locacaoProdutoSugeridoTO : locacaoTO.getProdutosSugeridos()) {
                locacaoProdutoSugeridoTO.setProduto(consultarProduto(ctx, locacaoProdutoSugeridoTO.getProduto().getCodigo()));
                if (locacaoProdutoSugeridoTO.getObrigatorio()) {
                    locacaoProdutoSugeridoTO.setQuantidade(new QuantidadeDTO(1, "1"));
                    config.getProdutosObrigatorios().add(locacaoProdutoSugeridoTO);
                    config.setValorExtrasObrigatorios(
                            config.getValorExtrasObrigatorios() + locacaoProdutoSugeridoTO.getValor()
                    );
                } else {
                    config.getProdutosSugeridos().add(locacaoProdutoSugeridoTO);
                }
            }
            config.setValorTotal(config.getValorLocacao() + config.getValorExtrasObrigatorios());
            config.setTipoHorario(locacaoHorario.getLocacao().getTipoHorario());
            config.setValorHora(locacaoTO.getProduto().getValorFinal());
            config.setTempoMinimoMinutos(locacaoTO.getTempoMinimoMinutos());

            if (Objects.equals(locacaoTO.getTipoHorario(), TipoHorarioLocacaoEnum.PLAY.getCodigo())) {
                String dataFormatada = LocalDate.parse(data, DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

                // Convertendo para LocalDateTime
                LocalDateTime inicio = converterParaLocalDateTime(dataFormatada, locacaoHorario.getHoraInicio());
                LocalDateTime fim = converterParaLocalDateTime(dataFormatada, locacaoHorario.getHoraFim());

                // Formatando para String no formato desejado
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String inicioFormatado = inicio.format(formatter);
                String fimFormatado = fim.format(formatter);

                String query = "SELECT * FROM agendamentolocacao ag WHERE ("
                        + " ('" + inicioFormatado + "' > ag.inicio AND '" + inicioFormatado + "' < ag.fim) OR"
                        + " ('" + fimFormatado + "' > ag.inicio AND '" + inicioFormatado + "' <= ag.fim) OR"
                        + " (ag.inicio >= '" + inicioFormatado + "' AND ag.inicio < '" + inicioFormatado + "'))";

                List<AgendamentoLocacao> agendamentos = new ArrayList<>();

                try (ResultSet rs = agendamentoLocacaoDao.createStatement(ctx, query)) {
                    while (rs.next()) {
                        LocalDateTime localDateTimeInicio = LocalDateTime.parse(rs.getString("inicio"), formatter);
                        LocalDateTime localDateTimeFim = LocalDateTime.parse(rs.getString("fim"), formatter);

                        AgendamentoLocacao ag = new AgendamentoLocacao();
                        ag.setCodigo(rs.getInt("codigo"));
                        ag.setInicio(Timestamp.valueOf(localDateTimeInicio));
                        ag.setFim(Timestamp.valueOf(localDateTimeFim));

                        agendamentos.add(ag);
                    }
                }

                config.setBloqueado(!agendamentos.isEmpty());
            } else {
                config.setBloqueado(false);
            }

            return config;
        } catch (Exception e) {
            Uteis.logar(e, LocacaoServiceImpl.class);
            throw new ServiceException(e);
        }
    }

    private static LocalDateTime converterParaLocalDateTime(String data, String hora) {
        LocalDate localDate = LocalDate.parse(data);
        LocalTime localTime = LocalTime.parse(hora);
        return LocalDateTime.of(localDate, localTime);
    }

    @Override
    public Integer agendar(Date dia, AgendamentoLocacaoDTO agendamentoDTO, Integer empresaId, String ctx, Integer usuarioZWId) throws ServiceException {
        AtomicReference<Integer> locacaoCodigo = new AtomicReference<>(0);

        try {
            checarHorariosDuplicados(agendamentoDTO.getHorarios());

            ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(ctx, agendamentoDTO.getMatricula());

            agendamentoDTO.getHorarios().forEach(it -> {
                LocacaoHorario locacaoHorario = null;
                try {
                    locacaoHorario = locacaoHorarioDao.findById(ctx, it.getCodigo());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String diaFormatado = formatter.format(dia);
                String query = "WITH disponibilidade AS ( " +
                        "    SELECT lh.horaInicio::time AS inicio_disponivel, " +
                        "           lh.horaFim::time AS fim_disponivel " +
                        "    FROM locacaohorario lh " +
                        "    JOIN locacao l ON l.codigo = lh.locacao " +
                        "    WHERE '" + diaFormatado + "' BETWEEN l.datainicial::date AND l.datafinal::date " +
                        "      AND '" + it.getHoraInicio() + "' >= lh.horaInicio::time " +
                        "      AND '" + it.getHoraFim() + "' <= lh.horaFim::time " +
                        "), " +
                        "conflitos AS ( " +
                        "    SELECT 1 " +
                        "    FROM agendamentolocacao al " +
                        "    WHERE al.inicio::date = '" + diaFormatado + "' " +
                        "      AND al.inicio::time < '" + it.getHoraFim() + "' " +
                        "      AND al.fim::time > '" + it.getHoraInicio() + "' " +
                        "      AND al.iscancelado = false" +
                        ") " +
                        "SELECT CASE " +
                        "           WHEN EXISTS (SELECT 1 FROM disponibilidade) AND NOT EXISTS (SELECT 1 FROM conflitos) " +
                        "           THEN true ELSE false " +
                        "       END AS pode_agendar;";

                try (ResultSet rs = agendamentoLocacaoDao.createStatement(ctx, query)) {
                    if (rs.next()) {
                        if (!rs.getBoolean("pode_agendar")) {
                            throw new ValidacaoException(AgendaExcecoes.SEM_DISPONIBILIDADE_HORARIO.getDescricaoExcecao());
                        }
                    }
                } catch (ServiceException e) {
                    try {
                        throw new ValidacaoException(AgendaExcecoes.SEM_DISPONIBILIDADE_HORARIO.getDescricaoExcecao());
                    } catch (ValidacaoException ex) {
                        throw new RuntimeException(ex);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

                AgendamentoLocacao agendamento = new AgendamentoLocacao(agendamentoDTO, cliente, locacaoHorario);
                agendamento.setModificadoPeloAluno(usuarioZWId == null);
                agendamento.setUsuarioZW(usuarioZWId);
                agendamento.setUsuarioResponsavelZW(locacaoHorario.getResponsavel());
                agendamento.setValorTotal(agendamentoDTO.getValorTotal());
                agendamento.setValorLocacao(agendamentoDTO.getValorLocacao());
                agendamento.setCancelado(false);

                try {
                    agendamento = agendamentoLocacaoDao.insert(ctx, agendamento);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

                for (LocacaoProdutoSugeridoTO servico : agendamentoDTO.getServicos()) {
                    AgendamentoLocacaoProduto agendamentoLocacaoProduto = getAgendamentoLocacaoProduto(servico, agendamento);
                    try {
                        agendamentoLocacaoProdutoDao.insert(ctx, agendamentoLocacaoProduto);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }

                agendamento.setInicio(Calendario.getDataComHora(dia, it.getHoraInicio()));
                agendamento.setFim(Calendario.getDataComHora(dia, it.getHoraFim()));

                try {
                    locacaoCodigo.set(agendamentoLocacaoDao.update(ctx, agendamento).getCodigo());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        } catch (Exception e) {
            Uteis.logar(e, LocacaoServiceImpl.class);
            throw new ServiceException(e);
        }

        return locacaoCodigo.get();
    }

    private void executeStatement(String chave, StringBuilder sql) {
        try (ResultSet rs = agendamentoLocacaoDao.createStatement(chave, sql.toString())) {
            if (rs.next()) {
                boolean isCancelado = rs.getBoolean("iscancelado");
                if (!isCancelado) {
                    throw new ServiceException(AgendaExcecoes.SEM_DISPONIBILIDADE_AMBIENTE.name());
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void checarHorariosDuplicados(List<LocacaoHorarioTO> horarios) throws ServiceException {
        Set<Integer> codigosChecados = new HashSet<>();

        for (LocacaoHorarioTO horario : horarios) {
            if (!codigosChecados.add(horario.getCodigo())) { // `add` retorna falso se o elemento já existir
                throw new ServiceException(AgendaExcecoes.HORARIO_DUPLICADO.name());
            }
        }
    }

    private static AgendamentoLocacaoProduto getAgendamentoLocacaoProduto(LocacaoProdutoSugeridoTO servico, AgendamentoLocacao agendamento) {
        AgendamentoLocacaoProduto agendamentoLocacaoProduto = new AgendamentoLocacaoProduto();
        agendamentoLocacaoProduto.setProduto(servico.getProduto().getCodigo());
        agendamentoLocacaoProduto.setQuantidade(servico.getQuantidade().getId());
        agendamentoLocacaoProduto.setObrigatorio(servico.getObrigatorio());
        agendamentoLocacaoProduto.setValorUnitario(servico.getValor());
        agendamentoLocacaoProduto.setValorTotal(
                agendamentoLocacaoProduto.getQuantidade() * servico.getValor());
        agendamentoLocacaoProduto.setAgendamento(agendamento);
        return agendamentoLocacaoProduto;
    }

    private Integer gerarVenda(String chave,
                               AgendamentoLocacaoDTO agendamentoDTO,
                               Locacao locacao,
                               String nomeComprador,
                               Integer codigoPessoa,
                               Double valorTotal,
                               Date dia,
                               Integer empresaId){
        try {
            VendaAvulsaDTO vendaAvulsaDTO = new VendaAvulsaDTO();
            vendaAvulsaDTO.setColaborador(false);
            vendaAvulsaDTO.setNomeComprador(nomeComprador);
            vendaAvulsaDTO.setDescricaoParcela(locacao.getNome().toUpperCase());
            vendaAvulsaDTO.setPessoa(codigoPessoa);
            vendaAvulsaDTO.setLancamento(Calendario.hoje().getTime());
            vendaAvulsaDTO.setParcelas(1);
            vendaAvulsaDTO.setTipo(0);
            vendaAvulsaDTO.setPrimeiraParcela(dia.getTime());
            vendaAvulsaDTO.setItens(new ArrayList<>());
            try (Connection conZW = conexaoZWService.conexaoZw(chave)){
                vendaAvulsaDTO.getItens().add(montarItemVenda(chave,
                        locacao.getProduto(),
                        1,
                        valorTotal,
                        conZW));
                for(LocacaoProdutoSugeridoTO produtoSugeridoTO : agendamentoDTO.getServicos()){
                    vendaAvulsaDTO.getItens().add(montarItemVenda(chave,
                            produtoSugeridoTO.getProduto().getCodigo(),
                            produtoSugeridoTO.getQuantidade().getId(),
                            produtoSugeridoTO.getValor(),
                            conZW));
                }
            }
            return enviarVenda(chave, vendaAvulsaDTO, empresaId);
        }catch (Exception e){
            Uteis.logar(e, LocacaoServiceImpl.class);
            return null;
        }
    }

    private Integer enviarVenda(String chave, VendaAvulsaDTO venda, Integer empresaId) throws Exception{
        JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/"+chave);
        String admCoreUrl = result.getJSONObject("content")
                .getJSONObject("serviceUrls").getString("admCoreUrl");
        HttpPost httpPost = new HttpPost(admCoreUrl + "/venda-avulsa");
        StringEntity entity = new StringEntity(new JSONObject(venda).toString(), "UTF-8");
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("Authorization", sessaoService.getUsuarioAtual().getToken());
        httpPost.setHeader("empresaId", empresaId.toString());
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        if (statusCode != 200) {
            throw new Exception(responseBody);
        }
        JSONObject json = new JSONObject(responseBody);
        return json.getJSONObject("content").getInt("venda");
    }

    private ItemVendaAvulsaDTO montarItemVenda(String chave,
                                               Integer codigoProduto,
                                               Integer qtd,
                                               Double valor,
                                               Connection conZW) throws Exception {
        ProdutoTO produtoLocacao = consultarProduto(chave, codigoProduto);
        if(produtoLocacao == null){
            throw new Exception("nao foi possivel encontrar o produto da locacao");
        }
        ItemVendaAvulsaDTO item = new ItemVendaAvulsaDTO();
        item.setCodigoProduto(codigoProduto);
        item.setQtd(qtd);
        valor = valor == null ? produtoLocacao.getValorFinal() : valor;
        item.setValorParcial(qtd * valor);
        item.setPrecoProduto(valor);
        item.setDescricaoProduto(produtoLocacao.getDescricao());
        return item;
    }

    @Override
    public void reagendar(Integer codigoAgendamentoLocacao, AgendamentoLocacaoDTO agendamentoDTO, Date dia, String ctx) throws ServiceException {
        try {
            if (agendamentoDTO.getHorarios().isEmpty()) {
                throw new ServiceException(LocacaoExcecoes.ERRO_REAGENDAR_LOCACAO_SEM_HORARIO);
            }

            String diaDaSemana = obterDiaSemanaFormatoLocacaoHorario(dia);
            // validar se o horario selecionado está disponível
            List<LocacaoHorarioTO> horariosDisponiveis = validarDataReagendamento(ctx, codigoAgendamentoLocacao, dia);
            boolean isHorarioValido = horariosDisponiveis.stream().anyMatch(horario -> horario.getCodigo().equals(agendamentoDTO.getHorarios().get(0).getCodigo()));

            if (!isHorarioValido) {
                throw new ServiceException(LocacaoExcecoes.ERRO_REAGENDAR_LOCACAO_SEM_DISPONIBILIDADE);
            }

            LocacaoHorario horarioSelecionado = locacaoHorarioDao.findById(ctx, agendamentoDTO.getHorarios().get(0).getCodigo());
            AgendamentoLocacao agendamentoLocacao = agendamentoLocacaoDao.findById(ctx, codigoAgendamentoLocacao);
            String descAnterior = getDescricaoLocacaoHorarioParaLog(agendamentoLocacao.getLocacaoHorario());
            String descAtual = getDescricaoLocacaoHorarioParaLog(horarioSelecionado);

            agendamentoLocacao.setLocacaoHorario(horarioSelecionado);
            agendamentoLocacao.setInicio(Calendario.getDataComHora(dia, horarioSelecionado.getHoraInicio()));
            agendamentoLocacao.setFim(Calendario.getDataComHora(dia, horarioSelecionado.getHoraFim()));
            agendamentoLocacao.setAmbiente(agendamentoDTO.getAmbiente());

            agendamentoLocacaoDao.update(ctx, agendamentoLocacao);

            // Gerar log de reagendamento
            incluirLog(ctx, codigoAgendamentoLocacao.toString(), "", descAnterior, descAtual, "ALTERAÇÃO", "REAGENDAMENTO DE LOCAÇÃO", EntidadeLogEnum.LOCACAO_REAGENDAMENTO, "Locação", sessaoService, logDao, null, null);

        } catch (Exception ex) {
            Uteis.logar(ex, LocacaoService.class);
            throw new ServiceException(LocacaoExcecoes.ERRO_REAGENDAR_LOCACAO, ex);
        }
    }

    private String getDescricaoLocacaoHorarioParaLog(LocacaoHorario horario) {
        return "Dia da semana: " + horario.getDiaSemana() +
                ", Hora de início: " + horario.getHoraInicio() +
                ", Hora de fim: " + horario.getHoraFim() +
                ", Permite agendamento pelo app: " + horario.getPermiteAgendarPeloAppTreino() +
                ", Ativo: " + horario.getAtivo();
    }

    private boolean exibirAgendaLocacao(FiltroTurmaDTO filtro){
        return filtro.getTipo() != null &&
                (filtro.getTipo().equals(TipoAulaCheiaOrigemEnum.LOCACAO) || filtro.getTipo().equals(TipoAulaCheiaOrigemEnum.TODAS));
    }


    public void cardsLocacoesAgendadas(String chave, String chaveAgrupadora,
                                         Date diaMes, List<String> listaHorarios,
                                         Integer empresa,
                                         Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda, FiltroTurmaDTO filtro) throws Exception{
        if(!exibirAgendaLocacao(filtro)){
            return;
        }

        try (ResultSet rs = agendamentoLocacaoDao.createStatement(chave,
                "select l.cor, al.inicio, al.fim, al.codigo, l.nome, a.nome as ambiente, a.codigo as ambienteCodigo, al.iscancelado, l.tipohorario, lh.codigo as locacaoHorarioCodigo from agendamentolocacao al\n" +
                        " inner join locacaoHorario lh on lh.codigo = al.locacaohorario\n" +
                        " inner join locacao l on l.codigo = lh.locacao\n" +
                        " inner join ambientehorariolocacao ahl on ahl.ambiente = al.ambiente \n" +
                        " inner join ambiente a on a.codigo = ahl.ambiente\n" +
                        " where al.inicio between '" + Uteis.getDataAplicandoFormatacao(Calendario.getDataComHoraZerada(diaMes), "yyyy-MM-dd") + " 00:00:00' \n" +
                        " and '" + Uteis.getDataAplicandoFormatacao(Calendario.getDataComHoraZerada(diaMes), "yyyy-MM-dd") + " 23:59:59' \n" +
                        " group by l.cor, al.inicio, al.fim, al.codigo, l.nome, a.nome, a.codigo, l.tipohorario, lh.codigo ")) {
            while (rs.next()) {
                Date inicio = rs.getTimestamp("inicio");
                String horainicio = Uteis.getDataAplicandoFormatacao(inicio, "HH:mm");
                String faixaHorario = Uteis.encontrarFaixaHorario(listaHorarios, horainicio);
                Date fim = rs.getTimestamp("fim");
                HorarioItemAgendaDTO servicoDia = new HorarioItemAgendaDTO();
                servicoDia.setTipo(TipoItemAgendaEnum.agendamento_locacao);
                servicoDia.setCodigo(rs.getInt("codigo"));
                servicoDia.setDiaMes(Uteis.getDataAplicandoFormatacao(inicio, "yyyyMMdd"));
                servicoDia.setTurma(rs.getString("nome"));
                servicoDia.setAmbiente(rs.getString("ambiente"));
                servicoDia.setAmbienteCodigo(rs.getInt("ambienteCodigo"));
                servicoDia.setCor(rs.getString("cor"));
                servicoDia.setInicio(horainicio);
                servicoDia.setFim(Uteis.getDataAplicandoFormatacao(fim, "HH:mm"));
                servicoDia.setAgendamentoLocacaoCancelada(rs.getBoolean("iscancelado"));
                servicoDia.setTipoHorario(TipoHorarioLocacaoEnum.getByCodigo(Integer.parseInt(rs.getString("tipohorario"))));
                servicoDia.setLocacaoHorarioCodigo(rs.getInt("locacaoHorarioCodigo"));
                Map<String, List<HorarioItemAgendaDTO>> aulasNoHorario = agenda.computeIfAbsent(faixaHorario, k -> new HashMap<>());
                List<HorarioItemAgendaDTO> aulas = aulasNoHorario.computeIfAbsent(
                        chaveAgrupadora, k -> new ArrayList<>());
                aulas.add(servicoDia);
            }
        }
    }

    private String getDescricaoLocacaoParaLog(LocacaoTO v1, LocacaoTO v2) {
        try {
            StringBuilder log = new StringBuilder();

            List<String> camposIgnorarLog = asList("horarios", "produtosSugeridos", "produtosValidacao", "planosValidacao", "dias", "base64Imagem");
            List<String> camposLog = new ArrayList<>();
            UtilReflection.getListFields(LocacaoTO.class).stream().forEach(field -> {
                if (!camposIgnorarLog.contains(field.getName())) {
                    camposLog.add(field.getName());
                }
            });

            if (v2 == null) {
                log.append(UtilReflection.difference(v1, null, camposLog.stream().toArray(String[]::new)));
            } else {
                log.append(UtilReflection.difference(v1, v2, camposLog.stream().toArray(String[]::new)));
            }

            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    private String getDescricaoLocacaoHorarioParaLog(LocacaoHorarioTO v1, LocacaoHorarioTO v2) {
        try {
            StringBuilder log = new StringBuilder();

            if (v2 == null) {
                log.append(UtilReflection.difference(v1, null));
            } else {
                log.append(UtilReflection.difference(v1, v2));
            }

            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    private String getDescricaoLocacaoProdutoSugeridoParaLog(LocacaoProdutoSugeridoTO v1, LocacaoProdutoSugeridoTO v2) {
        try {
            StringBuilder log = new StringBuilder();

            if (v2 == null) {
                log.append(UtilReflection.difference(v1, null));
                log.append(UtilReflection.difference(v1.getProduto(), null));
            } else {
                log.append(UtilReflection.difference(v1, v2));
                log.append(UtilReflection.difference(v1.getProduto(), v2.getProduto()));
            }

            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    @Override
    public ConfigAgendamentoLocacaoDTO cancelarAgendamentoLocacao(String ctx, Integer codigoUsuarioZW, Boolean canceladoPeloAluno, Integer codigoAgendamentoLocacao, String justificativa) throws ServiceException {
        validacoesCancelamentoAgendamento(ctx, codigoUsuarioZW, justificativa, canceladoPeloAluno);
        ConfigAgendamentoLocacaoDTO config = null;
        try {
            AgendamentoLocacao agendamentoLocacao = agendamentoLocacaoDao.findById(ctx, codigoAgendamentoLocacao);
            agendamentoLocacao.setCancelado(true);
            agendamentoLocacao.setJustificativa(justificativa);
            agendamentoLocacao.setDataCancelamento(new Date());
            agendamentoLocacao.setUsuarioCancelou(codigoUsuarioZW);
            agendamentoLocacao.setModificadoPeloAluno(true);
            agendamentoLocacaoDao.update(ctx, agendamentoLocacao);

            String descAnterior = "Agendamento ativo";
            String descAtual = getDescricaoCancelamentoParaLog(agendamentoLocacao, justificativa);
            incluirLog(ctx, codigoAgendamentoLocacao.toString(), "", descAnterior, descAtual, "CANCELAMENTO", "CANCELAMENTO DE AGENDAMENTO DE LOCAÇÃO", EntidadeLogEnum.LOCACAO_CANCELADA, "Locação", sessaoService, logDao, null, null);

            config = editAgendamento(codigoAgendamentoLocacao, "", ctx);
            notificacaoService.notificarCancelamentoAgendamentoLocacao(ctx, agendamentoLocacao);
        } catch (Exception ex) {
            Uteis.logar(ex, LocacaoService.class);
            throw new ServiceException(LocacaoExcecoes.ERRO_CANCELAR_AGENDAMENTO_LOCACAO);
        }
        return config;
    }

    private String getDescricaoCancelamentoParaLog(AgendamentoLocacao agendamentoLocacao, String justificativa) {
        return "Código do agendamento: " + agendamentoLocacao.getCodigo() +
                ", Justificativa: " + justificativa +
                ", Usuário que cancelou: " + agendamentoLocacao.getUsuarioCancelou() +
                ", Data do cancelamento: " + agendamentoLocacao.getDataCancelamento();
    }

    @Override
    public boolean finalizarAgendamentoLocacao(String ctx, Integer codigoUsuarioZW, Integer codigoAgendamentoLocacao, AgendamentoLocacaoDTO agendamentoDTO, Date dia, Integer empresaId) throws ServiceException {
        try {
            boolean redirect = false;
            AgendamentoLocacao agendamentoLocacao = agendamentoLocacaoDao.findById(ctx, codigoAgendamentoLocacao);

            if (agendamentoLocacao.getFinalizado()) {
                throw new ServiceException(LocacaoExcecoes.AGENDAMENTO_JA_FINALIZADO);
            }

            if (agendamentoLocacao.getVendaAvulsa() == null && !agendamentoLocacao.getLocacaoHorario().getLocacao().getTipoHorario().getDescricao().equalsIgnoreCase(TipoHorarioLocacaoEnum.PLAY.getDescricao())) {
                try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                    try (ResultSet result = ConexaoZWServiceImpl.criarConsulta(String.format("SELECT p.nome FROM pessoa p WHERE codigo = %s", agendamentoDTO.getCodigoPessoa()), conZW)) {
                        if (result.next()) {
                            redirect = true;
                            agendamentoLocacao.setVendaAvulsa(
                                    gerarVenda(ctx,
                                            agendamentoDTO,
                                            agendamentoLocacao.getLocacaoHorario().getLocacao(),
                                            result.getString("nome"),
                                            agendamentoDTO.getCodigoPessoa(),
                                            (agendamentoDTO.getValorHora() / 60) * Duration.between(agendamentoLocacao.getInicio().toInstant(), agendamentoLocacao.getFim().toInstant()).toMinutes(),
                                            dia,
                                            empresaId
                                    )
                            );
                        }
                    }
                }
            }

            agendamentoLocacao.setFinalizado(true);
            agendamentoLocacao.setUsuarioZW(codigoUsuarioZW);
//            if (codigoCliente != null) {
//                agendamentoLocacao.setCliente(clienteSinteticoService.obterPorCodigo(ctx, codigoCliente));
//            }

            agendamentoLocacaoDao.update(ctx, agendamentoLocacao);

            if (agendamentoDTO.getServicos() != null) {
                for (LocacaoProdutoSugeridoTO servico : agendamentoDTO.getServicos()) {
                    AgendamentoLocacaoProduto agendamentoLocacaoProduto = getAgendamentoLocacaoProduto(servico, agendamentoLocacao);
                    agendamentoLocacaoProdutoDao.insert(ctx, agendamentoLocacaoProduto);
                }
            }

            String descAnterior = "Agendamento não finalizado";
            String descAtual = getDescricaoFinalizacaoParaLog(agendamentoLocacao, codigoUsuarioZW);
            incluirLog(ctx, codigoAgendamentoLocacao.toString(), "", descAnterior, descAtual, "FINALIZAÇÃO",
                    "FINALIZAÇÃO DE AGENDAMENTO DE LOCAÇÃO",
                    EntidadeLogEnum.LOCACAO_FINALIZADA,
                    "Locação",
                    sessaoService, logDao, null, null);

            return redirect;
        } catch (Exception ex) {
            Uteis.logar(ex, LocacaoServiceImpl.class);
            throw new ServiceException(LocacaoExcecoes.ERRO_FINALIZAR_AGENDAMENTO_LOCACAO, ex);
        }
    }


    private String getDescricaoFinalizacaoParaLog(AgendamentoLocacao agendamentoLocacao, Integer usuario) {
        return "Código do agendamento: " + agendamentoLocacao.getCodigo() +
                ", Usuário que finalizou: " + usuario +
                ", Data de finalização: " + new Date();
    }

    private void validacoesCancelamentoAgendamento(String ctx, Integer usuario, String justificativa, Boolean canceladoPeloAluno) throws ServiceException {
        if (ctx == null) {
            throw new ServiceException(LocacaoExcecoes.ERRO_CHAVE_NULL);
        }
        if (!canceladoPeloAluno && usuario == null) {
            throw new ServiceException(LocacaoExcecoes.ERRO_USUARIO_NAO_INFORMADO);
        }
        String[] qtPalavrasJustificativa = !UteisValidacao.emptyString(justificativa) ? justificativa.split(" ") : new String[0];
        if (UteisValidacao.emptyString(justificativa) || qtPalavrasJustificativa.length < 3) {
            throw new ServiceException(LocacaoExcecoes.ERRO_JUSTIFICATIVA_VAZIA);
        }
    }

    @Override
    public List<LocacaoHorarioTO> validarDataReagendamento(String ctx, Integer codigoAgendamentoLocacao, Date novaData) throws ServiceException {
        try {
            AgendamentoLocacao agendamento = agendamentoLocacaoDao.findById(ctx, codigoAgendamentoLocacao);
            String diaDaSemana = obterDiaSemanaFormatoLocacaoHorario(novaData);

            // verificar se o dia selecionado está dentro da vigencia da locação
            boolean locacaoVigenteNaData = locacaoDao.isLocacaoVigente(ctx, agendamento.getLocacaoHorario().getLocacao().getCodigo(), novaData);
            if (!locacaoVigenteNaData) {
                throw new ServiceException(LocacaoExcecoes.VALIDACAO_LOCACAO_SEM_VIGENCIA);
            }

            // verificar se para o dia selecionado possui horário disponível
            List<LocacaoHorarioTO> horariosDisponiveis = locacaoDao.consultarDisponibilidadesLocacao(ctx, agendamento.getLocacaoHorario().getLocacao().getCodigo(), novaData, diaDaSemana);
            if (UteisValidacao.emptyList(horariosDisponiveis)) {
                throw new ServiceException(LocacaoExcecoes.VALIDACAO_LOCACAO_SEM_DISPONIBILIDADE);
            }

            return horariosDisponiveis;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private String obterDiaSemanaFormatoLocacaoHorario(Date novaData) throws Exception {
        String diaDaSemana = Uteis.getDiaDaSemana(novaData);
        String sigla = "";
        switch (diaDaSemana) {
            case "Dom":
                sigla = "DM";
                break;
            case "Seg":
                sigla = "SG";
                break;
            case "Ter":
                sigla = "TR";
                break;
            case "Qua":
                sigla = "QA";
                break;
            case "Qui":
                sigla = "QI";
                break;
            case "Sex":
                sigla = "SX";
                break;
            case "Sab":
                sigla = "SB";
                break;
        }
        return sigla;
    }

    public void sincronizarVendaAvulsa(Integer agendamentoLocacaoCodigo, Integer vendaAvulsaCodigo) throws ServiceException {
    try {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        String query = "SELECT al FROM AgendamentoLocacao al\n" +
                "WHERE al.codigo = :locacaoHorario\n" +
                "ORDER BY al.codigo DESC LIMIT 1; ";
        Map<String, Object> params = new HashMap<>();
        params.put("locacaoHorario", agendamentoLocacaoCodigo);

        AgendamentoLocacao agendamento = agendamentoLocacaoDao.findObjectByParam(ctx, query, params);
        if (agendamento == null) {
            throw new ServiceException(String.format("Agendamento locação com id %s não encontrado!", agendamentoLocacaoCodigo));
        }

        agendamento.setVendaAvulsa(vendaAvulsaCodigo);
        agendamentoLocacaoDao.update(ctx, agendamento);
    } catch (Exception e) {
        Uteis.logar(e, LocacaoServiceImpl.class);
        throw new ServiceException(e);
    }
    }

    public List<LocacaoTO> getLocacoes(Integer empresaId, String ctx, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<LocacaoTO> locacoes = new ArrayList<>();

            String query = "SELECT * FROM locacao l INNER JOIN empresa e ON e.codigo = l.empresa WHERE e.codzw = " + empresaId + ";";

            try (ResultSet rs = locacaoDao.createStatement(ctx, query)) {
                while (rs.next()) {
                    LocacaoTO locacao = new LocacaoTO();
                    locacao.setCodigo(rs.getInt("codigo"));
                    locacao.setAtivo(rs.getBoolean("ativo"));
                    locacao.setCor(rs.getString("cor"));

                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                    locacao.setDataInicial(Date.from(LocalDateTime.parse(rs.getString("datainicial").split("\\.")[0], formatter).atZone(ZoneId.systemDefault()).toInstant()));
                    locacao.setDataFinal(Date.from(LocalDateTime.parse(rs.getString("datafinal").split("\\.")[0], formatter).atZone(ZoneId.systemDefault()).toInstant()));

                    locacao.setNome(rs.getString("nome"));
                    locacao.setDescricao(rs.getString("descricao"));
                    locacao.setTipoHorario(rs.getInt("tipohorario"));
                    locacao.setTempoMinimoMinutos(rs.getInt("tempominimominutos"));
                    locacao.setDuracaoMinutos(rs.getInt("duracaominutos"));
                    if (!UteisValidacao.emptyNumber(rs.getInt("produto"))) {
                        locacao.setProduto(new ProdutoTO());
                        locacao.getProduto().setCodigo(rs.getInt("produto"));
                        montarProduto(ctx, locacao);
                    }

                    locacoes.add(locacao);
                }
            }

            return paginar(locacoes, paginadorDTO);
        } catch (Exception e) {
            Uteis.logar(e, LocacaoServiceImpl.class);
            throw new ServiceException(e);
        }
    }

    @Override
    public List<LocacaoVO> getLocacaoPorUsuario(Integer empresaId, String ctx, Date data, Integer codUsuario) {
        List<LocacaoVO> locacoes = new ArrayList<>();

        String sql = "SELECT l.codigo, al.codigo as codigoAgendamento, l.nome, l.tipohorario, al.inicio, al.fim, al.iscancelado, lh.horafim, lh.horainicio " +
                " FROM agendamentolocacao al " +
                " INNER JOIN locacaohorario lh ON lh.codigo = al.locacaohorario " +
                " INNER JOIN locacao l ON l.codigo = lh.locacao " +
                " INNER JOIN empresa e ON e.codigo = l.empresa " +
                " INNER JOIN clientesintetico cs ON cs.codigo = al.cliente_codigo " +
                " INNER JOIN usuario u ON u.cliente_codigo = cs.codigo " +
                " WHERE e.codzw = " + empresaId +
                " AND u.codigo = " + codUsuario +
                " AND al.inicio::date = '" + Uteis.getDataAplicandoFormatacao(Calendario.getDataComHoraZerada(data), "yyyy-MM-dd") + " 00:00:00' ";

        try (ResultSet rs = locacaoDao.createStatement(ctx, sql)) {
            while (rs.next()) {
                LocacaoVO locacao = new LocacaoVO();
                locacao.setCodigo(rs.getInt("codigo"));
                locacao.setCodigoAgendamento(rs.getInt("codigoAgendamento"));
                locacao.setNome(rs.getString("nome"));
                locacao.setTipo(TipoHorarioLocacaoEnum.getByCodigo(rs.getInt("tipohorario")).getDescricao());
                locacao.setDataInicio(Uteis.getDataAplicandoFormatacao(rs.getDate("inicio"), "dd/MM/yyyy ") + rs.getString("horainicio"));
                locacao.setDataFim(Uteis.getDataAplicandoFormatacao(rs.getDate("fim"), "dd/MM/yyyy " ) + rs.getString("horafim"));
                locacao.setCancelado(rs.getBoolean("iscancelado"));

                locacoes.add(locacao);
            }
        } catch (Exception e) {
            Uteis.logar(e, LocacaoServiceImpl.class);
        }
        return locacoes;
    }

    public static List<LocacaoTO> paginar(List<LocacaoTO> locacoes, PaginadorDTO paginadorDTO){
        paginadorDTO.setQuantidadeTotalElementos(new Long(locacoes.size()));
        if(paginadorDTO.getSize() > paginadorDTO.getQuantidadeTotalElementos()){
            return locacoes;
        }
        Long index = paginadorDTO.getPage() * paginadorDTO.getSize();
        long last = (index + paginadorDTO.getSize() > (locacoes.size() - 1) ?
                (locacoes.size()) : index + paginadorDTO.getSize());
        return locacoes.subList(index.intValue(), (int) last);
    }
}

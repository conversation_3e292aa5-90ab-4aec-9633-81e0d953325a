/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.perfil;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.perfil.*;
import br.com.pacto.bean.perfil.permissao.*;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.dao.intf.perfil.permissao.PermissaoDao;
import br.com.pacto.dao.intf.professor.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.log.TipoLogEnum;
import br.com.pacto.service.impl.notificacao.excecao.PerfilExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.UsuarioExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.perfil.PerfilService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.CategoriaRecursosTO;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.bean.RecursoTO;
import br.com.pacto.util.enumeradores.TipoColaboradorZWEnum;
import br.com.pacto.util.impl.Ordenacao;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static org.apache.commons.lang3.StringUtils.isBlank;

/**
 *
 * <AUTHOR>
 */
@Service
public class PerfilServiceImpl implements PerfilService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private PerfilDao perfilDao;
    @Autowired
    private PermissaoDao permissaoDao;
    @Autowired
    private UsuarioDao usuariodao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ProfessorSinteticoDao professorSinteticoDao;
    @Autowired
    private ConexaoZWService conexaoZWService;
    @Autowired
    private LogDao logDao;

    public UsuarioDao getUsuariodao() {
        return usuariodao;
    }

    public void setUsuariodao(UsuarioDao usuariodao) {
        this.usuariodao = usuariodao;
    }

    public PermissaoDao getPermissaoDao() {
        return permissaoDao;
    }

    public void setPermissaoDao(PermissaoDao permissaoDao) {
        this.permissaoDao = permissaoDao;
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public PerfilDao getPerfilDao() {
        return this.perfilDao;
    }

    public void setPerfilDao(PerfilDao perfilDao) {
        this.perfilDao = perfilDao;
    }

    public Perfil alterar(final String ctx, Perfil object) throws ServiceException {
        try {
            return getPerfilDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, Perfil object) throws ValidacaoException, ServiceException {
        try {
            Long count = (Long) getUsuariodao().count(ctx, "codigo", new String[]{"perfil.codigo"}, new Object[]{object.getCodigo()});
            if (count > 0) {
                throw new ValidacaoException("validacao.perfil.existeUsuario");
            }
            getPerfilDao().delete(ctx, object);
        } catch (ValidacaoException ex) {
            throw new ValidacaoException(ex.getMessage());
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Perfil inserir(final String ctx, Perfil object) throws ServiceException {
        try {
            return getPerfilDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Perfil obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getPerfilDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Perfil obterPorUsuario(final String ctx, final Integer idUsuario) throws ServiceException {
        try {
            String where = " where u.usuariozw = " + idUsuario;
            if (SuperControle.independente(ctx)) {
                where = " where u.codigo = " + idUsuario;
            }
            try (ResultSet rs = usuariodao.createStatement(ctx, "select perfil_codigo  from usuario u " + where)) {
                if (rs.next()) {
                    return obterPorId(ctx, rs.getInt("perfil_codigo"));
                }
            }
            return null;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public Perfil obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getPerfilDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Perfil> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getPerfilDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Perfil> obterPorParam(final String ctx, String query,
                                      Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getPerfilDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Perfil> obterTodos(final String ctx) throws ServiceException {
        try {
            return getPerfilDao().findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Permissao obterPermissaoPorPerfilRecurso(final String ctx, final Integer perfil, final RecursoEnum recurso) throws ServiceException {
        try {
            perfilDao.getCurrentSession(ctx).clear();
            List<Permissao> permissao = getPermissaoDao().findListByAttributes(ctx, new String[]{"perfil.codigo", "recurso"},
                    new Object[]{perfil, recurso}, null, 0);

            if (permissao == null || permissao.isEmpty()) {
                return null;
            }
            if (permissao.size() > 1) {
                Permissao perm = permissao.get(0);
                for (int i = 1; i < permissao.size(); i++) {
                    getPermissaoDao().delete(ctx, permissao.get(i));
                }
                return perm;
            }
            return permissao.get(0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void marcarTodos(final String ctx, final Perfil perfil, final GenericoTO tipo,
                            final CategoriaRecursosTO categoria, boolean marcarRecurso) throws ServiceException {

        List<RecursoTO> listaMarcarTodos = marcarRecurso ? categoria.getRecursos() : categoria.getFuncionalidades();

        for (RecursoTO recurso : listaMarcarTodos) {
            if (recurso.getCodigo() == null) {
                continue;
            }
            GenericoTO tipoPermissao = perfil.getTipoFromRecurso(tipo.getCodigo(), recurso);
            if (tipoPermissao.getCodigo().intValue() == tipo.getCodigo().intValue()) {
                tipoPermissao.setEscolhido(tipo.getEscolhido());
                if (perfil.getCodigo() != null && perfil.getCodigo() > 0) {
                    Permissao permissao = perfil.getPermissao(recurso.getCodigo());
                    if (permissao == null) {
                        permissao = new Permissao(RecursoEnum.getFromId(recurso.getCodigo()), new HashSet<TipoPermissaoEnum>(), perfil);
                        perfil.getPermissoes().add(gerenciarPermissaoSemSalvar(perfil, tipo, permissao,
                                RecursoEnum.getFromId(recurso.getCodigo().intValue())));
                    } else {
                        gerenciarPermissaoSemSalvar(perfil, tipo, permissao,
                                RecursoEnum.getFromId(recurso.getCodigo().intValue()));
                    }

                }
            }
        }
        if (perfil.getCodigo() != null && perfil.getCodigo() > 0) {
            alterar(ctx, perfil);
        }
    }

    public void gerenciarPermissao(final String ctx, final Perfil perfil, final RecursoTO recurso, final GenericoTO tipo) throws ServiceException {
        try {
            RecursoEnum rec = RecursoEnum.getFromId(recurso.getCodigo().intValue());
            Permissao permissao = obterPermissaoPorPerfilRecursoNativo(ctx, perfil.getCodigo(), rec);
            permissao = gerenciarPermissaoSemSalvar(perfil, tipo, permissao, rec);
            if (permissao.getCodigo() == null || permissao.getCodigo() == 0) {
                getPermissaoDao().insert(ctx, permissao);
            } else {
                getPermissaoDao().update(ctx, permissao);
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void alterarNome(final String ctx, final Perfil perfil) throws ServiceException {
        try {
            getPerfilDao().updateAlgunsCampos(ctx, new String[]{"nome"}, new Object[]{perfil.getNome()},
                    new String[]{"codigo"}, new Object[]{perfil.getCodigo()});
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void inserirNovo(final String ctx, final Perfil perfil) throws ServiceException {
        try {
            for (CategoriaRecursosTO categoria : perfil.getCategorias()) {
                for (RecursoTO recurso : categoria.getRecursos()) {
                    if (recurso.getCodigo() == null) {
                        continue;
                    }
                    RecursoEnum recursoEnum = RecursoEnum.getFromId(recurso.getCodigo());
                    Set<TipoPermissaoEnum> tipos = new HashSet<TipoPermissaoEnum>();
                    for (GenericoTO tipo : recurso.getTiposPermissoes()) {
                        if (tipo.getEscolhido()) {
                            TipoPermissaoEnum tipoEnum = TipoPermissaoEnum.getFromId(tipo.getCodigo());
                            tipos.add(tipoEnum);
                        }
                    }
                    if (!tipos.isEmpty()) {
                        Permissao nova = new Permissao(recursoEnum, tipos, perfil);
                        perfil.addPermissao(nova);
                    }
                }
            }
            inserir(ctx, perfil);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public boolean temPermissoesConjuntas(RecursoEnum rec, TipoPermissaoEnum tipoPer) {
        if (rec.getTiposConjuntos() != null
                && rec.getTiposConjuntos().length > 0) {
            for (TipoPermissaoEnum t : rec.getTiposConjuntos()) {
                if (t.equals(tipoPer)) {
                    return true;
                }
            }
        }
        return false;
    }

    private Permissao gerenciarPermissaoSemSalvar(final Perfil perfil, final GenericoTO tipo, Permissao permissao, RecursoEnum rec) throws ServiceException {
        if (permissao == null) {
            permissao = new Permissao();
            permissao.setPerfil(perfil);
            permissao.setRecurso(rec);
        }
        permissao.setTipoPermissoes(permissao.getTipoPermissoes() == null ? new HashSet<TipoPermissaoEnum>() : permissao.getTipoPermissoes());
        TipoPermissaoEnum tipoPer = TipoPermissaoEnum.getFromId(tipo.getCodigo().intValue());
        if (temPermissoesConjuntas(rec, tipoPer)) {
            for (TipoPermissaoEnum t : rec.getTiposConjuntos()) {
                if (tipo.getEscolhido()) {
                    permissao.getTipoPermissoes().add(t);
                } else {
                    permissao.getTipoPermissoes().remove(t);
                }
            }
        } else {
            if (tipo.getEscolhido()) {
                permissao.getTipoPermissoes().add(tipoPer);
            } else {
                permissao.getTipoPermissoes().remove(tipoPer);
            }
        }
        return permissao;
    }

    public boolean verificarPerfilExcluir(final String ctx, final Perfil perfil) throws ServiceException {
        try {
            Long count = (Long) getUsuariodao().count(ctx, "codigo", new String[]{"perfil.codigo"}, new Object[]{perfil.getCodigo()});
            return count <= 0;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    @Override
    public PerfilResponseTO cadastrarPerfil(HashMap<String, Object> perfilDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Perfil perfil = convertToPerfil(ctx, perfilDTO, new Perfil());

            PerfilResponseTO novoPerfil = new PerfilResponseTO(inserir(ctx, perfil));

            incluirLog(ctx, perfil.getCodigo().toString(), null,

                    " - ",
                    "Nome - " + novoPerfil.getNome() + ", TIPO - " + TipoColaboradorZWEnum.obterPorSigla(novoPerfil.getTipo()),
                    String.valueOf(TipoLogEnum.INCLUSAO),

                    TipoLogEnum.INCLUSAO + " DE " + EntidadeLogEnum.PERFIL,
                    EntidadeLogEnum.PERFIL, "",
                    sessaoService, logDao, false);

            return novoPerfil;
        } catch (Exception ex) {
            throw new ServiceException(PerfilExcecoes.ERRO_CRIAR_PERFIL, ex);
        }
    }

    @Override
    public PerfilDTO editarPerfil(HashMap<String, Object> perfilDTO, Integer perfilId, String perfilNome) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            perfilDao.getCurrentSession(ctx).clear();
            Perfil perfil;
            if (perfilId != null && perfilNome == null) {
                perfil = perfilDao.findById(ctx, perfilId);
            } else {
                perfil = perfilDao.findObjectByAttribute(ctx, "nome", perfilNome);
            }
            if (perfil == null || UteisValidacao.emptyNumber(perfil.getCodigo())) {
                throw new ServiceException(PerfilExcecoes.ERRO_AO_BUSCAR_PERFIS);
            }
            if (perfilDTO.get("nome") != null && perfilDTO.get("tipo") != null) {
                if (!perfilDTO.get("nome").equals(perfil.getNome()) || !perfilDTO.get("tipo").equals(perfil.getTipo())) {
                    incluirLog(ctx, perfil.getCodigo().toString(), null,

                            "Nome - " + perfil.getNome() + ", TIPO - " + TipoColaboradorZWEnum.obterPorSigla(perfil.getTipo()),
                            "Nome - " + perfilDTO.get("nome") + ", TIPO - " + TipoColaboradorZWEnum.obterPorSigla((String) perfilDTO.get("tipo")),
                            String.valueOf(TipoLogEnum.ALTERACAO),

                            TipoLogEnum.ALTERACAO + " DE " + EntidadeLogEnum.PERFIL,
                            EntidadeLogEnum.PERFIL, "",
                            sessaoService, logDao, false);
                }
            }


            return new PerfilDTO(alterar(ctx, convertToPerfil(ctx, perfilDTO, perfil)));
        } catch (Exception ex) {
            throw new ServiceException(PerfilExcecoes.ERRO_EDITAR_PERFIL, ex);
        }
    }

    private Perfil convertToPerfil(String ctx, HashMap<String, Object> perfilDTO, Perfil perfil) throws Exception {

        if (perfilDTO.get("nome") != null) {
            perfil.setNome(perfilDTO.get("nome").toString().toUpperCase());
        }

        if (perfilDTO.get("tipo") != null) {
            perfil.setTipo(perfilDTO.get("tipo").toString().toUpperCase());
        }

        validarCampos(ctx, perfil);

        List<Permissao> permissoes = new ArrayList<>();
        HashMap<String, List> recursos = (HashMap<String, List>) perfilDTO.get("recursos");
        if (recursos != null) {
            for (HashMap.Entry<String, List> recurso : recursos.entrySet()) {
                if (recurso.getValue() != null) {

                    List<Permissao> customersRecurso = perfil.getPermissoes()
                            .stream()
                            .filter(c -> c.getRecurso().equals(RecursoEnum.valueOf(recurso.getKey().toUpperCase())))
                            .collect(Collectors.toList());
                    if (customersRecurso.size() > 0) {
                        if (customersRecurso.get(0).getTipoPermissoes().size() == recurso.getValue().size()) {
                            customersRecurso.get(0).getTipoPermissoes().forEach(season -> {
                                AtomicReference<Boolean> resposta = new AtomicReference<>(false);
                                recurso.getValue().forEach(seasonB -> {
                                    if (seasonB.toString().equals(season.toString())) {
                                        resposta.set(true);
                                    }
                                });
                                if (!resposta.get() && !season.toString().equals("TOTAL")) {
                                    incluirLog(ctx, perfil.getCodigo().toString(), customersRecurso.get(0).getRecurso().getCategoria().toString(),
                                            customersRecurso.get(0).getRecurso().getNome() + " - " + (customersRecurso.get(0).getTipoPermissoes().toString()),
                                            RecursoEnum.valueOf(recurso.getKey().toUpperCase()).getNome() + " - " + recurso.getValue().toString(), "ALTERAÇÃO",
                                            "ALTERAÇÃO DE PERFIL DE ACESSO - " + perfil.getNome(),
                                            EntidadeLogEnum.PERFIL, customersRecurso.get(0).getRecurso().toString(),
                                            sessaoService, logDao, false);
                                }
                            });
                        } else {
                            incluirLog(ctx, perfil.getCodigo().toString(), customersRecurso.get(0).getRecurso().getCategoria().toString(),
                                    customersRecurso.get(0).getRecurso().getNome() + " - " + (customersRecurso.get(0).getTipoPermissoes().toString()),
                                    RecursoEnum.valueOf(recurso.getKey().toUpperCase()).getNome() + " - " + recurso.getValue().toString(), "ALTERAÇÃO",
                                    "ALTERAÇÃO DE PERFIL DE ACESSO - " + perfil.getNome(),
                                    EntidadeLogEnum.PERFIL, customersRecurso.get(0).getRecurso().toString(),
                                    sessaoService, logDao, false);
                        }
                    } else if (recurso.getValue().size() > 0) {
                        incluirLog(ctx, perfil.getCodigo().toString(), RecursoEnum.valueOf(recurso.getKey().toUpperCase()).getCategoria().toString(),
                                RecursoEnum.valueOf(recurso.getKey().toUpperCase()).getNome() + " - []",
                                RecursoEnum.valueOf(recurso.getKey().toUpperCase()).getNome() + " - " + recurso.getValue().toString(), "ALTERAÇÃO",
                                "ALTERAÇÃO DE PERFIL DE ACESSO - " + perfil.getNome(),
                                EntidadeLogEnum.PERFIL, RecursoEnum.valueOf(recurso.getKey().toUpperCase()).toString(),
                                sessaoService, logDao, false);
                    }

                    Permissao permissaoEntidade = new Permissao();
                    permissaoEntidade.setRecurso(RecursoEnum.valueOf(recurso.getKey().toUpperCase()));

                    for (Object permissao : recurso.getValue()) {
                        permissaoEntidade.getTipoPermissoes().add(TipoPermissaoEnum.valueOf(permissao.toString().toUpperCase()));
                    }
                    if (!UteisValidacao.emptyNumber(perfil.getCodigo())) {
                        Permissao permissao = permissaoDao.findObjectByAttributes(ctx, new String[]{"recurso", "perfil.codigo"},
                                new Object[]{RecursoEnum.valueOf(recurso.getKey().toUpperCase()), perfil.getCodigo()}, null);
                        if (permissao != null) {
                            permissaoEntidade.setCodigo(permissao.getCodigo());
                            perfil.getPermissoes().remove(permissao);
                        }
                    }
                    permissaoEntidade.setPerfil(perfil);
                    permissoes.add(permissaoEntidade);
                }
            }
        }

        HashMap<String, Boolean> funcionalidades = (HashMap<String, Boolean>) perfilDTO.get("funcionalidades");
        if (funcionalidades != null) {
            for (HashMap.Entry<String, Boolean> funcionalidade : funcionalidades.entrySet()) {

                List<Permissao> customersFuncionalidade = perfil.getPermissoes()
                        .stream()
                        .filter(c -> c.getRecurso().equals(RecursoEnum.valueOf(funcionalidade.getKey().toUpperCase())))
                        .collect(Collectors.toList());

                if (customersFuncionalidade.size() > 0) {

                    if (customersFuncionalidade.get(0).getTipoPermissoes().size() > 0 != funcionalidade.getValue()) {
                        incluirLog(ctx, perfil.getCodigo().toString(), customersFuncionalidade.get(0).getRecurso().getCategoria().toString(),
                                customersFuncionalidade.get(0).getRecurso().getNome() + " - " + (customersFuncionalidade.get(0).getTipoPermissoes().size() > 0 ? " Habilitado" : " Desabilitado"),
                                RecursoEnum.valueOf(funcionalidade.getKey().toUpperCase()).getNome() + " - " + (funcionalidade.getValue() ? " Habilitado" : " Desabilitado"), "ALTERAÇÃO",
                                "ALTERAÇÃO DE PERFIL DE ACESSO - " + perfil.getNome(),
                                EntidadeLogEnum.PERFIL, customersFuncionalidade.get(0).getRecurso().toString(),
                                sessaoService, logDao, false);
                    }
                } else if (funcionalidade.getValue()) {
                    incluirLog(ctx, perfil.getCodigo().toString(), RecursoEnum.valueOf(funcionalidade.getKey().toUpperCase()).getCategoria().toString(),
                            RecursoEnum.valueOf(funcionalidade.getKey().toUpperCase()).getNome() + " - Desabilitado",
                            RecursoEnum.valueOf(funcionalidade.getKey().toUpperCase()).getNome() + " - " + (funcionalidade.getValue() ? " Habilitado" : " Desabilitado"), "ALTERAÇÃO",
                            "ALTERAÇÃO DE PERFIL DE ACESSO - " + perfil.getNome(),
                            EntidadeLogEnum.PERFIL, RecursoEnum.valueOf(funcionalidade.getKey().toUpperCase()).toString(),
                            sessaoService, logDao, false);
                }
                if (funcionalidade.getValue() != null) {
                    Permissao permissaoFuncionalidade = new Permissao();

                    permissaoFuncionalidade.setRecurso(RecursoEnum.valueOf(funcionalidade.getKey().toUpperCase()));
                    permissaoFuncionalidade.setPerfil(perfil);

                    if (funcionalidade.getValue()) {
                        permissaoFuncionalidade.getTipoPermissoes().add(TipoPermissaoEnum.TOTAL);
                    }

                    if (!UteisValidacao.emptyNumber(perfil.getCodigo())) {
                        Permissao permissao = permissaoDao.findObjectByAttributes(ctx, new String[]{"recurso", "perfil.codigo"},
                                new Object[]{RecursoEnum.valueOf(funcionalidade.getKey().toUpperCase()), perfil.getCodigo()}, null);
                        if (permissao != null) {
                            permissaoFuncionalidade.setCodigo(permissao.getCodigo());
                            perfil.getPermissoes().remove(permissao);
                        }
                    }
                    permissoes.add(permissaoFuncionalidade);
                }
            }
        }
        perfil.getPermissoes().addAll(permissoes);

        return perfil;
    }

    private void validarCampos(String ctx, Perfil perfil) throws ServiceException {
        if (perfil.getNome() == null) {
            throw new ServiceException(PerfilExcecoes.ERRO_NOME_NAO_INFORMADO);
        }
        if (isBlank(perfil.getTipo())) {
            throw new ServiceException(PerfilExcecoes.ERRO_TIPO_PERFIL_NAO_INFORMADO);
        }
        if (perfilDao.exists(ctx, perfil, "nome", "codigo")) {
            throw new ServiceException(PerfilExcecoes.ERRO_PERFIL_DUPLICADO);
        }
    }

    @Override
    public List<PerfilResponseTO> consultarPerfil(FiltroPerfilJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            if (isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort("nome,ASC");
            }
            perfilDao.getCurrentSession(ctx).clear();
            List<Perfil> perfis = perfilDao.consultarPerfil(ctx, filtros, paginadorDTO);

            List<PerfilResponseTO> ret = new ArrayList<>();

            for (Perfil perfil : perfis) {
                ret.add(new PerfilResponseTO(perfil));
            }

            return ret;
        } catch (Exception ex) {
            throw new ServiceException(PerfilExcecoes.ERRO_AO_BUSCAR_PERFIS, ex);
        }
    }

    @Override
    public PerfilDTO obterPerfil(Integer perfilId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            perfilDao.getCurrentSession(ctx).clear();
            Perfil perfil = obterPorId(ctx, perfilId);

            if (perfil == null || UteisValidacao.emptyNumber(perfil.getCodigo())) {
                Perfil perfilUnificado = verificaPerfilUnificado(ctx, perfilId);
                if (perfilUnificado != null) {
                    return new PerfilDTO(perfilUnificado);
                }
                throw new ServiceException(PerfilExcecoes.ERRO_AO_BUSCAR_PERFIS);
            }
            return new PerfilDTO(perfil);
        } catch (ServiceException e) {
            throw new ServiceException(PerfilExcecoes.ERRO_AO_BUSCAR_PERFIS);
        }
    }

    private Perfil verificaPerfilUnificado(String ctx, Integer perfilId) throws ServiceException {
        String sql = "SELECT codigoperfiltreino FROM perfilacesso WHERE codigo = ? AND unificado = true";

        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             PreparedStatement ps = conZW.prepareStatement(sql)) {

            ps.setInt(1, perfilId);

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return obterPorId(ctx, rs.getInt("codigoperfiltreino"));
                }
            }
        } catch (Exception e) {
            throw new ServiceException("Erro ao verificar perfil unificado", e);
        }
        return null;
    }

    public void restaurarPerfilCoordenador(String ctx) throws Exception {
        Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);
        List<Permissao> permissoesRemover = new ArrayList<>();

        perfilCoordenador.getPermissoes().forEach(permissao -> {
            if (permissao.getRecurso().equals(RecursoEnum.PERFIL_USUARIO)
                    || permissao.getRecurso().equals(RecursoEnum.USUARIOS)
                    || permissao.getRecurso().equals(RecursoEnum.ALUNOS)) {
                permissoesRemover.add(permissao);
            }
        });

        if (!permissoesRemover.isEmpty()) {
            perfilCoordenador.getPermissoes().removeAll(permissoesRemover);
        }

        perfilCoordenador.addPermissao(new Permissao(RecursoEnum.USUARIOS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
        perfilCoordenador.addPermissao(new Permissao(RecursoEnum.PERFIL_USUARIO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
        perfilCoordenador.addPermissao(new Permissao(RecursoEnum.ALUNOS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));

        perfilDao.update(ctx, perfilCoordenador);

        Perfil perfilId = null;
        try {
            List<Perfil> perfis = obterTodos(ctx);
            for (Perfil p : perfis) {
                if (p.getNome().equals("COORDENADOR")) {
                    perfilId = p;
                }
            }
            Usuario usuario = usuarioService.consultarPorUserName(ctx, "pactobr");
            usuario.setPerfil(perfilId);
            usuarioService.alterar(ctx, usuario);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public String corrigirNomeUsuarioPacto(String ctx) throws Exception {
        try {
            Usuario usuario = usuarioService.consultarPorUserName(ctx, "pactobr");
            if (usuario != null && usuario.getProfessor() != null) {
                ProfessorSintetico ps = usuario.getProfessor();
                ps.setNome("PACTO - MÉTODO DE GESTÃO");
                professorSinteticoDao.update(ctx, ps);

                JSONObject content = chamadaZW(ctx, "/prest/manutencao/corrigir-nome-usuario-pacto");
                String contentZw = content.getString("content");

                return "Banco Treino: Nome do professor atualizado com sucesso " + contentZw;
            } else {
                return "O usuário não foi localizado";
            }
        } catch (ServiceException ex) {
            throw new ServiceException(UsuarioExcecoes.ERRO_BUSCAR_USUARIO, ex);
        }
    }

    private JSONObject chamadaZW(String ctx, String endpoint) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));

        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();

        return new JSONObject(body);
    }

    @Override
    public void removerPerfil(Integer perfilId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Perfil perfil = obterPorId(ctx, perfilId);
            if (perfil == null || UteisValidacao.emptyNumber(perfil.getCodigo())) {
                throw new ServiceException(PerfilExcecoes.ERRO_AO_BUSCAR_PERFIS);
            }
            if (verificarPerfilExcluir(ctx, perfil)) {
                perfilDao.delete(ctx, perfil);
                incluirLog(ctx, perfil.getCodigo().toString(), null,

                        "Nome - " + perfil.getNome() + ", TIPO - " + TipoColaboradorZWEnum.obterPorSigla(perfil.getTipo()),
                        "Nome - , TIPO - ",
                        String.valueOf(TipoLogEnum.EXCLUSAO),

                        TipoLogEnum.EXCLUSAO + " DE " + EntidadeLogEnum.PERFIL,
                        EntidadeLogEnum.PERFIL, "",
                        sessaoService, logDao, false);
            } else {
                throw new ServiceException(PerfilExcecoes.ERRO_REGISTRO_ESTA_SENDO_UTILIZADO);
            }
        } catch (Exception ex) {
            throw new ServiceException(PerfilExcecoes.ERRO_AO_EXCLUIR_PERFIL, ex);
        }
    }

    public List<PerfilResponseTO> consultarTodos() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<Perfil> lista = obterTodos(ctx);
        List<PerfilResponseTO> ret = new ArrayList<>();
        for (Perfil perfil : lista) {
            ret.add(new PerfilResponseTO(perfil));
        }
        return Ordenacao.ordenarLista(ret, "nome");
    }

    public Permissao obterPermissaoPorPerfilRecursoNativo(final String ctx, final Integer perfil, final RecursoEnum recurso) throws ServiceException {
        try {
            return getPermissaoDao().obterPermissaoPorPerfilRecurso(ctx, perfil, recurso);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<ProfessorResponseTO> consultarProfessoresPorPerfil(Integer perfilId, PaginadorDTO paginadorDTO, String filter, Integer empresaIdZw) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<ProfessorResponseTO> professoresResponse = new ArrayList<>();
        int indiceInicial = 0;
        int maxResults = paginadorDTO.getSize().intValue();
        if (paginadorDTO != null) {
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }
        boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
        JSONObject objJson = new JSONObject();
        if (filter != null) {
            filter = Uteis.retirarAcentuacaoRegex(filter);
            objJson = new JSONObject(filter);
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT u.professor_codigo, p.nome, p.codigocolaborador ");
        sql.append("FROM usuario u ");
        sql.append("INNER JOIN professorsintetico p ON u.professor_codigo = p.codigo ");
        sql.append("WHERE u.perfil_codigo = ? ");
        sql.append("AND u.status <> 1 ");
        sql.append("AND p.empresa_codigo = ? ");
        sql.append("AND p.ativo = TRUE ");
        sql.append("AND NOT EXISTS ( ");
        sql.append("    SELECT 1 FROM usuario u2 ");
        sql.append("    INNER JOIN professorsintetico p2 ON u2.professor_codigo = p2.codigo ");
        sql.append("    WHERE u2.perfil_codigo = u.perfil_codigo ");
        sql.append("    AND p2.empresa_codigo = p.empresa_codigo ");
        sql.append("    AND u2.professor_codigo = u.professor_codigo ");
        sql.append("    AND CAST(u2.codigo AS BIGINT) < CAST(u.codigo AS BIGINT) ");
        sql.append(") ");

        if (buscaRapida) {
            sql.append("AND UPPER(p.nome) LIKE UPPER(?) ");
        }

        String orderBy = (paginadorDTO.getSort() != null)
                ? paginadorDTO.getSort().replace("nome", "p.nome")
                : "p.nome ASC";

        sql.append("ORDER BY ").append(orderBy).append(" ");
        sql.append("LIMIT ? OFFSET ? ");

        try (Connection con = perfilDao.getConnection(ctx);
             PreparedStatement ps = con.prepareStatement(sql.toString())) {

            int i = 1;
            ps.setInt(i++, perfilId);
            ps.setInt(i++, empresaIdZw);
            if (buscaRapida) {
                String pesquisa = objJson.getString("quicksearchValue").replaceAll(" ", "%") + "%";
                ps.setString(i++, pesquisa);
            }
            ps.setInt(i++, maxResults);
            ps.setInt(i, indiceInicial);

            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    int codColaborador = rs.getInt("codigocolaborador");
                    String nomeAbreviado = rs.getString("nome");
                    professoresResponse.add(new ProfessorResponseTO(codColaborador, nomeAbreviado));
                }
            }
            StringBuilder countSql = new StringBuilder();
            countSql.append("SELECT COUNT(*) FROM usuario u ");
            countSql.append("INNER JOIN professorsintetico p ON u.professor_codigo = p.codigo ");
            countSql.append("WHERE u.perfil_codigo = ? ");
            countSql.append("AND u.status <> 1 ");
            countSql.append("AND p.empresa_codigo = ? ");
            countSql.append("AND p.ativo = TRUE ");
            countSql.append("AND NOT EXISTS ( ");
            countSql.append("    SELECT 1 FROM usuario u2 ");
            countSql.append("    INNER JOIN professorsintetico p2 ON u2.professor_codigo = p2.codigo ");
            countSql.append("    WHERE u2.perfil_codigo = u.perfil_codigo ");
            countSql.append("    AND p2.empresa_codigo = p.empresa_codigo ");
            countSql.append("    AND u2.professor_codigo = u.professor_codigo ");
            countSql.append("    AND CAST(u2.codigo AS BIGINT) < CAST(u.codigo AS BIGINT) ");
            countSql.append(") ");
            if (buscaRapida) {
                countSql.append("AND UPPER(p.nome) LIKE UPPER(?) ");
            }

            try (PreparedStatement countStmt = con.prepareStatement(countSql.toString())) {
                int j = 1;
                countStmt.setInt(j++, perfilId);
                countStmt.setInt(j++, empresaIdZw);
                if (buscaRapida) {
                    String pesquisa = objJson.getString("quicksearchValue").replaceAll(" ", "%") + "%";
                    countStmt.setString(j++, pesquisa);
                }

                try (ResultSet countRs = countStmt.executeQuery()) {
                    if (countRs.next()) {
                        paginadorDTO.setQuantidadeTotalElementos(countRs.getLong(1));
                    }
                }
            }

        } catch (Exception ex) {
            throw new ServiceException(PerfilExcecoes.ERRO_AO_BUSCAR_PERFIS, ex);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return professoresResponse;
    }
}

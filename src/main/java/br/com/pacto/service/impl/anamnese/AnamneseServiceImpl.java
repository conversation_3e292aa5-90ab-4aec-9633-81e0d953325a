package br.com.pacto.service.impl.anamnese;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.anamnese.*;
import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisicaEnum;
import br.com.pacto.bean.avaliacao.RespostaClienteParQ;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.log.Log;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.avaliacao.FiltrosJSON;
import br.com.pacto.controller.json.avaliacao.PerguntaJSON;
import br.com.pacto.controller.json.avaliacao.QuestionarioJSON;
import br.com.pacto.controller.json.avaliacao.RespostaPerguntaJSON;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.dao.intf.anamnese.*;
import br.com.pacto.dao.intf.avaliacao.ItemAvaliacaoFisicaDao;
import br.com.pacto.dao.intf.avaliacao.ParQDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.log.AlteracoesTO;
import br.com.pacto.service.impl.log.LogTO;
import br.com.pacto.service.impl.notificacao.excecao.AnamneseExcecoes;
import br.com.pacto.service.impl.usuario.UsuarioServiceImpl;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.AuditUtilities;
import br.com.pacto.util.impl.JSFUtilities;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import java.sql.ResultSet;
import java.util.*;

import static br.com.pacto.objeto.Uteis.incluirLog;

/**
 * Created by Joao Alcides on 05/05/2017.
 */
@Service
public class AnamneseServiceImpl implements AnamneseService {

    @Autowired
    private AnamneseDao anamneseDao;
    @Autowired
    private PerguntaDao perguntaDao;
    @Autowired
    private PerguntaAnamneseDao perguntaAnamneseDao;
    @Autowired
    private OpcaoPerguntaDao opcaoPerguntaDao;
    @Autowired
    private RespostaClienteDao respostaClienteDao;
    @Autowired
    private ItemAvaliacaoFisicaDao itemDao;
    @Autowired
    private ClienteSinteticoService clienteService;
    @Autowired
    private AvaliacaoFisicaService avaliacaoService;
    @Autowired
    private Movimento3DDao m3DDao;
    @Autowired
    private AulaService aulaService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ConfiguracaoSistemaService configService;
    @Autowired
    private ParQDao parQDao;
    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    LogDao logDao;
    @Autowired
    private UsuarioServiceImpl usuarioServiceImpl;

    @Override
    public List<Anamnese> obterTodas(final String key) throws ServiceException{
        try {
            return anamneseDao.findByParam(key, "SELECT obj FROM Anamnese obj WHERE obj.parq IS FALSE or obj.parq is null", new HashMap<String, Object>());
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    @Override
    public List<Anamnese> consultarPovoando(final String key, final Usuario usuario, final Boolean apenasAtivos, final ViewUtils vu) throws ServiceException{
        try {
            List<Anamnese> anamneses = obterTodas(key);
            if(UteisValidacao.emptyList(anamneses)){
                Anamnese anamnese = new Anamnese();
                anamnese.setResponsavelLancamento(usuario.getCodigo());
                anamnese.setParq(Boolean.FALSE);
                anamnese.setDataLancamento(Calendario.hoje());
                anamnese.setDescricao("ANAMNESE NORMAL");
                String[] perguntasDesc = new String[]{"voce.fuma", "medico.aferiu.pa.alta",
                        "possui.diabeticos", "possui.problema.cardiaco",
                        "nivel.colesterol.alto", "sobrepeso",
                        "lesoes.problemas.ortopedicos", "medicamento.prescrito",
                        "medico.recomendou", "pratica.exercicios"};
                List<Pergunta> perguntas = new ArrayList<Pergunta>();
                int i = 1;
                for(String p : perguntasDesc){
                    Pergunta pergunta = new Pergunta();
                    pergunta.setDescricao(vu.getLabel(p));
                    pergunta.setOrdem(i++);
                    pergunta.setTipoPergunta(TiposPerguntaEnum.SIM_NAO);
                    perguntas.add(pergunta);
                }
                anamneses.add(gravarAnamnese(key, anamnese, perguntas, new ArrayList<>(), new ArrayList<>()));
            }
            if(apenasAtivos){
                List<Anamnese> ativas = new ArrayList<Anamnese>();
                for(Anamnese a : anamneses){
                    if(a.getAtiva()){
                        ativas.add(a);
                    }
                }
                return ativas;
            }else{
                return anamneses;
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public void excluirAnamnese(final String key, final Anamnese anamnese) throws ServiceException {
        try {
            Number count = itemDao.count(key, "codigo", new String[]{"anamnese.codigo"}, new Object[]{anamnese.getCodigo()});
            if(count.intValue() > 0){
                throw new Exception("anamnese.nao.pode.excluir");
            }
            List<PerguntaAnamnese> perguntaAnamneses = obterPerguntasAnamnese(key, anamnese.getCodigo());
            for(PerguntaAnamnese p : perguntaAnamneses){
                perguntaAnamneseDao.delete(key, p);
            }
            anamneseDao.delete(key, anamnese);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public boolean anamneseUsada(final String key, final Integer codigo) throws Exception{
        Number count = itemDao.count(key, "codigo", new String[]{"anamnese.codigo"}, new Object[]{codigo});
        return count.intValue() > 0;
    }

    public boolean perguntaUsada(final String key, final Integer codigo) throws Exception{
        Number count = respostaClienteDao.count(key, "codigo", new String[]{"perguntaAnamnese.pergunta.codigo"}, new Object[]{codigo});
        return count.intValue() > 0;
    }

    public void gravarAnamneseCliente(final String key, final Anamnese anamnese,
                                      final ClienteSintetico cliente, ItemAvaliacaoFisica item) throws ServiceException {
        try {
            for (PerguntaAnamnese pa : anamnese.getPerguntas()) {
                RespostaCliente rc = new RespostaCliente();
                rc.setPerguntaAnamnese(pa);
                rc.setCliente(cliente);
                rc.setItemAvaliacao(item);

                if (pa != null) {
                    rc.setObs(pa.getComplemento());
                    TiposPerguntaEnum tipoPergunta = pa.getPergunta().getTipoPergunta();
                    if (TiposPerguntaEnum.MULTIPLA_ESCOLHA.equals(tipoPergunta)) {
                        StringBuilder sb = new StringBuilder();
                        for (String r : pa.getRespostas()) {
                            sb.append("_").append(r);
                        }
                        if (sb.length() > 0) sb.deleteCharAt(0);
                        rc.setResposta(sb.toString());
                    } else {
                        rc.setResposta(pa.getResposta() == null ? "" : pa.getResposta());
                    }
                }

                if (item.getEditado() && pa != null) {
                    Map<String, Object> params = new HashMap<>();
                    params.put("clienteCodigo", cliente.getCodigo());
                    params.put("itemAvaliacaoCodigo", item.getCodigo());
                    params.put("perguntaAnamneseCodigo", pa.getCodigo());

                    List<RespostaCliente> lista = respostaClienteDao.findByParam(key,
                            "select obj from RespostaCliente obj where " +
                                    "obj.cliente.codigo = :clienteCodigo and obj.itemAvaliacao.codigo = :itemAvaliacaoCodigo and " +
                                    "obj.perguntaAnamnese.codigo = :perguntaAnamneseCodigo", params);

                    if (lista != null && !lista.isEmpty()) {
                        RespostaCliente before = lista.get(0);
                        String obsAntes = before.getObs() == null ? "" : before.getObs();
                        String respAntesRaw = before.getResposta() == null ? "" : before.getResposta();
                        rc.setCodigo(before.getCodigo());
                        respostaClienteDao.update(key, rc);

                        String obsDepois = rc.getObs() == null ? "" : rc.getObs();
                        String respDepoisRaw = rc.getResposta() == null ? "" : rc.getResposta();
                        TiposPerguntaEnum tipo = pa.getPergunta().getTipoPergunta();
                        String perguntaDesc = pa.getPergunta().getDescricao();
                        String descAnterior, descAtual;

                        switch (tipo) {
                            case SIM_NAO:
                            case TEXTUAL:
                            case SIMPLES_ESCOLHA:
                                descAnterior = String.format(
                                        "Pergunta: %s, Tipo: %s, Resposta: %s, Obs: %s",
                                        perguntaDesc, tipo, respAntesRaw, obsAntes
                                );
                                descAtual   = String.format(
                                        "Pergunta: %s, Tipo: %s, Resposta: %s, Obs: %s",
                                        perguntaDesc, tipo, respDepoisRaw, obsDepois
                                );
                                break;
                            case MULTIPLA_ESCOLHA:
                                List<String> listAntes = respAntesRaw.isEmpty()
                                        ? Collections.emptyList()
                                        : Arrays.asList(respAntesRaw.split("_"));
                                List<String> listDepois = respDepoisRaw.isEmpty()
                                        ? Collections.emptyList()
                                        : Arrays.asList(respDepoisRaw.split("_"));
                                String joinedAntes = String.join(", ", listAntes);
                                String joinedDepois = String.join(", ", listDepois);

                                descAnterior = String.format(
                                        "Pergunta: %s, Tipo: %s, Resposta: %s, Obs: %s",
                                        perguntaDesc, tipo, joinedAntes, obsAntes
                                );
                                descAtual   = String.format(
                                        "Pergunta: %s, Tipo: %s, Resposta: %s, Obs: %s",
                                        perguntaDesc, tipo, joinedDepois, obsDepois
                                );
                                break;
                            default:
                                descAnterior = String.format(
                                        "Pergunta: %s, Tipo: %s, Resposta: %s, Obs: %s",
                                        perguntaDesc, tipo, respAntesRaw, obsAntes
                                );
                                descAtual   = String.format(
                                        "Pergunta: %s, Tipo: %s, Resposta: %s, Obs: %s",
                                        perguntaDesc, tipo, respDepoisRaw, obsDepois
                                );
                        }

                        if (!descAnterior.equals(descAtual)) {
                            incluirLog(
                                    key,
                                    rc.getCodigo().toString(),
                                    pa.getCodigo().toString(),
                                    descAnterior,
                                    descAtual,
                                    "ALTERAÇÃO",
                                    "ALTERAÇÃO RESPOSTA ANAMNESE",
                                    EntidadeLogEnum.ANAMNESE,
                                    "RespostaCliente",
                                    sessaoService,
                                    logDao,
                                    false
                            );
                        }
                    } else {
                        respostaClienteDao.insert(key, rc);
                    }
                } else {
                    respostaClienteDao.insert(key, rc);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }


    public Anamnese obterAvaliacaoIntegrada(final String key, Usuario usuario) throws ServiceException {
        try {
            Anamnese avaliacao = anamneseDao.findObjectByAttributes(key, new String[]{"ativa", "tipo"},
                    new Object[]{Boolean.TRUE, TipoAnamneseEnum.AVALIACAO_INTEGRADA}, "codigo");
            if (avaliacao == null && (anamneseDao.findObjectByAttributes(key, new String[]{"tipo"},
                    new Object[]{TipoAnamneseEnum.AVALIACAO_INTEGRADA}, "codigo") == null)) {
                avaliacao = povoarAvaliacaoIntegrada(key, usuario);
            }
            return avaliacao;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }


    @Override
    public void excluirAnamneseAluno(final String key, final ItemAvaliacaoFisica prs) throws Exception {
        try {
            if (prs.getAnamnese().getPerguntas() == null) {
                prs.getAnamnese().setPerguntas(perguntaAnamneseDao.findListByAttributes(key, new String[]{"anamnese.codigo"}, new Object[]{prs.getAnamnese().getCodigo()}, null, 0));
            }
            for (PerguntaAnamnese pa : prs.getAnamnese().getPerguntas()) {
                RespostaCliente resposta = obterRespostaPerguntasAnamnese(key, pa.getCodigo(), prs.getCliente().getCodigo(), prs.getCodigo());
                if (resposta != null) {
                    respostaClienteDao.delete(key, resposta);
                }
            }
            itemDao.delete(key, prs);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }


    public void alterarAnamnese(final String key, final Usuario usuario, final ClienteSintetico cliente, final Anamnese anamnese, boolean parq, AvaliacaoFisica avaliacao) throws Exception{
        List<ItemAvaliacaoFisica> anamnesesAluno = avaliacaoService.obterItensAvaliacaoFisica(key,
                cliente.getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, parq, avaliacao);
        if (UteisValidacao.emptyList(anamnesesAluno)) {
            ItemAvaliacaoFisica itemAnamnese = avaliacaoService.adicionarItem(key, anamnese.getDescricao(), cliente,
                    usuario, ItemAvaliacaoFisicaEnum.ANAMNESE, anamnese, null, avaliacao);
            gravarAnamneseCliente(key, anamnese, cliente, itemAnamnese);

        } else {
            ItemAvaliacaoFisica itemAvaliacaoFisica = anamnesesAluno.get(0);
            if (anamnesesAluno.get(0).getAnamnese().getDescricao() != anamnese.getDescricao()){
                itemAvaliacaoFisica.setAnamnese(anamnese);
            }

            if(parq) {
                Iterator<PerguntaAnamnese> itr = obterPerguntasAnamnese(key, itemAvaliacaoFisica.getAnamnese().getCodigo()).iterator();
                while (itr.hasNext()) {
                    PerguntaAnamnese pa = itr.next();
                    RespostaCliente resposta = obterRespostaPerguntasAnamnese(key, pa.getCodigo(), itemAvaliacaoFisica.getCliente().getCodigo(),
                            itemAvaliacaoFisica.getCodigo());
                    if (resposta != null) {
                        for (PerguntaAnamnese pa1 : anamnese.getPerguntas()) {
                            if (pa1 != null && pa1.getCodigo().equals(resposta.getPerguntaAnamnese().getCodigo())) {
                                respostaClienteDao.deleteComParam(key, new String[]{"codigo"}, new Object[]{resposta.getCodigo()});
                            }
                        }
                    }
                }
            } else {
                itemAvaliacaoFisica.setEditado(true);
            }
            gravarAnamneseCliente(key, anamnese, cliente, itemAvaliacaoFisica);
        }
    }

    private void incluirPergunta(String key, Pergunta p, Anamnese anamneseInsert) throws Exception {
        if (UteisValidacao.emptyString(p.getDescricao())) {
            return;
        }
        List<OpcaoPergunta> opcoes = p.getOpcoes();
        Pergunta perguntaInsert = perguntaDao.insert(key, p);

        for (OpcaoPergunta op : opcoes) {
            op.setPergunta(perguntaInsert);
            opcaoPerguntaDao.insert(key, op);
        }

        PerguntaAnamnese pa = new PerguntaAnamnese();
        pa.setPergunta(perguntaInsert);
        pa.setAnamnese(anamneseInsert);
        perguntaAnamneseDao.insert(key, pa);
    }

    @Override
    public Anamnese gravarAnamnese(final String key, final Anamnese anamnese, final List<Pergunta> perguntas,
                                   final List<Pergunta> perguntasExcluir, final List<OpcaoPergunta> opcoesExcluir) throws ServiceException {
        try {
            if(anamnese.getCodigo() == null || anamnese.getCodigo().equals(0)){
                Anamnese anamneseInsert = anamneseDao.insert(key, anamnese);
                for(Pergunta p : perguntas){
                    incluirPergunta(key, p, anamneseInsert);
                }
                return anamnese;
            }else{
                for(OpcaoPergunta op : opcoesExcluir){
                    opcaoPerguntaDao.delete(key, op);
                }
                for(Pergunta pex : perguntasExcluir){
                    Map<String, Object> p = new HashMap<>();
                    p.put("perguntaCodigo", pex.getCodigo());
                    respostaClienteDao.deleteComParam(key, new String[]{"perguntaanamnese_codigo"},
                            new Object[]{perguntaAnamneseDao.findByParam(key, "select obj.codigo from PerguntaAnamnese obj where Obj.pergunta.codigo = :perguntaCodigo", p)});
                    perguntaAnamneseDao.deleteComParam(key, new String[]{"pergunta.codigo"}, new Object[]{pex.getCodigo()});
                    opcaoPerguntaDao.deleteComParam(key, new String[]{"pergunta.codigo"}, new Object[]{pex.getCodigo()});
                    perguntaDao.delete(key, pex);
                }
                for(Pergunta pad : perguntas){
                    if(UteisValidacao.emptyNumber(pad.getCodigo())){
                        incluirPergunta(key, pad, anamnese);
                    }else{
                        List<OpcaoPergunta> opcoes = pad.getOpcoes();
                        Pergunta perguntaUpdate = perguntaDao.update(key, pad);
                        for (OpcaoPergunta op : opcoes) {
                            if(UteisValidacao.emptyNumber(op.getCodigo())){
                                op.setPergunta(perguntaUpdate);
                                opcaoPerguntaDao.insert(key, op);
                            }else{
                                opcaoPerguntaDao.update(key, op);
                            }
                        }
                    }
                }
                return anamneseDao.update(key, anamnese);
            }

        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public List<PerguntaAnamnese> obterPerguntasAnamnese(final String key, final Integer anamnese) throws ServiceException {
        try {
            String query = " SELECT obj FROM PerguntaAnamnese obj WHERE obj.anamnese.codigo = :anamnese";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("anamnese", anamnese);
            return Ordenacao.ordenarLista(perguntaAnamneseDao.findByParam(key, query, params), "ordem");
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public List<OpcaoPergunta> obterOpcoes(final String key, final Integer pergunta) throws ServiceException {
        try {
            String query = " SELECT obj FROM OpcaoPergunta obj WHERE obj.pergunta.codigo = :pergunta";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("pergunta", pergunta);
            return opcaoPerguntaDao.findByParam(key, query, params);
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public RespostaCliente obterRespostaPerguntasAnamnese(final String key, final Integer perguntaanamnese,
                                                          final Integer cliente, final Integer itemAvaliacao) throws ServiceException {
        try {
            String query = " SELECT obj FROM RespostaCliente obj "
                    + "WHERE obj.perguntaAnamnese.codigo = :pa and obj.cliente.codigo = :cli and obj.itemAvaliacao.codigo = :itemAvaliacao ";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("pa", perguntaanamnese);
            params.put("cli", cliente);
            params.put("itemAvaliacao", itemAvaliacao);
            return respostaClienteDao.findObjectByParam(key, query, params);
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public Boolean obterRespostaParQAssinaturaDigital(final String ctx, final Integer cliente) throws ServiceException {
        try {
            List<RespostaCliente> respostasRecentes = respostaClienteDao.obterRespostasMaisRecentesPorCliente(ctx, cliente);

            if (respostasRecentes != null) {
                for (RespostaCliente rc : respostasRecentes) {
                    if ("SIM".equals(rc.getResposta())) {
                        return true;
                    }
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
        return false;
    }
    public RespostaCliente obterRespostaPerguntasAnamnese(final String key, final Integer itemAvaliacao) throws ServiceException {
        try {
            String query = " SELECT obj FROM RespostaCliente obj WHERE obj.itemAvaliacao.codigo = :itemAvaliacao";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("itemAvaliacao", itemAvaliacao);
            return respostaClienteDao.findObjectByParam(key, query, params);
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    @Override
    public Anamnese consultarParq(final String key, final Integer usuario, final ViewUtils vu) throws ServiceException {
        try {
            String jpql = "SELECT obj FROM Anamnese obj WHERE obj.parq IS TRUE AND obj.parqpadraorj IS TRUE";

            Anamnese anamnese = anamneseDao.findObjectByParam(key, jpql, new HashMap<>());

            if (anamnese == null && vu != null) {
                gravarParqPadrao10Perguntas(key, vu);
                anamnese = anamneseDao.findObjectByParam(key, jpql, new HashMap<>());
            }

            if (anamnese != null) {
                anamnese.setPerguntas(obterPerguntasAnamnese(key, anamnese.getCodigo()));
            }

            return anamnese;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }


    private Anamnese criarNovaAnamnese(Integer usuario, ViewUtils vu) {
        Anamnese anamnese = new Anamnese();
        anamnese.setResponsavelLancamento(usuario);
        anamnese.setDataLancamento(Calendario.hoje());
        anamnese.setDescricao("PAR-Q");
        anamnese.setParq(Boolean.TRUE);
        return anamnese;
    }

    private List<Pergunta> criarPerguntasPadrao(ViewUtils vu) {
        String[] perguntasDesc = {
                "parq1", "parq2", "parq3", "parq4", "parq5", "parq6", "parq7"
        };

        List<Pergunta> perguntas = new ArrayList<>();
        int ordem = 1;
        for (String desc : perguntasDesc) {
            Pergunta pergunta = new Pergunta();
            pergunta.setDescricao(vu.getLabel(desc));
            pergunta.setOrdem(ordem++);
            pergunta.setTipoPergunta(TiposPerguntaEnum.SIM_NAO);
            perguntas.add(pergunta);
        }
        return perguntas;
    }


    public void excluirAvaliacaoIntegrada(final String key, ItemAvaliacaoFisica item) throws Exception{
        m3DDao.deleteComParam(key, new String[]{"item.codigo"}, new Object[]{item.getCodigo()});
        respostaClienteDao.deleteComParam(key, new String[]{"itemAvaliacao.codigo"}, new Object[]{item.getCodigo()});
        itemDao.delete(key, item);
    }

    public void alterarAvaliacaoIntegrada(final String key, ItemAvaliacaoFisica item, Anamnese avaliacao,
                                          List<Movimento3D> mobilidade, List<Movimento3D> estabilidade) throws Exception{
        for(Movimento3D m : mobilidade){
            m3DDao.update(key, m);
        }
        for(Movimento3D e : estabilidade){
            m3DDao.update(key, e);
        }
        for (PerguntaAnamnese pa : item.getAnamnese().getPerguntas()) {
            RespostaCliente resposta = obterRespostaPerguntasAnamnese(key, pa.getCodigo(), item.getCliente().getCodigo(),
                    item.getCodigo());
            if(resposta != null){
                respostaClienteDao.delete(key, resposta);
            }
        }
        gravarAnamneseCliente(key, avaliacao, item.getCliente(), item);
        itemDao.update(key, item);
    }

    public Anamnese povoarAvaliacaoIntegrada(final String key, final Usuario usuario) throws Exception{
        Anamnese anamnese = new Anamnese();
        anamnese.setResponsavelLancamento(usuario.getCodigo());
        anamnese.setDataLancamento(Calendario.hoje());
        anamnese.setDescricao("AVALIAÇÃO INTEGRADA - DEFAULT");
        anamnese.setTipo(TipoAnamneseEnum.AVALIACAO_INTEGRADA);

        List<Pergunta> perguntas = new ArrayList<Pergunta>();
        int i = 1;
        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.MULTIPLA_ESCOLHA, AgrupamentoAvaliacaoIntegradaEnum.GERAL,
                "O que é mais importante para você em um treinamento? Escolha apenas duas opções que são muito importantes.",
                "Intensidade do treino","Treino integrado de várias modalidades","Equipamentos diferenciados",
                "O treino sempre diferente e dinâmico","Correção nos exercícios","Atendimento do professor"));
        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.SIMPLES_ESCOLHA, AgrupamentoAvaliacaoIntegradaEnum.GERAL,
                "Você GOSTA de um treino em que intensidade?",
                "Avançado - Intenso e forte","Intermediário forte - Intenso em alguns momentos","Intermediário - Leve e moderado"));
        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.SIMPLES_ESCOLHA, AgrupamentoAvaliacaoIntegradaEnum.GERAL,
                "Qual o seu objetivo em buscar atividade física?","Preparação para algum esporte","Hipertrofia","Emagrecer e definição muscular"));
        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.TEXTUAL, AgrupamentoAvaliacaoIntegradaEnum.GERAL,
                "Nos ajude a traçar uma meta para daqui 10 semanas. O que gostaria de fazer com seu corpo Que hoje não faz?"));

        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.SIMPLES_ESCOLHA, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_VIDA,
                "Avalie a quantidade do seu sono em número de horas","muito pouco","suficiente","as vezes suficiente e as vezes excelente","excelente"));
        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.SIMPLES_ESCOLHA, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_VIDA,
                "Avalie a qualidade do seu sono durante a noite:",
                "muito ruim","boa","as vezes boa e as vezes excelente","excelente"));
        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.SIMPLES_ESCOLHA, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_VIDA,
                "Avalie sua disposição durante o dia:","me sinto muito cansado","cansado","bem disposto na maioria dos dias",
                "muito disposto para todas as atividades"));
        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.SIMPLES_ESCOLHA, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_VIDA,
                "Como considera sua alimentação?",
                "muito ruim e pouco saudável","pouco saudável","equilibrada apenas durante a semana","equilibrada e saudável sempre"));
        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.SIMPLES_ESCOLHA, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_VIDA,
                "Avalie seu condicionamento",
                "muito ruim","melhorando","bom","excelente"));
        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.SIMPLES_ESCOLHA, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_MOVIMENTO,
                "Você sente dor em alguma região do corpo? Se quiser descreva sobre ela",
                "sinto dor sempre parado ou me movimentando","sinto dor em alguns momentos do dia","sinto dor apenas de vez em quando","não sinto dor"));
        perguntas.add(addNovaPergunta(i++,TiposPerguntaEnum.SIMPLES_ESCOLHA, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_MOVIMENTO,
                "Descreva como é essa dor",
                "sinto dor independente do que eu faça","sinto dor somente quando movimento a região","sinto dor apenas de vez em quando","não sinto dor"));
        return gravarAnamnese(key, anamnese, perguntas, new ArrayList<>(), new ArrayList<>());
    }

    private Pergunta addNovaPergunta(int i, TiposPerguntaEnum t, AgrupamentoAvaliacaoIntegradaEnum agrupamento, String p, String... opcoes){
        Pergunta pergunta = new Pergunta();
        pergunta.setDescricao(p);
        pergunta.setOrdem(i++);
        pergunta.setTipoPergunta(t);
        pergunta.setAgrupamento(agrupamento);
        int peso = 0;
        pergunta.setOpcoes(new ArrayList<OpcaoPergunta>());
        for(String o : opcoes){
            OpcaoPergunta op = new OpcaoPergunta();
            op.setOpcao(o);
            if(!agrupamento.equals(AgrupamentoAvaliacaoIntegradaEnum.GERAL)){
                op.setPeso(peso++);
            }
            pergunta.getOpcoes().add(op);
        }
        return pergunta;
    }

    public QuestionarioJSON consultarParQ(String key, Integer empresa, final ViewUtils vu) throws Exception {
        Anamnese anamnese = consultarParq(key, 0, vu);
        QuestionarioJSON q = new QuestionarioJSON();
        q.setCodigo(anamnese.getCodigo());
        q.setTipoQuestionario("parq");
        q.setDescricao(anamnese.getDescricao());
        q.setPerguntas(new ArrayList<PerguntaJSON>());
        anamnese.setPerguntas(obterPerguntasAnamnese(key, anamnese.getCodigo()));
        for(PerguntaAnamnese pa : anamnese.getPerguntas()){
            PerguntaJSON p = new PerguntaJSON();
            p.setCodigo(pa.getCodigo());
            p.setTipoPergunta(pa.getPergunta().getTipoPergunta().name());
            p.setDescricao(pa.getPergunta().getDescricao());
            p.setRespostas(new ArrayList<RespostaPerguntaJSON>());

            RespostaPerguntaJSON r = new RespostaPerguntaJSON();
            r.setDescricao("Sim");
            r.setCodigo(1);
            p.getRespostas().add(r);

            RespostaPerguntaJSON rn = new RespostaPerguntaJSON();
            rn.setDescricao("Não");
            rn.setCodigo(2);
            p.getRespostas().add(rn);

            q.getPerguntas().add(p);
        }
        return q;

    }

    public void responderParQServico(String ctx, String matricula, JSONObject respostas, String assinatura) throws Exception{
        ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula);
        if(cliente == null){
            cliente = aulaService.addAlunoAutomaticamente(ctx, matricula);
        }
        Anamnese anamnese = consultarParq(ctx, 0, null);
        AvaliacaoFisica avaliacaoFisica = new AvaliacaoFisica();
        avaliacaoFisica.setCliente(cliente);
        avaliacaoFisica.setDataAvaliacao(Calendario.hoje());

        ItemAvaliacaoFisica itemAnamnese = null;
        if (!UteisValidacao.emptyString(assinatura) && !assinatura.contains("consentimento-pelo-vendas-online2.0")) {
            avaliacaoFisica = avaliacaoService.inserir(ctx, avaliacaoFisica);
            itemAnamnese = avaliacaoService.adicionarItem(ctx, anamnese.getDescricao(), cliente,
                    null, ItemAvaliacaoFisicaEnum.ANAMNESE, anamnese, null, avaliacaoFisica);
        }

        RespostaClienteParQ respostaParQ = avaliacaoService.adicionarRespostaParQ(ctx, null, 2, cliente);
        if(!UteisValidacao.emptyString(assinatura)){
            respostaParQ = avaliacaoService.salvarAssinaturaParQ(ctx, assinatura, respostaParQ);
        }

        for(PerguntaAnamnese pa : anamnese.getPerguntas()){
            Boolean sim = Boolean.valueOf(respostas.optString("resp" + pa.getCodigo() + "-1"));
            String resposta = sim ? "SIM" : "NAO";
            RespostaCliente rc = new RespostaCliente();
            rc.setPerguntaAnamnese(pa);
            rc.setCliente(cliente);
            rc.setItemAvaliacao(itemAnamnese);
            rc.setObs(pa.getComplemento());
            rc.setResposta(resposta);
            rc.setRespostaClienteParQ(respostaParQ);
            respostaClienteDao.insert(ctx, rc);
        }

        if(!UteisValidacao.emptyString(assinatura) && !assinatura.contains("consentimento-pelo-vendas-online2.0")){
            avaliacaoFisica.setAssinatura(assinatura);
            avaliacaoService.salvarAssinatura(ctx, avaliacaoFisica);
        }
    }

    public List<AnamneseResponseTO>consultarAnamneses(FiltrosJSON filtro, PaginadorDTO paginadorDTO)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return consultarAnamneses(ctx, filtro, paginadorDTO);
    }

    private static final int MAXIMO_ANAMNESES_CONSULTAR = 50;

    public List<AnamneseResponseTO> consultarAnamneses(final String ctx, FiltrosJSON filtro, PaginadorDTO paginadorDTO)throws ServiceException{
        int maxResults = MAXIMO_ANAMNESES_CONSULTAR;
        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_ANAMNESES_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        where.append(" where 1=1 ");
        List<AnamneseResponseTO> listaRet = new ArrayList<>();
        hql.append("SELECT obj FROM Anamnese obj where 1=1 ");

        hql.append(" AND (obj.parq IS FALSE or obj.parq is null) ");
        where.append(" AND (obj.parq IS FALSE or obj.parq is null) ");
        if ((filtro.getParamentro()  != null) && (!filtro.getParamentro().trim().equals(""))) {
            hql.append("and upper(obj.descricao) like '%").append(filtro.getParamentro().toUpperCase()).append("%'");
            where.append("and upper(obj.descricao) like '%").append(filtro.getParamentro().toUpperCase()).append("%'");
        }

        if(filtro.getParamAtivos() != null){
            String status = filtro.getParamAtivos().toString();
            if (status.equalsIgnoreCase("[ATIVO]")) {
                hql.append(" and ativa is true");
                where.append(" and ativa is true");
            } else if (status.equalsIgnoreCase("[INATIVO]")){
                hql.append(" and ativa is not true");
                where.append(" and ativa is not true");
            }
        }

        StringBuilder orderBy = new StringBuilder();
        if ((paginadorDTO != null) && (paginadorDTO.getSortMap() != null) && (paginadorDTO.getSortMap().size() > 0)) {
            for (Map.Entry<String, String> entry : paginadorDTO.getSortMap().entrySet()){
                if (orderBy.length() >=1){
                    orderBy.append(",");
                }
                orderBy.append(entry.getKey().equals("nome") ? "descricao" : entry.getKey()).append(" ").append(entry.getValue());
            }
        }

        if (orderBy.length() > 0){
            hql.append(" order by ").append(orderBy.toString());
        }
        List<Anamnese> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(anamneseDao.countWithParam(ctx, "codigo", where, null).longValue());
            }
            lista = anamneseDao.findByParam(ctx, hql.toString(), new HashMap<String, Object>(),maxResults,indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AnamneseExcecoes.ERRO_BUSCAR_ANAMNESES, e);
        }
        if (lista != null) {
            for (Anamnese anamnese : lista) {
                boolean emUso = false;
                ItemAvaliacaoFisica item = avaliacaoService.obterItemAvaliacaoPorAnamnese(ctx, anamnese.getCodigo());

                if (item != null && !UteisValidacao.emptyNumber(item.getCodigo())) {
                    emUso = true;
                }
                listaRet.add(new AnamneseResponseTO(anamnese, emUso));
            }
        }
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return listaRet;
    }

    @Override
    public AnamneseResponseTO consultarAnamnese(Integer codigoAnamnese) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Anamnese anamnese = anamneseDao.findById(ctx, codigoAnamnese);
            if ((anamnese == null) || (anamnese.getCodigo() == null) || (anamnese.getCodigo() <= 0)) {
                throw new ServiceException(AnamneseExcecoes.ANAMNESE_NAO_ENCONTRADA);
            }
            anamnese.setPerguntas(obterPerguntasAnamnese(ctx, anamnese.getCodigo()));
            for (PerguntaAnamnese pa : anamnese.getPerguntas()) {
                Pergunta pergunta = pa.getPergunta();
                pergunta.setOpcoes(obterOpcoes(ctx, pergunta.getCodigo()));
            }
            boolean emUso = false;
            ItemAvaliacaoFisica itemAvaliaca = avaliacaoService.obterItemAvaliacaoPorAnamnese(ctx, anamnese.getCodigo());
            if (itemAvaliaca != null && !UteisValidacao.emptyNumber(itemAvaliaca.getCodigo())) {
                emUso = true;
            }
            return new AnamneseResponseTO(anamnese, emUso);
        }catch (ServiceException e) {
            throw e;
        }catch (Exception e) {
            throw new ServiceException(AnamneseExcecoes.ERRO_BUSCAR_ANAMNESE, e);
        }
    }

    public void validarDados(final String ctx, Anamnese object) throws ServiceException {
        if (object.getDescricao() == null || object.getDescricao().trim().isEmpty()) {
            if (!JSFUtilities.isJSFContext()){
                throw new ServiceException(AnamneseExcecoes.VALIDACAO_DESCRICAO_ANAMNESE);
            }
            throw new  ValidacaoException("validacao.descricao");
        }
        if (anamneseDao.exists(ctx, object, "descricao")) {
            if (!JSFUtilities.isJSFContext()){
                throw new ServiceException(AnamneseExcecoes.VALIDACAO_ANAMNESE_JA_EXISTE);
            }
            throw new ValidacaoException("validacao.anamnese");
        }
        /*
        if (object.getPerguntas() != null && object.getPerguntas().size() > 0) {
            for (PerguntaAnamnese pergunta: object.getPerguntas()) {
                if (pergunta.getDescricao()...
            }
        }
        */
    }

    private Anamnese montarAnamnese(final String ctx, AnamneseTO anamneseTO)throws ServiceException {
        Anamnese anamnese = new Anamnese();
        if (anamneseTO.getTipo() != null) {
            anamnese.setTipo(anamneseTO.getTipo());
        }
        if(anamneseTO.getDatalancamento() != null){
            anamnese.setDataLancamento(anamneseTO.getDatalancamento());
        }
        if (anamneseTO.getId() != null && anamneseTO.getId() > 0) {
            anamnese.setCodigo(anamneseTO.getId());
            anamnese.setPerguntas(obterPerguntasAnamnese(ctx, anamneseTO.getId()));
        } else {
            anamnese.setPerguntas(new ArrayList<PerguntaAnamnese>());
        }
        anamnese.setDescricao(anamneseTO.getNome());
        anamnese.setAtiva(anamneseTO.isAtiva());
        int ordem = 1;
        if ((anamneseTO.getPerguntas() != null) && (anamneseTO.getPerguntas().size() > 0)){
            // Perguntas da anamnese
            for (PerguntaTO perguntaTO: anamneseTO.getPerguntas()){
                perguntaTO.setOrdem(ordem++);
                Pergunta pergunta = null;
                if (perguntaTO.getId() != null && perguntaTO.getId() > 0) {
                    for (PerguntaAnamnese pa : anamnese.getPerguntas()) {
                        // o id do perguntaTO está referenciando o código da tabela PerguntaAnamnese
                        if (pa.getCodigo().equals(perguntaTO.getId())) {
                            // Pergunta alterada
                            pergunta = pa.getPergunta();
                            pergunta.setOpcoes(obterOpcoes(ctx, pergunta.getCodigo()));
                            break;
                        }
                    }
                }
                if (pergunta == null){
                    // Pergunta nova
                    pergunta = new Pergunta();
                    pergunta.setOpcoes(new ArrayList<OpcaoPergunta>());
                    PerguntaAnamnese perguntaAnamnese = new PerguntaAnamnese();
                    perguntaAnamnese.setAnamnese(anamnese);
                    perguntaAnamnese.setPergunta(pergunta);
                    anamnese.getPerguntas().add(perguntaAnamnese);
                }
                pergunta.setDescricao(perguntaTO.getPergunta());
                pergunta.setTipoPergunta(perguntaTO.getTipo().getTiposPerguntaEnum());
                pergunta.setOrdem(perguntaTO.getOrdem());
                if (perguntaTO.getOpcoes() != null && perguntaTO.getOpcoes().size() > 0){
                    for (OpcaoPerguntaTO opcaoTO: perguntaTO.getOpcoes()){
                        OpcaoPergunta opcao = null;
                        if (opcaoTO.getId() != null && opcaoTO.getId() > 0) {
                            for (OpcaoPergunta op : pergunta.getOpcoes()) {
                                // Opção alterada
                                if (opcaoTO.getId().equals(op.getCodigo())) {
                                    opcao = op;
                                    break;
                                }
                            }
                        }
                        if (opcao == null) {
                            opcao = new OpcaoPergunta();
                            opcao.setPergunta(pergunta);
                            pergunta.getOpcoes().add(opcao);
                        }
                        opcao.setOpcao(opcaoTO.getNome());
                    }
                }
            }
        }
        validarDados(ctx, anamnese);
        return anamnese;
    }

    private Anamnese prepararAnamneseParaLog(String ctx, Integer codigoAnamnese) throws Exception {
        Anamnese anamneseAnterior = UtilReflection.copy(codigoAnamnese != null ? anamneseDao.findById(ctx, codigoAnamnese) : new Anamnese());
        List<PerguntaAnamnese> perguntasAnterior = obterPerguntasAnamnese(ctx, anamneseAnterior.getCodigo());
        List<PerguntaAnamnese> perguntasAnteriorTratada = new ArrayList<>();
        for (PerguntaAnamnese pa : perguntasAnterior) {
            perguntasAnteriorTratada.add(UtilReflection.copy(perguntaAnamneseDao.findByIdClearSession(ctx, pa.getCodigo())));
        }
        anamneseAnterior.setPerguntas(perguntasAnteriorTratada);
        for (PerguntaAnamnese pa : anamneseAnterior.getPerguntas()) {
            Pergunta pergunta = pa.getPergunta();
            List<OpcaoPergunta> opcaoPerguntasPreparadas = new ArrayList<>();
            List<OpcaoPergunta> opcaoPerguntas = obterOpcoes(ctx, pergunta.getCodigo());
            for (OpcaoPergunta op : opcaoPerguntas) {
                opcaoPerguntasPreparadas.add(UtilReflection.copy(opcaoPerguntaDao.findByIdClearSession(ctx, op.getCodigo())));
            }
            pergunta.setOpcoes(opcaoPerguntasPreparadas);
        }
        return anamneseAnterior;
    }

    private AnamneseResponseTO incluirOuAlterar(AnamneseTO anamneseTO) throws Exception {
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Anamnese anamneseAnterior = prepararAnamneseParaLog(ctx, anamneseTO.getId());
            Anamnese anamnese = montarAnamnese(ctx, anamneseTO);
            acao(anamnese, UteisValidacao.emptyNumber(anamnese.getCodigo()) ? TipoRevisaoEnum.INSERT : TipoRevisaoEnum.UPDATE);
            List<Pergunta> perguntas = new ArrayList<Pergunta>();
            List<Pergunta> perguntasExcluidas = new ArrayList<Pergunta>();
            for (PerguntaAnamnese pa : anamnese.getPerguntas()) {
                Boolean achouPergunta = false;
                for (PerguntaTO perguntaTO : anamneseTO.getPerguntas()) {
                    // o id do perguntaTO está referenciando o código da tabela PerguntaAnamnese
                    if (perguntaTO.getId() != null && perguntaTO.getId() > 0 && pa.getCodigo() != null
                            && pa.getCodigo().equals(perguntaTO.getId())) {
                        achouPergunta = true;
                        // Lista temporária para permitir remover opções na lista original
                        List<OpcaoPergunta> listaTemp = new ArrayList<OpcaoPergunta>();
                        listaTemp.addAll(pa.getPergunta().getOpcoes());
                        for (OpcaoPergunta opcao : listaTemp) {
                            Boolean achouOpcao = false;
                            for (OpcaoPerguntaTO opcaoTO : perguntaTO.getOpcoes()) {
                                if (opcaoTO.getId() != null && opcaoTO.getId() > 0
                                        && opcao.getCodigo() != null && opcao.getCodigo().equals(opcaoTO.getId())) {
                                    achouOpcao = true;
                                    break;
                                }
                            }
                            if (!achouOpcao && opcao.getCodigo() != null && opcao.getCodigo() > 0) {
                                opcaoPerguntaDao.delete(ctx, opcao);
                                // Remove da lista original para impedir a reinclusão
                                pa.getPergunta().getOpcoes().remove(opcao);
                            }
                        }
                        break;
                    }
                }
                if (achouPergunta || pa.getPergunta().getCodigo() == null || pa.getPergunta().getCodigo() < 1) {
                    perguntas.add(pa.getPergunta());
                } else {
                    perguntasExcluidas.add(pa.getPergunta());
                }
            }
            anamnese = gravarAnamnese(ctx, anamnese, perguntas, perguntasExcluidas, new ArrayList<>());
            anamnese.setPerguntas(obterPerguntasAnamnese(ctx, anamnese.getCodigo()));
            for (PerguntaAnamnese pa : anamnese.getPerguntas()) {
                Pergunta pergunta = pa.getPergunta();
                pergunta.setOpcoes(obterOpcoes(ctx, pergunta.getCodigo()));
            }
            if (!UteisValidacao.emptyNumber(anamneseAnterior.getCodigo())) {
                incluirLog(
                        ctx,
                        anamnese.getCodigo().toString(),
                        null,
                        anamneseAnterior.getDescricaoParaLog(anamnese),
                        anamnese.getDescricaoParaLog(anamneseAnterior),
                        "ALTERAÇÃO",
                        "ALTERAÇÃO DE ANAMNESE",
                        EntidadeLogEnum.ANAMNESE,
                        "Anamnese",
                        sessaoService,
                        logDao,
                        null, null);
            }
            boolean emUso = false;
            if (!UteisValidacao.emptyNumber(anamnese.getCodigo())) {
                ItemAvaliacaoFisica itemAvaliacao = avaliacaoService.obterItemAvaliacaoPorAnamnese(ctx, anamnese.getCodigo());

                if (itemAvaliacao != null && !UteisValidacao.emptyNumber(itemAvaliacao.getCodigo())) {
                    emUso = true;
                }
            }
            return new AnamneseResponseTO(anamnese, emUso);
        } catch (Exception e){
            throw e;
        } finally {
            leaveAcao();
        }


    }

    public AnamneseResponseTO inserir(AnamneseTO anamneseTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            anamneseTO.setDatalancamento(new Date());
            Anamnese anamnese = montarAnamnese(ctx, anamneseTO);
            AnamneseResponseTO retorno = incluirOuAlterar(anamneseTO);
            incluirLog(ctx, retorno.getId().toString(), "", "", anamnese.getDescricaoParaLog(null),
                    "INCLUSÃO", "INCLUSÃO DE ANAMNESE", EntidadeLogEnum.ANAMNESE, "Anamnese", sessaoService, logDao, null, null);
            return retorno ;
        }catch (ServiceException e) {
            throw e;
        }catch (Exception e) {
            throw new ServiceException(AnamneseExcecoes.ERRO_INCLUIR_ANAMNESE, e);
        }
    }

    public AnamneseResponseTO alterar(AnamneseTO anamneseTO) throws ServiceException{
        try {
            return incluirOuAlterar(anamneseTO);
        }catch (ServiceException e) {
            throw e;
        }catch (Exception e) {
            throw new ServiceException(AnamneseExcecoes.ERRO_ALTERAR_ANAMNESE, e);
        }
    }

    public void excluir(Integer codigoAnamnese) throws Exception, ValidacaoException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Anamnese anamnese = null;
        try{
            anamnese = anamneseDao.findById(ctx, codigoAnamnese);
            acao(anamnese,  TipoRevisaoEnum.DELETE);
        }catch (Exception e){
            throw new ServiceException(AnamneseExcecoes.ERRO_BUSCAR_ANAMNESE, e);
        }
        if ((anamnese == null) || (anamnese.getCodigo() == null) || (anamnese.getCodigo() <= 0)){
            throw new ServiceException(AnamneseExcecoes.ANAMNESE_NAO_ENCONTRADA);
        }
        try{
        incluirLog(ctx, anamnese.getCodigo().toString(), "",
                anamnese.getDescricaoParaLog(null),"", "EXCLUSÃO",
                "EXCLUSÃO DE ANAMNESE", EntidadeLogEnum.ANAMNESE, "Anamnese", sessaoService, logDao, null, null);
            excluirAnamnese(ctx, anamnese);
        }catch (ServiceException e){
            throw new ValidacaoException(e.getMessage(), e);
        }finally {
            leaveAcao();
        }
    }

    public List<AnamneseResponseTO> obterTodas(Boolean isIntegrada, Boolean ativas) throws ServiceException{
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<Anamnese> anamneseList = new ArrayList<>();
            if (isIntegrada) {
                if (ativas) {
                    anamneseList = anamneseDao.listaTodasAtivasIntegradasNaoEParq(ctx);
                } else {
                    anamneseList = anamneseDao.listaTodasIntegradasNaoEParq(ctx);
                }
            } else {
                anamneseList = anamneseDao.listaTodasNaoEParq(ctx);
            }
            List<AnamneseResponseTO> listaRet = new ArrayList<>();
            for (Anamnese anamnese: anamneseList) {
                anamnese.setPerguntas(obterPerguntasAnamnese(ctx, anamnese.getCodigo()));
                for (PerguntaAnamnese pta : anamnese.getPerguntas()) {
                    Pergunta pergunta = pta.getPergunta();
                    pergunta.setOpcoes(obterOpcoes(ctx, pergunta.getCodigo()));
                }
                boolean emUso = false;
                ItemAvaliacaoFisica item = avaliacaoService.obterItemAvaliacaoPorAnamnese(ctx, anamnese.getCodigo());
                if (item != null && !UteisValidacao.emptyNumber(item.getCodigo())) {
                    emUso = true;
                }
                listaRet.add(new AnamneseResponseTO(anamnese, emUso));
            }
            return listaRet;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }


    @Override
    public Anamnese getAnamneseForId(String ctx, Integer id) throws ServiceException {
        String sql = "SELECT obj FROM Anamnese obj WHERE (obj.parq IS FALSE or obj.parq is null) and obj.codigo = :id";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        try {
            return anamneseDao.findObjectByParam(ctx, sql , map);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public String salvarRespostasParQ(String ctx, JSONObject json, Integer usuarioZw) throws Exception{
        ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, json.optString("matriculaAluno"));
        if(cliente == null){
            cliente = aulaService.addAlunoAutomaticamente(ctx, json.optString("matriculaAluno"));
        }
        Anamnese anamnese = consultarParq(ctx, 0, null);
        RespostaClienteParQ respostaParQ = new RespostaClienteParQ();
        respostaParQ.setCliente(cliente);
        respostaParQ.setUsuario_codigo(usuarioZw);
        respostaParQ.setDataResposta(new Date());

        if(!UteisValidacao.emptyString(json.optString("assinatura64"))){
            respostaParQ = avaliacaoService.salvarAssinaturaParQ(ctx, json.optString("assinatura64"), respostaParQ);
        }
        if (UteisValidacao.emptyNumber(respostaParQ.getCodigo())) {
            respostaParQ = parQDao.insert(ctx, respostaParQ);
        }
        Boolean parqPositivo = false;
        Boolean perguntaAnamneseFoiInformada = true;
        for(PerguntaAnamnese pa : anamnese.getPerguntas()) {
            if(!perguntaAnamneseFoiInformada) {
                throw new ServiceException(AnamneseExcecoes.ANAMNESE_PERGUNTA_NAO_ENCONTRADA);
            }
            for(Object itemResposta : json.getJSONArray("respostas")) {
                JSONObject respostaJson = (JSONObject) itemResposta;
                if (respostaJson.optInt("codigo") == pa.getCodigo()) {
                    perguntaAnamneseFoiInformada = true;
                    RespostaCliente rc = respostaClienteDao.consultarPorCodigoRespostaClienteParQEPergunta(ctx, respostaParQ.getCodigo(), pa.getCodigo());
                    rc = (rc == null) ? new RespostaCliente() : rc;
                    String resposta = respostaJson.optString("resposta");
                    if (resposta.equals("1")) {
                        parqPositivo = true;
                    }
                    rc.setResposta(resposta.equals("1") ? "SIM" : "NAO");
                    rc.setObs(respostaJson.optString("observacao"));
                    rc.setPerguntaAnamnese(pa);
                    rc.setCliente(cliente);
                    rc.setRespostaClienteParQ(respostaParQ);
                    respostaClienteDao.insert(ctx, rc);
                    break;
                } else {
                    perguntaAnamneseFoiInformada = false;
                }
            }
        }
        respostaParQ.setParqPositivo(parqPositivo);
        respostaParQ.setAtivo(true);
        parQDao.update(ctx, respostaParQ);
        inativarRespostaParQAntigas(ctx, respostaParQ);

        cliente.setParq(parqPositivo);
        clienteService.alterar(ctx, cliente);
        clienteService.atualizarParQClienteZW(ctx, cliente.getCodigoCliente(), parqPositivo);
        JSONObject retorno = new JSONObject();
        retorno.put("retorno", "sucesso");
        retorno.put("parqpositivo", parqPositivo);
        return retorno.toString();
    }

    private void inativarRespostaParQAntigas(String ctx, RespostaClienteParQ rcp) throws Exception {
        // quando o aluno responder um novo parq, os antigos que estavam ativos deverão ser marcados como inativos, pois somente o mais recente deve ser o ativo;
        List<RespostaClienteParQ> listRespostaClienteParQ = parQDao.consultarTodosPorCliente(ctx, rcp.getCliente().getCodigo());
        for (RespostaClienteParQ r : listRespostaClienteParQ) {
            if (!rcp.getCodigo().equals(r.getCodigo()) && r.getAtivo()) {
                r.setAtivo(false);
                parQDao.update(ctx, r);
            }
        }

    }

    public String salvarEdicaoRespostasParQ(String ctx, JSONObject json, Integer usuarioZw, Integer codigoRespostaParq) throws Exception{
        Usuario usuario = usuarioServiceImpl.obterPorId(ctx, usuarioZw); // essa consulta busca o usuario pelo id no banco do zw

        RespostaClienteParQ respostaClienteParQ = parQDao.consultarRespostaParQPorCodigo(ctx, codigoRespostaParq);
        ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, json.optString("matriculaAluno"));
        if (cliente == null) {
            cliente = aulaService.addAlunoAutomaticamente(ctx, json.optString("matriculaAluno"));
        }
        Anamnese anamnese = consultarParq(ctx, 0, null);

        if (!UteisValidacao.emptyString(json.optString("assinatura64"))) {
            // irá sobreescrever a assinatura antiga
            MidiaService.getInstanceWood().deleteObject(respostaClienteParQ.getUrlAssinatura());
            String timeStamp = "?time=" + System.currentTimeMillis();
            String dataSource = json.optString("assinatura64").replace(" ", "+");
            String url = MidiaService.getInstanceWood().uploadObjectFromByteArray(
                    ctx,
                    MidiaEntidadeEnum.ASSINATURA_PARQ,
                    "assinaturaparq" + respostaClienteParQ.getCodigo()  + "_" + timeStamp,
                    Base64.decodeBase64(dataSource)
            );
            respostaClienteParQ.setUrlAssinatura(url);
        }
        Boolean parqPositivo = false;
        StringBuilder anterior = new StringBuilder();
        StringBuilder alterado = new StringBuilder();
        for(PerguntaAnamnese pa : anamnese.getPerguntas()) {
            for(Object itemResposta : json.getJSONArray("respostas")) {
                JSONObject respostaJson = (JSONObject) itemResposta;
                if (respostaJson.optInt("codigo") == pa.getCodigo()) {
                    RespostaCliente rc = respostaClienteDao.consultarPorCodigoRespostaClienteParQEPergunta(ctx, codigoRespostaParq, pa.getCodigo());
                    rc = (rc == null) ? new RespostaCliente() : rc;

                    anterior.append("[Pergunta: <br/>");
                    anterior.append("[Código: " + rc.getPerguntaAnamnese().getPergunta().getCodigo() + " ]<br/>");
                    anterior.append("[Descrição: " + rc.getPerguntaAnamnese().getPergunta().getDescricao() + " ]<br/>");
                    anterior.append("[Resposta: " + rc.getResposta() + " ]<br/>");
                    anterior.append("[Observação: " + rc.getObs() + " ]]<br/><br/>");

                    String resposta = respostaJson.optString("resposta");
                    if (resposta.equals("1")) {
                        parqPositivo = true;
                    }
                    rc.setResposta(resposta.equals("1") ? "SIM" : "NAO");
                    rc.setObs(respostaJson.optString("observacao"));

                    alterado.append("[Pergunta: <br/>");
                    alterado.append("[Código: " + rc.getPerguntaAnamnese().getPergunta().getCodigo() + " ]<br/>");
                    alterado.append("[Descrição: " + rc.getPerguntaAnamnese().getPergunta().getDescricao() + " ]<br/>");
                    alterado.append("[Resposta: " + rc.getResposta() + " ]<br/>");
                    alterado.append("[Observação: " + rc.getObs() + " ]]<br/><br/>");

                    if (UteisValidacao.emptyNumber(rc.getCodigo())) {
                        rc.setPerguntaAnamnese(pa);
                        rc.setCliente(cliente);
                        rc.setRespostaClienteParQ(respostaClienteParQ);
                        respostaClienteDao.insert(ctx, rc);
                    } else {
                        respostaClienteDao.update(ctx, rc);
                    }
                    break;
                }
            }
        }
        respostaClienteParQ.setDataUltimaEdicao(new Date());
        respostaClienteParQ.setParqPositivo(parqPositivo);
        respostaClienteParQ.setAtivo(true);
        parQDao.update(ctx, respostaClienteParQ);
        inativarRespostaParQAntigas(ctx, respostaClienteParQ);

        cliente.setParq(parqPositivo);
        clienteService.alterar(ctx, cliente);
        clienteService.atualizarParQClienteZW(ctx, cliente.getCodigoCliente(), parqPositivo);

        try {
            Log log = new Log();
            log.setNomeEntidade(EntidadeLogEnum.PARQ.name());
            log.setNomeEntidadeDescricao("ALTERAÇÃO PARQ");
            log.setChavePrimaria(respostaClienteParQ.getCodigo().toString());
            log.setChavePrimariaEntidadeSubordinada(cliente.getCodigo().toString());
            log.setNomeCampo("Formulário Par-Q");
            log.setValorCampoAnterior(anterior.toString());
            log.setValorCampoAlterado(alterado.toString());
            log.setOperacao("ALTERAÇÃO");
            log.setCliente(cliente.getCodigo());
            log.setPessoa(0);
            String username = usuario != null
                    ? usuario.getUserName()
                    : "Não localizado username para o usuarioZw: " + usuarioZw + " informado";
            log.setResponsavelAlteracao(username);
            parQDao.gravarLogParq(ctx, log);
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao registrar log de alteração do parq: " + e.getMessage());
        }

        JSONObject retorno = new JSONObject();
        retorno.put("retorno", "sucesso");
        retorno.put("parqpositivo", parqPositivo);
        return retorno.toString();

    }//

    //------------------------------- Log de anamnese

    public List<LogTO> listarLogAnamneses(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codigoAnamnese) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            if(UteisValidacao.emptyNumber(codigoAnamnese) && !UteisValidacao.emptyString(quicksearchValue)){
                try {
                    codigoAnamnese = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
                }catch (Exception e){
                    //ignore
                }
            }
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();
            sql.append(" _campos_ from customrevisionentity c\n");
            sql.append(" left join anamnese_aud aa on aa.rev = c.id \n");
            sql.append(" where c.processo like 'anamnese_%' \n");

            if(codigoAnamnese != null){
                sql.append(" and ( c.processo like '%\\_").append(codigoAnamnese);
                sql.append("' or aa.codigo = ").append(codigoAnamnese).append(")");
            }

            if(inicio != null && inicio > 0l){
                sql.append(" and c.\"timestamp\"/5000 >= ").append(inicio/5000);
            }
            if(fim != null && fim > 0l){
                sql.append(" and c.\"timestamp\"/5000 <= ").append(Calendario.fimDoDia(new Date(fim)).getTime()/5000);
            }
            if(tipos != null && tipos.length() > 0){
                sql.append(" and ( ");
                for(int i = 0; i < tipos.length(); i++){
                    TipoRevisaoEnum tipoRevisaoEnum = TipoRevisaoEnum.valueOf(tipos.getString(i));
                    if(i > 0){
                        sql.append(" or ");
                    }
                    sql.append(" c.processo like '%\\_t").append(tipoRevisaoEnum.getId()).append("_%'\n");
                }
                sql.append(" ) \n ");
            }

            if (!UteisValidacao.emptyString(quicksearchValue) && (codigoAnamnese == null || !codigoAnamnese.toString().equals(quicksearchValue))) {
                sql.append(" and (upper(c.processo) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
                sql.append(" or upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%') \n");
            }
            sql.append(" group by c.\"timestamp\"/5000, c.processo, c.username ");

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = anamneseDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "select c.\"timestamp\"/5000 as segundo, c.username, c.processo ")
                            .concat("order by 1 desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    String[] processo = rs.getString("processo").split("_");
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(Integer.valueOf(processo[2].replace("t", "")));
                    Integer codigo = Integer.valueOf(processo[3]);
                    Long timestamp = rs.getLong("segundo") * 5000;
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo,
                            revtype.getDescricaoLog(),
                            processo[1],
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());
                    try (ResultSet rsRevs = descobrirQuemFoiAlteradoNoMomento(ctx, rs.getLong("segundo"), username, codigo, revtype.getId())) {
                        logTO.setAlteracoes(new ArrayList<>());
                        while (rsRevs.next()) {
                            codigo = rsRevs.getInt("codigo");
                            logTO.setChave(String.valueOf(codigo));
                            Map<String, String> valoresAlterados = new HashMap() {{
                                put("ativa", rsRevs.getBoolean("ativa") ? "Sim" : "Não");
                                put("descricao", rsRevs.getString("descricao"));
                            }};
                            Map<String, String> valoresAnteriores = null;
                            if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                                try (ResultSet rsAnterior = anamneseDao.createStatement(ctx,
                                        "select aa.codigo, aa.revtype, aa.rev, " +
                                                " aa.descricao, aa.ativa from anamnese_aud aa where codigo = " + codigo +
                                                " and rev < " + rsRevs.getLong("rev") +
                                                " order by rev desc limit 1 ")) {
                                        if (rsAnterior.next()) {
                                            valoresAnteriores = new HashMap() {{
                                                put("ativa", rsAnterior.getBoolean("ativa") ? "Sim" : "Não");
                                                put("descricao", rsAnterior.getString("descricao"));
                                            }};
                                    }
                                }
                            }
                            logTO.getAlteracoes().addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
                        }
                    }

                    logTO.getAlteracoes().addAll(alteracoesPerguntas(ctx, rs.getLong("segundo"), username, codigo));
                    logTO.getAlteracoes().addAll(opcoesAlteradasMomento(ctx, rs.getLong("segundo"), username, codigo));

                    int limit = 0;
                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                + a.getValorAlterado() + "']<br/>"));
                        if (limit > 5) {
                            logTO.setDescricao(logTO.getDescricao() + "<br/>...");
                            break;
                        }
                        limit++;
                    }
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = anamneseDao.createStatement(ctx, sql.toString().replace("_campos_", " select count(segundo) as cont  from (" +
                            " select c.\"timestamp\"/5000 as segundo ")
                    .concat(") as t"))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }

        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }

    private ResultSet descobrirQuemFoiAlteradoNoMomento(String chave, Long momento, String username, Integer codigo, Integer revtype) throws Exception{
        return anamneseDao.createStatement(chave, "select aa.codigo, aa.rev, aa.ativa, aa.descricao from anamnese_aud aa " +
                " inner join customrevisionentity c on c.id = aa.rev" +
                " where c.username = '" + username +
                "' and c.\"timestamp\"/5000 = " + momento + (UteisValidacao.emptyNumber(codigo) ? "" : (" and aa.codigo = " + codigo)) +
                " and aa.revtype = " + revtype);
    }

    private String sqlOpcoes(Long momento, String username, Integer codigoAnamnese, String revtype){
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct oa.opcao, pa.codigo as pergunta_codigo, oa.codigo, oa.rev, oa.revtype from opcaopergunta_aud oa \n");
        sql.append(" inner join customrevisionentity c on c.id = oa.rev \n");
        sql.append(" inner join pergunta_aud pa on coalesce(oa.pergunta_codigo, (select pergunta_codigo from opcaopergunta_aud where codigo = oa.codigo and revtype = 0 limit 1)) = pa.codigo \n");
        sql.append(" inner join perguntaanamnese_aud aa on aa.pergunta_codigo = pa.codigo \n");
        sql.append(" where c.username = '").append(username).append("' and oa.revtype in (").append(revtype).append(") ");
        sql.append(" and c.\"timestamp\"/5000 = ").append(momento).append(" and aa.anamnese_codigo = ").append(codigoAnamnese);
        sql.append(" order by oa.codigo \n");
        return sql.toString();
    }

    private Map<Integer, List<String>> opcoesInseridasMomento(String chave, Long momento, String username, Integer codigoAnamnese) throws Exception{

        Map<Integer, List<String>> opcoes;
        try (ResultSet rs = anamneseDao.createStatement(chave, sqlOpcoes(momento, username, codigoAnamnese, "0"))) {
            opcoes = new HashMap<>();
            while (rs.next()) {
                List<String> opcoesPergunta = opcoes.get(rs.getInt("pergunta_codigo"));
                if (opcoesPergunta == null) {
                    opcoesPergunta = new ArrayList<>();
                    opcoes.put(rs.getInt("pergunta_codigo"), opcoesPergunta);
                }
                opcoesPergunta.add(rs.getString("opcao"));
            }
        }
        return opcoes;
    }

    private List<AlteracoesTO> opcoesAlteradasMomento(String chave, Long momento, String username, Integer codigoAnamnese) throws Exception {
        Map<String, Map<String, List<String>>> perguntasEOpcoes;
        List<AlteracoesTO> alteracoes;
        try (ResultSet rs = anamneseDao.createStatement(chave, sqlOpcoes(momento, username, codigoAnamnese, "1,2"))) {
            perguntasEOpcoes = new HashMap<>();
            alteracoes = new ArrayList<>();
            while (rs.next()) {
                try (ResultSet rsNomePergunta = anamneseDao.createStatement(chave, "select descricao from pergunta where codigo = " + rs.getInt("pergunta_codigo"))) {
                    if (rsNomePergunta.next()) {
                        String pergunta = rsNomePergunta.getString("descricao");
                        Map<String, List<String>> opcoes = perguntasEOpcoes.get(pergunta);
                        if (opcoes == null) {
                            opcoes = new HashMap<>();
                            List<String> anteriores = new ArrayList<>();
                            List<String> alteradas = new ArrayList<>();
                            opcoes.put("anteriores", anteriores);
                            opcoes.put("alteradas", alteradas);
                            perguntasEOpcoes.put(pergunta, opcoes);
                        }
                        opcoes.get("alteradas").add(UteisValidacao.emptyString(rs.getString("opcao")) ? "Opção removida" : rs.getString("opcao"));
                        Integer codigo = rs.getInt("codigo");
                        try (ResultSet rsAnterior = anamneseDao.createStatement(chave,
                                "select oa.opcao from opcaopergunta_aud oa where codigo = " + codigo +
                                        " and rev < " + rs.getLong("rev") +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                opcoes.get("anteriores").add(rsAnterior.getString("opcao"));
                            }
                        }
                    }
                }
            }
        }
        for(String p : perguntasEOpcoes.keySet()){
            alteracoes.add(new AlteracoesTO("alteração de opções de pergunta '" + p + "'",
                    Uteis.listToString(perguntasEOpcoes.get(p).get("anteriores")),
                    Uteis.listToString(perguntasEOpcoes.get(p).get("alteradas"))));
        }
        return alteracoes;
    }

    private List<AlteracoesTO> alteracoesPerguntas(String chave, Long momento, String username, Integer codigoAnamnese) throws Exception {
        Map<Integer, List<String>> opcoes = opcoesInseridasMomento(chave, momento, username, codigoAnamnese);
        List<AlteracoesTO> alteracoes;
        try (ResultSet rs = anamneseDao.createStatement(chave, "select distinct pa.rev, pa.revtype, pa.codigo, pa.descricao, pa.tipopergunta " +
                " from pergunta_aud pa " +
                " inner join customrevisionentity c on c.id = pa.rev" +
                " inner join perguntaanamnese_aud aa on aa.pergunta_codigo = pa.codigo" +
                " where c.username = '" + username +
                "' and c.\"timestamp\"/5000 = " + momento + " and aa.anamnese_codigo = " + codigoAnamnese)) {
            alteracoes = new ArrayList<>();

            while (rs.next()) {
                TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                String operacao = revtype.equals(TipoRevisaoEnum.INSERT) ? "adicionada" : revtype.equals(TipoRevisaoEnum.UPDATE) ? "editada" : "removida";
                Map<String, String> valoresAlterados = new HashMap() {{

                    if (revtype.equals(TipoRevisaoEnum.DELETE)) {
                        put("pergunta " + operacao, "");
                    } else {
                        String desc = rs.getString("descricao") + " " + TiposPerguntaEnum.getFromOrdinal(rs.getInt("tipopergunta")).name();
                        List<String> opcoesDaPergunta = opcoes.get(rs.getInt("codigo"));
                        desc += opcoesDaPergunta == null || revtype.equals(TipoRevisaoEnum.UPDATE) ? "" : (" - opções: " + Uteis.listToString(opcoesDaPergunta));
                        opcoes.remove(rs.getInt("codigo"));
                        put("pergunta " + operacao, desc);
                    }
                }};

                Integer codigo = rs.getInt("codigo");
                Map<String, String> valoresAnteriores = null;
                if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                    try (ResultSet rsAnterior = anamneseDao.createStatement(chave,
                            "select aa.codigo, aa.revtype, aa.rev, " +
                                    " aa.descricao, aa.tipopergunta from pergunta_aud aa where codigo = " + codigo +
                                    " and rev < " + rs.getLong("rev") +
                                    " order by rev desc limit 1 ")) {
                        if (rsAnterior.next()) {
                            valoresAnteriores = new HashMap() {{
                                put("pergunta " + operacao, rsAnterior.getString("descricao") + " " + TiposPerguntaEnum.getFromOrdinal(rsAnterior.getInt("tipopergunta")).name());
                            }};
                        }
                    }
                }
                alteracoes.addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
            }
        }
        for(Integer codigoPergunta : opcoes.keySet()){
            try (ResultSet rsNomePergunta = anamneseDao.createStatement(chave, "select descricao from pergunta where codigo = " + codigoPergunta)) {
                if (rsNomePergunta.next()) {
                    alteracoes.add(new AlteracoesTO("opcao_adicionada", "",
                            Uteis.listToString(opcoes.get(codigoPergunta)) + " add na pergunta " + rsNomePergunta.getString("descricao")));
                }
            }
        }
        return alteracoes;
    }


    protected void acao(final Anamnese anamnese, TipoRevisaoEnum tipo) throws ServiceException {
        AuditUtilities.putAcaoFromCurrentThread(Thread.currentThread().getId(), (anamnese.getParq() ? "parq" : "anamnese") +
                "_" + anamnese.getDescricao() +
                "_t" + tipo.getId() +
                "_" + (anamnese.getCodigo() == null ? "0" : anamnese.getCodigo()));
    }

    protected void leaveAcao() {
        try {
            AuditUtilities.leaveAcaoFromCurrentThread();
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    //------------------------------- Log de anamnese

    public void gravarParqPadrao10Perguntas(final String key, final ViewUtils vu) throws ServiceException{
        try {
            Anamnese anamnese = anamneseDao.findObjectByParam(key, "SELECT obj FROM Anamnese obj WHERE obj.parq IS TRUE AND obj.parqpadraorj IS TRUE", new HashMap<String, Object>());
            if(anamnese == null){
                anamnese = new Anamnese();
                Usuario usuario = usuarioService.consultarPorUserName(key, "PACTOBR");
                if (usuario != null) {
                    anamnese.setResponsavelLancamento(usuario.getCodigo());
                }
                anamnese.setParq(Boolean.TRUE);
                anamnese.setParqpadraorj(Boolean.TRUE);
                anamnese.setDataLancamento(Calendario.hoje());
                anamnese.setDescricao("PAR-Q");
                String[] perguntasDesc = new String[]{"medico.diagnostico.problema.coracao_pressao.atividade_fisica",
                        "sente.dores.peito.atividade_fisica",
                        "ultimos.dores.peito.atividade_fisica",
                        "desequilibrio.tontura.perda_consciencia",
                        "problema.osseo_articular.atividade_fisica",
                        "medicacao.continua",
                        "tratamento.medico.pressao_coracao",
                        "tratamento.medico.continuo.atividade_fisica",
                        "cirurgia.afeta_atividade_fisica",
                        "outra.razao.comprometer_saude.atividade_fisica"};
                List<Pergunta> perguntas = new ArrayList<Pergunta>();
                int i = 1;
                for(String p : perguntasDesc){
                    Pergunta pergunta = new Pergunta();
                    pergunta.setDescricao(vu.getLabel(p));
                    pergunta.setOrdem(i++);
                    pergunta.setTipoPergunta(TiposPerguntaEnum.SIM_NAO);
                    perguntas.add(pergunta);
                }
                gravarAnamnese(key, anamnese, perguntas, new ArrayList<>(), new ArrayList<>());
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

}

package br.com.pacto.service.impl.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.bi.*;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.OrigemExecucaoEnum;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.to.FiltrosGestaoTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.professor.DetalhesProfessorRankingDTO;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.controller.json.professor.InfoProfessorRankingDTO;
import br.com.pacto.controller.json.professor.RankingProfessoresResponseDTO;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.dashboardbi.GeracaoRankingProfessoresDao;
import br.com.pacto.dao.intf.dashboardbi.ProfessorRankingDao;
import br.com.pacto.dao.intf.dashboardbi.ProfessorRankingIndicadorDao;
import br.com.pacto.dao.intf.dashboardbi.ProfessorRankingModalidadeDao;
import br.com.pacto.dao.intf.dashboardbi.RankingProfessoresDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.serie.TreinoRealizadoDao;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.ProfessorExcecoes;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gestao.BiAppService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.gestao.RankingProfessoresService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.Ordenacao;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

@Service
public class RankingProfessoresServiceImpl implements RankingProfessoresService {

    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private RankingProfessoresDao rankingProfessoresDao;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private TreinoRealizadoDao treinoRealizadoDao;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    private BiAppService biAppService;
    @Autowired
    private DashboardBIService dashboardBIService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ProgramaTreinoDao programaTreinoDao;
    @Autowired
    private ProfessorSinteticoDao professorSinteticoDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private GeracaoRankingProfessoresDao geracaoDao;
    @Autowired
    private ProfessorRankingDao professorRankingDao;
    @Autowired
    private ProfessorRankingModalidadeDao professorRankingModalidadeDao;
    @Autowired
    private ProfessorRankingIndicadorDao professorRankingIndicadorDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;

    private static final String SEPARADOR_ORDENACOES = ";";
    private static final String SEPARADOR_ORDENACAO = ",";
    private static final int INDEX_CAMPO_ORDENAR = 0;
    private static final int INDEX_DIRECAO_ORDENACAO = 1;


    @Override
    public Map<String, Object> obterDeletarDia(final String key, final Integer mes, final Integer ano, final Integer professor, final FiltrosDashboard filtros, final Date dataProcessamento) throws Exception {
        Map<String, Object> backupDeletarDia = new HashMap<>();
        List<RankingProfessores> ListaDashDeletar = rankingProfessoresDao.findListByAttributes(key, new String[]{"mes", "ano", "codigoProfessor", "empresa"}, new Object[]{mes, ano, professor, filtros.getEmpresa()}, null, 1, 0);
        RankingProfessores dashdeletar = null;
        if (!UteisValidacao.emptyList(ListaDashDeletar)){
            dashdeletar = ListaDashDeletar.get(0);
        }
        if(dashdeletar != null){
            backupDeletarDia.put("DashboardBI", dashdeletar);
        }
        return backupDeletarDia;
    }

    @Override
    public void deletarDiasGuardados(final String key, Map<String, Object> objectMap) throws Exception {
        if (objectMap.get("DashboardBI") != null) {
            rankingProfessoresDao.delete(key, (RankingProfessores) objectMap.get("DashboardBI"));
        }
    }

    private List<Integer> codigosProfessores(String key, Integer codEmpresaZW, String situacao, String tipos,
                                      List<Integer> cargaHoraria, List<Integer> modalidadeIds) throws Exception{
        Map<String, String> requestParams = new HashMap<>();
        if(situacao != null){
            requestParams.put("situacaoProfessor", situacao);
        }
        requestParams.put("tipos", tipos);
        if(modalidadeIds != null){
            String modalidades = "";
            for(Integer mod : modalidadeIds){
                modalidades += "," + mod;
            }
            requestParams.put("modalidades", modalidades.replaceFirst(",", ""));
        }
        if(cargaHoraria != null){
            String carga = "";
            for(Integer car : cargaHoraria){
                carga += "," + car;
            }
            requestParams.put("carga", carga.replaceFirst(",", ""));
        }
        JSONArray professores = chamadaZW(key,
                "/prest/treino/codigos-professores",
                codEmpresaZW, null, null, null, null, requestParams);
        return new ArrayList(){{
            for(int i = 0; i < professores.length();i++){
                add(professores.getInt(i));
            }
        }};
    }


    public GeracaoRankingProfessores gerarRankingNoBanco(final String key,
                                    final Date inicio,
                                    final Date fim,
                                    boolean treinoIndependente,
                                    int usuario,
                                    int codEmpresaZW) throws ServiceException{
        try {
            Usuario usuarioResponsavel = usuarioService.obterPorId(key, usuario);
            GeracaoRankingProfessores geracao = new GeracaoRankingProfessores();
            geracao.setEmpresa(codEmpresaZW);
            geracao.setInicio(inicio);
            geracao.setFim(fim);
            geracao.setInicioProcessamento(Calendario.hoje());
            geracao.setResponsavelLancamento_codigo(usuarioResponsavel.getCodigo());
            geracao = geracaoDao.insert(key, geracao);
            List<ConfiguracaoRankingProfessores> listConfig = dashboardBIService.obterCfgRanking(key, null, codEmpresaZW, null, true);
            List<ProfessorRanking> professoresRanking = new ArrayList<>();

            String codigosProfessores = Uteis.concatenarListaVirgula(codigosProfessores(key, codEmpresaZW, "ativo",
                    "'TW','PE','PI'", null, null));

            try (ResultSet rs = geracaoDao.createStatement(key, "select p.codigo, p.codigocolaborador from professorsintetico p " +
                    " where codigocolaborador in (" + codigosProfessores + ") ")) {
                while (rs.next()) {
                    professoresRanking.add(processarIndicadoresProfessorRanking(key,
                            geracao, rs.getInt("codigo"), rs.getInt("codigocolaborador"),
                            listConfig, treinoIndependente));
                }
            }
            professoresRanking = Ordenacao.ordenarListaReverse(professoresRanking, "pontos");
            int posicao = 1;
            for(ProfessorRanking professorRanking : professoresRanking){
                professorRanking.setPosicao(posicao++);
                List<ProfessorRankingIndicador> indicadores = professorRanking.getIndicadores();
                professorRanking = professorRankingDao.insert(key, professorRanking);
                for(ProfessorRankingIndicador pri : indicadores){
                    pri.setProfessorRanking(professorRanking);
                    professorRankingIndicadorDao.insert(key, pri);
                }
            }
            geracao.setFimProcessamento(Calendario.hoje());
            geracao.setProfessores(professoresRanking);
            geracaoDao.update(key, geracao);
            return geracao;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    public ProfessorRanking processarIndicadoresProfessorRanking(final String key,
                                                                 final GeracaoRankingProfessores geracao,
                                                                 final Integer professor,
                                                                 final Integer codigoColaborador,
                                                                 final List<ConfiguracaoRankingProfessores> configs,
                                                                 boolean treinoIndependente) throws ServiceException {

        try {
            ProfessorSintetico professorSintetico = professorSinteticoService.obterPorId(key, professor);
            ProfessorRanking professorRanking = new ProfessorRanking();
            professorRanking.setGeracao(geracao);
            professorRanking.setProfessor(professorSintetico);
            Empresa empresa = empresaService.obterPorIdZW(key, geracao.getEmpresa());
            List<ProfessorRankingIndicador> indicadoresDoProfessor = new ArrayList<>();
            processarTreinosRealizados(key, indicadoresDoProfessor, configs, professorSintetico, geracao.getEmpresa(), geracao.getInicio(), geracao.getFim());
            if(!treinoIndependente) {
                processarCancelados(professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(),geracao.getEmpresa(), key);
            }
            processarAlunos(key, indicadoresDoProfessor, configs, professorSintetico, geracao.getEmpresa(), geracao.getInicio(), geracao.getFim(), treinoIndependente);
            processarAlunosTreinos(key, professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(), geracao.getEmpresa(), false);
            processarAlunosTreinos(key, professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(), geracao.getEmpresa(), true);

            processarProgramasTreinos(professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(), geracao.getEmpresa(), key, treinoIndependente);
            processarTreinosEmDia(key, professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(),geracao.getEmpresa(), IndicadorDashboardEnum.EM_DIA);
            processarTreinosEmDia(key, professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(),geracao.getEmpresa(), IndicadorDashboardEnum.VENCIDOS);
            processarTreinosEmDia(key, professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(),geracao.getEmpresa(), IndicadorDashboardEnum.PERC_TREINO_VENCIDOS);
            processarTreinosEmDia(key, professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(),geracao.getEmpresa(), IndicadorDashboardEnum.PERC_TREINO_EM_DIA);

            biAppService.processarRankingAlunosComApp(key, professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(), geracao.getEmpresa());
            processarAgenda(professorSintetico, indicadoresDoProfessor,  configs, geracao.getInicio(), geracao.getFim(), empresa.getCodigo(), empresa.getCodZW(), key);

            obterTempoMedioPermanenciaNoTreino(professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(), geracao.getEmpresa(), key);
            if (!treinoIndependente) {
                processarDadosZW(professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(),geracao.getEmpresa(), key);
            }
            processarAvaliacoesFisicas(professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(), geracao.getEmpresa(), key, treinoIndependente, false);
            processarAvaliacoesFisicas(professorSintetico, indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(), geracao.getEmpresa(), key, treinoIndependente, true);

            execucoes(professorSintetico,indicadoresDoProfessor, configs, geracao.getInicio(), geracao.getFim(), empresa.getCodigo(), key);
            Double pontos = 0.0;
            for(ProfessorRankingIndicador pri : indicadoresDoProfessor){
                if(pri.getIndicador().equals(IndicadorDashboardEnum.PERCENTUAL_RENOVACAO_CARTEIRA) ||
                        pri.getIndicador().equals(IndicadorDashboardEnum.PERC_TREINO_EM_DIA)){
                    pontos += pri.getValor();
                }else{
                    pontos += (pri.getValor() * pri.getMultiplicador()) * (pri.getPositivo() ? 1.0 : -1.0);
                }
            }
            professorRanking.setPontos(pontos);
            professorRanking.setIndicadores(indicadoresDoProfessor);
            return professorRanking;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private void processarCancelados(ProfessorSintetico professorSintetico,
                                     final List<ProfessorRankingIndicador> listaItems,
                                     final List<ConfiguracaoRankingProfessores> configs,
                                     Date inicio,
                                     Date fim, Integer empresa, String key) throws Exception {
        if(contemIndicador(configs, IndicadorDashboardEnum.ALUNOS_CANCELADOS)){
            List<Integer> retorno = obterCancelados(key, empresa, Uteis.getMesData(inicio),
                    Uteis.getAnoData(inicio), professorSintetico.getCodigoColaborador());
            addIndicador(listaItems, IndicadorDashboardEnum.ALUNOS_CANCELADOS,
                    configIndicador(configs, IndicadorDashboardEnum.ALUNOS_CANCELADOS),
                    new Double(retorno.size()));
        }
    }


    private void execucoes(ProfessorSintetico professorSintetico,
                           final List<ProfessorRankingIndicador> listaItems,
                           final List<ConfiguracaoRankingProfessores> configs,
                           Date inicio,
                           Date fim, Integer empresa, String key) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append(" select count(professor_codigo) as total, origem from treinorealizado t ") ;
        sql.append(" where  dataInicio BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd HH:mm:ss")).append("' ");
        sql.append(" AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd")).append(" 23:59:59' ");
        sql.append(" AND professor_codigo =  ").append(professorSintetico.getCodigo());
        sql.append(" group by origem");
        Double geral;
        try (ResultSet statement = rankingProfessoresDao.createStatement(key, sql.toString())) {
            geral = 0.0;
            while (statement.next()) {
                Integer origem = statement.getInt("origem");
                Double total = new Double(statement.getInt("total"));
                geral += total;
                if (OrigemExecucaoEnum.getFromId(origem).equals(OrigemExecucaoEnum.SMARTPHONE)) {
                    addIndicador(listaItems, IndicadorDashboardEnum.SMARTPHONE,
                            configIndicador(configs, IndicadorDashboardEnum.SMARTPHONE),
                            total);
                }
            }
        }
        addIndicador(listaItems, IndicadorDashboardEnum.EXECUCOES_TREINO,
                configIndicador(configs, IndicadorDashboardEnum.EXECUCOES_TREINO),
                geral);
    }

    public void obterTempoMedioPermanenciaNoTreino(ProfessorSintetico professorSintetico,
                                                   final List<ProfessorRankingIndicador> listaItems,
                                                   final List<ConfiguracaoRankingProfessores> configs,
                                                   Date inicio,
                                                   Date fim, Integer empresa, String key) throws Exception {
        int tempoMediana = 0;
        int tempoMedio = 0;
        String hql = "SELECT cast(dataterminoprevisto as date) - cast(datainicio  as date) FROM programatreino  "
                +"  INNER JOIN ClienteSintetico cli on cli.codigo = programatreino.cliente_codigo "
                +"  WHERE"
                + " cli.situacao = 'AT' "
                + " AND professorcarteira_codigo = " + professorSintetico.getCodigo();
        List<Integer> valoresInt = programaTreinoDao.listOfObjects(key, hql);
        if(valoresInt == null || valoresInt.isEmpty()){
            tempoMediana = 0;
            tempoMedio = 0;
        } else {
            Integer somatoria = 0;
            List<Integer> valoresAdd = new ArrayList<Integer>();
            for(Integer bv : valoresInt){
                if(bv != null){
                    somatoria += bv;
                    valoresAdd.add(bv);
                }
            }
            tempoMediana = Uteis.mediana(valoresAdd);
            tempoMedio = somatoria/valoresAdd.size();
        }
        addIndicador(listaItems, IndicadorDashboardEnum.BI_TEMPO_PROGRAMA,
                configIndicador(configs, IndicadorDashboardEnum.BI_TEMPO_PROGRAMA),
                tempoMedio/30.0);
    }

    public void processarAgenda(ProfessorSintetico professorSintetico,
                                final List<ProfessorRankingIndicador> listaItems,
                                final List<ConfiguracaoRankingProfessores> configs,
                                Date inicio,
                                Date fim, Integer empresaTreino, Integer codEmpresaZw, String key) throws Exception {
        long minutosDisponibilidade = 0;
        long minutosAtendimento = 0;
        DashboardBI dashboard = new DashboardBI();
        dashboard.setCodigoProfessor(professorSintetico.getCodigo());
        dashboard.setEmpresa(empresaTreino);
        List<Agendamento> agendamentosIndisponiveis = dashboardBIService.obterAgendamentos(key, professorSintetico.getCodigo(), dashboard, inicio, fim, false, null, null,null, null);
        List<Agendamento> agendamentosDisponiveis = dashboardBIService.obterAgendamentos(key, professorSintetico.getCodigo(), dashboard, inicio, fim, true, null, null,null, null);
        for (Agendamento agendamentoDisponivel : agendamentosDisponiveis){
            long min = Uteis.minutosEntreDatas(agendamentoDisponivel.getInicio(), agendamentoDisponivel.getFim());
            minutosDisponibilidade = minutosDisponibilidade + min;
        }
        int agendamentos = 0;
        int compareceram = 0;
        int cancelaram = 0;
        int avaliacoes = 0;
        int faltaram = 0;
        int aguardando = 0;
        for (Agendamento agendamentoIndisponivel : agendamentosIndisponiveis) {
            long min = Uteis.minutosEntreDatas(agendamentoIndisponivel.getInicio(), agendamentoIndisponivel.getFim());
            minutosAtendimento = minutosAtendimento + (min == 59 ? 60 : min);
            agendamentos++;
            if (agendamentoIndisponivel.getStatus() != null && agendamentoIndisponivel.getStatus().equals(StatusAgendamentoEnum.EXECUTADO)) {
                compareceram++;
            } else if (agendamentoIndisponivel.getStatus() != null && agendamentoIndisponivel.getStatus().equals(StatusAgendamentoEnum.CANCELADO)) {
                cancelaram++;
            } else if (agendamentoIndisponivel.getStatus() != null && agendamentoIndisponivel.getStatus().equals(StatusAgendamentoEnum.FALTOU)) {
                faltaram++;
            } else if (agendamentoIndisponivel.getStatus() != null && agendamentoIndisponivel.getStatus().equals(StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO)) {
                aguardando++;
            }
            if(agendamentoIndisponivel.getTipoEvento()!=null){
                if(agendamentoIndisponivel.getTipoEvento().getComportamento() != null
                        && agendamentoIndisponivel.getTipoEvento().getComportamento().equals(TipoAgendamentoEnum.AVALIACAO_FISICA)){
                    avaliacoes++;
                }
            }
        }
        addIndicador(listaItems, IndicadorDashboardEnum.FALTARAM,
                configIndicador(configs, IndicadorDashboardEnum.FALTARAM),
                new Double(faltaram));
        addIndicador(listaItems, IndicadorDashboardEnum.AVALIACOES_FISICAS,
                configIndicador(configs, IndicadorDashboardEnum.AVALIACOES_FISICAS),
                new Double(avaliacoes));
        addIndicador(listaItems, IndicadorDashboardEnum.AG_CONFIRMACAO,
                configIndicador(configs, IndicadorDashboardEnum.AG_CONFIRMACAO),
                new Double(aguardando));
        addIndicador(listaItems, IndicadorDashboardEnum.CANCELARAM,
                configIndicador(configs, IndicadorDashboardEnum.CANCELARAM),
                new Double(cancelaram));
        addIndicador(listaItems, IndicadorDashboardEnum.COMPARECERAM,
                configIndicador(configs, IndicadorDashboardEnum.COMPARECERAM),
                new Double(compareceram));
        addIndicador(listaItems, IndicadorDashboardEnum.AGENDAMENTOS,
                configIndicador(configs, IndicadorDashboardEnum.AGENDAMENTOS),
                new Double(agendamentos));
        addIndicador(listaItems, IndicadorDashboardEnum.HRS_ATENDIMENTO,
                configIndicador(configs, IndicadorDashboardEnum.HRS_ATENDIMENTO),
                minutosAtendimento/60.0);
        addIndicador(listaItems, IndicadorDashboardEnum.HRS_DISPONIBILIDADE,
                configIndicador(configs, IndicadorDashboardEnum.HRS_DISPONIBILIDADE),
                minutosDisponibilidade/60.0);
        addIndicador(listaItems, IndicadorDashboardEnum.OCUPACAO,
                configIndicador(configs, IndicadorDashboardEnum.OCUPACAO),
                (minutosDisponibilidade/60.0) == 0 ? 0 : (new Double(((minutosAtendimento/60.0)/(minutosDisponibilidade/60.0))*100.0)));
        addIndicador(listaItems, IndicadorDashboardEnum.NOVOS_TREINOS,
                configIndicador(configs, IndicadorDashboardEnum.NOVOS_TREINOS),
                new Double(dashboardBIService.obterNumeroProgramasAgendamento(key, professorSintetico.getCodigo(), codEmpresaZw,
                        inicio, fim, TipoAgendamentoEnum.PRESCRICAO_TREINO)));
        addIndicador(listaItems, IndicadorDashboardEnum.TREINOS_RENOVADOS,
                configIndicador(configs, IndicadorDashboardEnum.TREINOS_RENOVADOS),
                new Double(dashboardBIService.obterNumeroProgramasAgendamento(key, professorSintetico.getCodigo(), codEmpresaZw, inicio, fim, TipoAgendamentoEnum.RENOVAR_TREINO)));
        addIndicador(listaItems, IndicadorDashboardEnum.TREINOS_REVISADOS,
                configIndicador(configs, IndicadorDashboardEnum.TREINOS_REVISADOS),
                new Double(dashboardBIService.obterNumeroProgramasAgendamento(key, professorSintetico.getCodigo(), codEmpresaZw, inicio, fim, TipoAgendamentoEnum.REVISAO_TREINO)));
    }

    public void processarAvaliacoesFisicas(ProfessorSintetico professorSintetico,
                                           final List<ProfessorRankingIndicador> listaItems,
                                           final List<ConfiguracaoRankingProfessores> configs,
                                           Date inicio,
                                           Date fim, Integer empresaZW, String key, boolean treinoIndependente, Boolean semAvaliacao) throws Exception {
        StringBuilder query = new StringBuilder();
        StringBuilder where = new StringBuilder();
        montarWhereAlunos(professorSintetico.getCodigo(), empresaZW, where, treinoIndependente);
        query.append("SELECT DISTINCT obj.codigo FROM clientesintetico obj left join professorsintetico ps on ps.codigo = obj.professorsintetico_codigo\n");
        where.append(semAvaliacao ? " AND NOT " : " AND ");
        where.append("(EXISTS(");
        where.append(" SELECT ag.cliente_codigo FROM agendamento ag  \n");
        where.append(" INNER JOIN tipoevento te ON ag.tipoevento_codigo = te.codigo \n");
        where.append(" WHERE te.comportamento = ").append(TipoAgendamentoEnum.AVALIACAO_FISICA.ordinal());
        where.append(" AND ag.status = ").append(StatusAgendamentoEnum.EXECUTADO.ordinal());
        where.append(" AND ag.inicio BETWEEN '");
        where.append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd"));
        where.append(" 00:00:00' AND '");
        where.append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd"));
        where.append(" 23:59:59'  AND ag.cliente_codigo = obj.codigo) ");
        where.append("  OR EXISTS( ");
        where.append("  SELECT av.cliente_codigo FROM avaliacaofisica  av ");
        where.append("  where  av.cliente_codigo = obj.codigo and dataavaliacao between '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd"));
        where.append(" 00:00:00' and '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd"));
        where.append(" 23:59:59')) ");

        List<Integer> lista = clienteSinteticoDao.listOfObjects(key, query.toString() + where.toString());
        if (semAvaliacao) {
            addIndicador(listaItems, IndicadorDashboardEnum.SEM_AVALIACAO,
                    configIndicador(configs, IndicadorDashboardEnum.SEM_AVALIACAO),
                    new Double(lista.size()));
        } else {
            addIndicador(listaItems, IndicadorDashboardEnum.COM_AVALIACAO_FISICA,
                    configIndicador(configs, IndicadorDashboardEnum.COM_AVALIACAO_FISICA),
                    new Double(lista.size()));
        }
    }

    private void processarDadosZW(ProfessorSintetico professorSintetico,
                                  final List<ProfessorRankingIndicador> listaItems,
                                  final List<ConfiguracaoRankingProfessores> configs,
                                  Date inicio,
                                  Date fim, Integer empresa, String key) throws Exception {
        if(!contemIndicador(configs,
                IndicadorDashboardEnum.RENOVARAM,
                IndicadorDashboardEnum.NAO_RENOVARAM,
                IndicadorDashboardEnum.NOVOS_CARTEIRA,
                IndicadorDashboardEnum.TROCARAM_CARTEIRA,
                IndicadorDashboardEnum.ALUNOS_A_VENCER,
                IndicadorDashboardEnum.BI_TEMPO_CARTEIRA,
                IndicadorDashboardEnum.NOVOS_CARTEIRA_NOVOS,
                IndicadorDashboardEnum.NOVOS_CARTEIRA_TROCARAM,
                IndicadorDashboardEnum.ACESSOS,
                IndicadorDashboardEnum.PERCENTUAL_RENOVACAO_CARTEIRA
        )){
            return;
        }
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);

        DadosBITreinoJSON dadosZW;
        String modo = Aplicacao.getProp(Aplicacao.modoConsultaDashBI);
        if (modo.equals("consultaBD")) {
            dadosZW = obterDadosBIRanking(key, professorSintetico.getCodigoColaborador(), empresa, inicio, fim);
        } else {
            dadosZW = integracaoWS.obterDadosBIRanking(url, key, professorSintetico.getCodigoColaborador(), empresa, inicio, fim);
        }

        addIndicador(listaItems, IndicadorDashboardEnum.RENOVARAM,
                configIndicador(configs, IndicadorDashboardEnum.RENOVARAM),
                new Double(dadosZW.getRenovados()));
        addIndicador(listaItems, IndicadorDashboardEnum.NAO_RENOVARAM,
                configIndicador(configs, IndicadorDashboardEnum.NAO_RENOVARAM),
                new Double(dadosZW.getNaoRenovados()));
        addIndicador(listaItems, IndicadorDashboardEnum.NOVOS_CARTEIRA,
                configIndicador(configs, IndicadorDashboardEnum.NOVOS_CARTEIRA),
                new Double(dadosZW.getNovosNaCarteira()));
        addIndicador(listaItems, IndicadorDashboardEnum.TROCARAM_CARTEIRA,
                configIndicador(configs, IndicadorDashboardEnum.TROCARAM_CARTEIRA),
                new Double(dadosZW.getTrocasCarteira()));
        addIndicador(listaItems, IndicadorDashboardEnum.ALUNOS_A_VENCER,
                configIndicador(configs, IndicadorDashboardEnum.ALUNOS_A_VENCER),
                new Double(dadosZW.getaVencer()));
        addIndicador(listaItems, IndicadorDashboardEnum.BI_TEMPO_CARTEIRA,
                configIndicador(configs, IndicadorDashboardEnum.BI_TEMPO_CARTEIRA),
                new Double(dadosZW.getMediaPermanenciaCarteira() / 30.0));
        addIndicador(listaItems, IndicadorDashboardEnum.NOVOS_CARTEIRA_NOVOS,
                configIndicador(configs, IndicadorDashboardEnum.NOVOS_CARTEIRA_NOVOS),
                new Double(dadosZW.getNovosNaCarteiraNovos()));
        addIndicador(listaItems, IndicadorDashboardEnum.NOVOS_CARTEIRA_TROCARAM,
                configIndicador(configs, IndicadorDashboardEnum.NOVOS_CARTEIRA_TROCARAM),
                new Double(dadosZW.getNovosNaCarteiraTrocaram()));
        addIndicador(listaItems, IndicadorDashboardEnum.ACESSOS,
                configIndicador(configs, IndicadorDashboardEnum.ACESSOS),
                new Double(dadosZW.getAlunosAcessaram()));
        addIndicador(listaItems, IndicadorDashboardEnum.PERCENTUAL_RENOVACAO_CARTEIRA,
                configIndicador(configs, IndicadorDashboardEnum.PERCENTUAL_RENOVACAO_CARTEIRA),
                dadosZW.getRenovados() + dadosZW.getNaoRenovados() == 0
                        ? 0.0
                        : (dadosZW.getRenovados() * 100.0) / (dadosZW.getRenovados() + dadosZW.getNaoRenovados()));
    }

    private DadosBITreinoJSON obterDadosBIRanking(final String key,
                                                  final Integer professor,
                                                  final Integer codEmpresaZW,
                                                  Date inicio, Date fim) throws Exception {
        try {
            DashboardBITreinoModoBDServiceImpl dashboardBITreinoModoBDService;
            try (Connection conZW = conexaoZWService.conexaoZw(key)) {
                dashboardBITreinoModoBDService = new DashboardBITreinoModoBDServiceImpl(conZW, key);
                String dadosBITreino = dashboardBITreinoModoBDService.consultarDadosBITreino(
                        key,
                        Uteis.getData(inicio),
                        Uteis.getData(fim),
                        Uteis.getData(inicio),
                        Uteis.getData(fim),
                        professor,
                        codEmpresaZW);
                return JSONMapper.getObject(new JSONObject(dadosBITreino), DadosBITreinoJSON.class);
            }

        } catch (Exception e) {
            Uteis.logar(e, IntegracaoCadastrosWSConsumer.class);
            return new DadosBITreinoJSON();
        }
    }

    public void processarTreinosEmDia(final String key, ProfessorSintetico professorSintetico,
                                      final List<ProfessorRankingIndicador> listaItems,
                                      final List<ConfiguracaoRankingProfessores> configs,
                                      Date inicio,
                                      Date fim, Integer empresazw, IndicadorDashboardEnum indicador) throws Exception {
        if(contemIndicador(configs, IndicadorDashboardEnum.EM_DIA, IndicadorDashboardEnum.VENCIDOS, IndicadorDashboardEnum.PERC_TREINO_VENCIDOS, IndicadorDashboardEnum.PERC_TREINO_EM_DIA)){
            StringBuilder where = new StringBuilder();
            where.append(" SELECT DISTINCT obj.cliente_codigo, max(obj.dataterminoprevisto) FROM ProgramaTreino obj ");
            where.append(" INNER JOIN ClienteSintetico cli ON cli.codigo = obj.cliente_codigo ");
            where.append(" INNER JOIN professorsintetico ps on ps.codigo = cli.professorsintetico_codigo  \n");
            where.append(" WHERE cli.empresa = ").append(empresazw);
            where.append(" AND cli.situacao = 'AT' ");

            if (professorSintetico != null) {
                where.append(" and ps.codigo = ").append(professorSintetico.getCodigo());
            }
            StringBuilder whereEmDia = new StringBuilder();
            whereEmDia.append(" AND obj.datainicio <= '" ).append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd")).append("' ");
            whereEmDia.append(" AND obj.dataTerminoPrevisto >= '");
            whereEmDia.append(Uteis.getDataAplicandoFormatacao(Calendario.maior(fim, Calendario.hoje()) ? Calendario.hoje() : fim, "yyyy-MM-dd"));
            whereEmDia.append("'");

            StringBuilder whereNaoEmDia = new StringBuilder();
            whereNaoEmDia.append(" AND obj.dataTerminoPrevisto < '");
            whereNaoEmDia.append(Uteis.getDataAplicandoFormatacao(Calendario.maior(fim, Calendario.hoje()) ? Calendario.hoje() : fim, "yyyy-MM-dd"));
            whereNaoEmDia.append("'");
            whereNaoEmDia.append(" AND NOT EXISTS(SELECT proN.codigo FROM ProgramaTreino proN ");
            whereNaoEmDia.append(" WHERE proN.cliente_codigo = obj.cliente_codigo  AND ");
            whereNaoEmDia.append(" proN.dataTerminoPrevisto > obj.dataTerminoPrevisto)");

            StringBuilder group = new StringBuilder();
            group.append(" group by obj.cliente_codigo ");
            Double valor = 0.0;
            switch (indicador){
                case PERC_TREINO_EM_DIA:
                    StringBuilder whereNaoEmdia = new StringBuilder(where);
                    List<Integer> listaEmDia = programaTreinoDao.listOfObjects(key, where.append(whereEmDia).append(group).toString());
                    List<Integer> listaNaoEmdia = programaTreinoDao.listOfObjects(key, whereNaoEmdia.append(whereNaoEmDia).append(group).toString());
                    valor = (listaEmDia.size() + listaNaoEmdia.size()) == 0
                            ? 0.0
                            : (listaEmDia.size() * 100.0) / (listaEmDia.size() + listaNaoEmdia.size());
                    break;
                case EM_DIA:
                    valor = new Double(programaTreinoDao.listOfObjects(key, where.append(whereEmDia).append(group).toString()).size());
                    break;
                case VENCIDOS:
                    valor = new Double(programaTreinoDao.listOfObjects(key, where.append(whereNaoEmDia).append(group).toString()).size());
                    break;
                case PERC_TREINO_VENCIDOS:
                    valor = new Double(programaTreinoDao.listOfObjects(key, where.append(whereNaoEmDia).append(group).toString()).size());
                    break;
            }
            addIndicador(listaItems, indicador,
                    configIndicador(configs, indicador),
                    valor);
        }
    }


    public void processarProgramasTreinos(ProfessorSintetico professorSintetico,
                                          final List<ProfessorRankingIndicador> listaItems,
                                          final List<ConfiguracaoRankingProfessores> configs,
                                          Date inicio,
                                          Date fim, Integer empresazw, String key,
                                          boolean treinoIndependente) throws Exception {

        if(contemIndicador(configs, IndicadorDashboardEnum.TREINOS_A_VENCER)){
            StringBuilder query = new StringBuilder();
            query.append(" SELECT distinct obj.cliente_codigo FROM ProgramaTreino obj  \n");
            query.append(" INNER JOIN ClienteSintetico cli ON cli.codigo = obj.cliente_codigo  \n");
            query.append(" INNER JOIN professorsintetico ps on ps.codigo = cli.professorsintetico_codigo  \n");
            query.append(" WHERE obj.dataTerminoPrevisto >= '");
            query.append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
            query.append(" AND obj.dataTerminoPrevisto <= '");
            ConfiguracaoSistema diasAntesVencimento = configuracaoSistemaService.consultarPorTipo(key, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
            query.append(Uteis.getDataAplicandoFormatacao(Uteis.somarDias(Calendario.hoje(), diasAntesVencimento.getValorAsInteger()), "yyyy-MM-dd "));
            query.append("' ");
            query.append(" AND NOT EXISTS(SELECT proN.codigo FROM ProgramaTreino proN ");
            query.append(" WHERE proN.cliente_codigo = obj.cliente_codigo  AND ");
            query.append(" proN.dataTerminoPrevisto > obj.dataTerminoPrevisto)");
            query.append(" AND cli.empresa = ").append(empresazw);
            if(!treinoIndependente){
                query.append(" AND cli.situacao = 'AT' ");
            }
            if (professorSintetico != null) {
                query.append(" AND ps.codigo = ").append(professorSintetico.getCodigo());
            }
            List<Integer> lista = clienteSinteticoDao.listOfObjects(key, query.toString());
            addIndicador(listaItems, IndicadorDashboardEnum.TREINOS_A_VENCER,
                    configIndicador(configs, IndicadorDashboardEnum.TREINOS_A_VENCER),
                    new Double(lista.size()));
        }


    }

    public void processarAlunosTreinos(final String key,
                                       ProfessorSintetico professorSintetico,
                                       final List<ProfessorRankingIndicador> listaItems,
                                       final List<ConfiguracaoRankingProfessores> configs,
                                       Date inicio,
                                       Date fim,
                                       Integer codEmpresaZW,
                                       boolean semTreino) throws Exception {
        if(!contemIndicador(configs, IndicadorDashboardEnum.ATIVOS_SEM_TREINO, IndicadorDashboardEnum.ATIVOS_COM_TREINO)){
            return;
        }

        String sql = "SELECT obj.codigo FROM ClienteSintetico obj\n" +
                "INNER JOIN professorsintetico ps on ps.codigo = obj.professorsintetico_codigo";
        StringBuilder query = new StringBuilder();
        query.append(" WHERE obj.empresa = ").append(codEmpresaZW);
        //informações da carteira considera apenas alunos ativos
        query.append(" AND obj.situacao = 'AT' ");

        if (professorSintetico != null) {
            query.append(" AND ps.codigo = ").append(professorSintetico.getCodigo());
        }
        if (semTreino) {
            query.append(" and NOT EXISTS (SELECT cliente.codigo FROM ProgramaTreino p inner join ClienteSintetico cliente\n")
                    .append("    on p.cliente_codigo = cliente.codigo\n")
                    .append("  WHERE obj.codigo = cliente.codigo)");
        } else {
            query.append(" and EXISTS (SELECT cliente.codigo FROM ProgramaTreino p inner join ClienteSintetico cliente\n")
                    .append("    on p.cliente_codigo = cliente.codigo\n" )
                    .append("  WHERE obj.codigo = cliente.codigo)");
        }

        List<Integer> todos = clienteSinteticoDao.listOfObjects(key, sql + query);
        if (semTreino) {
            addIndicador(listaItems, IndicadorDashboardEnum.ATIVOS_SEM_TREINO,
                    configIndicador(configs, IndicadorDashboardEnum.ATIVOS_SEM_TREINO),
                    new Double(todos.size()));
        } else {
            addIndicador(listaItems, IndicadorDashboardEnum.ATIVOS_COM_TREINO,
                    configIndicador(configs, IndicadorDashboardEnum.ATIVOS_COM_TREINO),
                    new Double(todos.size()));
        }
    }

    public void processarAlunos(final String key,
                                final List<ProfessorRankingIndicador> listaItems,
                                final List<ConfiguracaoRankingProfessores> configs,
                                final ProfessorSintetico professorSintetico,
                                Integer empresaZW,
                                Date inicio,
                                Date fim,
                                Boolean treinoIndependente) throws Exception {
        if(!contemIndicador(configs, IndicadorDashboardEnum.TOTAL_ALUNOS, IndicadorDashboardEnum.ATIVOS,
                IndicadorDashboardEnum.INATIVOS,IndicadorDashboardEnum.VISITANTES)){
           return;
        }
        StringBuilder where = new StringBuilder();
        montarWhereAlunos(professorSintetico.getCodigo(), empresaZW, where, treinoIndependente);

        String sql = "SELECT obj.codigo FROM ClienteSintetico obj\n" +
                "left join professorsintetico ps on ps.codigo = obj.professorsintetico_codigo";

        List<Integer> todos = clienteSinteticoDao.listOfObjects(key, sql + where);
        addIndicador(listaItems, IndicadorDashboardEnum.TOTAL_ALUNOS,
                configIndicador(configs, IndicadorDashboardEnum.TOTAL_ALUNOS),
                new Double(todos.size()));

        List<Integer> ativos = new ArrayList<>();
        if(!treinoIndependente && Uteis.getMesData(inicio) != Uteis.getMesData(Calendario.hoje())){
            List<Integer> matriculas = obterAtivosNumaData(key, empresaZW, inicio, professorSintetico.getCodigoColaborador());
            if(!matriculas.isEmpty()){
                String matriculasFiltro = "";
                for(Integer mat : matriculas){
                    matriculasFiltro += ","+mat;
                }
                ativos = clienteSinteticoDao.listOfObjects(key, sql +
                        (" WHERE obj.matricula in ("+matriculasFiltro.replaceFirst(",", "")+") "));
            }
        } else {
            ativos = clienteSinteticoDao.listOfObjects(key, sql + where.toString() +
                    (treinoIndependente ? "AND obj.situacao = 'AT' " : " AND obj.situacao in ('AT','NO','AV')"));
        }
        addIndicador(listaItems, IndicadorDashboardEnum.ATIVOS,
                configIndicador(configs, IndicadorDashboardEnum.ATIVOS),
                new Double(ativos.size()));

        ConfiguracaoSistema somenteAlunoDesistente = configuracaoSistemaService.consultarPorTipo(key, ConfiguracoesEnum.SOMENTE_ALUNO_CONTRATO_DESISTENTE);
        String andInativos = somenteAlunoDesistente.getValorAsBoolean() ?
                " AND ( obj.situacao = 'IN' AND obj.situacaocontrato = 'DE')" :
                " AND ( obj.situacao = 'VI' OR obj.situacaocontrato in ('DE','VE','CA','TR') ) ";
        ConfiguracaoSistema inativosXdias = configuracaoSistemaService.consultarPorTipo(key, ConfiguracoesEnum.INATIVOS_A_X_DIAS);
        if (inativosXdias != null && inativosXdias.getValorAsInteger() != null && inativosXdias.getValorAsInteger() > 0) {
            Date dataLimite = Uteis.somarDias(Calendario.hoje(), -inativosXdias.getValorAsInteger());
            andInativos += " AND obj.datavigenciaateajustada IS NOT NULL AND obj.datavigenciaateajustada > '"
                    + Uteis.getDataAplicandoFormatacao(dataLimite, "yyyy-MM-dd") + "' ";
        }
        List<Integer> inativos = clienteSinteticoDao.listOfObjects(key, sql + where.toString() + (treinoIndependente ? " AND obj.situacao = 'IN' " : andInativos));
        addIndicador(listaItems, IndicadorDashboardEnum.INATIVOS,
                configIndicador(configs, IndicadorDashboardEnum.INATIVOS),
                new Double(inativos.size()));

        if (treinoIndependente) {
            List<Integer> visitantes = clienteSinteticoDao.listOfObjects(key, sql + where.toString() + "AND obj.situacao = 'VI' ");
            addIndicador(listaItems, IndicadorDashboardEnum.VISITANTES,
                    configIndicador(configs, IndicadorDashboardEnum.VISITANTES),
                    new Double(visitantes.size()));
        }
    }

    public void montarWhereAlunos(final Integer professor,
                                  final Integer codEmpresaZW,
                                  StringBuilder where,
                                  boolean treinoIndependente) {
        where.append(" WHERE obj.empresa = ").append(codEmpresaZW);
        where.append(" and obj.professorSintetico_codigo IS NOT NULL AND obj.professorSintetico_codigo > 0 ");
        if (!treinoIndependente) {
            where.append(" AND obj.situacaoContrato not in ('TV') ");
        }
        if (professor != null && professor > 0) {
            where.append(" and ps.codigo = ").append(professor);
        }
    }

    public List<Integer> obterAtivosNumaData(String chave, Integer empresa, Date data, Integer codigoColaborador) throws Exception {
        List<Integer> retorno = new ArrayList<>();
        JSONArray ocupacao = chamadaZW(chave,
                "/prest/treino/ativos-profesor-data",
                empresa, null, null, codigoColaborador, data.getTime(), null);
        if(ocupacao.length()>0){
            for(int i=0;i<ocupacao.length();i++){
                retorno.add(ocupacao.optInt(i));
            }
        }
        return retorno ;
    }

    public void processarTreinosRealizados(final String key,
                                           final List<ProfessorRankingIndicador> listaItems,
                                           final List<ConfiguracaoRankingProfessores> configs,
                                           final ProfessorSintetico professorSintetico,
                                           Integer empresaZW,
                                           Date inicio,
                                           Date fim) throws Exception {
        if(!contemIndicador(configs, IndicadorDashboardEnum.AVALIACOES,
                IndicadorDashboardEnum.ESTRELAS_1,
                IndicadorDashboardEnum.ESTRELAS_2,
                IndicadorDashboardEnum.ESTRELAS_3,
                IndicadorDashboardEnum.ESTRELAS_4,
                IndicadorDashboardEnum.ESTRELAS_5
                )){
            return;
        }
        List<TreinoRealizado> treinoRealizados = resultadosTreino(key, empresaZW, inicio, fim, professorSintetico.getCodigo());
        Integer somaAvaliacoes = 0;
        int nrAvaliacoes = 0;
        int estrelas1 = 0;
        int estrelas2 = 0;
        int estrelas3 = 0;
        int estrelas4 = 0;
        int estrelas5 = 0;

        for (TreinoRealizado treino : treinoRealizados) {
            if (treino.getNota() != null && !treino.getNota().isEmpty() && !treino.getNota().equals("0")) {
                try {
                    Integer nota = Integer.valueOf(treino.getNota());
                    nrAvaliacoes++;
                    somaAvaliacoes += nota;
                    switch (nota) {
                        case 1:
                            estrelas1++;
                            break;
                        case 2:
                            estrelas2++;
                            break;
                        case 3:
                            estrelas3++;
                            break;
                        case 4:
                            estrelas4++;
                            break;
                        case 5:
                            estrelas5++;
                            break;
                    }
                } catch (Exception e) {
                    Uteis.logar(e, GestaoServiceImpl.class);
                }
            }
        }
        addIndicador(listaItems, IndicadorDashboardEnum.AVALIACOES,
                    configIndicador(configs, IndicadorDashboardEnum.AVALIACOES),
                    new Double(nrAvaliacoes));
        addIndicador(listaItems, IndicadorDashboardEnum.ESTRELAS_1,
                configIndicador(configs, IndicadorDashboardEnum.ESTRELAS_1),
                new Double(estrelas1));
        addIndicador(listaItems, IndicadorDashboardEnum.ESTRELAS_2,
                configIndicador(configs, IndicadorDashboardEnum.ESTRELAS_2),
                new Double(estrelas2));
        addIndicador(listaItems, IndicadorDashboardEnum.ESTRELAS_3,
                configIndicador(configs, IndicadorDashboardEnum.ESTRELAS_3),
                new Double(estrelas3));
        addIndicador(listaItems, IndicadorDashboardEnum.ESTRELAS_4,
                configIndicador(configs, IndicadorDashboardEnum.ESTRELAS_4),
                new Double(estrelas4));
        addIndicador(listaItems, IndicadorDashboardEnum.ESTRELAS_5,
                configIndicador(configs, IndicadorDashboardEnum.ESTRELAS_5),
                new Double(estrelas5));

    }

    public void addIndicador(List<ProfessorRankingIndicador> itemRanking,
            IndicadorDashboardEnum indicador,
            ConfiguracaoRankingProfessores config,
            Double valor){
        if(config == null){
            return;
        }
        ProfessorRankingIndicador profIndicador = new ProfessorRankingIndicador();
        profIndicador.setIndicador(indicador);
        profIndicador.setPositivo(config.getPositivo());
        profIndicador.setMultiplicador(config.getPeso());
        profIndicador.setValor(valor);
        itemRanking.add(profIndicador);
    }

    public ConfiguracaoRankingProfessores configIndicador(List<ConfiguracaoRankingProfessores> configs, IndicadorDashboardEnum indicador) {
        for (ConfiguracaoRankingProfessores config : configs) {
            if (config.getIndicador().equals(indicador)) {
                return config;
            }
        }
        return null;
    }

    public boolean contemIndicador(List<ConfiguracaoRankingProfessores> configs, IndicadorDashboardEnum ... indicadores){
        for(IndicadorDashboardEnum indicador : indicadores){
            for(ConfiguracaoRankingProfessores config : configs){
                if(config.getIndicador().equals(indicador)){
                    return true;
                }
            }
        }
        return false;
    }

    private List<TreinoRealizado> resultadosTreino(String key, Integer empresaZW, Date inicio, Date fim, Integer professor) throws Exception{
        Map<String, Object> p = new HashMap<String, Object>();
        fim = Calendario.getDataComHora(fim, "23:59");
        StringBuilder sql = new StringBuilder();
        sql.append(" select tr.codigo, tr.nota, tr.datainicio, tr.origem from treinorealizado tr ");
        sql.append(" inner join clientesintetico cl on cl.codigo = tr.cliente_codigo ");
        sql.append(" WHERE cl.empresa = ").append(empresaZW);
        sql.append(" and dataInicio BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd HH:mm:ss"));
        sql.append("' AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd HH:mm:ss")).append("' ");
        sql.append(" and tr.professor_codigo notnull");
        if (professor != null && professor > 0) {
            sql.append(" and tr.professor_codigo = ").append(professor);
        }
        List<TreinoRealizado> resultadosTreino;
        try (ResultSet rs = treinoRealizadoDao.createStatement(key, sql.toString())) {
            resultadosTreino = new ArrayList();
            while (rs.next()) {
                TreinoRealizado tr = new TreinoRealizado();
                tr.setCodigo(rs.getInt("codigo"));
                tr.setNota(rs.getString("nota"));
                tr.setDataInicio(rs.getTimestamp("datainicio"));
                tr.setOrigem(OrigemExecucaoEnum.getFromId(rs.getInt("origem")));
                resultadosTreino.add(tr);
            }
        }
        return resultadosTreino;
    }

    private JSONArray chamadaZW(String ctx,
                                String endpoint, Integer empresa, Integer mes, Integer ano, Integer professor, Long data,
                                Map<String, String> requestParams
    ) throws Exception{
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        if(requestParams != null){
            for(String k : requestParams.keySet()){
                params.add(new BasicNameValuePair(k, requestParams.get(k)));
            }
        }
        if(mes != null){
            params.add(new BasicNameValuePair("mes", mes.toString()));
        }
        if(ano != null){
            params.add(new BasicNameValuePair("ano", ano.toString()));
        }
        if(data != null){
            params.add(new BasicNameValuePair("data", data.toString()));
        }
        if(professor != null){
            params.add(new BasicNameValuePair("professor", professor.toString()));
        }


        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONArray(body);
    }

    public List<Integer> obterCancelados(String chave, Integer empresa, Integer mes, Integer ano, Integer professor) throws Exception {
        List<Integer> retorno =  new ArrayList<>();
        JSONArray ocupacao = chamadaZW(chave,
                "/prest/rankingprofessores/listar-todos",
                empresa, mes, ano, professor, null, null);
        JSONObject objects = new JSONObject();
        if(ocupacao.length()>0){
            for(int i=0;i<ocupacao.length();i++){
                objects = ocupacao.optJSONObject(i);
                retorno.add(objects.getInt("codigoContrato"));
            }
        }
        return retorno ;
    }

    private GeracaoRankingProfessores rankingGerado(String ctx,
                                                    Date inicio,
                                                    Date fim,
                                                    Integer empresaId) throws Exception{
        try (ResultSet rs = geracaoDao.createStatement(ctx, "select codigo, fimProcessamento, empresa from geracaorankingprofessores where empresa = " + empresaId
                + " and inicio::date = '" + Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd") + "' "
                + " and fim::date = '" + Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd") + "' order by codigo desc")) {
            if (rs.next()) {
                if(rs.getTimestamp("fimProcessamento")!=null){
                    return new GeracaoRankingProfessores(rs.getInt("codigo"), rs.getTimestamp("fimProcessamento"), rs.getInt("empresa"));
                }else{
                    return new GeracaoRankingProfessores(rs.getInt("codigo"), new Date(), rs.getInt("empresa"));
                }
            }
        }
        return null;
    }

    private void checarProfessorNaoGeradoAinda(String ctx,
                                               GeracaoRankingProfessores geracao, List<Integer> professores) throws Exception{
        List<ConfiguracaoRankingProfessores> listConfig = null;
        boolean treinoIndependente = SuperControle.independente(ctx);
        boolean atualizar = false;
        for(Integer professor : professores){
            try (ResultSet rs = geracaoDao.createStatement(ctx, "select prk.codigo from professorranking prk\n" +
                    " inner join professorsintetico pr on prk.professor_codigo = pr.codigo \n" +
                    " where geracao_codigo = " + geracao.getCodigo() +
                    " and pr.codigocolaborador = " + professor)) {
                if (!rs.next()) {
                    atualizar = true;
                    if (listConfig == null) {
                        geracao = geracaoDao.findById(ctx, geracao.getCodigo());
                        listConfig = dashboardBIService.obterCfgRanking(ctx, null, geracao.getEmpresa(), null, true);
                    }
                    try (ResultSet rsProf = geracaoDao.createStatement(ctx, "select p.codigo from professorsintetico p " +
                            " where codigocolaborador  = " + professor)) {
                        if (rsProf.next()) {
                            ProfessorRanking professorRanking = processarIndicadoresProfessorRanking(ctx,
                                    geracao, rsProf.getInt("codigo"), professor,
                                    listConfig, treinoIndependente);
                            List<ProfessorRankingIndicador> indicadores = professorRanking.getIndicadores();
                            professorRanking = professorRankingDao.insert(ctx, professorRanking);
                            for (ProfessorRankingIndicador pri : indicadores) {
                                pri.setProfessorRanking(professorRanking);
                                professorRankingIndicadorDao.insert(ctx, pri);
                            }
                        }
                    }
                }
            }
        }
        if(atualizar){
            try (ResultSet rsProf = geracaoDao.createStatement(ctx, "select codigo, pontos from professorranking p \n" +
                    "where geracao_codigo = " + geracao.getCodigo() +
                    "\norder by pontos desc, codigo")) {
                int posicao = 1;
                while (rsProf.next()) {
                    geracaoDao.executeNativeSQL(ctx, "update professorranking set posicao = " + posicao +
                            " where codigo = " + rsProf.getInt("codigo"));
                    posicao++;
                }
            }
        }

    }

    private void povoarProfessoresDoRanking(String ctx,
                                            GeracaoRankingProfessores geracao,
                                            FiltroGestaoJSON filtroGestaoJSON,
                                            PaginadorDTO paginadorDTO) throws Exception {

        StringBuilder hql = new StringBuilder();
        hql.append(" where obj.geracao.codigo = :geracao");
        Map<String, Object> params = new HashMap<>();
        if(!UteisValidacao.emptyString(filtroGestaoJSON.getNome())){
            hql.append(" and unaccent(lower(obj.professor.nome)) LIKE unaccent('%").append(filtroGestaoJSON.getNome().toLowerCase()).append("%') ");
        }
        params.put("geracao", geracao.getCodigo());
        String situacao = UteisValidacao.emptyList(filtroGestaoJSON.getSituacao()) ? "ativo" : "";
        //caso esteja vazio o filtro de situação, ou o usuario marcou as duas opções, não precisa ser filtrado
        //somente no caso dele ter escolhido apenas uma
        if (filtroGestaoJSON.getSituacao() != null
                && filtroGestaoJSON.getSituacao().size() == 1) {
            situacao = filtroGestaoJSON.getSituacao().get(0);
        }
        String tipos = "";
        if (UteisValidacao.emptyList(filtroGestaoJSON.getPersonal())) {
            tipos = "'TW','PI','PE'";
        } else {
            for (String sit : filtroGestaoJSON.getPersonal()) {
                switch (sit){
                    case "naoincluir":
                        tipos += ",'TW'";
                        break;
                    case "interno":
                        tipos += ",'PI'";
                        break;
                    case "externo":
                        tipos += ",'PE'";
                        break;
                }
            }
            tipos = tipos.replaceFirst(",", "");
        }
        List<Integer> professores = codigosProfessores(ctx, geracao.getEmpresa(),
                situacao,
                tipos,
                filtroGestaoJSON.getCargaHoraria(),
                filtroGestaoJSON.getModalidadeIds());
        checarProfessorNaoGeradoAinda(ctx, geracao, professores);
        String codigosProfessores = Uteis.concatenarListaVirgula(professores);

        hql.append(" and obj.professor.codigoColaborador in (");
        hql.append(codigosProfessores.isEmpty() ? "0" : codigosProfessores).append(") \n");

        if (paginadorDTO == null) {
            List<ProfessorRanking> professorRankings = professorRankingDao.findByParam(ctx, hql, params);
            geracao.setProfessores(professorRankings);
        } else {
            int maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue() * maxResults;
            List<ProfessorRanking> professorRankings = professorRankingDao.findByParam(ctx,
                            "select obj FROM ProfessorRanking obj "
                                  + hql
                                  + (UteisValidacao.emptyString(paginadorDTO.getSQLOrderBy()) ?
                                        " ORDER BY posicao ASC, professor.nome ASC" :
                                        (paginadorDTO.getSQLOrderBy().replace("total", "pontos")
                                                .replace("nome", "obj.professor.nome")))
                                    ,
                            params,
                            maxResults, indiceInicial);
            Number count = professorRankingDao.countWithParam(ctx, "codigo", hql, params);
            paginadorDTO.setQuantidadeTotalElementos(count.longValue());
            geracao.setProfessores(professorRankings);
        }
    }

    private List<ProfessorRankingIndicador> indicadoresProfessor(String ctx,
                                                                 GeracaoRankingProfessores geracao,
                                                                 Integer codigoColaborador) throws Exception{
        StringBuilder hql = new StringBuilder();
        hql.append(" select obj FROM ProfessorRankingIndicador obj ");
        hql.append(" where obj.professorRanking.geracao.codigo = :geracao and obj.professorRanking.professor.codigoColaborador = :colaborador");
        Map<String, Object> params = new HashMap<>();
        params.put("geracao", geracao.getCodigo());
        params.put("colaborador", codigoColaborador);
        return professorRankingIndicadorDao.findByParam(ctx, hql.toString(), params);
    }

    public List<RankingProfessoresResponseDTO> podium(HttpServletRequest request,
                                                      Date inicio,
                                                      Date fim,
                                                      Integer empresaId,
                                                      FiltroGestaoJSON filtroGestaoJSON,
                                                      Boolean treinoindependente) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            inicio = Calendario.getDataComHora(inicio, "00:00");
            fim = Calendario.getDataComHora(fim, "23:59");

            PaginadorDTO paginadorDTO = new PaginadorDTO();
            paginadorDTO.setPage(0L);

            GeracaoRankingProfessores geracaoRankingProfessores = rankingGeradoOuAtualizado(ctx, inicio, fim, empresaId, treinoindependente, filtroGestaoJSON, paginadorDTO, false);

            List<RankingProfessoresResponseDTO> listRankingRetorno = new ArrayList<>();
            for (ProfessorRanking ranking : geracaoRankingProfessores.getProfessores()) {
                RankingProfessoresResponseDTO rankingProfessor = new RankingProfessoresResponseDTO(ranking);
                rankingProfessor.setColaborador(ranking.getProfessor().getCodigoColaborador());
                rankingProfessor.setPosicao(ranking.getPosicao() + "º lugar");
                String fotoKey = obterFotoKeyColaboradorZW(ctx, ranking.getProfessor().getCodigoColaborador());
                rankingProfessor.setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(fotoKey));
                listRankingRetorno.add(rankingProfessor);
            }
            return listRankingRetorno;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public DetalhesProfessorRankingDTO detalhesProfessor(HttpServletRequest request,
                                                  Date inicio,
                                                  Date fim,
                                                  Integer empresaId,
                                                  Integer codigoColaborador,
                                                  FiltroGestaoJSON filtroGestaoJSON, Boolean independente) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            inicio = Calendario.getDataComHora(inicio, "00:00");
            fim = Calendario.getDataComHora(fim, "23:59");
            DetalhesProfessorRankingDTO detalhes = new DetalhesProfessorRankingDTO();
            JSONObject jsonObject = detalhesZW(ctx, codigoColaborador);
            detalhes.setDataCadastro(jsonObject.getString("cadastro"));
            GeracaoRankingProfessores geracaoRankingProfessores = rankingGerado(ctx, inicio, fim, empresaId) ;
            if(geracaoRankingProfessores == null){
                geracaoRankingProfessores = gerarRankingNoBanco(ctx,
                        inicio,
                        fim, independente,
                        sessaoService.getUsuarioAtual().getId(),
                        empresaId);
            }
            List<ProfessorRankingIndicador> indicadors = indicadoresProfessor(ctx, geracaoRankingProfessores, codigoColaborador);
            for(ProfessorRankingIndicador ind : indicadors){
                detalhes.getContent().add(new InfoProfessorRankingDTO(ind));
                switch (ind.getIndicador().getAgrupamento()){
                    case AGENDA:
                        detalhes.getAgenda().add(new InfoProfessorRankingDTO(ind));
                        break;
                    case TREINO:
                        detalhes.getTreino().add(new InfoProfessorRankingDTO(ind));
                        break;
                    case ALUNOS:
                        detalhes.getAlunos().add(new InfoProfessorRankingDTO(ind));
                        break;
                }
            }
            detalhes.setContent(Ordenacao.ordenarLista(detalhes.getContent(), "pontos"));
            List<EntradasMes> entradas = new ArrayList<>();
            JSONArray jsonArray = jsonObject.getJSONArray("entradas");
            for(int i = 0; i < jsonArray.length(); i++){
                JSONObject object = jsonArray.getJSONObject(i);
                EntradasMes entrada = new EntradasMes();
                entrada.tipo= "EN";
                entrada.mes = Uteis.getDate("01/"+object.get("mes"));
                entrada.entradas = object.getInt("cont");
                entradas.add(entrada);
            }

            JSONArray jsonArraySaidas = jsonObject.getJSONArray("saidas");
            for(int i = 0; i < jsonArraySaidas.length(); i++){
                JSONObject object = jsonArraySaidas.getJSONObject(i);
                EntradasMes saida = new EntradasMes();
                saida.tipo= "SD";
                saida.mes = Uteis.getDate("01/"+object.get("mes"));
                saida.entradas = object.getInt("cont");
                entradas.add(saida);
            }

            entradas = Ordenacao.ordenarListaReverse(entradas, "mes");
            for(EntradasMes e : entradas){
                if (e.tipo.equals("SD")) {
                    detalhes.getLinha().add(new InfoProfessorRankingDTO(
                            Uteis.getDataAplicandoFormatacao(e.mes, "MM/yyyy"),
                            e.entradas + " saida de alunos na carteira em " +
                                    Uteis.getDataAplicandoFormatacao(e.mes, "MMMM 'de' yyyy")
                    ));
                } else {
                    detalhes.getLinha().add(new InfoProfessorRankingDTO(
                            Uteis.getDataAplicandoFormatacao(e.mes, "MM/yyyy"),
                            e.entradas + " novos alunos na carteira em " +
                                    Uteis.getDataAplicandoFormatacao(e.mes, "MMMM 'de' yyyy")
                    ));
                }
            }
            detalhes.getLinha().add(new InfoProfessorRankingDTO(detalhes.getDataCadastro(), "Início na academia"));
            return detalhes;
        }catch (Exception e){
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_PROFESSORES);
        }

    }

    public List<InfoProfessorRankingDTO>  detalhesCompartilharProfessor(HttpServletRequest request,
                                                         Date inicio,
                                                         Date fim,
                                                         Integer empresaId,
                                                         Integer codigoColaborador,
                                                         FiltroGestaoJSON filtroGestaoJSON, Boolean independente) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            inicio = Calendario.getDataComHora(inicio, "00:00");
            fim = Calendario.getDataComHora(fim, "23:59");
            List<InfoProfessorRankingDTO> detalhes = new ArrayList<>();
            GeracaoRankingProfessores geracaoRankingProfessores = rankingGerado(ctx, inicio, fim, empresaId) ;
            if(geracaoRankingProfessores == null){
                geracaoRankingProfessores = gerarRankingNoBanco(ctx,
                        inicio,
                        fim, independente,
                        sessaoService.getUsuarioAtual().getId(),
                        empresaId);
            }
            List<ProfessorRankingIndicador> indicadors = indicadoresProfessor(ctx, geracaoRankingProfessores, codigoColaborador);
            for(ProfessorRankingIndicador ind : indicadors){
                InfoProfessorRankingDTO infoProfessorRanking = new InfoProfessorRankingDTO(ind);
                detalhes.add(infoProfessorRanking);
            }
            return detalhes;
        }catch (Exception e){
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_PROFESSORES);
        }

    }

    public class EntradasMes{
        String tipo;
        Date mes;
        Integer entradas;

        public String getTipo() {
            return tipo;
        }
        public Date getMes() { return mes; }

        public Integer getEntradas() {
            return entradas;
        }
    }

    private JSONObject detalhesZW(String ctx, Integer professor) throws Exception{
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + "/prest/rankingprofessores/detalhes");

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("colaborador", professor.toString()));
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    private JSONObject detalhesProfessorZW(String ctx, Integer professor) throws Exception{
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + "/prest/rankingprofessores/detalhes-professor");

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("colaborador", professor.toString()));
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    @Override
    public List<RankingProfessoresResponseDTO> carregarRankingProfessores(HttpServletRequest request,
                                                                          PaginadorDTO paginadorDTO,
                                                                          Date inicio,
                                                                          Date fim,
                                                                          Integer empresaId,
                                                                          FiltroGestaoJSON filtroGestaoJSON,
                                                                          String sort,
                                                                          Boolean treinoindependente) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            inicio = Calendario.getDataComHora(inicio, "00:00");
            fim = Calendario.getDataComHora(fim, "23:59");

            GeracaoRankingProfessores geracaoRankingProfessores = rankingGeradoOuAtualizado(ctx, inicio, fim, empresaId, treinoindependente, filtroGestaoJSON, paginadorDTO, true);

            FiltrosGestaoTO filtros = professorSinteticoService.popularFiltroGestao(request, empresaId, filtroGestaoJSON);

            List<RankingProfessoresResponseDTO> listRankingRetorno = new ArrayList<>();
            Integer cont = 1;
            if (paginadorDTO.getPage() > 0) {
                Long mult = paginadorDTO.getSize() * paginadorDTO.getPage();
                cont = Integer.parseInt(mult.toString()) + cont;
            }
            boolean usarPosicao = UteisValidacao.emptyList(filtroGestaoJSON.getCargaHoraria())
                    && filtroGestaoJSON.getPersonal().size() == 1 && filtroGestaoJSON.getPersonal().get(0).equals("naoincluir")
                    && filtroGestaoJSON.getSituacao().size() == 1 && filtroGestaoJSON.getSituacao().get(0).equals("ativo")
                    && UteisValidacao.emptyList(filtroGestaoJSON.getModalidadeIds());

            for (ProfessorRanking ranking : geracaoRankingProfessores.getProfessores()) {
                if(UteisValidacao.emptyList(ranking.getIndicadores())){
                    try {
                        ranking.setIndicadores(indicadoresProfessor(ctx, geracaoRankingProfessores, ranking.getProfessor().getCodigoColaborador()));
                    }catch (Exception e){
                        Uteis.logar(e, RankingProfessoresServiceImpl.class);
                    }
                }
                RankingProfessoresResponseDTO rankingProfessor = new RankingProfessoresResponseDTO(ranking);
                rankingProfessor.setColaborador(ranking.getProfessor().getCodigoColaborador());
                if (usarPosicao) {
                    rankingProfessor.setPosicao(ranking.getPosicao() + "º lugar");
                } else {
                    rankingProfessor.setPosicao(cont + "º lugar");
                    cont++;
                }
                String fotoKey = obterFotoKeyColaboradorZW(ctx, ranking.getProfessor().getCodigoColaborador());
                rankingProfessor.setUrlFoto(Uteis.getPaintFotoDaNuvem(fotoKey));
                rankingProfessor.setPosicaoNumero(ranking.getPosicao());
                rankingProfessor.setGeracao(Uteis.getDataAplicandoFormatacao(geracaoRankingProfessores.getFimProcessamento(), "dd/MM/yyyy HH:mm"));
                rankingProfessor.setDesempenho(desempenho(ctx, inicio, ranking.getProfessor().getCodigoColaborador(), ranking.getPosicao()));
                listRankingRetorno.add(rankingProfessor);
            }

            return listRankingRetorno;
        } catch (Exception e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_PROFESSORES);
        }
    }

    private String obterFotoKeyColaboradorZW(String ctx, Integer codigoColaborador) {
        String fotoKey = "";
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                StringBuilder sql = new StringBuilder();
                sql.append("select p.fotokey \n");
                sql.append("from pessoa p \n");
                sql.append("inner join colaborador c on c.pessoa = p.codigo \n");
                sql.append("where c.codigo = ").append(codigoColaborador);
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                    if (rs.next()) {
                        fotoKey = rs.getString("fotokey");
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao tentar obter fotokey do colaborador ZW: " + ex.getMessage());
        }
        return fotoKey;
    }

    public HashMap<String, String> carregarSortMap(String sort) {
        if (StringUtils.isBlank(sort)) {
            return null;
        }

        final String[] ordenacoes = sort.split(SEPARADOR_ORDENACOES);
        if (ordenacoes.length < 1) {
            return null;
        }

        HashMap<String, String> sortMap= new HashMap<>();
        for (String ordenacao : ordenacoes) {
            final String[] ordem = ordenacao.split(SEPARADOR_ORDENACAO);
            if (ordem.length < 2) {
                continue;
            }

            sortMap.put(ordem[INDEX_CAMPO_ORDENAR], ordem[INDEX_DIRECAO_ORDENACAO]);
        }
        return sortMap;
    }

    public String desempenho(String ctx, Date inicio, Integer colaborador, Integer posicaoAtual) throws Exception{
        try (ResultSet rs = professorRankingDao.createStatement(ctx, "select p.posicao from professorranking p " +
                " inner join professorsintetico ps on ps.codigo = p.professor_codigo " +
                " inner join GeracaoRankingProfessores g on g.codigo = p.geracao_codigo " +
                " where g.fim < '" + Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd") +
                "' and ps.codigocolaborador = " + colaborador +
                " order by fim desc")) {
            if (rs.next() && posicaoAtual != null) {
                return rs.getInt("posicao") < posicaoAtual ? "up" : "down";
            }
        }
        return "draw";
    }

    public void conferir(Date inicio, Date fim, Integer empresaId, FiltroGestaoJSON filtroGestaoJSON, Boolean treinoindependente) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            inicio = Calendario.getDataComHora(inicio, "00:00");
            fim = Calendario.getDataComHora(fim, "23:59");
            GeracaoRankingProfessores geracaoRankingProfessores = rankingGerado(ctx, inicio, fim, empresaId) ;
            if(geracaoRankingProfessores == null){
                gerarRankingNoBanco(ctx,
                        inicio,
                        fim, treinoindependente,
                        sessaoService.getUsuarioAtual().getId(),
                        empresaId);
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    private GeracaoRankingProfessores rankingGeradoOuAtualizado(String ctx, Date inicio, Date fim, Integer empresaId, Boolean treinoindependente, FiltroGestaoJSON filtroGestaoJSON, PaginadorDTO paginadorDTO, Boolean aplicarPaginacao) throws Exception {
        GeracaoRankingProfessores geracaoRankingProfessores = rankingGerado(ctx, inicio, fim, empresaId);
        if (geracaoRankingProfessores != null) {
            if (precisaAtualizarRanking(geracaoRankingProfessores, fim)) {
                atualizarRanking(geracaoRankingProfessores, ctx, inicio, fim, empresaId, treinoindependente, filtroGestaoJSON, paginadorDTO);
            }
        } else {
            geracaoRankingProfessores = gerarRankingNoBanco(ctx, inicio, fim, treinoindependente, sessaoService.getUsuarioAtual().getId(), empresaId);
        }
        if(!aplicarPaginacao) {
            paginadorDTO.setSize(3L);
        }
        povoarProfessoresDoRanking(ctx, geracaoRankingProfessores, filtroGestaoJSON, paginadorDTO);

        return geracaoRankingProfessores;
    }

    private boolean precisaAtualizarRanking(GeracaoRankingProfessores ranking, Date fimAtual) {
        return fimAtual.after(ranking.getFimProcessamento());
    }

    private void atualizarRanking(GeracaoRankingProfessores ranking, String ctx, Date inicio, Date fim, Integer empresaId, Boolean treinoindependente, FiltroGestaoJSON filtroGestaoJSON, PaginadorDTO paginadorDTO) throws Exception {
        List<ConfiguracaoRankingProfessores> listConfig = dashboardBIService.obterCfgRanking(ctx, null, empresaId, null, true);
        povoarProfessoresDoRanking(ctx, ranking, filtroGestaoJSON, paginadorDTO);
        for (ProfessorRanking professorRanking : ranking.getProfessores()) {
            ranking.setInicio(inicio);
            ranking.setFim(fim);
            ProfessorRanking prAtualizado = processarIndicadoresProfessorRanking(ctx, ranking, professorRanking.getProfessor().getCodigo(),
                    professorRanking.getProfessor().getCodigoColaborador(),
                    listConfig, treinoindependente);
            List<ProfessorRankingIndicador> indicadoresExistentes = professorRankingIndicadorDao.obterIndicadoresPorProfessorRanking(ctx, professorRanking.getCodigo());
            for (ProfessorRankingIndicador priNovo : prAtualizado.getIndicadores()) {
                Optional<ProfessorRankingIndicador> indicadorExistenteOpt = indicadoresExistentes.stream()
                        .filter(ind -> ind.getIndicador().ordinal() == priNovo.getIndicador().ordinal())
                        .findFirst();
                if (indicadorExistenteOpt.isPresent()) {
                    ProfessorRankingIndicador indicadorExistente = indicadorExistenteOpt.get();
                    indicadorExistente.setValor(priNovo.getValor());
                    indicadorExistente.setMultiplicador(priNovo.getMultiplicador());
                    professorRankingIndicadorDao.update(ctx, indicadorExistente);
                } else {
                    priNovo.setProfessorRanking(professorRanking);
                    professorRankingIndicadorDao.insert(ctx, priNovo);
                }
            }
            professorRanking.setPontos(prAtualizado.getPontos());
            professorRankingDao.update(ctx, professorRanking);
        }
        List<ProfessorRanking> professoresRanking = ranking.getProfessores();
        professoresRanking = Ordenacao.ordenarListaReverse(professoresRanking, "pontos");

        int posicao = 1;
        for (ProfessorRanking professorRanking : professoresRanking) {
            professorRanking.setPosicao(posicao++);
            professorRankingDao.update(ctx, professorRanking);
        }
        ranking.setFimProcessamento(Calendario.hoje());
        geracaoDao.update(ctx, ranking);
    }
}

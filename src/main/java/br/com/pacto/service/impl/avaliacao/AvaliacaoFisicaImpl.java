package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.anamnese.AgrupamentoAvaliacaoIntegradaEnum;
import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.anamnese.AnamneseTO;
import br.com.pacto.bean.anamnese.Movimento3D;
import br.com.pacto.bean.anamnese.OpcaoPergunta;
import br.com.pacto.bean.anamnese.Pergunta;
import br.com.pacto.bean.anamnese.PerguntaAnamnese;
import br.com.pacto.bean.anamnese.PerguntaResponseTO;
import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.bean.anamnese.RespostaClienteTO;
import br.com.pacto.bean.anamnese.ResultadoMovimentoEnum;
import br.com.pacto.bean.anamnese.ResultadoVidaEnum;
import br.com.pacto.bean.anamnese.TipoMovimento3DEnum;
import br.com.pacto.bean.avaliacao.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoPerimetro;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.IdiomaBancoEnum;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.aluno.ClienteSinteticoDTO;
import br.com.pacto.controller.json.avaliacao.AvaliacaoFisicaJSON;
import br.com.pacto.controller.json.avaliacao.AvaliacaoIntegradaJSON;
import br.com.pacto.controller.json.avaliacao.AvaliacaoPosturalJSON;
import br.com.pacto.controller.json.avaliacao.AvaliadorDTO;
import br.com.pacto.controller.json.avaliacao.AvaliadorFisicoJSON;
import br.com.pacto.controller.json.avaliacao.DobraCutaneaJSON;
import br.com.pacto.controller.json.avaliacao.HorarioPersonalAgendaJSON;
import br.com.pacto.controller.json.avaliacao.HorarioPersonalJSON;
import br.com.pacto.controller.json.avaliacao.PerimetriaJSON;
import br.com.pacto.controller.json.avaliacao.SugestaoHorarioJSON;
import br.com.pacto.controller.json.avaliacao.SugestaoHorarioPersonalJSON;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.notificacao.PushMobileRunnable;
import br.com.pacto.controller.to.UploadedFile;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.anamnese.AnamneseDao;
import br.com.pacto.dao.intf.anamnese.Movimento3DDao;
import br.com.pacto.dao.intf.anamnese.PerguntaAnamneseDao;
import br.com.pacto.dao.intf.anamnese.PerguntaDao;
import br.com.pacto.dao.intf.anamnese.RespostaClienteDao;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoFisicaDao;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoPosturalDao;
import br.com.pacto.dao.intf.avaliacao.FlexibilidadeDao;
import br.com.pacto.dao.intf.avaliacao.ItemAvaliacaoFisicaDao;
import br.com.pacto.dao.intf.avaliacao.ItemAvaliacaoPosturalDao;
import br.com.pacto.dao.intf.avaliacao.ParQDao;
import br.com.pacto.dao.intf.avaliacao.PesoOsseoDao;
import br.com.pacto.dao.intf.avaliacao.VentilometriaDao;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.email.UteisEmail;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;
import br.com.pacto.notificacao.EnfileiradorNotificadorRecursoSistemaSingleton;
import br.com.pacto.notificacao.RecursoSistema;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.AvaliacaoFisicaIntegradaPDF;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.ColumnModel;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.authenticacao.AuthenticacaoMsService;
import br.com.pacto.service.discovery.ClientDiscoveryDataDTO;
import br.com.pacto.service.discovery.DiscoveryMsService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AvaliacaoFisicaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.PesoOsseoExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.VentilometriaExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.ConfigsEmail;
import br.com.pacto.util.PDFFromXmlFile;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.ItemRelatorioTO;
import br.com.pacto.util.enumeradores.ProtocolosAvaliacaoFisicaEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.WordUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ServletContextAware;
import servicos.integracao.adm.AdmWSConsumer;
import servicos.integracao.adm.client.EmpresaWS;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.internet.MimeBodyPart;
import javax.mail.util.ByteArrayDataSource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static java.util.Objects.isNull;

/**
 * Created by Rafael on 08/10/2016.
 */
@Service
public class AvaliacaoFisicaImpl implements AvaliacaoFisicaService, ServletContextAware {


    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private AvaliacaoFisicaDao avaliacaoFisicaDao;
    @Autowired
    private ItemAvaliacaoFisicaDao itemDao;
    @Autowired
    private VentilometriaDao ventDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ProfessorSinteticoDao professorSinteticoDao;
    @Autowired
    private ClienteSinteticoDao clienteDao;
    @Autowired
    private PesoOsseoDao pesoOsseoDao;
    @Autowired
    private FlexibilidadeDao flexDao;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private AgendamentoDao agendamentoDao;
    @Autowired
    private RespostaClienteDao respostaClienteDao;
    @Autowired
    private ConfiguracaoSistemaService cfgsService;
    @Autowired
    private AgendamentoService ages;
    @Autowired
    private Movimento3DDao m3dDao;
    @Autowired
    private AnamneseService anamneseService;
    @Autowired
    private AvaliacaoPosturalDao posturalDao;
    @Autowired
    private ItemAvaliacaoPosturalDao itemPosturalDao;
    @Autowired
    private PerguntaDao perguntaDao;
    @Autowired
    private PerguntaAnamneseDao perguntaAnamneseDao;
    @Autowired
    private AnamneseDao anamneseDao;
    private ServletContext sc;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ParQDao parQDao;
    @Autowired
    private FotoService fotoService;
    @Autowired
    private LogDao logDao;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private AvaliacaoFisicaApiService avaliacaoFisicaApiService;

    private boolean estaAlterando = false;


    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public AvaliacaoFisicaDao getAvaliacaoFisicaDao() {
        return avaliacaoFisicaDao;
    }

    public void setAvaliacaoFisicaDao(AvaliacaoFisicaDao avaliacaoFisicaDao) {
        this.avaliacaoFisicaDao = avaliacaoFisicaDao;
    }

    public AvaliacaoFisica alterar(final String ctx, AvaliacaoFisica object) throws ServiceException, ValidacaoException {
        try {
            return getAvaliacaoFisicaDao().update(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ItemAvaliacaoFisica alterarItem(final String ctx, ItemAvaliacaoFisica object) throws ServiceException, ValidacaoException {
        return alterarItem(ctx, null, object, null);
    }

    public ItemAvaliacaoFisica alterarItem(final String ctx, ItemAvaliacaoFisica objectAntesAlteracao, ItemAvaliacaoFisica object, AvaliacaoFisica avaliacaoFisica) throws ServiceException, ValidacaoException {
        try {
            ItemAvaliacaoFisica iAF = itemDao.update(ctx, object);
            if (object.getItem().equals(ItemAvaliacaoFisicaEnum.OBJETIVOS) && !isNull(objectAntesAlteracao)) {
                incluirLog(ctx, avaliacaoFisica.getCodigo().toString(), iAF.getCodigo().toString(), objectAntesAlteracao.getDescricaoParaLog(iAF), iAF.getDescricaoParaLog(objectAntesAlteracao),
                        "ALTERAÇÃO", "ALTERAÇÃO DE OBJETIVOS", EntidadeLogEnum.ITEMAVALIACAOFISICA, "Item Avaliação Física", sessaoService, logDao, null, null);
            }
            return iAF;
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, AvaliacaoFisica object, String origem) throws ServiceException {
        try {
            String clienteAvaliacaoLog = object.getCliente().getCodigo().toString();
            AvaliacaoFisica cloneAv = object;
            AvaliacaoFisica avAntesExclusao = UtilReflection.copy(object);
            if (object.getAgendamentoReavaliacao() != null) {
                agendamentoDao.deleteComParam(ctx, new String[]{"codigo"}, new Object[]{object.getAgendamentoReavaliacao().getCodigo()});
            }
            flexDao.deleteComParam(ctx, new String[]{"avaliacao.codigo"}, new Object[]{object.getCodigo()});
            pesoOsseoDao.deleteComParam(ctx, new String[]{"avaliacaoFisica.codigo"}, new Object[]{object.getCodigo()});
            AvaliacaoPostural avaliacaoPostural = consultarPostural(ctx, object);
            if (avaliacaoPostural != null) {
                deleteItensPostural(ctx, avaliacaoPostural);
                posturalDao.deleteComParam(ctx, new String[]{"codigo"}, new Object[]{avaliacaoPostural.getCodigo()});
            }
            ventDao.deleteComParam(ctx, new String[]{"avaliacao.codigo"}, new Object[]{object.getCodigo()});
            List<ItemAvaliacaoFisica> itens = itemDao.findListByAttributes(ctx, new String[]{"avaliacaoFisica.codigo"}, new Object[]{object.getCodigo()}, null, 0);
            for (ItemAvaliacaoFisica i : itens) {
                respostaClienteDao.deleteComParam(ctx, new String[]{"itemAvaliacao.codigo"}, new Object[]{i.getCodigo()});
                itemDao.deleteComParam(ctx, new String[]{"codigo"}, new Object[]{i.getCodigo()});
            }
            respostaClienteDao.deleteComParam(ctx, new String[]{"avaliacao.codigo"}, new Object[]{object.getCodigo()});
            getAvaliacaoFisicaDao().deleteComParam(ctx, new String[]{"codigo"}, new Object[]{object.getCodigo()});
            incluirLog(ctx, avAntesExclusao.getCodigo().toString(), clienteAvaliacaoLog, avAntesExclusao.getDescricaoParaLog(null), "",
                    "EXCLUSÃO", "EXCLUSÃO AVALIAÇÃO FÍSICA [Origem: "+origem+"]", EntidadeLogEnum.AVALIACAOFISICA, "Avaliação Física", sessaoService, logDao, null, null);
            avaliacaoFisicaApiService.deletaAvaliacaoFisicaApiExternaAsync(avAntesExclusao);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void deleteItensPostural(final String key, final AvaliacaoPostural postural) throws Exception {
        itemPosturalDao.deleteComParam(key, new String[]{"avaliacaoPostural.codigo"}, new Object[]{postural.getCodigo()});
    }

    public AvaliacaoFisica inserir(final String ctx, AvaliacaoFisica object) throws ServiceException, ValidacaoException {
        try {
            return getAvaliacaoFisicaDao().insert(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void classificarPercentualGordura(AvaliacaoFisica obj, Integer idade, String sexo) throws ServiceException {
        if (sexo.contains("F")) {
            if (idade <= 35) {
                if (obj.getPercentualGordura() < 20) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.RUIM);
                } else if (obj.getPercentualGordura() == 20) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ABAIXO_MEDIA);
                } else if (obj.getPercentualGordura() < 28) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.MEDIA);
                } else if (obj.getPercentualGordura() < 35) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ACIMA_MEDIA);
                } else if (obj.getPercentualGordura() > 35) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.OBESIDADE);
                }
            } else if (idade > 35 && idade < 49) {
                if (obj.getPercentualGordura() < 25) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.RUIM);
                } else if (obj.getPercentualGordura() == 25) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ABAIXO_MEDIA);
                } else if (obj.getPercentualGordura() < 32) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.MEDIA);
                } else if (obj.getPercentualGordura() < 38) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ACIMA_MEDIA);
                } else if (obj.getPercentualGordura() > 38) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.OBESIDADE);
                }
            } else if (idade > 49) {
                if (obj.getPercentualGordura() < 25) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.RUIM);
                } else if (obj.getPercentualGordura() == 25) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ABAIXO_MEDIA);
                } else if (obj.getPercentualGordura() < 30) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.MEDIA);
                } else if (obj.getPercentualGordura() < 35) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ACIMA_MEDIA);
                } else if (obj.getPercentualGordura() > 35) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.OBESIDADE);
                }
            }
        } else {
            if (idade <= 35) {
                if (obj.getPercentualGordura() < 8) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.RUIM);
                } else if (obj.getPercentualGordura() == 8) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ABAIXO_MEDIA);
                } else if (obj.getPercentualGordura() < 13) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.MEDIA);
                } else if (obj.getPercentualGordura() < 22) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ACIMA_MEDIA);
                } else if (obj.getPercentualGordura() > 22) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.OBESIDADE);
                }
            } else if (idade > 35 && idade < 49) {
                if (obj.getPercentualGordura() < 10) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.RUIM);
                } else if (obj.getPercentualGordura() == 10) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ABAIXO_MEDIA);
                } else if (obj.getPercentualGordura() < 18) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.MEDIA);
                } else if (obj.getPercentualGordura() < 25) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ACIMA_MEDIA);
                } else if (obj.getPercentualGordura() > 25) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.OBESIDADE);
                }
            } else if (idade > 49) {
                if (obj.getPercentualGordura() < 10) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.RUIM);
                } else if (obj.getPercentualGordura() == 10) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ABAIXO_MEDIA);
                } else if (obj.getPercentualGordura() < 16) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.MEDIA);
                } else if (obj.getPercentualGordura() < 23) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.ACIMA_MEDIA);
                } else if (obj.getPercentualGordura() > 23) {
                    obj.setCategoriaPercentualGordura(CategoriaPercentualGordura.OBESIDADE);
                }
            }
        }
    }

    public AvaliacaoFisica obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAvaliacaoFisicaDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public AvaliacaoFisica obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getAvaliacaoFisicaDao().findByIdClearSession(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AvaliacaoFisica> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAvaliacaoFisicaDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AvaliacaoFisica> obterPorParam(final String ctx, String query,
                                               Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getAvaliacaoFisicaDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<AvaliacaoFisica> obterTodos(final String ctx) throws ServiceException {
        try {
            return getAvaliacaoFisicaDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public AvaliacaoFisica obterAvaliacaoVigente(final String ctx, final int cliente) throws ServiceException {
        try {
            getAvaliacaoFisicaDao().getCurrentSession(ctx).clear();
            StringBuilder sql = new StringBuilder("SELECT obj FROM AvaliacaoFisica obj WHERE obj.cliente.codigo = :cliente\n");
            sql.append(" AND obj.dataAvaliacao IS NOT NULL\n");
            sql.append(" ORDER by obj.dataAvaliacao DESC, obj.codigo DESC ");
            HashMap params = new HashMap<String, Object>();
            params.put("cliente", cliente);
            return getAvaliacaoFisicaDao().findFirstObjectByParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<AvaliacaoFisica> obterAvaliacaoMovProduto(final String ctx, final int movProduto) throws ServiceException {
        try {
            String sql = "SELECT obj FROM AvaliacaoFisica obj WHERE obj.movProduto = :movProduto ";
            HashMap params = new HashMap<String, Object>();
            params.put("movProduto", movProduto);
            return getAvaliacaoFisicaDao().findByParam(ctx, sql, params, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ItemImpressao montarListaImpressao(final String ctx, final String codigosAvalicacao) throws Exception {
        String[] splitAvaliacoes = codigosAvalicacao.split("\\_");
        List<AvaliacaoFisica> avaliacoes = new ArrayList<AvaliacaoFisica>();
        for (String id : splitAvaliacoes) {
            avaliacoes.add(obterPorId(ctx, Integer.valueOf(id)));
        }
        if (avaliacoes.isEmpty()) {
            return new ItemImpressao();
        }
        Map<String, ItemImpressaoAvaliacao> mapaObjs = new HashMap<String, ItemImpressaoAvaliacao>();
        avaliacoes = Ordenacao.ordenarLista(avaliacoes, "codigo");
        AvaliacaoFisica ultima = avaliacoes.get(avaliacoes.size() - 1);
        AvaliacaoFisica avaliacoesFisicaAnterior = obterAvaliacaoFisicaAnterior(ctx, ultima);
        if (avaliacoesFisicaAnterior != null) {
            ultima.setAumentoPercentualGordura(ultima.getPercentualGordura() > avaliacoesFisicaAnterior.getPercentualGordura() ? true : false);
        }

        Double parametros;
        ItemImpressao obj = new ItemImpressao();
        obj.setColumns(new ArrayList<ColumnModel>());
        obj.setValues(new ArrayList<Object>());
        for (AvaliacaoFisica a : avaliacoes) {
            parametros = 0.0;
            obj.getColumns().add(new ColumnModel(Uteis.getData(a.getDataAvaliacao()), "", a.getCodigo().toString()));

            Usuario usu = usuarioService.obterPorId(ctx, a.getResponsavelLancamento_codigo());
            addIndicadorImpressao(mapaObjs, "01-Professor", a.getCodigo().toString(), usu.getProfessor().getNomeAbreviado());
            addIndicadorImpressao(mapaObjs, "02-Antebraço ESQ.", a.getCodigo().toString(), a.getAntebracoEsq());
            addIndicadorImpressao(mapaObjs, "03-Antebraço DIR.", a.getCodigo().toString(), a.getAntebracoDir());
            addIndicadorImpressao(mapaObjs, "04-Braço relaxado DIR.", a.getCodigo().toString(), a.getBracoRelaxadoDir());
            addIndicadorImpressao(mapaObjs, "05-Braço relaxado ESQ.", a.getCodigo().toString(), a.getBracoRelaxadoEsq());
            addIndicadorImpressao(mapaObjs, "06-Braço contraído DIR.", a.getCodigo().toString(), a.getBracoContraidoDir());
            addIndicadorImpressao(mapaObjs, "07-Braço contraído ESQ.", a.getCodigo().toString(), a.getBracoContraidoEsq());
            addIndicadorImpressao(mapaObjs, "08-Coxa média DIR.", a.getCodigo().toString(), a.getCoxaMediaDir());
            addIndicadorImpressao(mapaObjs, "09-Coxa média ESQ.", a.getCodigo().toString(), a.getCoxaMediaEsq());
            addIndicadorImpressao(mapaObjs, "10-Panturrilha DIR.", a.getCodigo().toString(), a.getPanturrilhaDir());
            addIndicadorImpressao(mapaObjs, "11-Panturrilha ESQ.", a.getCodigo().toString(), a.getPanturrilhaEsq());
            addIndicadorImpressao(mapaObjs, "12-Tórax / Busto relaxado", a.getCodigo().toString(), a.getToraxBusto());
            addIndicadorImpressao(mapaObjs, "13-Cintura", a.getCodigo().toString(), a.getCintura());
            addIndicadorImpressao(mapaObjs, "14-Circunferência abdominal", a.getCodigo().toString(), a.getCircunferenciaAbdominal());
            addIndicadorImpressao(mapaObjs, "15-Glúteo", a.getCodigo().toString(), a.getGluteo());
            addIndicadorImpressao(mapaObjs, "16-Total perímetria", a.getCodigo().toString(), Uteis.arredondarForcando2CasasDecimaisMantendoSinal(a.getTotalPerimetria()));
            addIndicadorImpressao(mapaObjs, "17-Flexibilidade", a.getCodigo().toString(), a.getFlexibilidade());

            if (a.getUsouAbdominal()) {
                addIndicadorImpressao(mapaObjs, "18-Abdominal", a.getCodigo().toString(), a.getAbdominal());
                parametros += a.getAbdominal();
            }

            if (a.getUsouSuprailiaca()) {
                addIndicadorImpressao(mapaObjs, "19-Suprailíaca", a.getCodigo().toString(), a.getSupraIliaca());
                parametros += a.getSupraIliaca();
            }


            if (a.getUsouTorax()) {
                addIndicadorImpressao(mapaObjs, "20-Peitoral", a.getCodigo().toString(), a.getPeitoral());
                parametros += a.getPeitoral();
            }

            if (a.getUsouTriceps()) {
                addIndicadorImpressao(mapaObjs, "21-Tríceps", a.getCodigo().toString(), a.getTriceps());
                parametros += a.getTriceps();
            }

            if (a.getUsouCoxa()) {
                addIndicadorImpressao(mapaObjs, "22-Coxa Medial", a.getCodigo().toString(), a.getCoxaMedial());
                parametros += a.getCoxaMedial();
            }

            if (a.getUsouSubescapular()) {
                addIndicadorImpressao(mapaObjs, "23-Subescapular", a.getCodigo().toString(), a.getSubescapular());
                parametros += a.getSubescapular();
            }

            if (a.getUsouAxilarMedia()) {
                addIndicadorImpressao(mapaObjs, "24-Axilar Média", a.getCodigo().toString(), a.getAxilarMedia());
                parametros += a.getAxilarMedia();
            }

            if (a.getUsouPanturrilha()) {
                addIndicadorImpressao(mapaObjs, "42-Panturrilha", a.getCodigo().toString(), a.getPanturrilha());
                parametros += a.getPanturrilha();
            }

            addIndicadorImpressao(mapaObjs, "25-Total Dobras", a.getCodigo().toString(), Uteis.arredondarForcando2CasasDecimaisMantendoSinal(parametros));

            addIndicadorImpressao(mapaObjs, "26-Massa magra", a.getCodigo().toString(), a.getMassaMagra());
            addIndicadorImpressao(mapaObjs, "27-Massa gorda", a.getCodigo().toString(), a.getMassaGorda());

            addIndicadorImpressao(mapaObjs, "28-Altura", a.getCodigo().toString(), Uteis.formatString(a.getAltura(), "#0.00"));
            addIndicadorImpressao(mapaObjs, "29-Peso", a.getCodigo().toString(), a.getPeso());
            addIndicadorImpressao(mapaObjs, "30-Protocolo", a.getCodigo().toString(), viewUtils.getLabel(a.getProtocolo().name()));
            addIndicadorImpressao(mapaObjs, "31-% Gordura", a.getCodigo().toString(), a.getPercentualGordura() + "%");
            if (a.getProtocolo() != null && a.getProtocolo().equals(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA)) {
                //bioimpedancia
//    private Double percentualAgua= 0.0;
                addIndicadorImpressao(mapaObjs, "32-% Água", a.getCodigo().toString(), a.getPercentualAgua() + "%");
//    private Double tmb= 0.0;
                addIndicadorImpressao(mapaObjs, "33-TMB", a.getCodigo().toString(), a.getTmb());
//    private Double resistencia= 0.0;
                addIndicadorImpressao(mapaObjs, "34- Resistência", a.getCodigo().toString(), a.getResistencia());
//    private Double reatancia= 0.0;
                addIndicadorImpressao(mapaObjs, "35- Reatância", a.getCodigo().toString(), a.getReatancia());
//    private Double gorduraIdeal= 0.0;
                addIndicadorImpressao(mapaObjs, "36- Gordura ideal", a.getCodigo().toString(), a.getGorduraIdeal());
//    private Double necessidadeFisica= 0.0;
                addIndicadorImpressao(mapaObjs, "37- Necessidade física", a.getCodigo().toString(), a.getNecessidadeFisica());
//    private Double necessidadeCalorica= 0.0;
                addIndicadorImpressao(mapaObjs, "38- Necessidade calórica", a.getCodigo().toString(), a.getNecessidadeCalorica());
                addIndicadorImpressao(mapaObjs, "40- Idade metabólica", a.getCodigo().toString(), a.getIdadeMetabolica());
                addIndicadorImpressao(mapaObjs, "41- Gordura viscera", a.getCodigo().toString(), a.getGorduraVisceral());
            }
            addIndicadorImpressao(mapaObjs, "39- Risco cardivascular (Circunferência abdominal)", a.getCodigo().toString(), getViewUtils().getLabelInternacionalizada(a.getResultadoRiscoCA(), "PT"));
        }

        List<ItemImpressaoAvaliacao> lista = Ordenacao.ordenarLista(new ArrayList(mapaObjs.values()), "descricao");
        for (ItemImpressaoAvaliacao i : lista) {
            i.setDescricao(i.getDescricao().split("-")[1]);
        }
        List t = lista;
        obj.setValues(t);
        obj.setAvaliacaoFisica(ultima);
        return obj;
    }

    private AvaliacaoFisica obterAvaliacaoFisicaAnterior(final String ctx, AvaliacaoFisica avaliacaoFisica) throws Exception {
        String sql = "Select obj from AvaliacaoFisica obj where obj.dataAvaliacao < :data and  obj.cliente.codigo = :cliente  order by obj.dataAvaliacao desc limit 1";
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put("data", avaliacaoFisica.getDataAvaliacao());
        params.put("cliente", avaliacaoFisica.getCliente().getCodigo());
        return obterObjetoPorParam(ctx, sql, params);
    }

    private void addIndicadorImpressao(Map<String, ItemImpressaoAvaliacao> mapaObjs, String chave, String avaliacao, Double valor) {
        addIndicadorImpressao(mapaObjs, chave, avaliacao, valor == null ? "-" : valor.toString());
    }

    private void addIndicadorImpressao(Map<String, ItemImpressaoAvaliacao> mapaObjs, String chave, String avaliacao, String valor) {
        ItemImpressaoAvaliacao itemImpressao = mapaObjs.get(chave);
        if (itemImpressao == null) {
            itemImpressao = new ItemImpressaoAvaliacao(chave);
            mapaObjs.put(chave, itemImpressao);
        }
        NumberFormat numberf = NumberFormat.getInstance(new Locale("pt", "BR"));
        try {
            itemImpressao.getValor().put(avaliacao, valor == null ? "-" : numberf.format(new Double(valor)));
        } catch (Exception e) {
            itemImpressao.getValor().put(avaliacao, valor == null ? "-" : valor.toString());
        }
    }

    @Override
    public List<AvaliacaoFisica> obterAvaliacaoCliente(final String ctx, final int cliente) throws ServiceException {
        try {
            getAvaliacaoFisicaDao().getCurrentSession(ctx).clear();
            StringBuilder sql = new StringBuilder("SELECT obj FROM AvaliacaoFisica obj WHERE obj.cliente.codigo = :cliente and obj.dataAvaliacao is not null ");
            sql.append(" ORDER by obj.dataAvaliacao DESC, obj.codigo DESC ");
            HashMap params = new HashMap<String, Object>();
            params.put("cliente", cliente);
            return getAvaliacaoFisicaDao().findByParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void calcularAvalicaoFisica(final AvaliacaoFisica avaliacaoFisica,
                                       final PesoOsseo pesoOsseo,
                                       ProtocolosAvaliacaoFisicaEnum protocolo,
                                       boolean temImportacaoBiosanny) throws ServiceException {
        try {
            switch (protocolo) {
                case POLLOCK_3_DOBRAS:
                    ProtocoloPolloc.calculaDados(avaliacaoFisica);
                    break;
                case POLLOCK_7_DOBRAS:
                    ProtocoloPollock7Dobras.calculaDados(avaliacaoFisica);
                    break;
                case GUEDES:
                    ProtocoloGuedes3Dobras.calculaDados(avaliacaoFisica);
                    break;
                case FAULKNER_DOBRAS:
                    ProtocoloFaulkner4Dobras.calculaDados(avaliacaoFisica);
                    break;
                case WELTMAN_OBESO:
                    ProtocoloWeltmanObeso.calculaDados(avaliacaoFisica);
                    break;
                case SLAUGHTER:
                    ProtocoloSlaugther.calculaDados(avaliacaoFisica);
                    break;
                case POLLOCK_ADOLESCENTE:
                    ProtocoloPolloc.calculaDados(avaliacaoFisica, true);
                    break;
                case YUHASZ:
                    ProtocoloYuhasz.calculaDados(avaliacaoFisica);
                    break;
                case TG_LOHMAN:
                    ProtocoloTGLohman.calculaDados(avaliacaoFisica);
                    break;
            }
            try {
                ProtocoloSomatotipia.calcular(avaliacaoFisica, pesoOsseo);
            } catch (Exception e) {
                //ignore
            }
            if ((avaliacaoFisica.getPeso() != null && avaliacaoFisica.getAltura() != null) && (avaliacaoFisica.getPeso() != 0 && avaliacaoFisica.getAltura() != 0) && !temImportacaoBiosanny) {
                avaliacaoFisica.setImc(Uteis.calculaIMC(avaliacaoFisica.getPeso(), avaliacaoFisica.getAltura()));
            }
            avaliacaoFisica.setCategoriaAvaliacaoIMC(ProtocoloPolloc.classificacaoIMC(avaliacaoFisica.getImc()));
            avaliacaoFisica.setCategoriaPercentualGordura(ProtocoloPolloc.classificacaoPercGodura(avaliacaoFisica.getPercentualGordura(),
                    avaliacaoFisica.getCliente().getSexo(), avaliacaoFisica.getCliente().getIdade()));
            avaliacaoFisica.setTotalPerimetria(0.0);
            for (PerimetriaEnum pn : PerimetriaEnum.values()) {
                try {
                    avaliacaoFisica.setTotalPerimetria(avaliacaoFisica.getTotalPerimetria() + ((Double) UtilReflection.getValor(avaliacaoFisica, pn.getField())));
                } catch (Exception e) {
                    //ignore
                }
            }

        } catch (Exception ex) {
            Uteis.logar(ex, AvaliacaoFisicaImpl.class);
            throw new ServiceException(ex);
        }
    }

    @Override
    public ItemAvaliacaoFisica adicionarItem(final String key, final String result, final ClienteSintetico cliente, final Usuario responsavel,
                                             final ItemAvaliacaoFisicaEnum i, Anamnese anamnese, final Ventilometria ventilometria, final AvaliacaoFisica avaliacaoFisica) throws ServiceException {
        try {
            ItemAvaliacaoFisica prs = new ItemAvaliacaoFisica();
            prs.setResult(result);
            prs.setAvaliacaoFisica(avaliacaoFisica);
            prs.setAnamnese(anamnese);
            if (ventilometria != null) {
                ventDao.insert(key, ventilometria);
                prs.setVentilometria(ventilometria);
            }
            prs.setItem(i);
            prs.setCliente(cliente);
            prs.setDataLancamento(Calendario.hoje());
            prs.setResponsavelLancamento_codigo(responsavel.getCodigo());
            prs = itemDao.insert(key, prs);
            if (i.equals(ItemAvaliacaoFisicaEnum.OBJETIVOS)) {
                incluirLog(key, avaliacaoFisica.getCodigo().toString(), prs.getCodigo().toString(), "", prs.getDescricaoParaLog(null),
                        "INCLUSÃO", "INCLUSÃO DE OBJETIVOS", EntidadeLogEnum.ITEMAVALIACAOFISICA, "Item Avaliação Física", sessaoService, logDao, null, null);
            }
            return prs;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public RespostaClienteParQ adicionarRespostaParQ(final String ctx, final JSONObject json, final Integer usuarioZw, final ClienteSintetico cliente) throws ServiceException {
        try {
            RespostaClienteParQ rcp = parQDao.consultarRespostaParQPorCliente(ctx, cliente.getCodigo());
            rcp = rcp == null ? new RespostaClienteParQ() : rcp;
            rcp.setCliente(cliente);
            rcp.setDataResposta(Calendario.hoje());
            rcp.setUrlAssinatura("");
            rcp.setUsuario_codigo(usuarioZw);
            return parQDao.insert(ctx, rcp);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void alterarVentilometria(final String key, final Ventilometria ventilometria) throws Exception {
        ventDao.update(key, ventilometria);
    }


    @Override
    public void calcularTabelFrequenciaCardiaca(final AvaliacaoFisica a, final ClienteSintetico cliente, Integer fcRepouso) throws ServiceException {
        if (fcRepouso == null) {
            try {
                String fcRepousoResult = obterItemAvaliacaoFisica(
                        sessaoService.getUsuarioAtual().getChave(),
                        a.getCliente().getCodigo(),
                        ItemAvaliacaoFisicaEnum.FREQUENCIA_CARDIACA,
                        null,
                        a).getResult();

                fcRepouso = fcRepousoResult != null ? Integer.parseInt(fcRepousoResult) : null;

                if (fcRepouso == null) {
                    throw new ServiceException("Não foi possível obter a frequência cardíaca de repouso.");
                }
            } catch (Exception e) {
                throw new ServiceException("Erro ao calcular a frequência cardíaca de repouso", e);
            }
        }
        if (UteisValidacao.emptyNumber(a.getFcMaxima()) && !UteisValidacao.emptyNumber(cliente.getIdade())) {
            if (cliente.getIdade() > 120) cliente.setIdade(120);
            a.setFcMaxima(cliente.isSexoMasculino() ? (220.0 - cliente.getIdade()) : (226.0 - cliente.getIdade()));
        }
        a.setValores(new HashMap<String, Integer>());
        Integer nivelAtividadeFisica = 55;
        while (nivelAtividadeFisica < 100) {
            a.getValores().put(nivelAtividadeFisica + "%",
                    Uteis.calcularFCMaxima(fcRepouso, a.getFcMaxima().intValue(), nivelAtividadeFisica));
            nivelAtividadeFisica += 5;
        }
    }


    @Override
    public ItemAvaliacaoFisica gravarItem(final String key, ItemAvaliacaoFisica prs) throws ServiceException {
        try {
            return itemDao.insert(key, prs);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluirItem(final String key, final ItemAvaliacaoFisica prs) throws Exception {
        try {
            itemDao.delete(key, prs);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluirPesoOsseo(final String key, final PesoOsseo po) throws ServiceException {
        try {
            pesoOsseoDao.delete(key, po);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public PesoOsseo inserirPesoOsseo(final String key, final PesoOsseo po) throws ServiceException {
        try {
            if (UteisValidacao.emptyNumber(po.getCodigo())) {
                return pesoOsseoDao.insert(key, po);
            } else {
                return pesoOsseoDao.update(key, po);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public PesoOsseo obterPesoOsseo(final String ctx, final int cliente, final AvaliacaoFisica avaliacao) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("SELECT obj FROM PesoOsseo obj WHERE obj.cliente.codigo = :cliente and obj.avaliacaoFisica.codigo = :avaliacao ");
            sql.append(" ORDER by obj.dataLancamento DESC LIMIT 1 ");
            Map params = new HashMap<String, Object>();
            params.put("cliente", cliente);
            params.put("avaliacao", avaliacao.getCodigo());
            PesoOsseo pesoOsseo = null;
            List<PesoOsseo> pesoOsseos = pesoOsseoDao.findByParam(ctx, sql.toString(), params, 1, 0);
            if (UteisValidacao.emptyList(pesoOsseos)) {
                sql = new StringBuilder("SELECT obj FROM PesoOsseo obj WHERE obj.cliente.codigo = :cliente and obj.avaliacaoFisica.codigo is null ORDER by obj.dataLancamento DESC LIMIT 1 ");
                params = new HashMap<String, Object>();
                params.put("cliente", cliente);
                pesoOsseos = pesoOsseoDao.findByParam(ctx, sql.toString(), params, 1, 0);
                if (!UteisValidacao.emptyList(pesoOsseos)) {
                    pesoOsseo = pesoOsseos.get(0);
                    pesoOsseo.setAvaliacaoFisica(avaliacao);
                    pesoOsseo = pesoOsseoDao.update(ctx, pesoOsseo);
                }
            } else {
                pesoOsseo = pesoOsseos.get(0);
            }
            return pesoOsseo;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public AvaliacaoBioimpedanciaDTO extrairInformacoesAvaliacaoBioimpedancia(ImportarAvaliacaoBioimpedanciaDTO avaliacaoBioimpedanciaDTO) throws ServiceException {
        final String arquivoBase64 = avaliacaoBioimpedanciaDTO.getArquivoUpload().split(",")[1];
        byte[] pdfBytes = Base64.decodeBase64(arquivoBase64);

        try (PDDocument document = PDDocument.load(new ByteArrayInputStream(pdfBytes))) {
            final PDFTextStripper pdfStripper = new PDFTextStripper();
            pdfStripper.setSortByPosition(true);

            final String text = pdfStripper.getText(document);
            final boolean layoutAntigo = text.contains("Composição Corporal");

            if (layoutAntigo) {
                return obterValoresAvaliacaoBioimpedenciaV1(text);
            }

            return obterValoresAvaliacaoBioimpedenciaV2(text);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private AvaliacaoBioimpedanciaDTO obterValoresAvaliacaoBioimpedenciaV1(final String text) {
        final AvaliacaoBioimpedanciaDTO avaliacaoBio = new AvaliacaoBioimpedanciaDTO();

        final String data = extractInfo(text, "Data da Avaliação:\\s*(\\d{2}/\\d{2}/\\d{4})");
        if (StringUtils.isNotBlank(data)) {
            avaliacaoBio.setData(data);
        }

        final String altura = extractInfo(text, "(\\d{2,3}) cm");
        if (StringUtils.isNotBlank(altura)) {
            avaliacaoBio.setAltura(converterParaNumero(altura) / 100);
        }

        final String peso = extractInfo(text, "(\\d+(?:[.,]\\d+)?)\\s*kg");
        if (StringUtils.isNotBlank(peso)) {
            avaliacaoBio.setPeso(converterParaNumero(peso));
        }

        final String resistencia = extractInfo(text, "(\\d+[,\\s]*\\d*)\\s*ohm");
        if (StringUtils.isNotBlank(resistencia)) {
            avaliacaoBio.setResistencia(converterParaNumero(resistencia));
        }

        final String reatancia = extractInfo(text, "(\\d+[,\\s]*\\d*)\\s*ohm(?!.*\\d+\\s*ohm)");
        if (StringUtils.isNotBlank(reatancia)) {
            avaliacaoBio.setReatancia(converterParaNumero(reatancia));
        }

        final String perimetroAbdominal = extractInfo(text, "Perímetro Abdominal:\\s*(\\d{2,3}[,.]?\\d*)\\s*cm");
        if (StringUtils.isNotBlank(perimetroAbdominal)) {
            avaliacaoBio.setPerimetroAbdominal(converterParaNumero(perimetroAbdominal));
        }

        final String aguaCorporalPercentual = extractInfo(text, "ACT\\s*\\d{2,3}[,.]\\d*\\s*(\\d{2,3}[,.]\\s*\\d{1,2})");
        if (StringUtils.isNotBlank(aguaCorporalPercentual)) {
            avaliacaoBio.setAguaCorporalPercentual(converterParaNumero(aguaCorporalPercentual));
        }

        final String massaLivreKg = extractInfo(text, "MLG\\s*(\\d{2,3}[,.]\\d*)");
        if (StringUtils.isNotBlank(massaLivreKg)) {
            avaliacaoBio.setMassaLivreKg(converterParaNumero(massaLivreKg));
        }

        final String massaLivrePercentual = extractInfo(text, "MLG\\s*\\d{2,3}\\s*[,.]\\s*\\d*\\s*(\\d{2,3}\\s*[,.]\\s*\\d{1,2})");
        if (StringUtils.isNotBlank(massaLivrePercentual)) {
            avaliacaoBio.setMassaLivrePercentual(converterParaNumero(massaLivrePercentual));
        }

        final String gorduraCorporalKg = extractInfo(text, "GC\\s*(\\d{2,3}[,.]\\d*)");
        if (StringUtils.isNotBlank(gorduraCorporalKg)) {
            avaliacaoBio.setGorduraCorporalKg(converterParaNumero(gorduraCorporalKg));
        }

        final String gorduraCorporalPercentual = extractInfo(text, "GC\\s*\\d{2,3}[,.]\\d*\\s*(\\d{2,3}[,.]\\d*)");
        if (StringUtils.isNotBlank(gorduraCorporalPercentual)) {
            avaliacaoBio.setGorduraCorporalPercentual(converterParaNumero(gorduraCorporalPercentual));
        }

        final String massaMuscularEsqueleticaKg = extractInfo(text, "MME\\s*\\n\\s*([\\d,.]+)\\s*kg");
        if (StringUtils.isNotBlank(massaMuscularEsqueleticaKg)) {
            avaliacaoBio.setMassaMuscularEsqueleticaKg(converterParaNumero(massaMuscularEsqueleticaKg));
        }

        final String imc = extractInfo(text, "IMC\\s*(?:\\n?.*?){1,4}\\s*(\\d{2},\\d\\s*\\d)\\s*\\d?");
        if (StringUtils.isNotBlank(imc)) {
            avaliacaoBio.setImc(converterParaNumero(imc));
        }

        return avaliacaoBio;
    }

    private AvaliacaoBioimpedanciaDTO obterValoresAvaliacaoBioimpedenciaV2(final String text) {
        final AvaliacaoBioimpedanciaDTO avaliacaoBio = new AvaliacaoBioimpedanciaDTO();

        final String data = extractInfo(text, "BIA1011-A\\w*\\s(\\d{2}/\\d{2}/\\d{2,4})");
        if (StringUtils.isNotBlank(data)) {
            avaliacaoBio.setData(data);
        }

        final String altura = extractInfo(text, "(?:Fem|Mas)\\s(\\d{2,3})\\s*cm");
        if (StringUtils.isNotBlank(altura)) {
            avaliacaoBio.setAltura(converterParaNumero(altura) / 100);
        }

        final String peso = extractInfo(text, "cm\\s(\\d+(?:\\.\\d+)?)\\s*kg");
        if (StringUtils.isNotBlank(peso)) {
            avaliacaoBio.setPeso(converterParaNumero(peso));
        }

        final List<String> ohmsValues = extractOhmsValues(text);
        if (!ohmsValues.isEmpty() && StringUtils.isNotBlank(ohmsValues.get(0))) {
            avaliacaoBio.setResistencia(converterParaNumero(ohmsValues.get(0)));
        }

        if (ohmsValues.size() > 1 && StringUtils.isNotBlank(ohmsValues.get(1))) {
            avaliacaoBio.setReatancia(converterParaNumero(ohmsValues.get(1)));
        }

        final String perimetroAbdominal = extractInfo(text, "kg\\s*.+?\\s*(\\d+(?:\\.\\d+)?)");
        if (StringUtils.isNotBlank(perimetroAbdominal)) {
            avaliacaoBio.setPerimetroAbdominal(converterParaNumero(perimetroAbdominal));
        }

        extractValuesByRow(text, avaliacaoBio);

        return avaliacaoBio;
    }

    void extractValuesByRow(String text, AvaliacaoBioimpedanciaDTO avaliacaoBio) {
        final String[] linhas = text.split("\\r?\\n");

        int linhaGatilho = -1;
        for (int i = 0; i < linhas.length; i++) {
            if (linhas[i].matches(".*\\d+(?:\\.\\d+)?°\\s*$")) {
                linhaGatilho = i;
                break;
            }
        }

        if (linhaGatilho == -1) {
            return;
        }

        final int linhaAguaCorporal = linhaGatilho + 3;
        if (linhaAguaCorporal < linhas.length) {
            final String aguaCorporalPercentual = extractInfo(linhas[linhaAguaCorporal], "(\\d+(?:\\.\\d+)?)\\s*%");
            if (StringUtils.isNotBlank(aguaCorporalPercentual)) {
                avaliacaoBio.setAguaCorporalPercentual(converterParaNumero(aguaCorporalPercentual));
            }
        }

        final int linhaMassaLivre = linhaGatilho + 6;
        if (linhaMassaLivre < linhas.length) {
            final Pattern pattern = Pattern.compile("(\\d+(?:\\.\\d+)?)\\s*kg\\s*(\\d+(?:\\.\\d+)?)\\s*%", Pattern.CASE_INSENSITIVE);
            final Matcher matcher = pattern.matcher(linhas[linhaMassaLivre]);

            if (matcher.find()) {
                final String massaLivreKg = matcher.group(1);
                if (StringUtils.isNotBlank(massaLivreKg)) {
                    avaliacaoBio.setMassaLivreKg(converterParaNumero(massaLivreKg));
                }
                
                final String massaLivrePercentual = matcher.group(2);
                if (StringUtils.isNotBlank(massaLivrePercentual)) {
                    avaliacaoBio.setMassaLivrePercentual(converterParaNumero(massaLivrePercentual));
                }
            }
        }

        final int linhaGorduraCorporal = linhaGatilho + 9;
        if (linhaGorduraCorporal < linhas.length) {
            final Pattern pattern = Pattern.compile("(\\d+(?:\\.\\d+)?)\\s*kg\\s*(\\d+(?:\\.\\d+)?)\\s*%", Pattern.CASE_INSENSITIVE);
            final Matcher matcher = pattern.matcher(linhas[linhaGorduraCorporal]);

            if (matcher.find()) {
                final String gorduraCorporalKg = matcher.group(1);
                if (StringUtils.isNotBlank(gorduraCorporalKg)) {
                    avaliacaoBio.setGorduraCorporalKg(converterParaNumero(gorduraCorporalKg));
                }

                final String gorduraCorporalPercentual = matcher.group(2);
                if (StringUtils.isNotBlank(gorduraCorporalPercentual)) {
                    avaliacaoBio.setGorduraCorporalPercentual(converterParaNumero(gorduraCorporalPercentual));
                }
            }
        }

        final int linhaMME = linhaGatilho + 11;
        if (linhaMME < linhas.length) {
            final String massaMuscularEsqueleticaKg = extractInfo(linhas[linhaMME], "(\\d+(?:\\.\\d+)?)\\s*kg");
            if (StringUtils.isNotBlank(massaMuscularEsqueleticaKg)) {
                avaliacaoBio.setMassaMuscularEsqueleticaKg(converterParaNumero(massaMuscularEsqueleticaKg));
            }
        }

        final int linhaIMC = linhaGatilho + 13;
        if (linhaIMC < linhas.length) {
            final String imc = extractInfo(linhas[linhaIMC], "^(\\d+(?:\\.\\d+)?)");
            if (StringUtils.isNotBlank(imc)) {
                avaliacaoBio.setImc(converterParaNumero(imc));
            }
        }
    }

    List<String> extractOhmsValues(String text) {
        final String regex = "\\s*(\\d+(?:\\.\\d+)?)\\s*ohms";

        final Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        final Matcher matcher = pattern.matcher(text);

        final List<String> ohmsValues = new ArrayList<>();
        while (matcher.find()) {
            ohmsValues.add(matcher.group(1));
        }

        return ohmsValues;
    }

    String extractInfo(String text, String regex) {
        final Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        final Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

    String extractData(String text, String regex) {
        final Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        final Matcher matcher = pattern.matcher(text);

        List<String> datas = new ArrayList<>();

        while (matcher.find()) {
            datas.add(matcher.group(1));
        }

        return datas.isEmpty() ? null : datas.get(datas.size() - 1);
    }

    Double converterParaNumero(String valor) {
        if (StringUtils.isNotBlank(valor)) {
            valor = valor.replace(",", ".").replace(" ", "");
            return Double.parseDouble(valor);
        }

        return null;
    }

    @Override
    public ItemAvaliacaoFisica obterItemAvaliacaoFisica(final String ctx, final int cliente,
                                                        final ItemAvaliacaoFisicaEnum i,
                                                        final Date dia,
                                                        final AvaliacaoFisica avaliacao) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("SELECT obj FROM ItemAvaliacaoFisica obj ");
            HashMap<String, Object> params = new HashMap<String, Object>();
            sql.append(" WHERE obj.cliente.codigo = :cliente AND obj.item = :i ");
            if (avaliacao != null) {
                sql.append(" AND obj.avaliacaoFisica.codigo = :avaliacao ");
                params.put("avaliacao", avaliacao.getCodigo());
            }

            if (dia != null) {
                sql.append(" AND obj.dataLancamento BETWEEN :ini AND :fim ");
                params.put("ini", Calendario.getDataComHoraZerada(dia));
                params.put("fim", Calendario.getDataComHora(dia, "23:59:59"));
            }
            sql.append(" ORDER by obj.dataLancamento");
            params.put("cliente", cliente);
            params.put("i", i);
            List<ItemAvaliacaoFisica> avaliacaoFisicas = itemDao.findByParam(ctx, sql.toString(), params, 1, 0);
            return UteisValidacao.emptyList(avaliacaoFisicas) ? null : avaliacaoFisicas.get(0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ItemAvaliacaoFisica> obterItensAvaliacaoFisica(final String ctx, final int cliente,
                                                               final ItemAvaliacaoFisicaEnum i, Integer limit, boolean parq,
                                                               final AvaliacaoFisica avaliacao) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("SELECT obj FROM ItemAvaliacaoFisica obj \n");
            sql.append(" WHERE obj.cliente.codigo = :cliente AND obj.item = :i \n");
            HashMap params = new HashMap<String, Object>();
            if (avaliacao != null) {
                sql.append(" AND obj.avaliacaoFisica.codigo = :avaliacao \n");
                params.put("avaliacao", avaliacao.getCodigo());
            }
            if (ItemAvaliacaoFisicaEnum.ANAMNESE.equals(i) && parq) {
                sql.append(" AND obj.anamnese.parq IS TRUE \n");
            } else if (ItemAvaliacaoFisicaEnum.ANAMNESE.equals(i) && !parq) {
                sql.append(" AND (obj.anamnese.parq IS NULL OR obj.anamnese.parq IS FALSE) \n");
            }
            sql.append(" ORDER by obj.codigo DESC");

            params.put("cliente", cliente);
            params.put("i", i);
            return itemDao.findByParam(ctx, sql.toString(), params, limit, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ItemAvaliacaoFisica> obterItensAvaliacaoIntegrada(final String ctx, final int cliente) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("SELECT obj FROM ItemAvaliacaoFisica obj ");
            sql.append(" WHERE obj.cliente.codigo = :cliente AND obj.item = :i ");
            sql.append(" ORDER by obj.dataResposta DESC, obj.codigo DESC");
            HashMap params = new HashMap<String, Object>();
            params.put("cliente", cliente);
            params.put("i", ItemAvaliacaoFisicaEnum.AVALIACAO_INTEGRADA);
            return itemDao.findByParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ItemAvaliacaoFisica obterItemAvaliacaoPorAnamnese(final String ctx, final int anamneseId) throws ServiceException {
        try {
            return itemDao.obterItemAvaliacaoPorAnamnese(ctx, anamneseId);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<PesoOsseo> obterItensPesoOsseo(final String ctx, final int cliente) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("SELECT obj FROM PesoOsseo obj WHERE obj.cliente.codigo = :cliente ");
            sql.append(" ORDER by obj.codigo DESC ");
            HashMap params = new HashMap<String, Object>();
            params.put("cliente", cliente);
            return pesoOsseoDao.findByParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public PesoOsseo calcularPesoOsseo(final PesoOsseo po, boolean masculino) throws ServiceException {
        return calcularPesoOsseo(po, masculino, null);
    }

    public PesoOsseo calcularPesoOsseo(final PesoOsseo po, boolean masculino, AvaliacaoFisica avaliacaoFisica) throws ServiceException {
        try {
            if (avaliacaoFisica == null) {
                po.setPesoOsseo(Math.pow((Math.pow(po.getAltura(), 2) * (po.getDiametroPunho() / 100) * (po.getDiametroFemur() / 100) * 400), 0.712) * 3.02);
            } else {
                po.setPesoOsseo(avaliacaoFisica.getPesoOsseo());
            }
            if (masculino) {
                po.setPesoResidual(po.getPeso() * 0.24);
            } else {
                po.setPesoResidual(po.getPeso() * 0.21);
            }
            po.setPesoGordura(po.getPeso() * (po.getPercentualGordura() / 100));
            po.setPesoMuscular(po.getPeso() - po.getPesoResidual() - po.getPesoOsseo() - po.getPesoGordura());

            return po;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ResultadoResistenciaEnum calcularResistencia(ItemAvaliacaoFisicaEnum tipo, Integer idade, Integer valor) throws Exception {
        try {
            valor = valor == null ? 0 : valor;
            for (RMLEnum r : RMLEnum.values()) {
                if (idade >= r.getIdadeMin() && idade <= r.getIdadeMax() && r.getTipo().equals(tipo)) {
                    if (valor >= r.getExcelente()) {
                        return ResultadoResistenciaEnum.EXCELENTE;
                    } else if (valor >= r.getAcimaMedia() && valor < r.getExcelente()) {
                        return ResultadoResistenciaEnum.ACIMA_MEDIA;
                    } else if (valor >= r.getMedia() && valor < r.getAcimaMedia()) {
                        return ResultadoResistenciaEnum.NA_MEDIA;
                    } else if (valor >= r.getAbaixoMedia() && valor < r.getMedia()) {
                        return ResultadoResistenciaEnum.ABAIXO_MEDIA;
                    } else if (valor >= r.getFraco() && valor < r.getAbaixoMedia()) {
                        return ResultadoResistenciaEnum.FRACO;
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ResultadoResistenciaEnum calcularResistenciaHomem(ItemAvaliacaoFisicaEnum tipo, Integer idade, Integer valor, SexoEnum sexo) throws Exception {
        try {
            if (sexo == SexoEnum.M) {
                valor = valor == null ? 0 : valor;
                for (RMLEnum r : RMLEnum.values()) {
                    if (r.name().contains("_HOMEM") && r.getTipo().equals(tipo) &&
                            idade >= r.getIdadeMin() && idade <= r.getIdadeMax()) {
                        if (valor >= r.getExcelente()) {
                            return ResultadoResistenciaEnum.EXCELENTE;
                        } else if (valor >= r.getAcimaMedia() && valor < r.getExcelente()) {
                            return ResultadoResistenciaEnum.ACIMA_MEDIA;
                        } else if (valor >= r.getMedia() && valor < r.getAcimaMedia()) {
                            return ResultadoResistenciaEnum.NA_MEDIA;
                        } else if (valor >= r.getAbaixoMedia() && valor < r.getMedia()) {
                            return ResultadoResistenciaEnum.ABAIXO_MEDIA;
                        } else if (valor >= r.getFraco() && valor < r.getAbaixoMedia()) {
                            return ResultadoResistenciaEnum.FRACO;
                        }
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ResultadoResistenciaEnum calcularResistenciaMulher(ItemAvaliacaoFisicaEnum tipo, Integer idade, Integer valor, SexoEnum sexo) throws Exception {
        try {
            if (sexo == SexoEnum.F) {
                valor = valor == null ? 0 : valor;
                for (RMLEnum r : RMLEnum.values()) {
                    if (r.name().contains("_MULHER") && r.getTipo().equals(tipo) &&
                            idade >= r.getIdadeMin() && idade <= r.getIdadeMax()) {
                        if (valor >= r.getExcelente()) {
                            return ResultadoResistenciaEnum.EXCELENTE;
                        } else if (valor >= r.getAcimaMedia() && valor < r.getExcelente()) {
                            return ResultadoResistenciaEnum.ACIMA_MEDIA;
                        } else if (valor >= r.getMedia() && valor < r.getAcimaMedia()) {
                            return ResultadoResistenciaEnum.NA_MEDIA;
                        } else if (valor >= r.getAbaixoMedia() && valor < r.getMedia()) {
                            return ResultadoResistenciaEnum.ABAIXO_MEDIA;
                        } else if (valor >= r.getFraco() && valor < r.getAbaixoMedia()) {
                            return ResultadoResistenciaEnum.FRACO;
                        }
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void calcularResultadoMovimento(List<PerguntaAnamnese> perguntas, ItemAvaliacaoFisica item, List<Movimento3D> movimento3DS) {
        Double resultado = perguntas == null ? 0.0 : somarPeso(perguntas);
        item.setSomaMobilidadeDir(0);
        item.setSomaMobilidadeEsq(0);
        item.setSomaEstabilidadeDir(0);
        item.setSomaEstabilidadeEsq(0);
        for (Movimento3D m3d : movimento3DS) {
            if (m3d.getMovimento().getTipo().equals(TipoMovimento3DEnum.mobilidade)) {
                item.setSomaMobilidadeDir(m3d.getDireita() + item.getSomaMobilidadeDir());
                item.setSomaMobilidadeEsq(m3d.getEsquerda() + item.getSomaMobilidadeEsq());
            } else {
                item.setSomaEstabilidadeDir(m3d.getDireita() + item.getSomaEstabilidadeDir());
                item.setSomaEstabilidadeEsq(m3d.getEsquerda() + item.getSomaEstabilidadeEsq());
            }
        }
        item.setMediaEstabilidadeDir(Uteis.arredondar(item.getSomaEstabilidadeDir() / 3.0, 1));
        item.setMediaEstabilidadeEsq(Uteis.arredondar(item.getSomaEstabilidadeEsq() / 3.0, 1));

        item.setMediaMobilidadeDir(Uteis.arredondar(item.getSomaMobilidadeDir() / 4.0, 1));
        item.setMediaMobilidadeEsq(Uteis.arredondar(item.getSomaMobilidadeEsq() / 4.0, 1));

        resultado = item.getMediaEstabilidadeDir() + item.getMediaEstabilidadeEsq()
                + item.getMediaMobilidadeDir() + item.getMediaMobilidadeEsq()
                + resultado;
        item.setSomaQualidadeMovimento(resultado);
        item.setQualidadeMovimento(ResultadoMovimentoEnum.getResult(resultado));

    }

    public void calcularResultadoVida(List<PerguntaAnamnese> perguntas, ItemAvaliacaoFisica item) {
        Double resultado = perguntas == null ? 0.0 : somarPeso(perguntas);
        item.setSomaQualidadeVida(resultado.intValue());
        item.setQualidadeVida(ResultadoVidaEnum.getResult(resultado.intValue()));
    }

    public Double somarPeso(List<PerguntaAnamnese> perguntas) {
        Double resultado = 0.0;
        for (PerguntaAnamnese pa : perguntas) {
            for (OpcaoPergunta op : pa.getPergunta().getOpcoes()) {
                if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                    if (pa.getRespostas() == null) {
                        continue;
                    }
                    for (String r : pa.getRespostas()) {
                        if (r.equals(op.getCodigo().toString())) {
                            resultado += op.getPeso();
                        }
                    }
                } else {
                    if (pa.getResposta() != null && pa.getResposta().equals(op.getCodigo().toString())) {
                        resultado += op.getPeso();
                    }
                }
            }
        }
        return resultado;
    }

    public void gravarMovimento3D(final String key, final ItemAvaliacaoFisica item, final List<Movimento3D> movimento3DS) throws Exception {
        for (Movimento3D m3d : movimento3DS) {
            m3d.setItem(item);
            m3dDao.insert(key, m3d);
        }
    }

    public List<Movimento3D> obterMovimento3D(final String key, final ItemAvaliacaoFisica item) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT obj FROM Movimento3D obj WHERE obj.item.codigo = :a ");
        sql.append(" ORDER by obj.codigo ");
        HashMap params = new HashMap<String, Object>();
        params.put("a", item.getCodigo());
        return m3dDao.findByParam(key, sql.toString(), params);
    }

    public ItemAvaliacaoFisica consultarItemCodigo(final String key, final Integer codigo) throws Exception {
        return itemDao.findById(key, codigo);
    }

    public ItemAvaliacaoFisica montarVisualizacao(final String key, final Integer i) throws Exception {
        ItemAvaliacaoFisica item = consultarItemCodigo(key, i);
        Anamnese avaliacao = new Anamnese(item.getAnamnese());
        List<PerguntaAnamnese> perguntaAnamneseList = anamneseService.obterPerguntasAnamnese(key, avaliacao.getCodigo());
        avaliacao.setPerguntas(new ArrayList<PerguntaAnamnese>());
        for (PerguntaAnamnese pa : perguntaAnamneseList) {
            if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                    || pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                pa.getPergunta().setOpcoes(anamneseService.obterOpcoes(key, pa.getPergunta().getCodigo()));
                if (pa.getPergunta().getAgrupamento() != null &&
                        !pa.getPergunta().getAgrupamento().equals(AgrupamentoAvaliacaoIntegradaEnum.GERAL)) {
                    pa.getPergunta().setOpcoes(Ordenacao.ordenarLista(pa.getPergunta().getOpcoes(), "peso"));
                }
            }
            avaliacao.getPerguntas().add(new PerguntaAnamnese(pa));
        }
        obterRespostas(key, avaliacao, item);
        List<Movimento3D> movimento3DS = obterMovimento3D(key, item);
        item.setMobilidade(new ArrayList<Movimento3D>());
        item.setEstabilidade(new ArrayList<Movimento3D>());
        for (Movimento3D m : movimento3DS) {
            if (m.getMovimento().getTipo().equals(TipoMovimento3DEnum.mobilidade)) {
                item.getMobilidade().add(m);
            } else {
                item.getEstabilidade().add(m);
            }
        }
        item.setAnamnese(avaliacao);
        List<PerguntaAnamnese> paMovimento = new ArrayList<PerguntaAnamnese>();
        List<PerguntaAnamnese> paVida = new ArrayList<PerguntaAnamnese>();
        for (PerguntaAnamnese pa : avaliacao.getPerguntas()) {
            if (AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_VIDA.equals(pa.getPergunta().getAgrupamento())) {
                paVida.add(pa);
            } else if (AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_MOVIMENTO.equals(pa.getPergunta().getAgrupamento())) {
                paMovimento.add(pa);
            }
        }

        calcularResultadoMovimento(paMovimento, item, movimento3DS);
        calcularResultadoVida(paVida, item);

        return item;
    }

    public void obterRespostas(String ctx, Anamnese a, ItemAvaliacaoFisica i) throws Exception {
        for (PerguntaAnamnese pa : a.getPerguntas()) {
            if(pa.getResposta() == null) {
                pa.setResposta("Sem resposta");
            }
            RespostaCliente respostaCliente = anamneseService.obterRespostaPerguntasAnamnese(
                    ctx, pa.getCodigo(), i.getCliente().getCodigo(), i.getCodigo());
            if (respostaCliente == null) {
                continue;
            }
            pa.getPergunta().setRespondida(true);
            pa.setComplemento(respostaCliente.getObs() == null ? "" : respostaCliente.getObs());
            setarResposta(pa, respostaCliente);
        }
    }

    @Override
    public void enviarEmailAvaliacao(final String key,
                                     final ClienteSintetico cliente,
                                     final String nomeEmpresa,
                                     final AvaliacaoFisica avaliacao,
                                     Flexibilidade flexibilidade,
                                     PesoOsseo pesoOsseo,
                                     Ventilometria ventilometria,
                                     final Anamnese anamnese,
                                     final Anamnese questionarioParq,
                                     final Usuario usuario, ViewUtils viewUtils,
                                     HttpServletRequest request, ServletContext servletContext, final String language) throws Exception {

        String pdf = gerarPDFAvaliacaoFisica(key, avaliacao, flexibilidade, pesoOsseo, ventilometria, anamnese, questionarioParq, usuario, viewUtils, request, servletContext, true, true, language);
        File arquivo = new File(pdf);
        //obter configurações do envio de email
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);

        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);

        JSONObject configsEmail = integracaoWS.configsEmail(url);
        UteisEmail u = new UteisEmail();

        u.addAnexo("AvaliacaoFisica_Treino_" + cliente.getNome() + ".pdf", arquivo);

        u.preencherConfiguracaoEmailPadrao(configsEmail, nomeEmpresa);

        JSONObject json = new JSONObject();
        json.put("k", key);
        json.put("a", avaliacao.getCodigo());

        String urlAplicacao = Aplicacao.getProp(Aplicacao.discoveryUrls) + "/redir" + "/" + key + "/treino?" + "m=imprimiravaliacao&j=" + Uteis.encriptar(json.toString(), "cript0p4r4msint");
        String dataAvaliacao = Uteis.getData(avaliacao.getDataAvaliacao());
        if (!SuperControle.independente(key) && usuario != null && UteisValidacao.emptyList(usuario.getEmpresasZW())) {
            usuario.setEmpresasZW(UtilContext.getBean(AdmWSConsumer.class).obterEmpresas(key));
        }
        Map<String, Object> paramsConteudoEmailImpressao = prepareParamsConteudoEmailImpressao(avaliacao, usuario, servletContext, key);
        String a = novoModeloConteudoEmailAvaliacao(paramsConteudoEmailImpressao);

        List<String> listaEmails = cliente.getListaEmails();
        String[] emails = new String[listaEmails.size()];
        emails = listaEmails.toArray(emails);
        for (String e : emails) {
            u.enviarEmail(e, cliente.getNome(), a, "", "Avaliação Física");
        }
    }


    public String montarLinksWpp(Integer avaliacaoId, String telefone, HttpServletRequest request, String idiomaBanco) throws Exception {

        String ctx = sessaoService.getUsuarioAtual().getChave();

        JSONObject json = new JSONObject();
        json.put("k", ctx);
        json.put("a", avaliacaoId);
        json.put("i", idiomaBanco);

        String urlAplicacao = Aplicacao.getProp(Aplicacao.discoveryUrls) + "/redir" + "/" + ctx + "/treino?" + "m=imprimiravaliacao&j=" + Uteis.encriptar(json.toString(), "cript0p4r4msint");

        telefone = telefone.replaceAll("[()]", "");

        String linkWpp = "https://web.whatsapp.com/send?phone=55"
                + Uteis.removerMascara(telefone.replaceAll(" ", "")).replaceAll("\\(", "").replaceAll("\\)", "")
                + "&text=" + URLEncoder.encode(urlAplicacao, "UTF-8").replaceAll("\\+", "%20");

        return linkWpp;

    }

    @Override
    public String montarLinksComparativoWpp(Integer alunoId, List<Integer> avaliacoes, HttpServletRequest request, ViewUtils viewUtils) throws Exception {

        String ctx = sessaoService.getUsuarioAtual().getChave();
        ClienteSintetico clienteSintetico = clienteSinteticoService.obterPorId(ctx, alunoId);
        Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
        String telefone = "";
        for (String tel : clienteSintetico.getListaTelefones()) {
            tel = Uteis.removerMascara(tel.replaceAll(" ", "")).replaceAll("\\(", "").replaceAll("\\)", "");
            if (isTelefoneCelular(tel) && UteisValidacao.emptyString(telefone)) {
                telefone = tel;
            }
        }

        List<AvaliacaoFisica> listaAvaliacao = new ArrayList<>();
        for (Integer i : avaliacoes) {
            AvaliacaoFisica avaliacaoFisica = new AvaliacaoFisica();
            avaliacaoFisica = obterPorId(ctx, i);
            listaAvaliacao.add(avaliacaoFisica);
        }

        String url = gerarPDFComparativo(
                ctx, listaAvaliacao, usuario, viewUtils, request,
                null, false);
        String linkWpp = "https://web.whatsapp.com/send?phone=55"
                + Uteis.removerMascara(telefone.replaceAll(" ", "")).replaceAll("\\(", "").replaceAll("\\)", "")
                + "&text=" + URLEncoder.encode(url, "UTF-8").replaceAll("\\+", "%20");

        return linkWpp;

    }

    public boolean isTelefoneCelular(String telefone) {
        return telefone.matches("[1-9][1-9]9?[6-9][0-9]{3}[0-9]{4}");
        //(".((10)|([1-9][1-9]).)\\s9?[6-9][0-9]{3}-[0-9]{4}");
    }

    public List<ResultadoVO2> calcularVo2Teste2400(AvaliacaoFisica avaliacao, boolean homem, Integer idade) throws Exception {
        List<ResultadoVO2> lista = new ArrayList<ResultadoVO2>();

        avaliacao.setTempo2400(avaliacao.getTempo2400() == null ?
                "00:00" : avaliacao.getTempo2400());
        int segundos = Uteis.converterMinutosEmSegundos(avaliacao.getTempo2400());
        avaliacao.setVo2Max2400(segundos == 0 ? 0.0 : ((2400 * 60 * 0.2) + 3.5) / (segundos));
        avaliacao.setVo2Max2400(Uteis.forcarCasasDecimais(4, avaliacao.getVo2Max2400() < 0.0 ? 0.0 : avaliacao.getVo2Max2400()));
        lista.add(new ResultadoVO2(1, "muito.fraca", "+16:30", "+17:30", "+18:30", "+19:00", "", ""));
        lista.add(new ResultadoVO2(1, "FRACA", "16:30 - 14:31", "17:30 - 15:31", "18:30 - 16:31", "19:00 - 17:01", "", ""));
        lista.add(new ResultadoVO2(1, "MEDIA", "14:30 - 12:01", "15:30 - 13:01", "16:30 - 14:01", "17:00 - 14:31", "", ""));
        lista.add(new ResultadoVO2(1, "BOA", "12:00 - 10:16", "13:00 - 11:01", "14:00 - 11:39", "14:30 - 12:01", "", ""));
        lista.add(new ResultadoVO2(1, "EXCELENTE", "-10:15", "-11:00", "-11:38", "-12:00", "", ""));
        classResult2400(idade, segundos, lista);
        return lista;
    }

    public void calcularVo2Queens(AvaliacaoFisica avaliacao, boolean homem) throws Exception {
//        VO² máx Homens = 111,33 - (0,42 x FC do final do teste )= VO² em ml 1/(kg.min)
        if (homem) {
            avaliacao.setVo2MaxQueens(Uteis.arredondarForcando2CasasDecimais(111.33 - (0.42 * avaliacao.getFcQueens())));
        } else {
//                VO² máx Mulheres = 65,81 - (0,1847 x FC do final do teste )= VO² em ml 1/(kg.min)
            avaliacao.setVo2MaxQueens(Uteis.arredondarForcando2CasasDecimais(65.81 - (0.1847 * avaliacao.getFcQueens())));
        }


    }

    public void calcularVo2Astrand(AvaliacaoFisica avaliacao, boolean homem) throws Exception {
        double v = 0.0;
        if (UteisValidacao.emptyNumber(avaliacao.getCargaAstrand())
                || UteisValidacao.emptyNumber(avaliacao.getFcAstrand4()) || UteisValidacao.emptyNumber(avaliacao.getFcAstrand5())) {
            avaliacao.setVo2Astrand(0.0);
            avaliacao.setVo2MaxAstrand(0.0);
            return;
        }
        double kg = avaliacao.getCargaAstrand();
        double pr = (avaliacao.getFcAstrand4() + avaliacao.getFcAstrand5()) / 2;
        if (homem) {
            if (kg < 750) {
                double r1 = 15.614286 + (pr * -0.148869) + (pr * pr * 0.0003869);
                double r2 = 17.9 + (pr * -0.164167) + (pr * pr * 0.0004167);
                v = r1 + (((r2 - r1) / 300) * (kg - 450));
            } else {
                double r2 = 17.142857 + (pr * -0.143274) + (pr * pr * 0.0003274);
                double r1 = 17.9 + (pr * -0.164167) + (pr * pr * 0.0004167);
                v = r1 + (((r2 - r1) / 150) * (kg - 750));
            }
        } else {
            if (kg <= 600) {
                double r1 = 15.754286 + (pr * -0.154577) + (pr * pr * 0.0004077);
                double r2 = 25.235714 + (pr * -0.265089) + (pr * pr * 0.0007589);
                v = r1 + (((r2 - r1) / 300) * (kg - 300));
            } else {
                double r2 = 25.371429 + (pr * -0.251845) + (pr * pr * 0.0006845);
                double r1 = 25.235714 + (pr * -0.265089) + (pr * pr * 0.0007589);
                v = r1 + (((r2 - r1) / 150) * (kg - 600));
            }
        }
        avaliacao.setVo2Astrand(Uteis.arredondarForcando2CasasDecimais(nt2dp(v, 2)));
        double peso = avaliacao.getPeso();
        if (peso != 0) {
            avaliacao.setVo2MaxAstrand(Uteis.arredondarForcando2CasasDecimais(nt2dp((v * 1000 / peso), 2)));
        } else {
            avaliacao.setVo2MaxAstrand(0.0);
        }
    }

    public double nt2dp(Double num, double dp) {
        num = num * 1 + (0.55 / Math.pow(10, dp));
        if (dp > 0) dp = dp + 1;
//        int b = new Double(Math.floor(num)).toString().length() + dp;
        return num;
    }

    public List<ResultadoVO2> calcularVo212Minutos(AvaliacaoFisica avaliacao, boolean homem, Integer idade) throws Exception {
        List<ResultadoVO2> lista = new ArrayList<ResultadoVO2>();
        avaliacao.setDistancia12(avaliacao.getDistancia12() == null ?
                0.0 : avaliacao.getDistancia12());
        avaliacao.setVo2Max12((avaliacao.getDistancia12() - 504.9) / 44.73);
        avaliacao.setVo2Max12(Uteis.forcarCasasDecimais(4, avaliacao.getVo2Max12() < 0.0 ? 0.0 : avaliacao.getVo2Max12()));
        if (homem) {
            lista.add(new ResultadoVO2(1, "muito.fraca", "-2090", "-1960", "-1900", "-1830", "-1660", "-1400"));
            lista.add(new ResultadoVO2(2, "FRACA", "2091-2200", "1961-2110", "1901-2090", "1831-1990", "1661-1870", "1401-1640"));
            lista.add(new ResultadoVO2(3, "MEDIA", "2201-2510", "2111-2400", "2091-2400", "1991-2240", "1871-2090", "1641-1930"));
            lista.add(new ResultadoVO2(4, "BOA", "2511-2770", "2401-2640", "2401-2510", "2241-2460", "2091-2320", "1931-2120"));
            lista.add(new ResultadoVO2(5, "EXCELENTE", "2771-3000", "2641-2830", "2511-2720", "2461-2660", "2321-2540", "2121-2490"));
            lista.add(new ResultadoVO2(6, "superior", "3000+", "2830+", "2720+", "2660+", "2540+", "2490+"));
        } else {
            lista.add(new ResultadoVO2(1, "muito.fraca", "-1610", "-1550", "-1510", "-1420", "-1350", "-1260"));
            lista.add(new ResultadoVO2(2, "FRACA", "1611-1900", "1551-1790", "1511-1690", "1421-1580", "1351-1500", "1261-1390"));
            lista.add(new ResultadoVO2(3, "MEDIA", "1901-2080", "1791-1970", "1691-1960", "1581-1790", "1501-1690", "1391-1590"));
            lista.add(new ResultadoVO2(4, "BOA", "2081-2300", "1971-2160", "1961-2080", "1791-2000", "1691-1900", "1591-1750"));
            lista.add(new ResultadoVO2(5, "EXCELENTE", "2301-2430", "2161-2330", "2081-2240", "2001-2160", "1901-2090", "1751-1900"));
            lista.add(new ResultadoVO2(6, "superior", "2430+", "2330+", "2240+", "2160+", "2090+", "1900+"));
        }
        classResult(idade, avaliacao.getDistancia12() == null ? 0 : avaliacao.getDistancia12().intValue(), lista);
        return lista;
    }

    public List<ResultadoVO2> calcularVoAerobicoDeBanco(AvaliacaoFisica avaliacaoFisica, boolean homem, Integer idade) throws Exception {
        List<ResultadoVO2> lista = new ArrayList<ResultadoVO2>();

        avaliacaoFisica.setVoMaxAerobico(avaliacaoFisica.getVoMaxAerobico() == null ? 0.0 : avaliacaoFisica.getVoMaxAerobico());
        if (homem) {
            avaliacaoFisica.setVoMaxAerobico(111.33 - (0.42 * avaliacaoFisica.getFcQueens()));
            lista.add(new ResultadoVO2(1, "condicao.atletica", ">=49", ">=48", ">=45", ">=42", ">=39"));
            lista.add(new ResultadoVO2(2, "faixa.recomendavel", "42-48", "40-47", "38-44", "35-41", "31-38"));
            lista.add(new ResultadoVO2(3, "baixa.aptidao", "38-41", "36-39", "34-37", "31-34", "27-30"));
            lista.add(new ResultadoVO2(4, "condicao.de.risco", "<=37", "<=35", "<=33", "<=30", "<=26"));
        } else {
            avaliacaoFisica.setVoMaxAerobico(65.81 - (0.1847 * avaliacaoFisica.getFcQueens()));
            lista.add(new ResultadoVO2(1, "condicao.atletica", "42+", "40+", "37+", "33+", "32+"));
            lista.add(new ResultadoVO2(2, "faixa.recomendavel", "35-41", "33-39", "31-36", "28-32", "26-31"));
            lista.add(new ResultadoVO2(3, "baixa.aptidao", "32-34", "30-32", "28-30", "25-27", "24-25"));
            lista.add(new ResultadoVO2(4, "condicao.de.risco", "<=31", "<=29", "<=27", "<=24", "<=23"));
        }
        classResultAerobico(idade, avaliacaoFisica.getVoMaxAerobico() == null ? 0 : avaliacaoFisica.getVoMaxAerobico().intValue(), lista);
        return lista;
    }

    private void classResultAerobico(Integer idade, Integer bpm, List<ResultadoVO2> lista) {
        for (ResultadoVO2 r : lista) {
            if (idade >= 20 && idade <= 29) {
                if (teste(r.getI1(), bpm, false)) {
                    r.setResult(" escolhido vo_1");
                }
            } else if (idade >= 30 && idade <= 39) {
                if (teste(r.getI2(), bpm, false)) {
                    r.setResult(" escolhido vo_2");
                }
            } else if (idade >= 40 && idade <= 49) {
                if (teste(r.getI3(), bpm, false)) {
                    r.setResult(" escolhido vo_3");
                }
            } else if (idade >= 50 && idade <= 59) {
                if (teste(r.getI4(), bpm, false)) {
                    r.setResult(" escolhido vo_4");
                }
            } else if (idade > 60) {
                if (teste(r.getI5(), bpm, false)) {
                    r.setResult(" escolhido vo_5");
                }
            }
        }
    }

    private void classResult(Integer idade, Integer distancia, List<ResultadoVO2> lista) {
        for (ResultadoVO2 r : lista) {
            if (idade <= 19) {
                if (teste(r.getI1(), distancia, false)) {
                    r.setResult(" escolhido vo_1 ");
                }
            } else if (idade > 19 && idade <= 29) {
                if (teste(r.getI2(), distancia, false)) {
                    r.setResult(" escolhido vo_2 ");
                }
            } else if (idade > 29 && idade <= 39) {
                if (teste(r.getI3(), distancia, false)) {
                    r.setResult(" escolhido vo_3 ");
                }
            } else if (idade > 39 && idade <= 49) {
                if (teste(r.getI4(), distancia, false)) {
                    r.setResult(" escolhido vo_4 ");
                }
            } else if (idade > 49 && idade <= 59) {
                if (teste(r.getI5(), distancia, false)) {
                    r.setResult(" escolhido vo_5 ");
                }
            } else if (idade > 59) {
                if (teste(r.getI6(), distancia, false)) {
                    r.setResult(" escolhido vo_6 ");
                }
            }
        }
    }

    private void classResult2400(Integer idade, Integer tempo, List<ResultadoVO2> lista) {
        for (ResultadoVO2 r : lista) {
            if (idade < 30) {
                if (teste(r.getI1(), tempo, true)) {
                    r.setResult(" escolhido vo_1 ");
                }
            } else if (idade > 29 && idade <= 39) {
                if (teste(r.getI3(), tempo, true)) {
                    r.setResult(" escolhido vo_2 ");
                }
            } else if (idade > 39 && idade <= 49) {
                if (teste(r.getI4(), tempo, true)) {
                    r.setResult(" escolhido vo_3 ");
                }
            } else if (idade > 49) {
                if (teste(r.getI6(), tempo, true)) {
                    r.setResult(" escolhido vo_4 ");
                }
            }
        }
    }

    private boolean teste(String valor, Integer distancia, Boolean minutos) {
        if (minutos) {
            if (valor.startsWith("-")) {
                int segundoslimite = Uteis.converterMinutosEmSegundos(valor.replace("-", ""));
                return distancia < segundoslimite;
            } else if (valor.startsWith("+")) {
                int segundoslimite = Uteis.converterMinutosEmSegundos(valor.replace("+", ""));
                return distancia > segundoslimite;
            } else if (valor.startsWith("<=")) {
                int segundoslimite = Uteis.converterMinutosEmSegundos(valor.replace("<=", ""));
                return distancia <= segundoslimite;
            } else if (valor.startsWith(">=")) {
                int segundoslimite = Uteis.converterMinutosEmSegundos(valor.replace(">=", ""));
                return distancia >= segundoslimite;
            } else {
                if (UteisValidacao.emptyString(valor)) {
                    return false;
                }
                String[] values = valor.split("-");
                int maior = Uteis.converterMinutosEmSegundos(values[0]);
                int menor = Uteis.converterMinutosEmSegundos(values[1]);
                return distancia >= menor && distancia <= maior;
            }

        } else {
            if (valor.startsWith("-")) {
                return distancia < Integer.valueOf(valor.replace("-", ""));
            } else if (valor.endsWith("+")) {
                return distancia > Integer.valueOf(valor.replace("+", ""));
            } else if (valor.startsWith("<=")) {
                return distancia <= Integer.valueOf(valor.replace("<=", ""));
            } else if (valor.startsWith(">=")) {
                return distancia >= Integer.valueOf(valor.replace(">=", ""));
            } else {
                String[] values = valor.split("-");
                int inicio = Integer.valueOf(values[0]);
                int fim = Integer.valueOf(values[1]);
                return distancia >= inicio && distancia <= fim;
            }
        }

    }

    public AvaliacaoFisicaJSON montarJSON(final String chave, final ClienteSintetico clienteSintetico, final AvaliacaoFisica avaliacaoFisica) throws Exception {
        AvaliacaoFisicaJSON avaliacaoFisicaJSON = new AvaliacaoFisicaJSON();

        avaliacaoFisicaJSON.setId(avaliacaoFisica.getCodigo());

        ResultadoResistenciaEnum abdomen = obterResultadoResistencia(chave, clienteSintetico, obterEnumResistenciaPorConfiguracao(chave, clienteSintetico.getSexo(), TipoResistenciaMuscular.ABDOMEN), avaliacaoFisica);
        ResultadoResistenciaEnum braco = obterResultadoResistencia(chave, clienteSintetico, obterEnumResistenciaPorConfiguracao(chave, clienteSintetico.getSexo(), TipoResistenciaMuscular.BRACO), avaliacaoFisica);
        avaliacaoFisicaJSON.setRmlAbdomen(abdomen == null ? "" : getViewUtils().getLabel(abdomen.name()));
        avaliacaoFisicaJSON.setRmlBracos(braco == null ? "" : getViewUtils().getLabel(braco.name()));

        PesoOsseo po = obterPesoOsseo(chave, clienteSintetico.getCodigo(), avaliacaoFisica);
        if (po == null) {
            po = new PesoOsseo();
            po.setAltura(avaliacaoFisica.getAltura());
            po.setPeso(avaliacaoFisica.getPeso());
            po.setPercentualGordura(avaliacaoFisica.getPercentualGordura());
            if (avaliacaoFisica.getResponsavelLancamento_codigo() == null && avaliacaoFisica.getProtocolo().equals(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA)) {
                po = calcularPesoOsseo(po, clienteSintetico.isSexoMasculino(), avaliacaoFisica);
            } else {
                po = calcularPesoOsseo(po, clienteSintetico.isSexoMasculino());
            }
        }

        AvaliacaoAlunoBIDTO bidto = new AvaliacaoAlunoBIDTO();
        bidto.montarGrafico(avaliacaoFisica, po);

        avaliacaoFisicaJSON.setGordura(bidto.getGordura());
        avaliacaoFisicaJSON.setOssos(bidto.getOssos());
        avaliacaoFisicaJSON.setResiduos(bidto.getResiduos());
        avaliacaoFisicaJSON.setMusculos(bidto.getMusculos());
        avaliacaoFisicaJSON.setNaoInformado(bidto.getNaoInformado());
        avaliacaoFisicaJSON.setBioimpedancia(avaliacaoFisica.getProtocolo().equals(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA));

        avaliacaoFisicaJSON.setPercResidual(po.percentPeso(po.getPesoResidual()));
        avaliacaoFisicaJSON.setPercGordura(po.getPercentualGordura());
        avaliacaoFisicaJSON.setPercMuscular(po.percentPeso(po.getPesoMuscular()));
        avaliacaoFisicaJSON.setPercAgua(avaliacaoFisica.getPercentualAgua());

        avaliacaoFisicaJSON.setPesoGordura(po.getPesoGordura());
        avaliacaoFisicaJSON.setPesoOsseo(po.getPesoOsseo() != null && po.getPesoOsseo() != 0 ? po.getPesoOsseo() : avaliacaoFisica.getPesoOsseo());
        avaliacaoFisicaJSON.setPercOsseo(po.percentPeso(avaliacaoFisicaJSON.getPesoOsseo()));
        avaliacaoFisicaJSON.setPesoMuscular(po.getPesoMuscular());
        avaliacaoFisicaJSON.setPesoResidual(po.getPesoResidual());
        avaliacaoFisicaJSON.setCategoriaIMC(getViewUtils().getLabelInternacionalizada(avaliacaoFisica.getCategoriaApresentar(), "PT"));
        avaliacaoFisicaJSON.setLegendaIMC(legendaIMC(avaliacaoFisica.getImc()));
        avaliacaoFisicaJSON.setImc(avaliacaoFisica.getImc());
        avaliacaoFisicaJSON.setCategoriaGordura(getViewUtils().getLabelInternacionalizada(avaliacaoFisica.getCategoriaPercGordApresentar(), "PT"));
        avaliacaoFisicaJSON.setPeso(avaliacaoFisica.getPeso());
        avaliacaoFisicaJSON.setAltura(avaliacaoFisica.getAltura());

        avaliacaoFisicaJSON.setIdadeMetabolica(avaliacaoFisica.getIdadeMetabolica());
        avaliacaoFisicaJSON.setDataAtual(Uteis.getData(avaliacaoFisica.getDataAvaliacao()));
        avaliacaoFisicaJSON.setDataAtualLong(avaliacaoFisica.getDataAvaliacao() == null ? null : avaliacaoFisica.getDataAvaliacao().getTime());

        if (avaliacaoFisica.getDataProxima() != null) {
            avaliacaoFisicaJSON.setDataProxima(Uteis.getData(avaliacaoFisica.getDataProxima()));
            avaliacaoFisicaJSON.setDataProximaLong(avaliacaoFisica.getDataProxima().getTime());
        }

        Usuario usu = usuarioService.obterPorId(chave, avaliacaoFisica.getResponsavelLancamento_codigo());
        avaliacaoFisicaJSON.setAvaliador(usu == null ?
                "BALANÇA DE BIOIMPEDÂNCIA" :
                usu.getProfessor().getNome());

        avaliacaoFisicaJSON.setDobras(new ArrayList<DobraCutaneaJSON>());

        avaliacaoFisicaJSON.getDobras().add(new DobraCutaneaJSON(getViewUtils().getLabel("cadastros.aluno.abdominal"),
                avaliacaoFisica.getAbdominal()));
        avaliacaoFisicaJSON.getDobras().add(new DobraCutaneaJSON(getViewUtils().getLabel("cadastros.aluno.suprailiaca"),
                avaliacaoFisica.getSupraIliaca()));
        avaliacaoFisicaJSON.getDobras().add(new DobraCutaneaJSON(getViewUtils().getLabel("cadastros.aluno.peitoral"),
                avaliacaoFisica.getPeitoral()));
        avaliacaoFisicaJSON.getDobras().add(new DobraCutaneaJSON(getViewUtils().getLabel("cadastros.aluno.triceps"),
                avaliacaoFisica.getTriceps()));
        avaliacaoFisicaJSON.getDobras().add(new DobraCutaneaJSON(getViewUtils().getLabel("cadastros.aluno.coxa_medial"),
                avaliacaoFisica.getCoxaMedial()));
        avaliacaoFisicaJSON.getDobras().add(new DobraCutaneaJSON(getViewUtils().getLabel("cadastros.aluno.subescapular"),
                avaliacaoFisica.getSubescapular()));
        avaliacaoFisicaJSON.getDobras().add(new DobraCutaneaJSON(getViewUtils().getLabel("cadastros.aluno.axilarMedia"),
                avaliacaoFisica.getAxilarMedia()));
        avaliacaoFisicaJSON.getDobras().add(new DobraCutaneaJSON(getViewUtils().getLabel("cadastros.aluno.biceps"),
                avaliacaoFisica.getBiceps()));

        avaliacaoFisicaJSON.setPerimetria(new ArrayList<PerimetriaJSON>());

        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.anteBraco") + " " + getViewUtils().getLabel("dir"),
                avaliacaoFisica.getAntebracoDir()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.anteBraco") + " " + getViewUtils().getLabel("esq"),
                avaliacaoFisica.getAntebracoEsq()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.bracoRelaxado") + " " + getViewUtils().getLabel("dir"),
                avaliacaoFisica.getBracoRelaxadoDir()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.bracoRelaxado") + " " + getViewUtils().getLabel("esq"),
                avaliacaoFisica.getBracoRelaxadoEsq()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.bracoContraido") + " " + getViewUtils().getLabel("dir"),
                avaliacaoFisica.getBracoContraidoDir()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.bracoContraido") + " " + getViewUtils().getLabel("esq"),
                avaliacaoFisica.getBracoContraidoEsq()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.coxaMedia") + " " + getViewUtils().getLabel("dir"),
                avaliacaoFisica.getCoxaMediaDir()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.coxaMedia") + " " + getViewUtils().getLabel("esq"),
                avaliacaoFisica.getCoxaMediaEsq()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.panturrilha") + " " + getViewUtils().getLabel("dir"),
                avaliacaoFisica.getPanturrilhaDir()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.panturrilha") + " " + getViewUtils().getLabel("esq"),
                avaliacaoFisica.getPanturrilhaEsq()));


//        cadastros.aluno.coxa_distal=Coxa Distal
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.coxa_distal") + " " + getViewUtils().getLabel("dir"),
                avaliacaoFisica.getCoxaDistalDir()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.coxa_distal") + " " + getViewUtils().getLabel("esq"),
                avaliacaoFisica.getCoxaDistalEsq()));

//        cadastros.aluno.coxa_proximal=Coxa Proximal
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.coxa_proximal") + " " + getViewUtils().getLabel("dir"),
                avaliacaoFisica.getCoxaProximalDir()));
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.coxa_proximal") + " " + getViewUtils().getLabel("esq"),
                avaliacaoFisica.getCoxaProximalEsq()));

        //        cadastros.aluno.pescoco=Pescoço
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.pescoco"),
                avaliacaoFisica.getPescoco()));
//        cadastros.aluno.ombro=Ombro
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.ombro"),
                avaliacaoFisica.getOmbro()));
//        cadastros.aluno.quadril=Quadril
        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.quadril"),
                avaliacaoFisica.getQuadril()));


        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.torax_bustoRelaxado"),
                avaliacaoFisica.getToraxBusto()));

        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.cintura"),
                avaliacaoFisica.getCintura()));

        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.gluteo"),
                avaliacaoFisica.getGluteo()));

        avaliacaoFisicaJSON.getPerimetria().add(new PerimetriaJSON(
                getViewUtils().getLabel("cadastros.aluno.circunferencia"),
                avaliacaoFisica.getCircunferenciaAbdominal()));

        if (avaliacaoFisica.getAvaliacaoPosturual() != null) {
            preencheAvaliacaoPostural(avaliacaoFisica, avaliacaoFisicaJSON);
        }

        return avaliacaoFisicaJSON;
    }

    private static void preencheAvaliacaoPostural(AvaliacaoFisica avaliacaoFisica, AvaliacaoFisicaJSON avaliacaoFisicaJSON) {
        AvaliacaoPosturalJSON avaliacaoPosturalJSON = new AvaliacaoPosturalJSON();
        avaliacaoPosturalJSON.setCodigo(avaliacaoFisica.getAvaliacaoPosturual().getCodigo());
        avaliacaoPosturalJSON.setCostas(avaliacaoFisica.getAvaliacaoPosturual().getUrlFotoCosta());
        avaliacaoPosturalJSON.setFrente(avaliacaoFisica.getAvaliacaoPosturual().getUrlFotoFrente());
        avaliacaoPosturalJSON.setEsquerdo(avaliacaoFisica.getAvaliacaoPosturual().getUrlFotoEsquerda());
        avaliacaoPosturalJSON.setDireito(avaliacaoFisica.getAvaliacaoPosturual().getUrlFotoDireita());
        avaliacaoFisicaJSON.setAvaliacaoPosturalJSON(avaliacaoPosturalJSON);
    }

    public List<SugestaoHorarioJSON> sugerirHorarios(final String chave, final Date data, final Integer professor, final Integer empresa)
            throws Exception {
        List<SugestaoHorarioJSON> horarios = new ArrayList<SugestaoHorarioJSON>();
        TipoEventoService tes = (TipoEventoService) UtilContext.getBean(TipoEventoService.class);
        List<TipoEvento> tipoEventos = tes.consultarPorTipo(chave, TipoAgendamentoEnum.AVALIACAO_FISICA);
        for (TipoEvento tipoEvento : tipoEventos) {
            if (tipoEvento.getAtivo() != null && tipoEvento.getAtivo()) {
                List<String> sugestoes = ages.sugestoesHorario(chave, professor, tipoEvento, data, empresa);
                for (String h : sugestoes) {
                    if (!h.equals("add")) {
                        horarios.add(new SugestaoHorarioJSON(tipoEvento.getCodigo(), h));
                    }
                }
            }
        }
        return horarios;
    }


    public List<AvaliadorFisicoJSON> avaliadoresFisicosDisponiveis(final String chave, final Integer empresaZw, final Date data, final HttpServletRequest request) throws Exception {
        List<AvaliadorFisicoJSON> professoresJSONS = new ArrayList<AvaliadorFisicoJSON>();
        Date dataLimite = Uteis.somarDias(data, 90);
        StringBuilder sql = new StringBuilder();
        sql.append(" select  pr.codigo as professor, pr.nome, pr.uriimagem, min(ag.inicio) as primeirodia from agendamento ag \n");
        sql.append(" inner join professorsintetico pr on pr.codigo = ag.professor_codigo \n");
        sql.append(" inner join tipoevento t on t.codigo = ag.tipoevento_codigo \n");
        sql.append(" where pr.ativo and ag.inicio > '").append(Uteis.getDataAplicandoFormatacao(data, "yyyy-MM-dd")).append(" 00:00:00'");
        sql.append(" and ag.inicio < '").append(Uteis.getDataAplicandoFormatacao(dataLimite, "yyyy-MM-dd")).append(" 23:59:59'");
        sql.append(" and t.comportamento = ").append(TipoAgendamentoEnum.AVALIACAO_FISICA.getId());
        if (!UteisValidacao.emptyNumber(empresaZw)) {
            sql.append(" and exists (\n");
            sql.append("	 select 1 from Agendamento age2\n");
            sql.append("	 inner join professorsintetico prof2 on prof2.codigo = age2.professor_codigo\n");
            sql.append("	 inner join empresa emp2 on emp2.codigo = prof2.empresa_codigo\n");
            sql.append("	 inner join tipoevento te2 on te2.codigo = age2.tipoevento_codigo\n");
            sql.append("	 where age2.disponibilidade IS true and prof2.codigo = pr.codigo\n");
            sql.append("	 and emp2.codZW = " + empresaZw + " \n");
            sql.append("	 and te2.ativo is true\n");
            sql.append("	 order by age2.inicio\n");
            sql.append(" )\n");
        }
        sql.append(" GROUP BY 1,2,3 ");
        List listOfObjects = agendamentoDao.listOfObjects(chave, sql.toString());
        for (Object o : listOfObjects) {
            Object[] arr = (Object[]) o;
            Integer prof = (Integer) arr[0];
            String nome = arr[1].toString();
            AvaliadorFisicoJSON p = new AvaliadorFisicoJSON();
            p.setCodigo(prof);
            p.setNome(nome);
            if(arr[2] != null){
                p.setAvatar(arr[2].toString());
            }
            p.setPrimeiroDia(Uteis.getData((Date) arr[3]));
            professoresJSONS.add(p);
        }
        return professoresJSONS;
    }

    public AvaliacaoFisicaJSON obterAvaliacaoAtual(final String chave, final String matricula) throws Exception {
        ClienteSintetico clienteSintetico = clienteSinteticoService.consultarPorMatricula(chave, matricula);
        AvaliacaoFisica avaliacaoFisica = obterAvaliacaoVigente(chave, clienteSintetico.getCodigo());
        if (avaliacaoFisica == null) {
            throw new Exception("aluno.sem.avaliacao");
        }
        return montarJSON(chave, clienteSintetico, avaliacaoFisica);
    }

    public List<AvaliacaoFisicaJSON> historico(final String chave, final String matricula) throws Exception {
        ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(chave, matricula);
        List<AvaliacaoFisica> avaliacaoFisicas = obterAvaliacaoCliente(chave, cliente.getCodigo());
        if (avaliacaoFisicas == null
                || avaliacaoFisicas.isEmpty()) {
            throw new Exception("aluno.sem.avaliacao");
        }
        List<AvaliacaoFisicaJSON> historico = new ArrayList<AvaliacaoFisicaJSON>();
        for (AvaliacaoFisica avaliacao : avaliacaoFisicas) {
            AvaliacaoFisicaJSON avaliacaoFisicaJSON = montarJSON(chave, cliente, avaliacao);
            try {
                List<Double> pesos;
                try (ResultSet rs = avaliacaoFisicaDao.createStatement(chave, "select dataavaliacao, peso from avaliacaofisica  \n" +
                        "where dataavaliacao::date <= '" + Uteis.getDataAplicandoFormatacao(avaliacao.getDataAvaliacao(), "yyyy-MM-dd") + "' and cliente_codigo = " +
                        cliente.getCodigo() +
                        "order by dataavaliacao desc limit 4")) {
                    pesos = new ArrayList<Double>();
                    while (rs.next()) {
                        pesos.add(rs.getDouble("peso"));
                    }
                }
                PesoOsseo po = obterPesoOsseo(chave, cliente.getCodigo(), avaliacao);
                if (po == null) {
                    po = new PesoOsseo();
                    po.setAltura(avaliacao.getAltura());
                    po.setPeso(avaliacao.getPeso());
                    po.setPercentualGordura(avaliacao.getPercentualGordura());
                    po = calcularPesoOsseo(po, cliente.isSexoMasculino());
                }
                AvaliacaoAlunoBIDTO bi = new AvaliacaoAlunoBIDTO(avaliacao, pesos, po);
                avaliacaoFisicaJSON.setPercGordura(bi.getComposicaoPorcentualGordura());
                avaliacaoFisicaJSON.setPercMuscular(bi.getComposicaoPorcentualMusculos());
                avaliacaoFisicaJSON.setPercMassaMagra(geraPorcentualMassaMagra(bi));
                avaliacaoFisicaJSON.setPercResidual(bi.getComposicaoPorcentualResiduos());
                avaliacaoFisicaJSON.setPercOsseo(bi.getComposicaoPorcentualOssos());
                avaliacaoFisicaJSON.setNaoInformado(bi.getNaoInformado());
            } catch (Exception e) {
                Uteis.logar(e, AvaliacaoFisicaImpl.class);
            }
            historico.add(avaliacaoFisicaJSON);
        }
        return historico;
    }

    private Double geraPorcentualMassaMagra(AvaliacaoAlunoBIDTO bi) {
        if(bi.getComposicaoPorcentualGordura() != null && bi.getComposicaoPorcentualGordura() != 0 && (bi.getComposicaoPorcentualMassaMagra() == null || bi.getComposicaoPorcentualMassaMagra() == 0)) {
            return 100 - bi.getComposicaoPorcentualGordura();
        }

        return bi.getComposicaoPorcentualMassaMagra();
    }

    public List<AvaliacaoFisica> historico(final String chave, final Integer cliente) throws Exception {
        return obterAvaliacaoCliente(chave, cliente);
    }

    /**
     * Obtém o enum de resistência muscular de acordo com a configuração e o sexo do cliente.
     *
     * @param ctx  chave do banco correspondente.
     * @param sexo sexo do cliente.
     * @param tipo tipo de resistência muscular (BRAÇO ou ABDOMEN).
     * @return enum de resistência muscular correspondente.
     * @throws ServiceException em caso de erro na operação.
     */
    private ItemAvaliacaoFisicaEnum obterEnumResistenciaPorConfiguracao(String ctx, String sexo, TipoResistenciaMuscular tipo) throws ServiceException {
        ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema rmlOpcoes = css.consultarPorTipo(ctx, ConfiguracoesEnum.CFG_RML_OPCOES);

        if (rmlOpcoes != null && rmlOpcoes.getValorAsBoolean()) {
            if (SexoEnum.M.getSexo().equals(sexo)) {
                return tipo == TipoResistenciaMuscular.BRACO
                        ? ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_HOMEM
                        : ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM;
            } else if (SexoEnum.F.getSexo().equals(sexo)) {
                return tipo == TipoResistenciaMuscular.BRACO
                        ? ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_MULHER
                        : ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER;
            }
        }
        return tipo == TipoResistenciaMuscular.BRACO
                ? ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO
                : ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN;
    }

    public ResultadoResistenciaEnum obterResultadoResistencia(final String chave, ClienteSintetico cliente,
                                                              ItemAvaliacaoFisicaEnum i, AvaliacaoFisica avaliacao) throws Exception {
        ItemAvaliacaoFisica item = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), i, null, avaliacao);
        if (item == null || item.getCodigo() == null || item.getCodigo() == 0) {
            return null;
        }
        if (item.getResult().contains(";")) {
            String[] split = item.getResult().split(";");
            if (ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM.equals(i) || ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER.equals(i) || ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN.equals(i)) {
                cliente.setFlexaoAbdomen(Integer.valueOf(split[0]));
            } else {
                cliente.setFlexaoBraco(Integer.valueOf(split[0]));
            }
            return ResultadoResistenciaEnum.valueOf(split[1]);
        }
        if ("0".equals(item.getResult()) || item.getResult() == null || UteisValidacao.emptyString(item.getResult())) {
            return ResultadoResistenciaEnum.FRACO;
        } else {
            return ResultadoResistenciaEnum.valueOf(item.getResult());
        }
    }

    private String legendaIMC(Double imc) {
        String mensagem = "Maior que %s e menor que %s";
        if (imc == null || imc == 0.0) {
            return "";
        } else if (imc > 0 && imc < 18.5) {
            return String.format("Menor que %s", new Object[]{18});
        } else if (imc >= 18.5 && imc < 25) {
            return String.format(mensagem, new Object[]{18, 25});
        } else if (imc >= 25 && imc < 30) {
            return String.format(mensagem, new Object[]{25, 30});
        } else if (imc >= 30 && imc < 35) {
            return String.format(mensagem, new Object[]{30, 35});
        } else if (imc >= 35 && imc < 40) {
            return String.format(mensagem, new Object[]{35, 40});
        } else if (imc >= 40) {
            return String.format("Maior que %s", new Object[]{40});
        }
        return "";
    }

    public void salvarFlexibilidade(final String ctx, final Flexibilidade flexibilidade) throws Exception {
        if (UteisValidacao.emptyNumber(flexibilidade.getCodigo())) {
            flexDao.insert(ctx, flexibilidade);
        } else {
            flexDao.update(ctx, flexibilidade);
        }

    }

    public Flexibilidade obterFlexibilidade(final String ctx, final Integer avaliacao) throws Exception {
        return flexDao.findObjectByAttribute(ctx, "avaliacao.codigo", avaliacao);
    }

    @Override
    public ItemAvaliacaoFisica adicionarItemAvaliacaoIntegrada(final String key,
                                                               final ItemAvaliacaoFisica item,
                                                               final String result,
                                                               final ClienteSintetico cliente,
                                                               final Usuario responsavel,
                                                               final Anamnese anamnese,
                                                               final Date dataResposta) throws ServiceException {
        try {
            item.setResult(result);
            item.setAnamnese(anamnese);
            item.setDataResposta(dataResposta);
            item.setItem(ItemAvaliacaoFisicaEnum.AVALIACAO_INTEGRADA);
            item.setCliente(cliente);
            item.setDataLancamento(Calendario.hoje());
            item.setResponsavelLancamento_codigo(responsavel.getCodigo());
            return itemDao.insert(key, item);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ItemAvaliacaoFisica alterarItemAvaliacaoIntegrada(final String key,
                                                             final ItemAvaliacaoFisica item,
                                                             final String result,
                                                             final Anamnese anamnese,
                                                             final Date dataResposta) throws ServiceException {
        try {
            item.setResult(result);
            item.setAnamnese(anamnese);
            item.setDataResposta(dataResposta);
            item.setItem(ItemAvaliacaoFisicaEnum.AVALIACAO_INTEGRADA);
            item.setDataLancamento(Calendario.hoje());
            return itemDao.update(key, item);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public String conteudoEmailAvaliacao(String nome, String urlAvaliacao, String dataAvaliacao, String nomeDoAvaliador, final Usuario usuario, String urlFoto) {
        StringBuilder myvar = new StringBuilder();
        myvar.append("<html><head>")
                .append("<title></title>")
                .append("<meta charset=\"UTF-8\">")
                .append("</head>")
                .append("<div style=\"font-family: Poppins; color: #2e3133; width:600px; height: 852px; background-color: #FFFFFF;margin: auto;\">")
                .append("<div style=\"width: 600px;height: 300px;position: flex;background: #2b59f1;\">")
                .append("  <div style=\"height: 212px; width: 456px; margin-left: 72px; margin-top: 88px; position: absolute; background: #ffffff;\">")
                .append("    <div style=\"logoPacto\">");
        if (urlFoto != null && !UteisValidacao.emptyString(urlFoto)) {
            myvar.append("<img style=\"height: 80px; width: 200px; padding: 70px 128px;\" src=\"").append(urlFoto).append("\">");
        } else {
            myvar.append("<img style=\"height: 80px; width: 200px; padding: 70px 128px;\" src=\"http://wiki.pactosolucoes.com.br/content/images/thumb/0/07/LOGOS_ZW_treino.png/150px-LOGOS_ZW_treino.png\">");
        }
        myvar.append("    </div>")
                .append("  </div>")
                .append("</div>")
                .append("<div style=\"height: 1014px; width: 600px; left: 1320px; top: 1032px; display: flex; background: #ffffff;\">")
                .append("  <div style=\"width: 100%; margin: 0px 72px; top: 1032px;\">")
                .append("    <p style=\"font-size: 14px; font-weight: 400; line-height: 21px; text-align: left;\">Ol&aacute;, ").append(nome).append(".</p>");
        if (dataAvaliacao != null && nomeDoAvaliador != null) {
            myvar.append("    <p style=\"font-size: 14px; font-weight: 400; line-height: 21px; text-align: left;\">Voc&ecirc; est&aacute; recebendo a sua avalia&ccedil;&atilde;o f&iacute;sica realizada em ").append(dataAvaliacao).append(" por ").append(nomeDoAvaliador).append(".");
        }
        myvar.append("    </p>")
                .append("    <div style=\"height: 90px; width: 456px; left: 72px; top: 456px; padding: 24px 0px 24px 0px; background: #fafafa;\">")
                .append("      <div style=\"padding: 24px 32px;\">")
                .append("        <p>Para visualizar, abra o anexo disponibilizado neste e-mail.")
                .append("        </p>")
                .append("      </div>")
                .append("    </div>")
                .append("    <p style=\"font-size: 12px; line-height: 18px;\">Aproveite para compartilhar este documento com o seu professor, nutricionista ou m&eacute;dico para um melhor acompanhamento dos seus resultados.")
                .append("    </p>")
                .append("    <hr style=\"background: #c7c9cc;\">")
                .append("    <p style=\"font-size: 12px; font-weight: 600; line-height: 18px; text-align: center;\">Enviado por</p>")
                .append("    <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">").append(usuario.getEmpresaDefault().getNome()).append("</p>")
                .append("    <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">").append(usuario.getEmpresaDefault().getEndereco()).append(" - ").append(usuario.getEmpresaDefault().getCidade()).append(" - ").append(usuario.getEmpresaDefault().getEstado()).append("</p>")
                .append("    <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">")
                .append("      ").append(usuario.getEmpresaDefault().getTelefone())
                .append("      ")
                .append("      ").append(usuario.getEmpresaDefault().getEmail()).append("</p>")
                .append("    <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">Desenvolvido com <span style=\"color: red;\">&hearts;</span> Pacto Solu&ccedil;&otilde;es Tecnol&oacute;gicas<br>")
                .append("      Copyright © 1999 - 2022 | Tech. People. Wellness.</p>")
                .append("  </div>")
                .append("</div>")
                .append("</div>")
                .append("</html>");
        return myvar.toString();
    }

    public String novoModeloConteudoEmailAvaliacao(Map<String, Object> params) {
        StringBuilder myvar = new StringBuilder();
        myvar.append("<html><head>")
                .append("<title></title>")
                .append("<meta charset=\"UTF-8\">")
                .append("</head>")
                .append("<div style=\"font-family: Poppins; color: #2e3133; width:600px; background-color: #FFFFFF;margin: auto;\">")
                .append("<div style=\"background: #2b59f1; padding: 72px 72px 0\">")
                .append("  <div style=\"background: #ffffff;\">")
                .append("    <div style=\"logoPacto\">");
        if (params.get("logoPadraoRelatorio") != null && params.get("logoPadraoRelatorio") != "") {
            myvar.append("<img style=\"height: 80px; width: 200px; top: 150px; border-radius: 0px; padding: 72px 128px;\" src=\"").append(params.get("logoPadraoRelatorio")).append("\">");
        } else {
            myvar.append("<img style=\"height: 80px; width: 200px; top: 150px; border-radius: 0px; padding: 72px 128px;\" src=\"https://app.pactosolucoes.com.br/midias/email_app/novo_logo_pacto.png\">");
        }
        myvar.append("    </div>")
                .append("  </div>")
                .append("</div>")
                .append("<div style=\"background: #ffffff;\">")
                .append("  <div style=\"margin: 0px 72px;\">")
                .append("    <p style=\"font-size: 14px; font-weight: 400; line-height: 21px; text-align: left;\">Ol&aacute;, ").append(params.get("nomeCliente")).append(".</p>");
        if (params.get("dataAvaliacao") != null && params.get("nomeDoAvaliador") != null) {
            myvar.append("    <p style=\"font-size: 14px; font-weight: 400; line-height: 21px; text-align: left;\">Voc&ecirc; est&aacute; recebendo a sua avalia&ccedil;&atilde;o f&iacute;sica realizada em ").append(params.get("dataAvaliacao")).append(" por ").append(params.get("nomeDoAvaliador")).append(".");
        }
        myvar.append("    </p>")
                .append("    <div style=\"padding: 24px 0px 24px 0px; background: #fafafa;\">")
                .append("      <div style=\"padding: 24px 32px;\">")
                .append("        <p>Para visualizar, abra o anexo disponibilizado neste e-mail.")
                .append("        </p>")
                .append("      </div>")
                .append("    </div>")
                .append("    <p style=\"font-size: 12px; line-height: 18px;\">Aproveite para compartilhar este documento com o seu professor, nutricionista ou m&eacute;dico para um melhor acompanhamento dos seus resultados.")
                .append("    </p>")
                .append("    <hr style=\"background: #c7c9cc;\">")
                .append("    <p style=\"font-size: 12px; font-weight: 600; line-height: 18px; text-align: center;\">Enviado por</p>")
                .append("    <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">").append(params.get("empresaNome")).append("</p>")
                .append("    <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">").append(params.get("empresaEndereco")).append(" - ").append(params.get("empresaCidade")).append(" - ").append(params.get("empresaEstado")).append("</p>")
                .append("    <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\"><img style=\"background: url('https://app.pactosolucoes.com.br/midias/email_app/pct-phone-2.png');\n" +
                        "        height: 15px;\n" +
                        "        width: 15px;border: 0;\" >")
                .append("      ").append(params.get("empresaTelefone"))
                .append("      <img style=\"background: url('https://app.pactosolucoes.com.br/midias/email_app/pct-mail-2.png');\n" +
                        "        height: 15px;\n" +
                        "        width: 15px;border: 0;\" >")
                .append("      ").append(params.get("empresaEmail")).append("</p>")
                .append("    <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">Desenvolvido com <span style=\"color: red;\">&hearts;</span> Pacto Solu&ccedil;&otilde;es Tecnol&oacute;gicas<br>")
                .append("      Copyright © 1999 - 2022 | Tech. People. Wellness.</p>")
                .append("  </div>")
                .append("</div>")
                .append("</div>")
                .append("</html>");
        return myvar.toString();
    }


    public void enviarAvaliacaoIntegrada(final String key, ItemAvaliacaoFisica i, String nomeEmpresa, Usuario usuario,
                                         String[] destinatarios,
                                         ServletContext context, HttpServletRequest request, String linkAvaliacao) throws Exception {

        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        JSONObject configsEmail = integracaoWS.configsEmail(url);
        UteisEmail u = new UteisEmail();
        u.preencherConfiguracaoEmailPadrao(configsEmail, nomeEmpresa);

        String a = "Avaliação Física " + (UteisValidacao.emptyString(nomeEmpresa) ? "" : ("- " + nomeEmpresa));
        URL urlRequest = new URL(request.getRequestURL().toString());
        String validHttps = String.valueOf(urlRequest.getPort());
        String urlAvaliacao = "";
        if (urlRequest.getPort() == -1) {
            urlAvaliacao = String.format("%s://%s%s/%s", new Object[]{
                    urlRequest.getProtocol(),
                    urlRequest.getHost(),
                    request.getContextPath(),
                    linkAvaliacao.replaceFirst("../", "")
            });
        } else {
            urlAvaliacao = String.format("%s://%s:%s%s/%s", new Object[]{
                    validHttps.startsWith("90") ? urlRequest.getProtocol() + "s" : urlRequest.getProtocol(),
                    urlRequest.getHost(),
                    urlRequest.getPort(),
                    request.getContextPath(),
                    linkAvaliacao.replaceFirst("../", "")
            });
        }
        for (String e : destinatarios) {
            u.enviarEmail(e, i.getCliente().getNome(),
                    urlAvaliacao,
                    "", a);
        }

    }

    public String gerarPDFAvaliacaoFisica(final String key, final AvaliacaoFisica avaliacao,
                                          Flexibilidade flexibilidade,
                                          PesoOsseo pesoOsseo,
                                          Ventilometria ventilometria,
                                          final Anamnese anamnese,
                                          final Anamnese questionarioParq,
                                          final Usuario usuario, ViewUtils viewUtils,
                                          HttpServletRequest request, ServletContext servletContext, boolean enviarPorEmail, boolean externo, String language) throws Exception {
        PDFFromXmlFile geradorPDF = new PDFFromXmlFile();
        ImpressaoAvaliacaoDTO impressaoAvaliacaoDTO = new ImpressaoAvaliacaoDTO();
        Map<String, Object> paramsImpressao = prepareParamsImpressao(avaliacao,
                flexibilidade, pesoOsseo, ventilometria, anamnese, questionarioParq, usuario, viewUtils,
                servletContext, key, language, impressaoAvaliacaoDTO);
        if(enviarPorEmail){
            return geradorPDF.gerarPDF(key, request, getDesignIReportAvaliacaoFisica(), paramsImpressao, "avaliacaofisicaaluno", enviarPorEmail, externo, language);
        }

        boolean treinoIndependente = Aplicacao.independente(key);
        String pdf = null;

        if (treinoIndependente) {
            pdf = imprimirAplicacaoLocal(request,
                    geradorPDF.gerarPDF(key, request, getDesignIReportAvaliacaoFisica(), paramsImpressao, "avaliacaofisicaaluno", enviarPorEmail, externo, language));
        } else {
            impressaoAvaliacaoDTO.setParametros(paramsImpressao);
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(key);
            String url = clientDiscoveryDataDTO.getServiceUrls().getRelatorioMsUrl();
            String endpoint = "/avaliacao-fisica/unica";

            try {
                pdf = geradorPDF.sendToMs(url, endpoint, impressaoAvaliacaoDTO, sessaoService.getUsuarioAtual().getToken());
            }catch (Exception e){
                Uteis.logar(e, AvaliacaoFisicaImpl.class);
                pdf = imprimirAplicacaoLocal(request,
                        geradorPDF.gerarPDF(key, request, getDesignIReportAvaliacaoFisica(), paramsImpressao, "avaliacaofisicaaluno", enviarPorEmail, externo, language));
            }
        }
        return pdf;
    }

    public String imprimirAplicacaoLocal(HttpServletRequest request, String path){
        String urlAplicacao = Uteis.getURLValidaProtocolo(request);
        String urlRemove = request.getPathInfo();
        if(urlRemove == null){
            return urlAplicacao + "/" + path;
        }
        urlRemove = urlRemove.replaceAll("/imprimir", "");
        urlAplicacao = urlAplicacao.replaceAll("prest" + urlRemove, "");
        return urlAplicacao = urlAplicacao + path;
    }

    public String gerarPDFComparativo(final String key, final List<AvaliacaoFisica> avaliacoes,
                                      final Usuario usuario, ViewUtils viewUtils,
                                      HttpServletRequest request, ServletContext servletContext, final Boolean enviarPorEmail) throws Exception {

        if (avaliacoes == null
                || avaliacoes.isEmpty()
                || avaliacoes.size() == 1) {
            throw new Exception("Selecione mais de uma avaliação para comparar.");
        }


        PDFFromXmlFile geradorPDF = new PDFFromXmlFile();
        Map<String, Object> parameters = new HashMap<String, Object>();
        if ((usuario.getEmpresaDefault() == null && usuario.getEmpresaZW() == null) || SuperControle.independente(key)) {
            povoarEmpresaVazia(servletContext, parameters);
        } else {
            if (usuario.getEmpresaDefault() == null) {
                usuario.setEmpresasZW(UtilContext.getBean(AdmWSConsumer.class).obterEmpresasZW(key, usuario.getUsuarioZW()));
            }
            if (usuario.getEmpresaDefault() == null) {
                povoarEmpresaVazia(servletContext, parameters);
            } else {
                String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, usuario.getEmpresaDefault().getCodigo().toString());
                String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
                parameters.put("logoPadraoRelatorio", urlFoto);
                parameters.put("empresaNome", usuario.getEmpresaDefault().getNome());
                parameters.put("empresaEndereco", usuario.getEmpresaDefault().getEndereco());
                parameters.put("empresaSite", prepparam(usuario.getEmpresaDefault().getSite()).toLowerCase().replaceAll("https://", "").replaceAll("http://", ""));
            }

        }
        parameters.put("SUBREPORT_DIR", getDesignIReportAvaliacaoFisicaSubReport());
        parameters.put("showcomparacoes", true);
        parameters.put("usuario", usuario == null ? "Impresso pelo aluno" : usuario.getNome());
        parameters.put("horaEmissao", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm"));


        ClienteSintetico cliente = avaliacoes.get(0).getCliente();

        String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_PESSOA, cliente.getCodigoPessoa().toString());
           String foto;
        if (cliente != null && cliente.getPessoa() != null && !UteisValidacao.emptyString(cliente.getPessoa().getFotoKey())) {
            foto = Aplicacao.obterUrlFotoDaNuvem(cliente.getPessoa().getFotoKey());
        } else {
            foto = Aplicacao.obterUrlFotoDaNuvem(null);
        }
        parameters.put("fotoAluno", foto);
        parameters.put("nomeAluno", WordUtils.capitalize(cliente.getNome().toLowerCase()));
        parameters.put("idade", cliente.getIdade().toString().concat(" anos"));
        parameters.put("sexo", cliente.getSexoFull());
        List<ComparativoTO> comparativoTOS = montarComparativo(key, Ordenacao.ordenarLista(avaliacoes, "dataAvaliacao"), viewUtils, "PT");

        boolean treinoIndependente = SuperControle.independente(key);

        if(enviarPorEmail || treinoIndependente){
            parameters.put("comparativo", new JRBeanCollectionDataSource(comparativoTOS));
            return geradorPDF.gerarPDF(key, request, getDesignIReportComparativo(), parameters, "comparacoesavaliacaofisicaaluno",
                    enviarPorEmail, false, "PT");
        }

        ImpressaoAvaliacaoDTO impressaoAvaliacaoDTO = new ImpressaoAvaliacaoDTO();
        impressaoAvaliacaoDTO.setParametros(parameters);
        impressaoAvaliacaoDTO.setComparativo(comparativoTOS);
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(key);
        String url = clientDiscoveryDataDTO.getServiceUrls().getRelatorioMsUrl();
        String endpoint = "/avaliacao-fisica/comparativo";
        String pdf = null;
        try {
            String authorization = "Bearer " + sessaoService.getUsuarioAtual().getToken();
            pdf = geradorPDF.sendToMs(url, endpoint, impressaoAvaliacaoDTO, authorization);
        } catch (Exception e){
            Uteis.logar(e, AvaliacaoFisicaImpl.class);
            try {
                parameters.put("comparativo", new JRBeanCollectionDataSource(comparativoTOS));
                pdf = imprimirComparacaoLocal(request,geradorPDF.gerarPDF(key, request, getDesignIReportComparativo(), parameters, "comparacoesavaliacaofisicaaluno",
                        enviarPorEmail, false, "PT"));
            } catch (Exception jasperException) {
                Uteis.logar(jasperException, AvaliacaoFisicaImpl.class);
                throw new ServiceException("Erro ao gerar PDF comparativo da avaliação física. Microserviço indisponível e JasperReports com problema. Verifique a configuração do ambiente.", jasperException);
            }
        }
        return pdf;
    }

    public String imprimirComparacaoLocal(HttpServletRequest request, String path){
        String urlAplicacao = Uteis.getURLValidaProtocolo(request);
        String urlRemove = request.getPathInfo();
        if(urlRemove == null){
            return urlAplicacao + "/" + path;
        }
        urlRemove = urlRemove.replaceAll("/imprimir", "");
        urlAplicacao = urlAplicacao.replaceAll("prest" + urlRemove, "");
        return urlAplicacao = urlAplicacao + path;
    }


    private void povoarEmpresaVazia(ServletContext servletContext, Map<String, Object> parameters) {
        if (servletContext != null) {
            final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
            parameters.put("logoPadraoRelatorio", imagemTreino);
            parameters.put("empresaNome", "");
            parameters.put("empresaEndereco", "");
            parameters.put("empresaSite", "");
        } else {
            //definir o caminho da imagem padrap
            final String imagemTreino = sc.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";

            parameters.put("logoPadraoRelatorio", imagemTreino);
            parameters.put("empresaNome", "");
            parameters.put("empresaEndereco", "");
            parameters.put("empresaSite", "");
        }
    }

    public String prepparam(Object o) {
        return o == null ? "" : o.toString();
    }

    private Map<String, Object> prepareParamsConteudoEmailImpressao(final AvaliacaoFisica avaliacaoFisica,
                                                                    final Usuario usuario,
                                                                    ServletContext servletContext,
                                                                    String key) throws Exception {

        Map<String, Object> parameters = new HashMap<String, Object>();

        parameters.put("nomeCliente", avaliacaoFisica.getCliente().getPrimeiroNome());
        parameters.put("dataAvaliacao", Uteis.getData(avaliacaoFisica.getDataAvaliacao()));
        Usuario usu = usuarioService.obterPorId(key, avaliacaoFisica.getResponsavelLancamento_codigo());
        parameters.put("nomeDoAvaliador", usu.getNomeApresentar());

        try {
            if (!SuperControle.independente(key) && usuario != null && !UteisValidacao.emptyList(usuario.getEmpresasZW())) {
                usuario.setEmpresasZW(UtilContext.getBean(AdmWSConsumer.class).obterEmpresas(key));
                parameters.put("empresaNome", usuario.getEmpresaDefault().getNome());
                parameters.put("empresaEndereco", usuario.getEmpresaDefault().getEndereco());
                parameters.put("empresaCidade", usuario.getEmpresaDefault().getCidade());
                parameters.put("empresaEstado", usuario.getEmpresaDefault().getEstado());
                parameters.put("empresaTelefone", usuario.getEmpresaDefault().getTelefone());
                parameters.put("empresaEmail", usuario.getEmpresaDefault().getEmail());
            }
        } catch (Exception e) {
        }

        if (SuperControle.independente(key) && usuario == null) {
            List<Empresa> empresaWSIndepedente = empresaService.obterTodos(key);
            for (Empresa ws : empresaWSIndepedente) {
                if (ws.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())) {
                    final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
                    parameters.put("logoPadraoRelatorio", imagemTreino);
                    parameters.put("empresaNome", "");
                    parameters.put("empresaEndereco", "");
                    parameters.put("empresaCidade", "");
                    parameters.put("empresaEstado", "");
                    parameters.put("empresaTelefone", "");
                    parameters.put("empresaEmail", "");
                }
            }
        } else if (usuario != null && usuario.getEmpresaDefault() == null) {
            final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
            parameters.put("logoPadraoRelatorio", imagemTreino);
            parameters.put("empresaNome", "");
            parameters.put("empresaEndereco", "");
            parameters.put("empresaCidade", "");
            parameters.put("empresaEstado", "");
            parameters.put("empresaTelefone", "");
            parameters.put("empresaEmail", "");
        }

        if (usuario == null) {
            List<EmpresaWS> empresaWS = UtilContext.getBean(AdmWSConsumer.class).obterEmpresas(key);
            for (EmpresaWS ws : empresaWS) {
                if (ws.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())) {
                    if (!SuperControle.independente(key)) {
                        String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, ws.getCodigo().toString());
                        String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
                        parameters.put("logoPadraoRelatorio", urlFoto);
                    }
                    parameters.put("empresaNome", ws.getNome());
                    parameters.put("empresaEndereco", ws.getEndereco());
                    parameters.put("empresaCidade", ws.getCidade());
                    parameters.put("empresaEstado", ws.getEstado());
                    parameters.put("empresaTelefone", ws.getTelefone());
                    parameters.put("empresaEmail", ws.getEmail());
                }

            }

        } else if (usuario.getEmpresaDefault() == null && usuario.getEmpresaZW() == null) {
            final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
            parameters.put("logoPadraoRelatorio", imagemTreino);
            parameters.put("empresaNome", "");
            parameters.put("empresaEndereco", "");
            parameters.put("empresaCidade", "");
            parameters.put("empresaEstado", "");
            parameters.put("empresaTelefone", "");
            parameters.put("empresaEmail", "");
        } else {
            if (!SuperControle.independente(key) && usuario.getEmpresaDefault() == null) {
            usuario.setEmpresasZW(UtilContext.getBean(AdmWSConsumer.class).obterEmpresasZW(key, usuario.getUsuarioZW()));
                if (usuario.getEmpresaDefault() != null || usuario.getEmpresasZW() != null) {
                    for (EmpresaWS empresa : usuario.getEmpresasZW()) {
                        if (empresa.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())) {
                            parameters.put("empresaNome", empresa.getNome());
                            parameters.put("empresaEndereco", empresa.getEndereco());
                            parameters.put("empresaCidade", empresa.getCidade());
                            parameters.put("empresaEstado", empresa.getEstado());
                            parameters.put("empresaTelefone", empresa.getTelefone());
                            parameters.put("empresaEmail", empresa.getEmail());
                        }
                    }
                }
            } else {
                if (!SuperControle.independente(key)) {
                    String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, usuario.getEmpresaDefault().getCodigo().toString());
                    String urlFoto = String.format("%s/%s", new Object[]{
                            Aplicacao.getProp(Aplicacao.urlFotosNuvem),
                            genKey});
                    parameters.put("logoPadraoRelatorio", urlFoto);
                }
            }
        }

        return parameters;
    }

    private Map<String, Object> prepareParamsImpressao(final AvaliacaoFisica avaliacaoFisica,
                                                       Flexibilidade flexibilidade,
                                                       PesoOsseo pesoOsseo,
                                                       Ventilometria ventilometria,
                                                       Anamnese as,
                                                       Anamnese questionarioParq,
                                                       final Usuario usuario,
                                                       ViewUtils viewUtils,
                                                       ServletContext servletContext,
                                                       String key, String language, ImpressaoAvaliacaoDTO impressaoAvaliacaoDTO) throws Exception {

        ConfiguracaoSistemaService cfs = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
        Map<String, Object> parameters = new HashMap<String, Object>();
        if (as == null) {
            as = new Anamnese();
            as.setPerguntas(new ArrayList<PerguntaAnamnese>());
        }
        if (questionarioParq == null) {
            questionarioParq = new Anamnese();
            questionarioParq.setPerguntas(new ArrayList<PerguntaAnamnese>());
        }
        flexibilidade = flexibilidade == null ? new Flexibilidade() : flexibilidade;

        parameters.put("apresentarAssinatura", !UteisValidacao.emptyString(avaliacaoFisica.getUrlAssinatura()));
        if (!UteisValidacao.emptyString(avaliacaoFisica.getUrlAssinatura())) {
            parameters.put("assinatura", avaliacaoFisica.getUrlProntaAssinatura());
        }


        parameters.put("showpesoaltura", true);
        parameters.put("showflexibilidade", validarShowFlexibilidade(flexibilidade));

        try {
            if (!SuperControle.independente(key) && usuario != null && UteisValidacao.emptyList(usuario.getEmpresasZW())) {
                usuario.setEmpresasZW(UtilContext.getBean(AdmWSConsumer.class).obterEmpresas(key));
                EmpresaWS empresaWS = usuario.getEmpresasZW().stream().filter(
                        item -> item.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())).findFirst().orElse(null);
                parameters.put("empresaNome", empresaWS != null ? empresaWS.getNome() : usuario.getEmpresaDefault().getNome());
                parameters.put("empresaEndereco", empresaWS != null ? empresaWS.getEndereco() : usuario.getEmpresaDefault().getEndereco());
                parameters.put("empresaSite", prepparam(empresaWS != null ? empresaWS.getSite() : usuario.getEmpresaDefault().getSite()).toLowerCase().replaceAll("https://", "").replaceAll("http://", ""));
            }
        } catch (Exception e) {
        }

        if (SuperControle.independente(key)) {
            List<Empresa> empresaWSIndepedente = empresaService.obterTodos(key);
            for (Empresa ws : empresaWSIndepedente) {
                if (ws.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())) {
                    // Para treino independente: Usar keyImgEmpresa da empresa (mesma lógica do EmpresaDTO)
                    if (ws.getKeyImgEmpresa() != null && !UteisValidacao.emptyString(ws.getKeyImgEmpresa())) {
                        String urlFoto = Aplicacao.obterUrlFotoDaNuvem(ws.getKeyImgEmpresa());
                        parameters.put("logoPadraoRelatorio", urlFoto);
                    } else {
                        final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
                        parameters.put("logoPadraoRelatorio", imagemTreino);
                    }
                    parameters.put("empresaNome", ws.getNome());
                    parameters.put("empresaEndereco", "");
                    parameters.put("empresaSite", "");
                }
            }
        } else if (usuario != null && usuario.getEmpresaDefault() == null) {
            final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
            parameters.put("logoPadraoRelatorio", imagemTreino);
            parameters.put("empresaNome", "");
            parameters.put("empresaEndereco", "");
            parameters.put("empresaSite", "");
        }

        if (usuario == null) {
            List<EmpresaWS> empresaWS = UtilContext.getBean(AdmWSConsumer.class).obterEmpresas(key);
            for (EmpresaWS ws : empresaWS) {
                if (ws.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())) {
                    if (!SuperControle.independente(key)) {
                        String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, ws.getCodigo().toString());
                        String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
                        parameters.put("logoPadraoRelatorio", urlFoto);
                    }
                    parameters.put("empresaNome", ws.getNome());
                    parameters.put("empresaEndereco", ws.getEndereco());
                    parameters.put("empresaSite", prepparam(ws.getSite()).toLowerCase().replaceAll("https://", "").replaceAll("http://", ""));
                }

            }

        } else if (usuario.getEmpresaDefault() == null && usuario.getEmpresaZW() == null) {
            final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
            parameters.put("logoPadraoRelatorio", imagemTreino);
            parameters.put("empresaNome", "");
            parameters.put("empresaEndereco", "");
            parameters.put("empresaSite", "");
        } else {
            if (usuario.getEmpresaDefault() == null) {
                if (usuario.getUsuarioZW() != null) {
                    usuario.setEmpresasZW(UtilContext.getBean(AdmWSConsumer.class).obterEmpresasZW(key, usuario.getUsuarioZW()));
                    if (usuario.getEmpresaDefault() != null || usuario.getEmpresasZW() != null) {
                        for (EmpresaWS empresa : usuario.getEmpresasZW()) {
                            if (empresa.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())) {
                                parameters.put("empresaNome", empresa.getNome());
                                parameters.put("empresaEndereco", empresa.getEndereco());
                                parameters.put("empresaSite", prepparam(empresa.getSite()).toLowerCase().replaceAll("https://", "").replaceAll("http://", ""));
                            }
                        }
                    }
                } else {
                    final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
                    parameters.put("logoPadraoRelatorio", imagemTreino);
                    parameters.put("empresaNome", "");
                    parameters.put("empresaEndereco", "");
                    parameters.put("empresaSite", "");
                }
            } else {
                Integer codigoEmpresa = null;
                if (usuario.getEmpresasZW() != null && !usuario.getEmpresasZW().isEmpty()) {
                    for (EmpresaWS empresa : usuario.getEmpresasZW()) {
                        if (empresa.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())) {
                            codigoEmpresa = empresa.getCodigo();
                            break;
                        }
                    }
                }
                if (codigoEmpresa == null) {
                    codigoEmpresa = usuario.getEmpresaDefault().getCodigo();
                }

                if (!SuperControle.independente(key)) {
                    String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, codigoEmpresa.toString());
                    String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
                    parameters.put("logoPadraoRelatorio", urlFoto);
                }
            }
        }

        ClienteSintetico cliente = avaliacaoFisica.getCliente();
        String foto;
        if (cliente != null && cliente.getPessoa() != null && !UteisValidacao.emptyString(cliente.getPessoa().getFotoKey())) {
            foto = Aplicacao.obterUrlFotoDaNuvem(cliente.getPessoa().getFotoKey());
        } else {
            foto = Aplicacao.obterUrlFotoDaNuvem(null);
        }
        parameters.put("fotoAluno", foto);
        parameters.put("nomeAluno", WordUtils.capitalize(cliente.getNome().toLowerCase()));
        parameters.put("idade", cliente.getIdade().toString().concat(" anos"));
        Usuario usu = usuarioService.obterPorId(key, avaliacaoFisica.getResponsavelLancamento_codigo());
        if(usu == null){
            parameters.put("avaliador", "");
        } else {
            parameters.put("avaliador", WordUtils.capitalize(usu.getNomeProfessor().toLowerCase()));
        }

        parameters.put("dataAvaliacao", Uteis.getData(avaliacaoFisica.getDataAvaliacao()));
        parameters.put("sexo", cliente.getSexoFull());
        parameters.put("proximaAvaliacao", avaliacaoFisica.getDataProxima() == null ? "" : Uteis.getData(avaliacaoFisica.getDataProxima()));

        parameters.put("circunferencia", Uteis.formatarValorNumerico(avaliacaoFisica.getCircunferenciaAbdominal()) + "cm");
        parameters.put("circunferenciaResultado", viewUtils.getLabelInternacionalizada(avaliacaoFisica.getResultadoRiscoCA(), language));
        parameters.put("imc", prepparam(avaliacaoFisica.getImcApresentar()));
        parameters.put("altura", prepparam(avaliacaoFisica.getAlturaApresentar()) + "m");
        parameters.put("peso", prepparam(avaliacaoFisica.getPesoApresentar()) + "kg");
        parameters.put("imcResultado", avaliacaoFisica.getCategoriaAvaliacaoIMC() == null ? "" : getViewUtils().getLabelInternacionalizada(avaliacaoFisica.getCategoriaAvaliacaoIMC().getDescricao(), language));
        parameters.put("composicaoResultado", getViewUtils().getLabelInternacionalizada(avaliacaoFisica.getCategoriaPercGordApresentar(), language));

        ItemAvaliacaoFisica objs = obterItemAvaliacaoFisica(key, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.OBJETIVOS, null, avaliacaoFisica);
        if (objs == null || objs.getResult() == null) {
            parameters.put("objetivosAluno", "");
        } else {
            parameters.put("objetivosAluno", StringUtils.capitalize(objs.getResult()).replaceAll("\\|\\#\\|", ", "));
        }
        parameters.put("showobjetivos", !UteisValidacao.emptyString((String) parameters.get("objetivosAluno")));
        String recomendacoes = prepparam(avaliacaoFisica.getRecomendacoes());
        parameters.put("recomendacoes", recomendacoes);
        parameters.put("showrecomendacoes", !UteisValidacao.emptyString(recomendacoes));

        if (avaliacaoFisica.getDataAvaliacao() == null) {
            throw new ServiceException("A data da avaliação física não foi preenchida.");
        }

        List<String> strs;
        try (ResultSet rs = avaliacaoFisicaDao.createStatement(key, "select dataavaliacao, peso from avaliacaofisica  \n" +
                "where dataavaliacao::date <= '" + Uteis.getDataAplicandoFormatacao(avaliacaoFisica.getDataAvaliacao(), "yyyy-MM-dd") + "' and cliente_codigo = " +
                cliente.getCodigo() +
                "order by dataavaliacao desc limit 4")) {
            strs = new ArrayList<String>();
            while (rs.next()) {
                strs.add(Uteis.getDataAplicandoFormatacao(rs.getDate("dataavaliacao"), "dd/MM/yy") + " - " +
                        Uteis.formatarValorNumerico(rs.getDouble("peso")) + "kg");
            }
        }
        parameters.put("peso1", strs.size() >= 1 ? strs.get(0) : "");
        parameters.put("peso2", strs.size() >= 2 ? strs.get(1) : "");
        parameters.put("peso3", strs.size() >= 3 ? strs.get(2) : "");
        parameters.put("peso4", strs.size() >= 4 ? strs.get(3) : "");
        parameters.put("usuario", usuario == null ? "Impresso pelo aluno" : usuario.getNome());
        parameters.put("horaEmissao", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm"));
        parameters.put("alturaAtual", prepparam(avaliacaoFisica.getAlturaApresentar()));
        parameters.put("pesoAtual", prepparam(avaliacaoFisica.getPesoApresentar()));

        ItemAvaliacaoFisica pressaoItem = obterItemAvaliacaoFisica(key, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL, null, avaliacaoFisica);
        String pressao = pressaoItem != null ? pressaoItem.getResult() : null;
        boolean usarPressaoSistolicaDiastolica = cfs.consultarPorTipo(key, ConfiguracoesEnum.USAR_PRESSAO_SISTOLICA_DIASTOLICA).getValorAsBoolean();
        if (usarPressaoSistolicaDiastolica) {
            ItemAvaliacaoFisica itemPressaoSistolica = obterItemAvaliacaoFisica(key, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL_SISTOLICA, null, avaliacaoFisica);
            ItemAvaliacaoFisica itemPressaoDiastolica = obterItemAvaliacaoFisica(key, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL_DIASTOLICA, null, avaliacaoFisica);
            if (itemPressaoSistolica != null && itemPressaoDiastolica != null) {
                pressao = itemPressaoSistolica.getResult() + "/" + itemPressaoDiastolica.getResult();
            }
        }

        ItemAvaliacaoFisica fc = obterItemAvaliacaoFisica(key, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.FREQUENCIA_CARDIACA, null, avaliacaoFisica);

        parameters.put("freqCardiaca", fc == null ? "" : fc.getResult());
        parameters.put("pressaoArterial", pressao == null ? "" : pressao);
        Boolean algumaResposta = false;
        List<ItemRelatorioTO> anamnese = new ArrayList<ItemRelatorioTO>();

        for (PerguntaAnamnese pa : as.getPerguntas()) {
            String resposta = "";
            switch (pa.getPergunta().getTipoPergunta()) {
                case MULTIPLA_ESCOLHA:
                    for (OpcaoPergunta op : pa.getPergunta().getOpcoes()) {
                        if (pa.getRespostas().contains(op.getCodigo().toString())) {
                            resposta += "," + op.getOpcao().trim();
                            algumaResposta = true;
                        }
                    }
                    resposta = resposta.replaceFirst(",", "");
                    break;
                case SIMPLES_ESCOLHA:
                    for (OpcaoPergunta op : pa.getPergunta().getOpcoes()) {
                        if (op.getCodigo().toString().equals(pa.getResposta())) {
                            resposta = op.getOpcao();
                            algumaResposta = true;
                        }
                    }
                    break;
                case SIM_NAO:
                    resposta = UteisValidacao.emptyString(pa.getResposta()) ? "" :
                            viewUtils.getLabel("cadastros." + pa.getResposta().toLowerCase());
                    if (!UteisValidacao.emptyString(pa.getResposta())) {
                        algumaResposta = true;
                    }
                    break;
                case TEXTUAL:
                    resposta = pa.getResposta();
                    if (!UteisValidacao.emptyString(pa.getResposta())) {
                        algumaResposta = true;
                    }
                    break;
            }
            resposta = resposta + (UteisValidacao.emptyString(pa.getComplemento()) ? "" :
                    " - ".concat(pa.getComplemento()));
            anamnese.add(new ItemRelatorioTO(
                    pa.getPergunta().getOrdem().toString().concat(" - ").concat(pa.getPergunta().getDescricao()),
                    resposta));
        }
        parameters.put("showanamnese", algumaResposta);
        parameters.put("anamneseJR", new JRBeanCollectionDataSource(anamnese));
        impressaoAvaliacaoDTO.setAnamnese(anamnese);
        boolean algumParq = false;
        List<ItemRelatorioTO> parq = new ArrayList<ItemRelatorioTO>();
        for (PerguntaAnamnese pa : questionarioParq.getPerguntas()) {
            String resposta = UteisValidacao.emptyString(pa.getResposta()) ? "" :
                    viewUtils.getLabel("cadastros." + pa.getResposta().toLowerCase());
            resposta = resposta + (UteisValidacao.emptyString(pa.getComplemento()) ? "" :
                    " - ".concat(pa.getComplemento()));
            if (!UteisValidacao.emptyString(pa.getResposta())) {
                algumParq = true;
            }
            parq.add(new ItemRelatorioTO(
                    pa.getPergunta().getOrdem().toString().concat(" - ").concat(pa.getPergunta().getDescricao()),
                    resposta));
        }
        parameters.put("showparq", algumParq);

        parameters.put("parqJR", new JRBeanCollectionDataSource(parq));
        impressaoAvaliacaoDTO.setParq(parq);
        parameters.put("parq", cliente.getParq() != null && cliente.getParq() ? viewUtils.getLabel("positivo") : viewUtils.getLabel("negativo"));
        parameters.put("protocolo", viewUtils.getLabel(avaliacaoFisica.getProtocolo().name()));
        boolean showDobras = false;
        List<ItemRelatorioTO> dobras = new ArrayList<ItemRelatorioTO>();
        Double totalDobras = 0.0;
        if (avaliacaoFisica.getUsouAbdominal() && !UteisValidacao.emptyNumber(avaliacaoFisica.getAbdominal())) {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("cadastros.aluno.abdominal", language), apresentar(avaliacaoFisica.getAbdominal()) + "mm"));
            showDobras = true;
            totalDobras += avaliacaoFisica.getAbdominal();
        }
        if (avaliacaoFisica.getUsouSuprailiaca() && !UteisValidacao.emptyNumber(avaliacaoFisica.getSupraIliaca())) {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("cadastros.aluno.suprailiaca", language), apresentar(avaliacaoFisica.getSupraIliaca()) + "mm"));
            showDobras = true;
            totalDobras += avaliacaoFisica.getSupraIliaca();
        }
        if (!UteisValidacao.emptyNumber(avaliacaoFisica.getPeitoral())) {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("cadastros.aluno.peitoral", language), apresentar(avaliacaoFisica.getPeitoral()) + "mm"));
            showDobras = true;
            totalDobras += avaliacaoFisica.getPeitoral();
        }
        if (avaliacaoFisica.getUsouTriceps() && !UteisValidacao.emptyNumber(avaliacaoFisica.getTriceps())) {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("cadastros.aluno.triceps", language), apresentar(avaliacaoFisica.getTriceps()) + "mm"));
            showDobras = true;
            totalDobras += avaliacaoFisica.getTriceps();
        }
        if (avaliacaoFisica.getUsouCoxa() && !UteisValidacao.emptyNumber(avaliacaoFisica.getCoxaMedial())) {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("cadastros.aluno.coxaMedia", language), apresentar(avaliacaoFisica.getCoxaMedial()) + "mm"));
            showDobras = true;
            totalDobras += avaliacaoFisica.getCoxaMedial();
        }
        if (avaliacaoFisica.getUsouBiceps() && !UteisValidacao.emptyNumber(avaliacaoFisica.getBiceps())) {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("cadastros.aluno.biceps", language), apresentar(avaliacaoFisica.getBiceps()) + "mm"));
            showDobras = true;
            totalDobras += avaliacaoFisica.getBiceps();
        }
        if (avaliacaoFisica.getUsouSubescapular() && !UteisValidacao.emptyNumber(avaliacaoFisica.getSubescapular())) {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("cadastros.aluno.subescapular", language), apresentar(avaliacaoFisica.getSubescapular()) + "mm"));
            showDobras = true;
            totalDobras += avaliacaoFisica.getSubescapular();
        }
        if (avaliacaoFisica.getUsouAxilarMedia() && !UteisValidacao.emptyNumber(avaliacaoFisica.getAxilarMedia())) {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("cadastros.aluno.axilarMedia", language), apresentar(avaliacaoFisica.getAxilarMedia()) + "mm"));
            showDobras = true;
            totalDobras += avaliacaoFisica.getAxilarMedia();
        }
        if (avaliacaoFisica.getUsouSupraespinhal() && !UteisValidacao.emptyNumber(avaliacaoFisica.getSupraEspinhal())) {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("cadastros.aluno.supraespinhal", language), apresentar(avaliacaoFisica.getSupraEspinhal()) + "mm"));
            showDobras = true;
            totalDobras += avaliacaoFisica.getSupraEspinhal();
        }
        if (avaliacaoFisica.getUsouPanturrilha() && !UteisValidacao.emptyNumber(avaliacaoFisica.getPanturrilha())) {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("cadastros.aluno.panturrilha", language), apresentar(avaliacaoFisica.getPanturrilha()) + "mm"));
            showDobras = true;
            totalDobras += avaliacaoFisica.getPanturrilha();
        }
        if (avaliacaoFisica.getProtocolo() != null && avaliacaoFisica.getProtocolo().equals(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA)) {
            showDobras = true;
            addItemBioImpedancia("IMC", avaliacaoFisica.getImc(), avaliacaoFisica.getImcApresentar(), dobras);
            addItemBioImpedancia("% Massa gorda", avaliacaoFisica.getPercentualGordura(), apresentar(avaliacaoFisica.getPercentualGordura()), dobras);
            addItemBioImpedancia("Massa gorda", avaliacaoFisica.getMassaGorda(), apresentar(avaliacaoFisica.getMassaGorda()), dobras);
            addItemBioImpedancia("Massa magra", avaliacaoFisica.getMassaMagra(), apresentar(avaliacaoFisica.getMassaMagra()), dobras);
            addItemBioImpedancia("% Massa magra", avaliacaoFisica.getPercentualMassaMagra(), apresentar(avaliacaoFisica.getPercentualMassaMagra()), dobras);
            addItemBioImpedancia("Ossos", avaliacaoFisica.getPesoOsseo(), apresentar(avaliacaoFisica.getPesoOsseo()), dobras);
            addItemBioImpedancia("Resíduos", avaliacaoFisica.getResidual(), apresentar(avaliacaoFisica.getResidual()), dobras);
            addItemBioImpedancia("Músculos", avaliacaoFisica.getPesoMuscular(), apresentar(avaliacaoFisica.getPesoMuscular()), dobras);
            addItemBioImpedancia("Gordura ideal", avaliacaoFisica.getGorduraIdeal(), apresentar(avaliacaoFisica.getGorduraIdeal()), dobras);
            addItemBioImpedancia("Resistência", avaliacaoFisica.getResistencia(), apresentar(avaliacaoFisica.getResistencia()), dobras);
            addItemBioImpedancia("Reatância", avaliacaoFisica.getReatancia(), apresentar(avaliacaoFisica.getReatancia()), dobras);
            addItemBioImpedancia("% Água", avaliacaoFisica.getPercentualAgua(), apresentar(avaliacaoFisica.getPercentualAgua()) + "%", dobras);
            addItemBioImpedancia("Necessidade física", avaliacaoFisica.getNecessidadeFisica(), apresentar(avaliacaoFisica.getNecessidadeFisica()), dobras);
            addItemBioImpedancia("TMB", avaliacaoFisica.getTmb(), apresentar(avaliacaoFisica.getTmb()), dobras);
            addItemBioImpedancia("Necessidade calórica", avaliacaoFisica.getNecessidadeCalorica(), apresentar(avaliacaoFisica.getNecessidadeCalorica()), dobras);
            addItemBioImpedancia("Idade metabólica", avaliacaoFisica.getIdadeMetabolica(), apresentar(avaliacaoFisica.getIdadeMetabolica()), dobras);
            addItemBioImpedancia("Gordura visceral", avaliacaoFisica.getGorduraVisceral(), apresentar(avaliacaoFisica.getGorduraVisceral()), dobras);
            parameters.put("percMusculos", apresentar(avaliacaoFisica.getPesoMuscular()) + "kg");
            if (UteisValidacao.emptyNumber(avaliacaoFisica.getMassaGorda()) && !UteisValidacao.emptyNumber(avaliacaoFisica.getPercentualGordura())) {
                avaliacaoFisica.setMassaGorda((avaliacaoFisica.getPeso() * avaliacaoFisica.getPercentualGordura()) / 100.0);
            }
            parameters.put("pesoGordura", apresentar(avaliacaoFisica.getMassaGorda()));
            parameters.put("pesoResidual", apresentar(avaliacaoFisica.getResidual()));
            parameters.put("pesoOsseo", apresentar(avaliacaoFisica.getPesoOsseo()));
            parameters.put("pesoMuscular", apresentar(avaliacaoFisica.getPesoMuscular()));
            parameters.put("percResiduos", apresentar(avaliacaoFisica.getResidual()) + "kg");
            parameters.put("percGordura", apresentar(avaliacaoFisica.getMassaGorda()) + "kg");
            parameters.put("percOssos", apresentar(avaliacaoFisica.getPesoOsseo()) + "kg");
        } else {
            dobras.add(new ItemRelatorioTO(viewUtils.getLabelInternacionalizada("avaliacao.protocolo.total", language), apresentar(totalDobras) + "mm"));
            parameters.put("percMusculos", pesoOsseo.getPercMuscularApresentar() + "%");
            parameters.put("pesoGordura", pesoOsseo.getPesoGorduraApresentar());
            parameters.put("pesoResidual", pesoOsseo.getPesoResidualApresentar());
            parameters.put("pesoOsseo", pesoOsseo.getPesoOsseoApresentar());
            parameters.put("pesoMuscular", pesoOsseo.getPesoMuscularApresentar());
            parameters.put("percResiduos", pesoOsseo.getPercResidualApresentar() + "%");
            parameters.put("percGordura", pesoOsseo.getPercentualGorduraApresentar() + "%");
            parameters.put("percOssos", pesoOsseo.getPercOsseoApresentar() + "%");
        }
        parameters.put("showdobras", showDobras);

        parameters.put("dobrasJR", new JRBeanCollectionDataSource(dobras));
        impressaoAvaliacaoDTO.setDobras(dobras);
        List<ItemRelatorioTO> perimetria = new ArrayList<ItemRelatorioTO>();

        List<ConfiguracaoPerimetro> cfgPerimetros = cfs.obterCfgsPerimetro(key);
        for (ConfiguracaoPerimetro cp : cfgPerimetros) {
            if ((cp.getEsquerda() != null) || (cp.getDireita() != null)) {
                addPerimetria(perimetria, viewUtils.getLabelInternacionalizada(cp.getLabel(), language),
                        cp.getEsquerda() == null ? null : Double.parseDouble((String) UtilReflection.getValor(avaliacaoFisica, cp.getEsquerda().getField())),
                        cp.getDireita() == null ? null : Double.parseDouble((String) UtilReflection.getValor(avaliacaoFisica, cp.getDireita().getField())));
            }

        }


        if (pesoOsseo == null || pesoOsseo.getCodigo() == null || pesoOsseo.getCodigo() == 0) {
            if (avaliacaoFisica != null) {
                pesoOsseo = gerarPesoOsseo(avaliacaoFisica, cliente);
            }
        }
        parameters.put("perimetriaJR", new JRBeanCollectionDataSource(perimetria));
        impressaoAvaliacaoDTO.setPerimetria(perimetria);
        parameters.put("diametroJoelho", cleanParam(pesoOsseo.getDiametroFemurApresentar()));
        parameters.put("diametroPunho", cleanParam(pesoOsseo.getDiametroPunhoApresentar()));
        parameters.put("diametroTornozelo", cleanParam(pesoOsseo.getDiametroTornozeloApresentar()));
        parameters.put("diametroCotovelo", cleanParam(pesoOsseo.getDiametroCotoveloApresentar()));

        parameters.put("somatotipia", !UteisValidacao.emptyNumber(avaliacaoFisica.getEctomorfia())
                && !UteisValidacao.emptyNumber(avaliacaoFisica.getEndomorfia())
                && !UteisValidacao.emptyNumber(avaliacaoFisica.getMesomorfia()));
        parameters.put("ectomorfia", avaliacaoFisica.getEctomorfiaApresentar());
        parameters.put("endomorfia", avaliacaoFisica.getEndomorfiaApresentar());
        parameters.put("mesomorfia", avaliacaoFisica.getMesomorfiaApresentar());

        parameters.put("showOsseo", !UteisValidacao.emptyNumber(pesoOsseo.getDiametroFemur())
                || !UteisValidacao.emptyNumber(pesoOsseo.getDiametroPunho())
                || !UteisValidacao.emptyNumber(pesoOsseo.getDiametroCotovelo())
                || !UteisValidacao.emptyNumber(pesoOsseo.getDiametroTornozelo())
        );
        parameters.put("showperimetria", !perimetria.isEmpty() || !UteisValidacao.emptyNumber(pesoOsseo.getDiametroFemur())
                || !UteisValidacao.emptyNumber(pesoOsseo.getDiametroPunho()));

        parameters.put("SUBREPORT_DIR", getDesignIReportAvaliacaoFisicaSubReport());

        calcularResultadoRelacaoCinturaQuadrilImpressao(parameters, perimetria, cliente);

        parameters.put("classificacaoFlexibilidade",
                flexibilidade.getClassificacao() == null ?
                        "" :
                        viewUtils.getLabelInternacionalizada(flexibilidade.getClassificacao().getNome(), language));
        parameters.put("alcanceMaximo", prepparam(flexibilidade.getAlcance()) + "cm");
        parameters.put("obsFlexibilidade", prepparam(flexibilidade.getObservacao()));
        parameters.put("mobilidadeOmbroEsquerdo",
                flexibilidade.getMobilidadeOmbroEsquerdo() == null
                        ? ""
                        : viewUtils.getLabelInternacionalizada(flexibilidade.getMobilidadeOmbroEsquerdo().getNome(), language));
        parameters.put("mobilidadeOmbroDireito",
                flexibilidade.getMobilidadeOmbroDireito() == null
                        ? ""
                        : viewUtils.getLabelInternacionalizada(flexibilidade.getMobilidadeOmbroDireito().getNome(), language));
        parameters.put("observacaoOmbro", prepparam(flexibilidade.getObservacaoOmbro()));

        parameters.put("mobilidadeQuadrilEsquerdo",
                flexibilidade.getMobilidadeQuadrilEsquerdo() == null
                        ? ""
                        : viewUtils.getLabelInternacionalizada(flexibilidade.getMobilidadeQuadrilEsquerdo().getNome(), language));
        parameters.put("mobilidadeQuadrilDireito",
                flexibilidade.getMobilidadeQuadrilDireito() == null
                        ? ""
                        : viewUtils.getLabelInternacionalizada(flexibilidade.getMobilidadeQuadrilDireito().getNome(), language));;
        parameters.put("observacaoQuadril", prepparam(flexibilidade.getObservacaoQuadril()));

        parameters.put("mobilidadeJoelhoEsquerdo",
                flexibilidade.getMobilidadeJoelhoEsquerdo() == null
                        ? ""
                        : viewUtils.getLabelInternacionalizada(flexibilidade.getMobilidadeJoelhoEsquerdo().getNome(), language));
        parameters.put("mobilidadeJoelhoDireito",
                flexibilidade.getMobilidadeJoelhoDireito() == null
                        ? ""
                        : viewUtils.getLabelInternacionalizada(flexibilidade.getMobilidadeJoelhoDireito().getNome(), language));
        parameters.put("observacaoJoelho", prepparam(flexibilidade.getObservacaoJoelho()));

        parameters.put("mobilidadeTornozeloEsquerdo",
                flexibilidade.getMobilidadeTornozeloEsquerdo() == null
                        ? ""
                        : prepparam(flexibilidade.getMobilidadeTornozeloEsquerdo()) + "cm");
        parameters.put("mobilidadeTornozeloDireito",
                flexibilidade.getMobilidadeTornozeloDireito() == null
                        ? ""
                        : prepparam(flexibilidade.getMobilidadeTornozeloDireito()) + "cm");
        parameters.put("observacaoTornozelo", prepparam(flexibilidade.getObservacaoTornozelo()));

        AvaliacaoPostural avaliacaoPostural = consultarPostural(key, avaliacaoFisica);
        if (avaliacaoPostural == null) {
            parameters.put("showpostural", false);
            parameters.put("obsPostural", "");
            parameters.put("assimetriaQuadril", "");
            parameters.put("ombrosAssimetricos", "");
            parameters.put("anterior", "");
            parameters.put("posterior", "");
            parameters.put("visaoLateral", "");
        } else {
            boolean usarAvaliacaoPostural = cfs.consultarPorTipo(key, ConfiguracoesEnum.CFG_POSTURA).getValorAsBoolean();
            if (!usarAvaliacaoPostural) {
                parameters.put("showpostural", false);
            } else {
                parameters.put("showpostural", true);
            }
            organizarFotoPostural(key, "urlFrente", parameters, avaliacaoPostural.getKeyImgPosterior(), servletContext, avaliacaoFisica.getCodigo());
            organizarFotoPostural(key, "urlCosta", parameters, avaliacaoPostural.getKeyImgAnterior(), servletContext, avaliacaoFisica.getCodigo());
            organizarFotoPostural(key, "urlEsquerda", parameters, avaliacaoPostural.getKeyImgEsquerda(), servletContext, avaliacaoFisica.getCodigo());
            organizarFotoPostural(key, "urlDireita", parameters, avaliacaoPostural.getKeyImgDireita(), servletContext, avaliacaoFisica.getCodigo());

            parameters.put("obsPostural", avaliacaoPostural.getObservacao());
            parameters.put("assimetriaQuadril", viewUtils.getLabel(avaliacaoPostural.getQuadril().name()));
            parameters.put("ombrosAssimetricos", viewUtils.getLabel(avaliacaoPostural.getOmbros().name()));
            String anterior = "";
            String posterior = "";
            String lateral = "";
            List<ItemAvaliacaoPostural> itens = consultarItens(key, avaliacaoPostural);
            List<ItemPosturaEnum> itensAdd = new ArrayList<>();
            for (ItemAvaliacaoPostural i : itens) {
                if (i.getSelecionado() && !itensAdd.contains(i.getItem())) {
                    itensAdd.add(i.getItem());
                    switch (i.getItem().getTipo()) {
                        case ANTERIOR:
                            anterior += "," + viewUtils.getLabel(i.getItem().name());
                            break;
                        case LATERAL:
                            lateral += "," + viewUtils.getLabel(i.getItem().name());
                            break;
                        case POSTERIOR:
                            posterior += "," + viewUtils.getLabel(i.getItem().name());
                            break;
                    }
                }
            }
            parameters.put("anterior", anterior.isEmpty() ?
                    viewUtils.getLabelInternacionalizada("avaliacao.postural.naoDetectada", language) : anterior.replaceFirst(",", ""));
            parameters.put("posterior", posterior.isEmpty() ?
                    viewUtils.getLabelInternacionalizada("avaliacao.postural.naoDetectada", language) : posterior.replaceFirst(",", ""));
            parameters.put("visaoLateral", lateral.isEmpty() ?
                    viewUtils.getLabelInternacionalizada("avaliacao.postural.naoDetectada", language) : lateral.replaceFirst(",", ""));
        }

        ResultadoResistenciaEnum resultAbdomen = obterResultadoResistencia(key, cliente, obterEnumResistenciaPorConfiguracao(key, cliente.getSexo(), TipoResistenciaMuscular.ABDOMEN), avaliacaoFisica);
        ResultadoResistenciaEnum resultBraco = obterResultadoResistencia(key, cliente, obterEnumResistenciaPorConfiguracao(key, cliente.getSexo(), TipoResistenciaMuscular.BRACO), avaliacaoFisica);
        Integer flexaoAbdomen = cliente.getFlexaoAbdomen();
        Integer flexaoBraco = cliente.getFlexaoBraco();

        parameters.put("showrml", !UteisValidacao.emptyNumber(flexaoAbdomen)
                || !UteisValidacao.emptyNumber(flexaoBraco));


        if (flexaoBraco == null) {
            parameters.put("rmlBraco", "");
        } else {
            parameters.put("rmlBraco", flexaoBraco + " - <b>" + viewUtils.getLabelInternacionalizada("avaliacao.resistencia", language) + "</b>: "
                    + (resultBraco == null ? "" : viewUtils.getLabelInternacionalizada(resultBraco.name(), language)));
        }
        if (flexaoAbdomen == null) {
            parameters.put("rmlAbdomen", "");
        } else {
            parameters.put("rmlAbdomen", flexaoAbdomen + " - <b>" + viewUtils.getLabelInternacionalizada("avaliacao.resistencia", language) + "</b>: "
                    + (resultAbdomen == null ? "" : viewUtils.getLabelInternacionalizada(resultAbdomen.name(), language)));
        }
        ventilometria = ventilometria == null ? new Ventilometria() : ventilometria;
        if (!UteisValidacao.emptyNumber(avaliacaoFisica.getFcQueens())) {
            calcularVo2Queens(avaliacaoFisica, cliente.isSexoMasculino());
        }
        parameters.put("vo2", ventilometria.getVo2max() == null ? "" :
                Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(ventilometria.getVo2max())));
        parameters.put("showvo2max", !UteisValidacao.emptyStringOrZero(avaliacaoFisica.getTempo2400())
                || !UteisValidacao.emptyNumber(avaliacaoFisica.getDistancia12())
                || !UteisValidacao.emptyNumber(ventilometria.getLimiarVentilatorio())
                || !UteisValidacao.emptyNumber(ventilometria.getLimiarVentilatorio2())
                || !UteisValidacao.emptyNumber(ventilometria.getVo2max())
                || !UteisValidacao.emptyNumber(avaliacaoFisica.getVo2MaxQueens()));
        parameters.put("showQueens", !UteisValidacao.emptyNumber(avaliacaoFisica.getVo2MaxQueens()) || !UteisValidacao.emptyNumber(avaliacaoFisica.getFcQueens()));
        parameters.put("fcQueens", "Frequência cardíaca (bpm): " + avaliacaoFisica.getFcQueens());
        parameters.put("vo2Queens", "VO2 Máximo (ml/kg/min): " + avaliacaoFisica.getVo2MaxQueens());

        parameters.put("showMeta", !UteisValidacao.emptyNumber(avaliacaoFisica.getMetaPercentualGordura()));
        parameters.put("metaGordura", avaliacaoFisica.getMetaApresentar() + "%");


        parameters.put("showventilometria", !UteisValidacao.emptyNumber(ventilometria.getLimiarVentilatorio())
                || !UteisValidacao.emptyNumber(ventilometria.getLimiarVentilatorio2())
                || !UteisValidacao.emptyNumber(ventilometria.getVo2max()));
        parameters.put("limiar1", ventilometria.getLimiarVentilatorio() == null ? "" :
                Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(ventilometria.getLimiarVentilatorio())));
        parameters.put("limiar2", ventilometria.getLimiarVentilatorio2() == null ? "" :
                Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(ventilometria.getLimiarVentilatorio2())));


        List<ResultadoVO2> resultados = new ArrayList<ResultadoVO2>();
        String resultVo2 = "";
        switch (avaliacaoFisica.getProtocoloVo()) {
            case VO_CAMINHADA_CORRIDA_12_MINUTOS:
                resultados = calcularVo212Minutos(avaliacaoFisica, cliente.isSexoMasculino(), cliente.getIdade());
                for (ResultadoVO2 r : resultados) {
                    if (r.getResult() != null && r.getResult().contains("escolhido")) {
                        resultVo2 = viewUtils.getLabel(r.getCategoria());
                    }
                }
                parameters.put("testeCampo", "Teste: " + viewUtils.getLabel(ProtocolosVo2MaxEnum.VO_CAMINHADA_CORRIDA_12_MINUTOS.name()));
                parameters.put("valor1TesteCampo", "Distância percorrida: " + avaliacaoFisica.getDistancia12()
                        + "m - " + resultVo2);
                parameters.put("valor2TesteCampo", "VO2 Máx (ml/kg/min): " + avaliacaoFisica.getVo2Max12());
                parameters.put("valor3TesteCampo", "");
                break;
            case VO_CAMINHADA_2400_M:
                resultados = calcularVo2Teste2400(avaliacaoFisica, cliente.isSexoMasculino(), cliente.getIdade());
                for (ResultadoVO2 r : resultados) {
                    if (r.getResult() != null && r.getResult().contains("escolhido")) {
                        resultVo2 = viewUtils.getLabel(r.getCategoria());
                    }
                }
                parameters.put("testeCampo", "Teste: " + viewUtils.getLabel(ProtocolosVo2MaxEnum.VO_CAMINHADA_2400_M.name()));
                parameters.put("valor1TesteCampo", "Tempo percorrido: " + avaliacaoFisica.getTempo2400() + "min - " + resultVo2);
                parameters.put("valor2TesteCampo", "VO2 Máx (ml/kg/min): " + avaliacaoFisica.getVo2Max2400());
                parameters.put("valor3TesteCampo", "");
                break;
            case VO_AEROBICO_DE_BANCO:
                resultados = calcularVoAerobicoDeBanco(avaliacaoFisica, cliente.isSexoMasculino(), cliente.getIdade());
                for (ResultadoVO2 r : resultados) {
                    if (r.getResult() != null && r.getResult().contains("escolhido")) {
                        resultVo2 = viewUtils.getLabel(r.getCategoria());
                    }
                }
                parameters.put("testeCampo", "Teste: " + viewUtils.getLabel(ProtocolosVo2MaxEnum.VO_AEROBICO_DE_BANCO.name()));
                parameters.put("valor1TesteCampo", "Frequência cardíaca (bpm): " + avaliacaoFisica.getFcQueens() + " - " + resultVo2);
                parameters.put("valor2TesteCampo", "VO2 Máximo (ml/kg/min): " + avaliacaoFisica.getVo2MaxQueens());
                parameters.put("valor3TesteCampo", "");
                break;
        }
        List<AvaliacaoFisica> precedentes = consultarPrecedentes(key, avaliacaoFisica.getDataAvaliacao(), avaliacaoFisica.getCliente().getCodigo());
        parameters.put("showcomparacoes", false);
        boolean mostrarAstrad = !UteisValidacao.emptyNumber(avaliacaoFisica.getFcAstrand4())
                || !UteisValidacao.emptyNumber(avaliacaoFisica.getFcAstrand5())
                || !UteisValidacao.emptyNumber(avaliacaoFisica.getCargaAstrand())
                || !UteisValidacao.emptyNumber(avaliacaoFisica.getVo2Astrand())
                || !UteisValidacao.emptyNumber(avaliacaoFisica.getVo2MaxAstrand());
        if (mostrarAstrad) {
            calcularVo2Astrand(avaliacaoFisica, cliente.isSexoMasculino());
        }
        parameters.put("showAstrand", mostrarAstrad);
        double pr = (avaliacaoFisica.getFcAstrand4() + avaliacaoFisica.getFcAstrand5()) / 2;
        parameters.put("frequenciaAstrand", pr);
        parameters.put("cargaAstrand", avaliacaoFisica.getCargaAstrand());
        parameters.put("vo2Astrand", avaliacaoFisica.getVo2Astrand());
        parameters.put("vo2MaxAstrand", avaliacaoFisica.getVo2MaxAstrand());

        return parameters;
    }

    private boolean validarShowFlexibilidade(Flexibilidade flexibilidade) {
        return flexibilidade != null
                && ((!UteisValidacao.emptyNumber(flexibilidade.getCodigo()) && !UteisValidacao.emptyNumber(flexibilidade.getAlcance()))
                || flexibilidade.getMobilidadeOmbroDireito() != null
                || flexibilidade.getMobilidadeOmbroEsquerdo() != null
                || flexibilidade.getMobilidadeQuadrilEsquerdo() != null
                || flexibilidade.getMobilidadeQuadrilDireito() != null
                || flexibilidade.getMobilidadeJoelhoDireito() != null
                || flexibilidade.getMobilidadeJoelhoEsquerdo() != null
                || !UteisValidacao.emptyNumber(flexibilidade.getMobilidadeTornozeloDireito())
                || !UteisValidacao.emptyNumber(flexibilidade.getMobilidadeTornozeloEsquerdo()))
                || flexibilidade.getObservacao() != null
                || flexibilidade.getObservacaoQuadril() != null
                || flexibilidade.getObservacaoOmbro() != null
                || flexibilidade.getObservacaoJoelho() != null
                || flexibilidade.getObservacaoTornozelo() != null;

    }

    private void calcularResultadoRelacaoCinturaQuadrilImpressao(Map<String, Object> parameters, List<ItemRelatorioTO> perimetria, ClienteSintetico cliente) {
        try {
            parameters.put("showResultadoRCQ", true);
            String resultado = "";
            String classificacao = "";
            Double valorRQC = 0.00;
            Double cintura = 0.00;
            Double quadril = 0.00;

            for (ItemRelatorioTO item : perimetria) {
                if (item.getValor1().toLowerCase().equals("cintura")) {
                    try {
                        cintura = Double.parseDouble(item.getValor2().replace(",","."));
                    } catch (NumberFormatException e) { }
                } else if (item.getValor1().toLowerCase().equals("quadril")) {
                    try {
                        quadril = Double.parseDouble(item.getValor2().replace(",","."));
                    } catch (NumberFormatException e) { }
                }
            }
            if (cintura > 0 && quadril > 0) {
                valorRQC = cintura / quadril;
                if (cliente.getSexo().toUpperCase().equals("M")) {
                    classificacao = "Classificação Masculina";

                    if (cliente.getIdade() <= 29) {
                        if (valorRQC < 0.83) {
                            resultado = "Baixo";
                        } else if (valorRQC >= 0.83 && valorRQC < 0.89) {
                            resultado = "Moderado";
                        } else if (valorRQC >= 0.89 && valorRQC <= 0.94) {
                            resultado = "Alto";
                        } else if (valorRQC > 0.94) {
                            resultado = "Muito Alto";
                        }
                    } else if (cliente.getIdade() >= 30 && cliente.getIdade() <= 39) {
                        if (valorRQC < 0.84) {
                            resultado = "Baixo";
                        } else if (valorRQC >= 0.84 && valorRQC < 0.92) {
                            resultado = "Moderado";
                        } else if (valorRQC >= 0.92 && valorRQC <= 0.96) {
                            resultado = "Alto";
                        } else if (valorRQC > 0.96) {
                            resultado = "Muito Alto";
                        }
                    } else if (cliente.getIdade() >= 40 && cliente.getIdade() <= 49) {
                        if (valorRQC < 0.88) {
                            resultado = "Baixo";
                        } else if (valorRQC >= 0.88 && valorRQC < 0.96) {
                            resultado = "Moderado";
                        } else if (valorRQC >= 0.96 && valorRQC <= 1.00) {
                            resultado = "Alto";
                        } else if (valorRQC > 1.00) {
                            resultado = "Muito Alto";
                        }
                    } else if (cliente.getIdade() >= 50 && cliente.getIdade() <= 59) {
                        if (valorRQC < 0.90) {
                            resultado = "Baixo";
                        } else if (valorRQC >= 0.90 && valorRQC < 0.97) {
                            resultado = "Moderado";
                        } else if (valorRQC >= 0.97 && valorRQC <= 1.02) {
                            resultado = "Alto";
                        } else if (valorRQC > 1.02) {
                            resultado = "Muito Alto";
                        }
                    } else if (cliente.getIdade() >= 60) {
                        if (valorRQC < 0.91) {
                            resultado = "Baixo";
                        } else if (valorRQC >= 0.91 && valorRQC < 0.99) {
                            resultado = "Moderado";
                        } else if (valorRQC >= 0.99 && valorRQC <= 1.03) {
                            resultado = "Alto";
                        } else if (valorRQC > 1.03) {
                            resultado = "Muito Alto";
                        }
                    }

                } else if (cliente.getSexo().toUpperCase().equals("F")) {
                    classificacao = "Classificação Feminina";

                    if (cliente.getIdade() <= 29) {
                        if (valorRQC < 0.71) {
                            resultado = "Baixo";
                        } else if (valorRQC >= 0.71 && valorRQC < 0.78) {
                            resultado = "Moderado";
                        } else if (valorRQC >= 0.78 && valorRQC <= 0.82) {
                            resultado = "Alto";
                        } else if (valorRQC > 0.82) {
                            resultado = "Muito Alto";
                        }
                    } else if (cliente.getIdade() >= 30 && cliente.getIdade() <= 39) {
                        if (valorRQC < 0.72) {
                            resultado = "Baixo";
                        } else if (valorRQC >= 0.72 && valorRQC < 0.79) {
                            resultado = "Moderado";
                        } else if (valorRQC >= 0.79 && valorRQC <= 0.84) {
                            resultado = "Alto";
                        } else if (valorRQC > 0.84) {
                            resultado = "Muito Alto";
                        }
                    } else if (cliente.getIdade() >= 40 && cliente.getIdade() <= 49) {
                        if (valorRQC < 0.73) {
                            resultado = "Baixo";
                        } else if (valorRQC >= 0.73 && valorRQC < 0.80) {
                            resultado = "Moderado";
                        } else if (valorRQC >= 0.80 && valorRQC <= 0.87) {
                            resultado = "Alto";
                        } else if (valorRQC > 0.87) {
                            resultado = "Muito Alto";
                        }
                    } else if (cliente.getIdade() >= 50 && cliente.getIdade() <= 59) {
                        if (valorRQC < 0.74) {
                            resultado = "Baixo";
                        } else if (valorRQC >= 0.74 && valorRQC < 0.82) {
                            resultado = "Moderado";
                        } else if (valorRQC >= 0.82 && valorRQC <= 0.88) {
                            resultado = "Alto";
                        } else if (valorRQC > 0.88) {
                            resultado = "Muito Alto";
                        }
                    } else if (cliente.getIdade() >= 60) {
                        if (valorRQC < 0.76) {
                            resultado = "Baixo";
                        } else if (valorRQC >= 0.76 && valorRQC < 0.84) {
                            resultado = "Moderado";
                        } else if (valorRQC >= 0.84 && valorRQC <= 0.90) {
                            resultado = "Alto";
                        } else if (valorRQC > 0.90) {
                            resultado = "Muito Alto";
                        }
                    }
                }
                BigDecimal bd = new BigDecimal(valorRQC);
                bd = bd.setScale(2, RoundingMode.DOWN);
                double truncatedNumber = bd.doubleValue();
                parameters.put("valorCalculoRCQ", String.format("%.2f", truncatedNumber));
                parameters.put("tipoRCQ", classificacao);
                parameters.put("resultadoRCQ", resultado);
            } else {
                parameters.put("showResultadoRCQ", false);
            }
        } catch (Exception e) {
            Uteis.logar(e, AvaliacaoFisicaImpl.class);
        }
    }



    private void organizarFotoPostural(String key, String chaveMap, Map<String, Object> parameters, String fotoKey, ServletContext servletContext, Integer codigo) throws Exception {
        if (UteisValidacao.emptyString(fotoKey)) {
            parameters.put(chaveMap, null);
        } else {
            byte[] b = MidiaService.getInstance().downloadObjectWithKeyByteArray(fotoKey);
            ResizeImage rzi = new ResizeImage();
            String nomeArq = "postural-".concat(chaveMap).concat(key).concat("-")
                    .concat(codigo.toString())
                    .concat(".jpg");
            b = rzi.scale(nomeArq, servletContext, b, 0, 320);
            String arquivo = rzi.salvarArquivoURL(nomeArq, b, servletContext);
            parameters.put(chaveMap, arquivo);
        }
    }

    private String cleanParam(String p) {
        return p == null ||
                p.equals("0") ||
                p.equals("0.0") ? "" : p;
    }

    private List<ComparativoTO> montarComparativo(String key, List<AvaliacaoFisica> avaliacoes, ViewUtils vu, String language) throws Exception {
        List<ComparativoTO> comparativoTOS = new ArrayList<ComparativoTO>();
        Map<String, ComparativoTO> mapaObjs = new HashMap<String, ComparativoTO>();
        int av = 1;
        ConfiguracaoSistemaService cfs = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
        List<ConfiguracaoPerimetro> cfgPerimetros = cfs.obterCfgsPerimetro(key);
        for (AvaliacaoFisica a : avaliacoes) {

            addValorComparativo(mapaObjs, "00-<b>Data</b>", av, "<b>" + Uteis.getDataAplicandoFormatacao(a.getDataAvaliacao(), "dd/MM/yy") + "</b>");
            String nomeResponsavelLancamento = " - ";
            if (a.getResponsavelLancamento_codigo() != null) {
                Usuario usu = usuarioService.obterPorId(key, a.getResponsavelLancamento_codigo());
                nomeResponsavelLancamento = usu == null ? "" : Uteis.getPrimeiroNome(usu.getNomeApresentar());
            }
            addValorComparativo(mapaObjs, "01-<b>Professor</b>", av, "<b>" + nomeResponsavelLancamento + "</b>");

            int i = 2;
            for (ConfiguracaoPerimetro cp : cfgPerimetros) {
                boolean direitaesquerda = cp.getEsquerda() != null && cp.getDireita() != null;
                String ordem = i < 10 ? ("0" + i) : String.valueOf(i);

                if (cp.getEsquerda() != null) {
                    addValorComparativo(mapaObjs, ordem + "-" + vu.getLabel(cp.getLabel()) + (direitaesquerda ? " ESQ" : ""),
                            av, Double.parseDouble((String) UtilReflection.getValor(a, cp.getEsquerda().getField())));
                }
                if (cp.getDireita() != null) {
                    addValorComparativo(mapaObjs, ordem + "-" + vu.getLabel(cp.getLabel()) + (direitaesquerda ? " DIR" : ""),
                            av, Double.parseDouble((String) UtilReflection.getValor(a, cp.getDireita().getField())));
                }

                i++;
            }
            addValorComparativo(mapaObjs, "16-Total perimetria", av,
                    a.getTotalPerimetria() == null ? 0.0 :
                            Uteis.arredondarForcando2CasasDecimaisMantendoSinal(a.getTotalPerimetria()));

            Flexibilidade flexibilidade = obterFlexibilidade(key, a.getCodigo());
            if (flexibilidade == null) {
                addValorComparativo(mapaObjs, "17-Flexibilidade", av, a.getFlexibilidade());
            } else {
                addValorComparativo(mapaObjs, "17-Flexibilidade", av, flexibilidade.getAlcance() == null ? 0.0 : flexibilidade.getAlcance());
            }
            Double totalDobras = 0.0;
            if (a.getUsouAbdominal()) {
                addValorComparativo(mapaObjs, "18-Abdominal", av, a.getAbdominal());
                totalDobras += a.getAbdominal();
            }
            if (a.getUsouSuprailiaca()) {
                addValorComparativo(mapaObjs, "19-Suprailíaca", av, a.getSupraIliaca());
                totalDobras += a.getSupraIliaca();
            }
            if (!UteisValidacao.emptyNumber(a.getPeitoral())) {
                addValorComparativo(mapaObjs, "20-Peitoral", av, a.getPeitoral());
                totalDobras += a.getPeitoral();
            }
            if (a.getUsouTriceps()) {
                addValorComparativo(mapaObjs, "21-Tríceps", av, a.getTriceps());
                totalDobras += a.getTriceps();
            }
            if (a.getUsouCoxa()) {
                addValorComparativo(mapaObjs, "22-Coxa Média", av, a.getCoxaMedial());
                totalDobras += a.getCoxaMedial();
            }
            if (a.getUsouSubescapular()) {
                addValorComparativo(mapaObjs, "23-Subescapular", av, a.getSubescapular());
                totalDobras += a.getSubescapular();
            }
            if (a.getUsouAxilarMedia()) {
                addValorComparativo(mapaObjs, "24-Axilar Média", av, a.getAxilarMedia());
                totalDobras += a.getAxilarMedia();
            }
            if (a.getUsouPanturrilha()) {
                addValorComparativo(mapaObjs, "43-Panturrilha", av, a.getPanturrilha());
                totalDobras += a.getPanturrilha();
            }
            addValorComparativo(mapaObjs, "25-Total Dobras", av, Uteis.arredondarForcando2CasasDecimaisMantendoSinal(totalDobras));
            addValorComparativo(mapaObjs, "26-Massa magra", av, a.getMassaMagra());
            addValorComparativo(mapaObjs, "27-Massa gorda", av, a.getMassaGorda());
            addValorComparativo(mapaObjs, "28-Altura", av, Uteis.formatString(a.getAltura(), "#0.00"));
            addValorComparativo(mapaObjs, "29-Peso", av, a.getPeso());
            addValorComparativo(mapaObjs, "30-Protocolo", av, vu.getLabel(a.getProtocolo().name()));
            addValorComparativo(mapaObjs, "31-% Gordura", av, a.getPercentualGordura() + "%");
            if (!UteisValidacao.emptyNumber(a.getMetaPercentualGordura())) {
                addValorComparativo(mapaObjs, "31-Meta % Gordura", av, a.getMetaApresentar() + "%");
            }

            if (a.getProtocolo() != null && a.getProtocolo().equals(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA)) {
                addValorComparativo(mapaObjs, "32-IMC", av, a.getImc());
                addValorComparativo(mapaObjs, "35-% M. magra", av, Uteis.arredondarForcando2CasasDecimaisMantendoSinal(a.getPercentualMassaMagra()) + "%");
                addValorComparativo(mapaObjs, "36-% M. gorda", av, Uteis.arredondarForcando2CasasDecimaisMantendoSinal(a.getPercentualGordura()) + "%");
                addValorComparativo(mapaObjs, "37-Ossos", av, a.getPesoOsseo());
                addValorComparativo(mapaObjs, "38-Resíduos", av, a.getResidual());
                if (!UteisValidacao.emptyNumber(a.getPesoMuscular())) {
                    addValorComparativo(mapaObjs, "39-Músculos", av, a.getPesoMuscular() + " kg");
                }
                addValorComparativo(mapaObjs, "40-% Água", av, a.getPercentualAgua() + "%");
                addValorComparativo(mapaObjs, "41-TMB", av, a.getTmb());
                addValorComparativo(mapaObjs, "42-Resistência", av, a.getResistencia());
                addValorComparativo(mapaObjs, "43-Reatância", av, a.getReatancia());
                addValorComparativo(mapaObjs, "44-Gordura ideal", av, a.getGorduraIdeal());
                addValorComparativo(mapaObjs, "45-Necessidade física", av, a.getNecessidadeFisica());
                addValorComparativo(mapaObjs, "46-Necessidade calórica", av, a.getNecessidadeCalorica());
                addValorComparativo(mapaObjs, "47-Idade metabólica", av, a.getIdadeMetabolica());
                addValorComparativo(mapaObjs, "48-Gordura visceral", av, a.getGorduraVisceral());
            }
            addValorComparativo(mapaObjs, "39-Risco cardivascular", av,
                    a.getResultadoRiscoCA() == null ? "" : vu.getLabelInternacionalizada(a.getResultadoRiscoCA(), language));

            addValorComparativo(mapaObjs, "40-Endomorfia", av, a.getEndomorfia());
            addValorComparativo(mapaObjs, "41-Mesomorfia", av, a.getMesomorfia());
            addValorComparativo(mapaObjs, "42-Ectomorfia", av, a.getEctomorfia());
            av++;
        }
        comparativoTOS = Ordenacao.ordenarLista(new ArrayList(mapaObjs.values()), "descricao");
        List<ComparativoTO> rt = new ArrayList<ComparativoTO>();
        for (ComparativoTO i : comparativoTOS) {
            i.setDescricao(i.getDescricao().split("-")[1]);
            if (!UteisValidacao.emptyString(i.getValor1())
                    || !UteisValidacao.emptyString(i.getValor2())
                    || !UteisValidacao.emptyString(i.getValor3())
                    || !UteisValidacao.emptyString(i.getValor4())
                    || !UteisValidacao.emptyString(i.getValor5())
            ) {
                rt.add(i);
            }
        }
        return rt;

    }

    private void addValorComparativo(Map<String, ComparativoTO> mapa,
                                     String d,
                                     Integer index,
                                     Double v) throws Exception {
        addValorComparativo(mapa, d, index, UteisValidacao.emptyNumber(v) ?
                "" : apresentar(v));
    }

    private void addValorComparativo(Map<String, ComparativoTO> mapa,
                                     String d,
                                     Integer index,
                                     String v) throws Exception {
        ComparativoTO comparativoTO = mapa.get(d);
        if (comparativoTO == null) {
            comparativoTO = new ComparativoTO();
            comparativoTO.setDescricao(d);
            mapa.put(d, comparativoTO);
        }
        UtilReflection.setValor(comparativoTO, v, "valor" + index);

    }

    private void addItemBioImpedancia(String label, Double valor, String v, List<ItemRelatorioTO> dobras) {
        if (!UteisValidacao.emptyNumber(valor)) {
            dobras.add(new ItemRelatorioTO(label, v));
        }
    }


    private void addPerimetria(List<ItemRelatorioTO> perimetria, String desc, Double v1, Double v2) {
        if (!UteisValidacao.emptyNumber(v1)
                || !UteisValidacao.emptyNumber(v2)) {
            perimetria.add(new ItemRelatorioTO(desc, v1, v2));
        }
    }

    public String getDesignIReportComparativo() {
        return ("br" + File.separator +
                "com" + File.separator +
                "pacto" + File.separator +
                "relatorio" + File.separator +
                "avaliacao" + File.separator +
                "comparacoes_avaliacoes.jrxml");
    }

    public String getDesignIReportParq() {
        return ("br" + File.separator +
                "com" + File.separator +
                "pacto" + File.separator +
                "relatorio" + File.separator +
                "avaliacao" + File.separator +
                "parq.jrxml");
    }

    public String getDesignIReportAvaliacaoFisica() {
        return ("br" + File.separator +
                "com" + File.separator +
                "pacto" + File.separator +
                "relatorio" + File.separator +
                "avaliacao" + File.separator +
                "avaliacao_fisica.jrxml");
    }

    public String getDesignIReportAvaliacaoFisicaSubReport() {
        return ("br" + File.separator +
                "com" + File.separator +
                "pacto" + File.separator +
                "relatorio" + File.separator +
                "avaliacao" + File.separator);
    }

    public PesoOsseo gerarPesoOsseo(AvaliacaoFisica avaliacaoFisica, ClienteSintetico cliente) throws Exception {
        PesoOsseo pesoOsseo = new PesoOsseo();
        pesoOsseo.setDiametroFemur(0.0);
        pesoOsseo.setDiametroPunho(0.0);
        pesoOsseo.setAltura(avaliacaoFisica.getAltura());
        pesoOsseo.setPeso(avaliacaoFisica.getPeso());
        pesoOsseo.setPercentualGordura(avaliacaoFisica.getPercentualGordura());
        pesoOsseo = calcularPesoOsseo(pesoOsseo, cliente.isSexoMasculino());
        return pesoOsseo;
    }

    public String apresentar(Double valor) {
        return valor == null ? "0" : Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(valor));
    }

    public List<ItemAvaliacaoPostural> consultarItens(final String key, final AvaliacaoPostural postural) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("postural", postural.getCodigo());
        return itemPosturalDao.findByParam(key,
                "SELECT obj FROM ItemAvaliacaoPostural obj where obj.avaliacaoPostural.codigo = :postural",
                params);
    }

    public AvaliacaoPostural consultarPostural(final String key, final AvaliacaoFisica avaliacao) throws Exception {
        return posturalDao.getAvaliacaoPostural(key, avaliacao);
    }

    public String salvarFotoPostural(String key, UploadedFile obj, String identificador) throws Exception {
        return MidiaService.getInstanceWood().uploadObjectFromByteArray(key, MidiaEntidadeEnum.FOTO_AVALIACAO_POSTURAL,
                identificador, obj.getContents());
    }

    public String salvarFotoPosturalComByte(String key, byte[] obj, String identificador) throws Exception {
        return MidiaService.getInstanceWood().uploadObjectFromByteArray(key, MidiaEntidadeEnum.FOTO_AVALIACAO_POSTURAL,
                identificador, obj);
    }

    public void deleteFotoPostural(final String key) throws ServiceException {
        try {
            MidiaService.getInstanceWood().deleteObject(key);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public AvaliacaoPostural gravarAvaliacaoPostural(final String key, AvaliacaoPostural avaliacaoPostural,
                                                     List<ItemAvaliacaoPostural> itens) throws Exception {
        boolean nenhumaResposta = true;

        if (UteisValidacao.emptyNumber(avaliacaoPostural.getCodigo()) &&
                avaliacaoPostural.getAvaliacao() != null &&
                verificarSeExisteAvPostural(key, avaliacaoPostural.getAvaliacao().getCodigo())) {
            avaliacaoPostural = consultarPostural(key, avaliacaoPostural.getAvaliacao());
        }

        for (ItemAvaliacaoPostural i : itens) {
            if (i.getSelecionado()) {
                nenhumaResposta = false;
                break;
            }
        }

        UploadedFile imgAnterior = avaliacaoPostural.getImgAnterior();
        UploadedFile imgPosterior = avaliacaoPostural.getImgPosterior();
        UploadedFile imgEsquerda = avaliacaoPostural.getImgEsquerda();
        UploadedFile imgDireita = avaliacaoPostural.getImgDireita();

        byte[] imgDireitaByte = avaliacaoPostural.getImgDireitaByte();
        byte[] imgEsquerdaByte = avaliacaoPostural.getImgEsquerdaByte();
        byte[] imgAnteriorByte = avaliacaoPostural.getImgAnteriorByte();
        byte[] imgPosteriorByte = avaliacaoPostural.getImgPosteriorByte();

        if (UteisValidacao.emptyNumber(avaliacaoPostural.getCodigo())) {
            avaliacaoPostural = posturalDao.insert(key, avaliacaoPostural);
        } else {
            avaliacaoPostural = posturalDao.update(key, avaliacaoPostural);
        }

        if (!nenhumaResposta) {
            for (ItemAvaliacaoPostural i : itens) {
                i.setAvaliacaoPostural(avaliacaoPostural);
                if (UteisValidacao.emptyNumber(i.getCodigo())) {
                    itemPosturalDao.insert(key, i);
                } else {
                    itemPosturalDao.update(key, i);
                }
            }
        }

        AvaliacaoPostural avaliacaoPosturalKeyImg = consultarPostural(key, avaliacaoPostural.getAvaliacao());
        String keyImgAnterior = null;
        String keyImgDireita = null;
        String keyImgEsquerda = null;
        String keyImgPosterior = null;
        if (imgAnterior != null) {
            avaliacaoPostural.setKeyImgAnterior(salvarFotoPostural(key, imgAnterior, avaliacaoPostural.getCodigo().toString().concat("postural-costa")
                    .concat(String.valueOf(Calendario.hoje().getTime()))));
        } else {
            String lastkey = avaliacaoPosturalKeyImg != null ? avaliacaoPosturalKeyImg.getKeyImgAnterior() : null;
            keyImgAnterior = uploadOrDeleteImg(key, avaliacaoPostural, imgAnteriorByte, avaliacaoPostural.getKeyImgAnterior(), lastkey);
            avaliacaoPostural.setKeyImgAnterior(keyImgAnterior);
        }

        if (imgDireita != null) {
            avaliacaoPostural.setKeyImgDireita(salvarFotoPostural(key, imgDireita, avaliacaoPostural.getCodigo().toString().concat("postural-direita")
                    .concat(String.valueOf(Calendario.hoje().getTime()))));
        } else {
            String lastkey = avaliacaoPosturalKeyImg != null ? avaliacaoPosturalKeyImg.getKeyImgDireita() : null;
            keyImgDireita = uploadOrDeleteImg(key, avaliacaoPostural, imgDireitaByte, avaliacaoPostural.getKeyImgDireita(), lastkey);
            avaliacaoPostural.setKeyImgDireita(keyImgDireita);
        }

        if (imgEsquerda != null) {
            avaliacaoPostural.setKeyImgEsquerda(salvarFotoPostural(key, imgEsquerda, avaliacaoPostural.getCodigo().toString().concat("postural-esquerda")
                    .concat(String.valueOf(Calendario.hoje().getTime()))));
        } else {
            String lastkey = avaliacaoPosturalKeyImg != null ? avaliacaoPosturalKeyImg.getKeyImgEsquerda() : null;
            keyImgEsquerda = uploadOrDeleteImg(key, avaliacaoPostural, imgEsquerdaByte, avaliacaoPostural.getKeyImgEsquerda(), lastkey);
            avaliacaoPostural.setKeyImgEsquerda(keyImgEsquerda);
        }

        if (imgPosterior != null) {
            avaliacaoPostural.setKeyImgPosterior(salvarFotoPostural(key, imgPosterior, avaliacaoPostural.getCodigo().toString().concat("postural-frente")
                    .concat(String.valueOf(Calendario.hoje().getTime()))));
        } else {
            String lastkey = avaliacaoPosturalKeyImg != null ? avaliacaoPosturalKeyImg.getKeyImgPosterior() : null;
            keyImgPosterior = uploadOrDeleteImg(key, avaliacaoPostural, imgPosteriorByte, avaliacaoPostural.getKeyImgPosterior(), lastkey);
            avaliacaoPostural.setKeyImgPosterior(keyImgPosterior);
        }

        if (imgAnterior != null ||
                imgPosterior != null ||
                imgDireita != null ||
                imgEsquerda != null ||
                keyImgAnterior != null ||
                keyImgDireita != null ||
                keyImgEsquerda != null ||
                keyImgPosterior != null
        ) {
            avaliacaoPostural = posturalDao.update(key, avaliacaoPostural);
        }
        return avaliacaoPostural;
    }

    private boolean verificarSeExisteAvPostural(String ctx, Integer codAvaliacao) throws Exception {
        try {
            String sql = "select exists (select codigo from avaliacaopostural a where avaliacao_codigo = " + codAvaliacao + ");";
            try (ResultSet rs = posturalDao.createStatement(ctx, sql)) {
                if (rs.next()) {
                    return rs.getBoolean("exists");
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, AvaliacaoFisicaImpl.class);
        }
        return false;
    }

    private String uploadOrDeleteImg(final String key, AvaliacaoPostural avaliacaoPostural, Object... args) throws ServiceException {
        byte[] img = (byte[]) args[0];
        String keyImg = (String) args[1];
        String lastKey = (String) args[2];
        String result = null;
        if (lastKey != null) {
            result = !lastKey.equals("") ? lastKey.replace(Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/", "") : null;
        }
        try {
            if (img != null) {
                result = (salvarFotoPosturalComByte(key, img, avaliacaoPostural.getCodigo().toString().concat("postural-costa")
                        .concat(String.valueOf(Calendario.hoje().getTime()))));

            } else if ((keyImg == null || keyImg.equals("")) && lastKey != null && !lastKey.equals("")) {
                deleteFotoPostural(lastKey);
                result = null;
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return result;
    }

    public List<AvaliacaoFisica> consultarPrecedentes(final String key, final Date dia, final Integer aluno) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("dia", dia);
        params.put("aluno", aluno);
        return avaliacaoFisicaDao.findByParam(key,
                "SELECT obj FROM AvaliacaoFisica obj where obj.dataAvaliacao <= :dia and obj.cliente.codigo = :aluno " +
                        " ORDER BY dataAvaliacao desc ",
                params, 5, 0);
    }


    public void salvarAssinatura(String key, AvaliacaoFisica avaliacao) throws Exception {
        String dataSource = avaliacao.getAssinatura().replace(" ", "+");
        String url = MidiaService.getInstanceWood().uploadObjectFromByteArray(key, MidiaEntidadeEnum.ASSINATURA_PARQ,
                "assinaturaparq" + avaliacao.getCodigo(), Base64.decodeBase64(dataSource));
        avaliacao.setUrlAssinatura(url);
        alterar(key, avaliacao);
    }

    public RespostaClienteParQ salvarAssinaturaParQ(String ctx, String assinatura64, RespostaClienteParQ respostaClienteParQ) throws Exception {
        if (UteisValidacao.emptyNumber(respostaClienteParQ.getCodigo())) {
            parQDao.insert(ctx, respostaClienteParQ);
        }
        String dataSource = assinatura64.replace(" ", "+");
        String url = MidiaService.getInstanceWood().uploadObjectFromByteArray(ctx, MidiaEntidadeEnum.ASSINATURA_PARQ,
                "assinaturaparq" + respostaClienteParQ.getCodigo(), Base64.decodeBase64(dataSource));
        respostaClienteParQ.setUrlAssinatura(url);
        parQDao.update(ctx, respostaClienteParQ);
        return respostaClienteParQ;
    }

    public void limparAssinatura(String key, AvaliacaoFisica avaliacao) throws Exception {
        MidiaService.getInstanceWood().deleteObject(avaliacao.getUrlAssinatura());
    }

    public String gerarPDFParQ(final String key, final Anamnese questionarioParq, AvaliacaoFisica avaliacaoFisica,
                               final Usuario usuario, ViewUtils viewUtils,
                               HttpServletRequest request, ServletContext servletContext, boolean externo, String assinaturaDigital) throws Exception {


        PDFFromXmlFile geradorPDF = new PDFFromXmlFile();
        Map<String, Object> parameters = new HashMap<String, Object>();
        if (usuario.getEmpresaDefault() == null) {
            final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
            parameters.put("logoPadraoRelatorio", imagemTreino);
            parameters.put("empresaNome", "");
            parameters.put("empresaEndereco", "");
            parameters.put("empresaSite", "");
        } else {
            if (!SuperControle.independente(key)) {
                String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, usuario.getEmpresaDefault().getCodigo().toString());
                String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
                parameters.put("logoPadraoRelatorio", urlFoto);
            }
            parameters.put("empresaNome", usuario.getEmpresaDefault().getNome());
            parameters.put("empresaEndereco", usuario.getEmpresaDefault().getEndereco());
            parameters.put("empresaSite", prepparam(usuario.getEmpresaDefault().getSite()).toLowerCase().replaceAll("https://", "").replaceAll("http://", ""));
        }
        parameters.put("SUBREPORT_DIR", getDesignIReportAvaliacaoFisicaSubReport());
        parameters.put("usuario", usuario == null ? "Impresso pelo aluno" : usuario.getNome());
        parameters.put("horaEmissao", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm"));
        parameters.put("data", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm"));

        String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_PESSOA, avaliacaoFisica.getCliente().getCodigoPessoa().toString());
        String urlFoto = String.format("%s/%s", new Object[]{
                Aplicacao.getProp(Aplicacao.urlFotosNuvem),
                genKey});
        parameters.put("fotoAluno", urlFoto);
        parameters.put("nomeAluno", WordUtils.capitalize(avaliacaoFisica.getCliente().getNome().toLowerCase()));
        parameters.put("idade", avaliacaoFisica.getCliente().getIdade().toString().concat(" anos"));
        parameters.put("sexo", avaliacaoFisica.getCliente().getSexoFull());
        parameters.put("dataAvaliacao", Uteis.getData(avaliacaoFisica.getDataAvaliacao()));
        parameters.put("apresentarAssinaturaDigital", !UteisValidacao.emptyString(assinaturaDigital));
        parameters.put("assinaturaDigital", assinaturaDigital);

        boolean algumParq = false;
        List<ItemRelatorioTO> parq = new ArrayList<ItemRelatorioTO>();
        for (PerguntaAnamnese pa : questionarioParq.getPerguntas()) {
            String resposta = UteisValidacao.emptyString(pa.getResposta()) ? "" :
                    viewUtils.getLabel("cadastros." + pa.getResposta().toLowerCase());
            resposta = resposta + (UteisValidacao.emptyString(pa.getComplemento()) ? "" :
                    " - ".concat(pa.getComplemento()));
            if (!UteisValidacao.emptyString(pa.getResposta())) {
                algumParq = true;
            }
            parq.add(new ItemRelatorioTO(
                    pa.getPergunta().getOrdem().toString().concat(" - ").concat(pa.getPergunta().getDescricao()),
                    resposta));
        }
        parameters.put("showparq", algumParq);

        parameters.put("parqJR", new JRBeanCollectionDataSource(parq));

        parameters.put("apresentarAssinatura", !UteisValidacao.emptyString(avaliacaoFisica.getUrlAssinatura()));
        if (!UteisValidacao.emptyString(avaliacaoFisica.getUrlAssinatura())) {
            parameters.put("assinatura", avaliacaoFisica.getUrlProntaAssinatura());
        }
        parameters.put("parq", avaliacaoFisica.getCliente().getParq() != null && avaliacaoFisica.getCliente().getParq() ?
                viewUtils.getLabel("positivo") : viewUtils.getLabel("negativo"));
        String pdf = geradorPDF.gerarPDF(key, request, getDesignIReportParq(), parameters, "parq",
                false, externo, "PT");
        return pdf;
    }

    public String gerarPDFParQAssinaturaDigital(final String ctx,
                                                final Anamnese questionarioParq,
                                                final List<RespostaCliente> respostaClienteList,
                                                RespostaClienteParQ respostaClienteParQ,
                                                ViewUtils viewUtils,
                                                HttpServletRequest request,
                                                ServletContext servletContext,
                                                boolean externo,
                                                Integer cliente) throws Exception {


        PDFFromXmlFile geradorPDF = new PDFFromXmlFile();
        Map<String, Object> parameters = new HashMap<String, Object>();
        Usuario usuario = null;

        if (UteisValidacao.notEmptyNumber(respostaClienteParQ.getUsuario_codigo())) {
            usuario = usuarioService.obterPorId(ctx, respostaClienteParQ.getUsuario_codigo());
        }

        if (usuario == null) {
            usuario = usuarioService.consultarPorCliente(ctx, cliente);
        }

        try {
            if (!SuperControle.independente(ctx) && usuario != null && UteisValidacao.emptyList(usuario.getEmpresasZW())) {
                usuario.setEmpresasZW(UtilContext.getBean(AdmWSConsumer.class).obterEmpresas(ctx));
            }
        } catch (Exception e) {
            Uteis.logar(e, AvaliacaoFisicaImpl.class);
        }

        if (usuario == null || (usuario != null && usuario.getEmpresaDefault() == null)) {
            final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
            parameters.put("logoPadraoRelatorio", imagemTreino);
            parameters.put("empresaNome", "");
            parameters.put("empresaEndereco", "");
            parameters.put("empresaSite", "");
        } else {
            if (!SuperControle.independente(ctx)) {
                String genKey = MidiaService.getInstance().genKey(ctx, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, usuario.getEmpresaDefault().getCodigo().toString());
                String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
                parameters.put("logoPadraoRelatorio", urlFoto);
            }
            parameters.put("empresaNome", usuario.getEmpresaDefault().getNome());
            parameters.put("empresaEndereco", usuario.getEmpresaDefault().getEndereco());
            parameters.put("empresaSite", prepparam(usuario.getEmpresaDefault().getSite()).toLowerCase().replaceAll("https://", "").replaceAll("http://", ""));
        }
        parameters.put("SUBREPORT_DIR", getDesignIReportAvaliacaoFisicaSubReport());
        parameters.put("usuario", usuario == null ? "Impresso pelo aluno" : usuario.getNome());
        parameters.put("horaEmissao", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm"));
        parameters.put("data", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm"));

        ClienteSintetico clienteParQ = respostaClienteParQ.getCliente();
        String urlFoto;
        if (clienteParQ != null && clienteParQ.getPessoa() != null && !UteisValidacao.emptyString(clienteParQ.getPessoa().getFotoKey())) {
            urlFoto = Aplicacao.obterUrlFotoDaNuvem(clienteParQ.getPessoa().getFotoKey());
        } else {
            urlFoto = Aplicacao.obterUrlFotoDaNuvem(null);
        }
        parameters.put("fotoAluno", urlFoto);
        parameters.put("nomeAluno", WordUtils.capitalize(clienteParQ.getNome().toLowerCase()));
        parameters.put("idade", clienteParQ.getIdade().toString().concat(" anos"));
        parameters.put("sexo", clienteParQ.getSexoFull());
        parameters.put("dataAvaliacao", Uteis.getData(respostaClienteParQ.getDataResposta()));
        parameters.put("apresentarAssinaturaDigital", !UteisValidacao.emptyString(respostaClienteParQ.getUrlAssinatura()));
        parameters.put("assinaturaDigital", respostaClienteParQ.getUrlAssinatura());

        boolean algumParq = false;
        List<ItemRelatorioTO> parq = new ArrayList<ItemRelatorioTO>();
        for (RespostaCliente rc : respostaClienteList) {
            String resposta = UteisValidacao.emptyString(rc.getResposta()) ? "" : viewUtils.getLabel("cadastros." + rc.getResposta().toLowerCase());
            resposta = resposta + (UteisValidacao.emptyString(rc.getObs()) ? "" : " - ".concat(rc.getObs()));
            if (!UteisValidacao.emptyString(rc.getResposta())) {
                algumParq = true;
            }
            parq.add(new ItemRelatorioTO(
                    rc.getPerguntaAnamnese().getPergunta().getOrdem().toString().concat(" - ").concat(rc.getPerguntaAnamnese().getPergunta().getDescricao()),
                    resposta));
        }
        parameters.put("showparq", algumParq);

        parameters.put("parqJR", new JRBeanCollectionDataSource(parq));

        parameters.put("apresentarAssinatura", !UteisValidacao.emptyString(respostaClienteParQ.getUrlAssinatura()));
        if (!UteisValidacao.emptyString(respostaClienteParQ.getUrlAssinatura())) {
            parameters.put("assinatura", respostaClienteParQ.getFullUrlAssinatura());
        }
        parameters.put(
                "parq",
                respostaClienteParQ.getParqPositivo() ? viewUtils.getLabel("positivo") : viewUtils.getLabel("negativo")
        );

        ConfiguracaoSistemaService cfs = UtilContext.getBean(ConfiguracaoSistemaService.class);

        String leiParqValue = "";
        boolean encontrouEstado = false;

        ClienteSintetico clienteSintetico = clienteDao.obterPorId(ctx, cliente);
        Integer empresaId = (clienteSintetico != null) ? clienteSintetico.getEmpresa() : null;

        if (empresaId != null) {
            Empresa emp = empresaService.obterPorId(ctx, empresaId);

            if (emp != null && emp.getCodZW() != null) {
                String queryEmp = String.format("SELECT * FROM EMPRESA WHERE codigo = %d", emp.getCodZW());
                try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                     ResultSet rsEmp = ConexaoZWServiceImpl.criarConsulta(queryEmp, conZW)) {

                    if (rsEmp.next()) {
                        String estadoCodigo = rsEmp.getString("estado");
                        if (!UteisValidacao.emptyString(estadoCodigo)) {
                            String queryEstado = String.format("SELECT * FROM estado WHERE codigo = %s", estadoCodigo);
                            try (Connection conEstado = conexaoZWService.conexaoZw(ctx);
                                 ResultSet rsEstado = ConexaoZWServiceImpl.criarConsulta(queryEstado, conEstado)) {

                                if (rsEstado.next()) {
                                    String sigla = rsEstado.getString("sigla");
                                    if (sigla != null) {
                                        String siglaU = sigla.toUpperCase();
                                        if (siglaU.equals("RJ")) {
                                            leiParqValue = "leiParqRJ";
                                            encontrouEstado = true;
                                        } else if (siglaU.equals("GO")) {
                                            leiParqValue = "leiParqGO";
                                            encontrouEstado = true;
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                Uteis.logar(e, ("Erro ao consultar estado para empresa com codZW " + emp.getCodZW()).getClass());
                            }
                        }
                    }
                } catch (Exception e) {
                    Uteis.logar(e, ("Erro ao consultar Empresa via ZW para codZW " + emp.getCodZW()).getClass());
                }
            }
        }

        if (!encontrouEstado) {
            boolean valorConfig = cfs.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_VISUALIZAR_LEI_PARQ).getValorAsBoolean();
            leiParqValue = String.valueOf(valorConfig);
        }

        parameters.put("showLeiParq", leiParqValue);

        boolean treinoIndependente = Aplicacao.independente(ctx);

        if (treinoIndependente) {
            // Para treino independente, usar sempre JasperReports local
            parameters.put("parqJR", new JRBeanCollectionDataSource(parq));
            String pdf = geradorPDF.gerarPDF(ctx, request, getDesignIReportParq(), parameters, "parq",
                    false, externo, "PT");
            return pdf;
        } else {
            // Para treino com ZW, usar microserviço
            ImpressaoAvaliacaoDTO impressaoAvaliacaoDTO = new ImpressaoAvaliacaoDTO();
            impressaoAvaliacaoDTO.setParametros(parameters);
            impressaoAvaliacaoDTO.setParq(parq);
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(ctx);
            String url = clientDiscoveryDataDTO.getServiceUrls().getRelatorioMsUrl();
            String endpoint = "/avaliacao-fisica/parq";
            String authorization = "Bearer " + AuthenticacaoMsService.getAutenticacaoTokenDTO(ctx, "").getToken();
            String pdf = geradorPDF.sendToMs(url, endpoint, impressaoAvaliacaoDTO, authorization);
            return pdf;
        }
    }


    public Double obterUltimaMetaGordura(String key, Integer cliente, Date diaAvaliacao, Integer avaliacao) throws Exception {
        Double meta = null;
        if (diaAvaliacao == null) {
            diaAvaliacao = Calendario.hoje();
        }
        StringBuilder sql = new StringBuilder();
        sql.append("select metapercentualgordura from avaliacaofisica  \n");
        sql.append("where cliente_codigo = ").append(cliente).append(" and dataavaliacao <= '").append(Uteis.getDataAplicandoFormatacao(diaAvaliacao, "yyyy-MM-dd")).append("' \n");
        if (!UteisValidacao.emptyNumber(avaliacao)) {
            sql.append(" and codigo < ").append(avaliacao);
        }
        sql.append(" order by dataavaliacao desc limit 1 ");
        List listOfObjects = avaliacaoFisicaDao.listOfObjects(key, sql.toString());
        for (Object o : listOfObjects) {
            meta = (Double) o;
        }
        return meta;
    }

    public List<AvaliacaoIntegradaJSON> obterJSONAvaliacoesIntegradas(final String ctx, final String matricula) throws Exception {
        ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(ctx, matricula);
        if (cliente == null) {
            return new ArrayList<AvaliacaoIntegradaJSON>();
        }
        List<AvaliacaoIntegradaJSON> integradas = new ArrayList<AvaliacaoIntegradaJSON>();
        List<ItemAvaliacaoFisica> itemAvaliacaoFisicas = obterItensAvaliacaoIntegrada(ctx, cliente.getCodigo());
        for (ItemAvaliacaoFisica i : itemAvaliacaoFisicas) {
            Map<AgrupamentoAvaliacaoIntegradaEnum, List<PerguntaAnamnese>> agrupamento = new HashMap<AgrupamentoAvaliacaoIntegradaEnum, List<PerguntaAnamnese>>();
            List<PerguntaAnamnese> perguntaAnamnese = anamneseService.obterPerguntasAnamnese(ctx, i.getAnamnese().getCodigo());
            for (PerguntaAnamnese pa : perguntaAnamnese) {
                if (pa.getPergunta().getAgrupamento() == null) {
                    continue;
                }

                RespostaCliente respostaCliente = anamneseService.obterRespostaPerguntasAnamnese(
                        ctx, pa.getCodigo(), i.getCliente().getCodigo(), i.getCodigo());
                setarResposta(pa, respostaCliente);

                if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                        || pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                    pa.getPergunta().setOpcoes(anamneseService.obterOpcoes(ctx, pa.getPergunta().getCodigo()));
                }
                List<PerguntaAnamnese> perguntas = agrupamento.get(pa.getPergunta().getAgrupamento());
                if (perguntas == null) {
                    perguntas = new ArrayList<PerguntaAnamnese>();
                    agrupamento.put(pa.getPergunta().getAgrupamento(), perguntas);
                }

                perguntas.add(pa);
            }

            List<Movimento3D> movimento3DS = obterMovimento3D(ctx, i);
            List<Movimento3D> mobilidade = new ArrayList<Movimento3D>();
            List<Movimento3D> estabilidade = new ArrayList<Movimento3D>();
            for (Movimento3D m : movimento3DS) {
                if (m.getMovimento().getTipo().equals(TipoMovimento3DEnum.mobilidade)) {
                    mobilidade.add(m);
                } else {
                    estabilidade.add(m);
                }
            }

            integradas.add(new AvaliacaoIntegradaJSON(i, agrupamento, mobilidade, estabilidade, viewUtils));
        }
        return integradas;
    }

    private void setarResposta(PerguntaAnamnese pa, RespostaCliente respostaCliente) {
        if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
            String[] respostas = respostaCliente.getResposta().split("\\_");
            pa.setRespostas(new ArrayList<String>());
            for (String r : respostas) {
                pa.getRespostas().add(r);
            }
        } else {
            pa.setResposta(respostaCliente == null ? "" : respostaCliente.getResposta());
        }
    }

    public Boolean enviarAvaliacaoAluno(Integer avaliacaoId) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Integer usuarioId = sessaoService.getUsuarioAtual().getId();
        Usuario usuario = usuarioService.obterPorId(ctx, usuarioId);
        AvaliacaoFisica avaliacaoFisica;
        if (avaliacaoId != null) {
            avaliacaoFisica = obterPorId(ctx, avaliacaoId);
        } else {
            throw new ServiceException(AvaliacaoFisicaExcecoes.ERRO_AVALIACAO_FISICA_NAO_INFORMADA);
        }

        if (avaliacaoFisica != null) {
            ClienteSintetico clienteSintetico = avaliacaoFisica.getCliente();
            Flexibilidade flexibilidade = carregarFlexibilidade(ctx, avaliacaoFisica);
            PesoOsseo pesoOsseo = carregarPesoOsseo(ctx, clienteSintetico, avaliacaoFisica);
            Ventilometria ventilometria = carregarVentilometria(ctx, clienteSintetico.getCodigo(), avaliacaoFisica);
            Anamnese questionarioParq = carregarQuestionarioParq(ctx, usuario, avaliacaoFisica, clienteSintetico.getCodigo());
            Anamnese anamnese = carregarAnamnese(ctx, usuario, avaliacaoFisica, clienteSintetico.getCodigo());


        } else {
            throw new ServiceException(AvaliacaoFisicaExcecoes.ERRO_BUSCAR_AVALIACAO_FISICA);
        }

        return null;
    }

    private Flexibilidade carregarFlexibilidade(String ctx, AvaliacaoFisica avaliacaoFisica) throws Exception {
        Flexibilidade flexibilidade = obterFlexibilidade(ctx, avaliacaoFisica.getCodigo());
        flexibilidade = flexibilidade == null ? new Flexibilidade() : flexibilidade;
        if (UteisValidacao.emptyNumber(flexibilidade.getAlcance()) && !UteisValidacao.emptyNumber(avaliacaoFisica.getFlexibilidade())) {
            flexibilidade.setAlcance(avaliacaoFisica.getFlexibilidade().intValue());
        }
        return flexibilidade;
    }

    private PesoOsseo carregarPesoOsseo(String ctx, ClienteSintetico clienteSintetico, AvaliacaoFisica avaliacaoFisica) throws ServiceException {
        try {
            PesoOsseo pesoOsseo = obterPesoOsseo(ctx, clienteSintetico.getCodigoCliente(), avaliacaoFisica);
            if (pesoOsseo == null || pesoOsseo.getCodigo() == null || pesoOsseo.getCodigo() == 0) {
                if (avaliacaoFisica != null) {
                    pesoOsseo = gerarPesoOsseo(avaliacaoFisica, clienteSintetico);
                }
            }
            return pesoOsseo;
        } catch (Exception e) {
            throw new ServiceException(PesoOsseoExcecoes.ERRO_BUSCAR_PESO_OSSEO);
        }
    }

    private Ventilometria carregarVentilometria(String ctx, Integer clienteId, AvaliacaoFisica avaliacaoFisica) throws ServiceException {
        try {
            ItemAvaliacaoFisica venItem = obterItemAvaliacaoFisica(ctx, clienteId, ItemAvaliacaoFisicaEnum.VENTILOMETRIA, null, avaliacaoFisica);
            Ventilometria ventilometria = new Ventilometria();
            if (venItem != null && !UteisValidacao.emptyNumber(venItem.getCodigo())) {
                ventilometria = venItem.getVentilometria();
            }
            return ventilometria;
        } catch (ServiceException e) {
            throw new ServiceException(VentilometriaExcecoes.ERRO_BUSCAR_VENTILOMETRIA);
        }
    }

    private Anamnese carregarQuestionarioParq(String ctx, Usuario usuario, AvaliacaoFisica avaliacaoFisica, Integer clienteId) throws Exception {
        Anamnese questionarioParq;
        questionarioParq = anamneseService.consultarParq(ctx, usuario.getCodigo(), getViewUtils());
        questionarioParq.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo()));

        return questionarioParq;
    }

    private Anamnese carregarAnamnese(String ctx, Usuario usuario, AvaliacaoFisica avaliacaoFisica, Integer clienteId) throws Exception {
        Anamnese anamnese = new Anamnese();
        List<ItemAvaliacaoFisica> anamnesesAluno = (UteisValidacao.emptyNumber(avaliacaoFisica.getCodigo())) ? new ArrayList<ItemAvaliacaoFisica>() :
                obterItensAvaliacaoFisica(ctx, clienteId, ItemAvaliacaoFisicaEnum.ANAMNESE, 1, false, avaliacaoFisica);
        if (UteisValidacao.emptyList(anamnesesAluno)) {
            List<Anamnese> anamneseList = anamneseService.consultarPovoando(ctx, usuario, true, getViewUtils());
            anamneseList = Ordenacao.ordenarLista(anamneseList, "descricao");
            if (anamneseList != null && !anamneseList.isEmpty()) {
                anamnese = selecionarAnamnese(ctx, anamneseList.get(0));
            }
        } else {
            anamnese = selecionarAnamnese(ctx, anamnesesAluno.get(0).getAnamnese());
            anamnese.setRespondidaEm(anamnesesAluno.get(0).getDataLancamento());
        }
        return anamnese;
    }

    private Anamnese selecionarAnamnese(String ctx, Anamnese anamnese) throws ServiceException {
        anamnese.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, anamnese.getCodigo()));

        return anamnese;
    }

    private Double valorJson(JSONObject jsondados, String campo) {
        try {
            Double valor;
            if (jsondados.optString(campo) != null) {
                valor = Double.valueOf(jsondados.getString(campo).replace(",", "."));
            } else {
                valor = jsondados.optDouble(campo);
            }
            return (valor.isNaN() ? 0.0 : valor);
        } catch (Exception e) {
            return 0.0;
        }
    }

    public void montarBioVittio(AvaliacaoFisica avaliacaoFisica, JSONObject jsonVittio) {
        try {
            JSONObject jsondados = jsonVittio.getJSONObject("payload");
            avaliacaoFisica.setPeso(valorJson(jsondados, "weight"));
            avaliacaoFisica.setAltura(valorJson(jsondados, "height") / 100.0);

            avaliacaoFisica.setPesoMuscular(valorJson(jsondados, "smm"));
            avaliacaoFisica.setGorduraVisceral(valorJson(jsondados, "vfl"));

            try {
                Double gorduraIdeal = (valorJson(jsondados, "lower_limit_bfm")
                        + valorJson(jsondados, "upper_limit_bfm")) / 2.0;
                avaliacaoFisica.setGorduraIdeal(gorduraIdeal);
            } catch (Exception e) {
                Uteis.logar(e, AvaliacaoFisicaImpl.class);
            }

            avaliacaoFisica.setImc(valorJson(jsondados, "bmi"));
            avaliacaoFisica.setCategoriaAvaliacaoIMC(ProtocoloPolloc.classificacaoIMC(avaliacaoFisica.getImc()));
            avaliacaoFisica.setPercentualGordura(valorJson(jsondados, "pbf"));
            avaliacaoFisica.setMassaGorda(valorJson(jsondados, "bfm"));
            avaliacaoFisica.setMassaMagra(valorJson(jsondados, "ffm_trunk"));
            avaliacaoFisica.setTmb(valorJson(jsondados, "bmr"));
            avaliacaoFisica.setPercentualMassaMagra((avaliacaoFisica.getMassaMagra() / avaliacaoFisica.getPeso()) * 100.0);
            Double total_body_water = valorJson(jsondados, "total_body_water");
            avaliacaoFisica.setPercentualAgua(total_body_water);
            avaliacaoFisica.setLogBalanca(jsonVittio.toString());
        } catch (Exception e) {
            Uteis.logar(e, AvaliacaoFisicaImpl.class);
        }
    }

    public void gravarBioimpedancia(final String ctx, ClienteSintetico cliente, final String dados) throws Exception {
        validarProdutoAvaliacao(ctx, cliente);
        AvaliacaoFisica avaliacaoFisica = new AvaliacaoFisica();
        avaliacaoFisica.setProtocolo(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA);
        JSONObject jsondados = new JSONObject(dados);
        avaliacaoFisica.setCliente(cliente);
        avaliacaoFisica.setDataAvaliacao(Calendario.hoje());

        if (jsondados.has("payload")) {
            montarBioVittio(avaliacaoFisica, jsondados);
        } else {
            double peso = jsondados.has("Peso") ? jsondados.optDouble("Peso") : 0;
            double altura = jsondados.has("Altura") ? jsondados.optDouble("Altura") / 100.0 : 0;
            int idade = cliente.getIdade();

            avaliacaoFisica.setPeso(peso);
            avaliacaoFisica.setAltura(altura);
            avaliacaoFisica.setPercentualAgua(Uteis.arredondarForcando2CasasDecimais(jsondados.optDouble("PercentagemAgua") * 100.00));
            avaliacaoFisica.setImc(Uteis.arredondarForcando2CasasDecimais(jsondados.optDouble("IMC")));
            avaliacaoFisica.setCategoriaAvaliacaoIMC(ProtocoloPolloc.classificacaoIMC(avaliacaoFisica.getImc()));
            avaliacaoFisica.setPercentualGordura(Uteis.arredondarForcando2CasasDecimais(jsondados.optDouble("PercentagemGordura") * 100.00));
            avaliacaoFisica.setIdadeMetabolica(Uteis.arredondarForcando2CasasDecimais(jsondados.optDouble("IdadeMetabolica")));
            avaliacaoFisica.setPesoOsseo(Uteis.arredondarForcando2CasasDecimais(jsondados.optDouble("PesoOssos")));
            avaliacaoFisica.setMassaMagra(Uteis.arredondarForcando2CasasDecimais(jsondados.optDouble("PesoNaoGordura")));
            avaliacaoFisica.setMassaGorda(Uteis.arredondarForcando2CasasDecimais(jsondados.optDouble("PesoGordura")));
            avaliacaoFisica.setGorduraVisceral(Uteis.arredondarForcando2CasasDecimais(jsondados.optDouble("GorduraVisceral")));

            if (cliente.isSexoMasculino()) {
                avaliacaoFisica.setResidual(Uteis.arredondarForcando2CasasDecimais(avaliacaoFisica.getPeso() * 0.24));
                double tmb = (peso == 0 || altura == 0) ? 0 : 66 + (13.8 * peso) + (5 * altura) - (6.8 * idade);
                avaliacaoFisica.setTmb(Uteis.arredondarForcando2CasasDecimais(tmb));
            } else {
                avaliacaoFisica.setResidual(Uteis.arredondarForcando2CasasDecimais(avaliacaoFisica.getPeso() * 0.21));
                double tmb = (peso == 0 || altura == 0) ? 0 : 655 + (9.6 * peso) + (1.8 * altura) - (4.7 * idade);
                avaliacaoFisica.setTmb(Uteis.arredondarForcando2CasasDecimais(tmb));
            }

            avaliacaoFisica.setPesoMuscular(avaliacaoFisica.getPeso() - avaliacaoFisica.getResidual() - avaliacaoFisica.getPesoOsseo() - (avaliacaoFisica.getPeso() * (avaliacaoFisica.getPercentualGordura() / 100)));
        }
        inserir(ctx, avaliacaoFisica);
        String origemBioimpedancia = jsondados.has("OrigemBioimpedancia") ? jsondados.optString("OrigemBioimpedancia") : "";
        incluirLog(ctx, avaliacaoFisica.getCodigo().toString(),
                avaliacaoFisica.getCliente().getCodigo().toString(), "",
                avaliacaoFisica.getDescricaoParaLog(null),
                "INCLUSÃO", "INCLUSÃO DE AVALIAÇÃO FÍSICA - BIOIMPEDANCIA [Origem: "+origemBioimpedancia+"]", EntidadeLogEnum.AVALIACAOFISICA, "Avaliação Física", sessaoService, logDao, null, null);
    }


    public boolean temNovaAvaliacao(String ctx, Integer cliente) throws Exception {
        String sql = "select codigo from avaliacaofisica where cliente_codigo = " + cliente
                + " and dataavaliacao > '" + Uteis.getDataAplicandoFormatacao(Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, -1), "yyyy-MM-dd HH:mm:ss") + "'";

        ResultSet resultSet = avaliacaoFisicaDao.createStatement(ctx, sql);

        return resultSet.next();
    }

    public List<AvaliacaoFisicaDTO> todasCliente(String chave, Integer codigoCliente, Usuario usuario) throws Exception {
        List<AvaliacaoFisicaDTO> dtos = new ArrayList<AvaliacaoFisicaDTO>();
        List<AvaliacaoFisica> avaliacaoFisicas = obterAvaliacaoCliente(chave, codigoCliente);

        for (AvaliacaoFisica a : avaliacaoFisicas) {
            AvaliacaoFisicaDTO dto = new AvaliacaoFisicaDTO();
            dto.setId(a.getCodigo());
            setarHora(a);
            dto.setDataAvaliacao(a.getDataAvaliacao().getTime());
            if (a.getResponsavelLancamento_codigo() != null && !UteisValidacao.emptyNumber(a.getResponsavelLancamento_codigo())) {
                Usuario usu = usuarioService.obterPorId(chave, a.getResponsavelLancamento_codigo());
                if (usu != null && usu.getProfessor() != null) {
                    dto.setAvaliador(new AvaliadorDTO(usu.getProfessor().getCodigo(), usu.getProfessor().getNome()));
                } else {
                    dto.setAvaliador(new AvaliadorDTO(0, ""));
                }
            } else {
                dto.setAvaliador(new AvaliadorDTO(0, ""));
            }

            dtos.add(dto);
        }

        return Ordenacao.ordenarListaReverse(dtos, "dataAvaliacao");
    }

    @Override
    public List<AvaliacaoFisicaDTO> todasAvaliacoesPorPeriodo(String chave, String dataInicio, String dataFim) throws Exception {
        final List<AvaliacaoFisicaDTO> dtos = new ArrayList<>();
        try {
            final List<AvaliacaoFisica> avaliacoes = obterAvaliacoesPorPeriodo(chave, Uteis.getDate(dataInicio), Uteis.getDate(dataFim));
            final Usuario usuarioAtual = buscarUsuarioAtual();

            getAvaliacaoFisicaDao().getCurrentSession(chave).clear();
            for (AvaliacaoFisica avaliacao : avaliacoes) {
                dtos.add(toDTO(chave, avaliacao, usuarioAtual));
            }
        } catch (ParseException ex) {
            throw new Exception("Erro ao parsear datas. As datas devem ser informadas no padrão dd/MM/yyyy");
        }

        return Ordenacao.ordenarListaReverse(dtos, "dataAvaliacao");
    }

    private Usuario buscarUsuarioAtual() {
        try {
            final UsuarioSimplesDTO usuarioTO = sessaoService.getUsuarioAtual();
            final String ctx = usuarioTO.getChave();
            return usuarioService.obterPorId(ctx, usuarioTO.getId());
        } catch (Exception ex) {
            Uteis.logarDebug("INTEGRAÇÃO NUTRIXY - Erro ao buscar usuário atual: " + ex.getMessage());
            return null;
        }
    }

    private List<AvaliacaoFisica> obterAvaliacoesPorPeriodo(final String ctx, final Date dataInicio, final Date dataFim) throws ServiceException {
        try {
            getAvaliacaoFisicaDao().getCurrentSession(ctx).clear();
            final StringBuilder sql = new StringBuilder("SELECT obj FROM AvaliacaoFisica obj WHERE obj.dataAvaliacao between :dataInicio and :dataFim\n");
            sql.append("ORDER by obj.dataAvaliacao DESC, obj.codigo DESC");

            final HashMap<String, Object> params = new HashMap<>();
            params.put("dataInicio", Calendario.getDataComHoraZerada(dataInicio));
            params.put("dataFim", Calendario.getDataComHora(dataFim, "23:59:59"));

            return getAvaliacaoFisicaDao().findByParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Boolean obterResultadoQuestionarioParq(String ctx, ClienteSintetico cliente) throws ServiceException {
        AvaliacaoFisica avaliacaoFisica = obterAvaliacaoVigente(ctx, cliente.getCodigo());
        if (avaliacaoFisica == null || avaliacaoFisica.getCodigo() == null) {
            return false;
        }
        Anamnese questionarioParq = anamneseService.consultarParq(ctx, sessaoService.getUsuarioAtual().getId(), getViewUtils());
        List<ItemAvaliacaoFisica> itemParq = obterItensAvaliacaoFisica(ctx, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, true, avaliacaoFisica);
        List<RespostaCliente> respostas = new ArrayList<>();
        if (!UteisValidacao.emptyList(itemParq)) {
            ItemAvaliacaoFisica i = itemParq.get(0);
            List<PerguntaAnamnese> perguntas = anamneseService.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo());
            for (PerguntaAnamnese pa : perguntas) {
                RespostaCliente respostaCliente = anamneseService.obterRespostaPerguntasAnamnese(
                        ctx, pa.getCodigo(), i.getCliente().getCodigo(), i.getCodigo());
                if (respostaCliente != null) {
                    respostas.add(respostaCliente);
                }
            }
        }

        return avaliarRespostaQuestionarioParq(respostas, cliente, ctx);
    }

    private Boolean avaliarRespostaQuestionarioParq(List<RespostaCliente> respostaClientes, ClienteSintetico cliente, String ctx) {
        for (RespostaCliente resposta : respostaClientes) {
            if (resposta.getPerguntaAnamnese().getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIM_NAO)) {
                if (!StringUtils.isBlank(resposta.getResposta()) && !resposta.getResposta().equals("NAO")) {
                    cliente.setParq(true);
                    return true;
                }
            }
        }

        return false;
    }

    public AvaliacaoFisicaDTO avaliacaoAtual(String chave, Integer codigoCliente, Usuario usuario) throws Exception {

        AvaliacaoFisica avaliacaoFisica = obterAvaliacaoVigente(chave, codigoCliente);
        ClienteSintetico clienteSintetico = clienteSinteticoService.obterPorId(chave, codigoCliente);
        AvaliacaoFisicaDTO avaliacaoFisicaDTO = toDTO(chave, avaliacaoFisica, usuario);

        if (avaliacaoFisicaDTO != null) {
            Double endomorfia = Uteis.arredondar(avaliacaoFisicaDTO.getSomatotipia().getEndomorfia(), 3);
            avaliacaoFisicaDTO.getSomatotipia().setEndomorfia(endomorfia);

            Double mesomorfia = Uteis.arredondar(avaliacaoFisicaDTO.getSomatotipia().getMesomorfia(), 3);
            avaliacaoFisicaDTO.getSomatotipia().setMesomorfia(mesomorfia);

            Double ectomorfia = Uteis.arredondar(avaliacaoFisicaDTO.getSomatotipia().getEctomorfia(), 3);
            avaliacaoFisicaDTO.getSomatotipia().setEctomorfia(ectomorfia);

            avaliacaoFisicaDTO.setResultadoParq(obterResultadoQuestionarioParq(chave, avaliacaoFisica.getCliente()));
        }

        return avaliacaoFisicaDTO;
    }

    public AvaliacaoFisicaDTO getToDTO(String chave, AvaliacaoFisica avaliacao, Usuario usuario) throws Exception {
        AnamnesePerguntaRespostaDTO anamnesePerguntaRespostaDTO = new AnamnesePerguntaRespostaDTO();
        if (avaliacao == null) {
            return null;
        }

        ClienteSintetico cliente = avaliacao.getCliente();
        AvaliacaoFisicaDTO dto = new AvaliacaoFisicaDTO();
//        Integer id;
        dto.setId(avaliacao.getCodigo());
//        Long dataAvaliacao;
        setarHora(avaliacao);
        dto.setDataAvaliacao(avaliacao.getDataAvaliacao().getTime());

        if (avaliacao.getDataProxima() != null) {
            dto.setDataProxima(avaliacao.getDataProxima().getTime());
        }


        if (avaliacao.getResponsavelLancamento_codigo() != null && !UteisValidacao.emptyNumber(avaliacao.getResponsavelLancamento_codigo())) {
            Usuario usu = usuarioService.obterPorId(chave, avaliacao.getResponsavelLancamento_codigo());
            dto.setAvaliador(usu == null ?
                    new AvaliadorDTO(0, "") :
                    new AvaliadorDTO(usu.getProfessor().getCodigo(), usu.getProfessor().getNome()));
        }
//        AnamneseTO anamneseSelecionada;//        List<AnamnesePerguntaRespostaDTO> anamneseRespostas;
        List<ItemAvaliacaoFisica> itemAnamnse = obterItensAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, false, avaliacao);
        dto.setAnamneseRespostas(new ArrayList<AnamnesePerguntaRespostaDTO>());
        if (!UteisValidacao.emptyList(itemAnamnse)) {
            ItemAvaliacaoFisica i = itemAnamnse.get(0);
            Anamnese anamnese = i.getAnamnese();
            anamnese.setPerguntas(anamneseService.obterPerguntasAnamnese(chave, anamnese.getCodigo()));
            for (PerguntaAnamnese pa : anamnese.getPerguntas()) {
                if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                        || pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                    pa.getPergunta().setOpcoes(anamneseService.obterOpcoes(chave, pa.getPergunta().getCodigo()));
                }
                RespostaCliente respostaCliente = anamneseService.obterRespostaPerguntasAnamnese(
                        chave, pa.getCodigo(), i.getCliente().getCodigo(), i.getCodigo());
                if (respostaCliente != null) {
                    if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                            || pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {

                        List<Integer> respostas = new ArrayList<Integer>();
                        if (!UteisValidacao.emptyString(respostaCliente.getResposta())) {
                            String[] split = respostaCliente.getResposta().split("\\_");
                            for (String r : split) {
                                if (anamnesePerguntaRespostaDTO.isNumber(r)) {
                                    respostas.add(Integer.valueOf(r));
                                }
                            }
                        }
                        Integer verifica = 0;
                        for (Integer r : respostas) {
                            for (int j = 0; j < pa.getPergunta().getOpcoes().size(); j++) {
                                if (pa.getPergunta().getOpcoes().get(j).getCodigo().equals(r)) {
                                    verifica++;
                                }
                            }

                        }
                        if (verifica < respostas.size()) {
                            respostaCliente.setResposta("");
                        }
                    }
                    dto.getAnamneseRespostas().add(new AnamnesePerguntaRespostaDTO(respostaCliente));
                }
            }
            dto.setAnamneseSelecionada(new AnamneseTO(anamnese));
        }
//        List<String> objetivos;
        ItemAvaliacaoFisica itemObjetivos = obterItemAvaliacaoFisica(chave,
                cliente.getCodigo(), ItemAvaliacaoFisicaEnum.OBJETIVOS, null, avaliacao);
        if (itemObjetivos != null) {
            String[] splitobjs = itemObjetivos.getResult().split("\\|#\\|");
            dto.setObjetivos(Arrays.asList(splitobjs));
        } else {
            dto.setObjetivos(new ArrayList<String>());
        }
//        AvaliacaoAlunoBIDTO alunoBI;
        List<Double> pesos;
        try (ResultSet rs = avaliacaoFisicaDao.createStatement(chave, "select dataavaliacao, peso from avaliacaofisica  \n" +
                "where dataavaliacao::date <= '" + Uteis.getDataAplicandoFormatacao(avaliacao.getDataAvaliacao(), "yyyy-MM-dd") + "' and cliente_codigo = " +
                cliente.getCodigo() +
                "order by dataavaliacao desc limit 4")) {
            pesos = new ArrayList<Double>();
            while (rs.next()) {
                pesos.add(rs.getDouble("peso"));
            }
        }
        PesoOsseo po = obterPesoOsseo(chave, cliente.getCodigo(), avaliacao);
        if (po == null) {
            po = new PesoOsseo();
            double altura = avaliacao.getAltura();
            if (Double.isNaN(altura) || altura <= 0.0 || altura > 3.00) {
                altura = 0.0;
            }
            po.setAltura(altura);
            po.setPeso(avaliacao.getPeso());
            po.setPercentualGordura(avaliacao.getPercentualGordura());
            po = calcularPesoOsseo(po, cliente.isSexoMasculino());
        }
        dto.setAlunoBI(new AvaliacaoAlunoBIDTO(avaliacao, pesos, po));
        /* cria a lista de emails para enviar avaliacao */
        dto.setEmails(cliente.getListaEmails());
        /* Cria lista de telefones celulares para enviar avaliacao */
        List<String> telefones = new ArrayList<>();
        for (String telefone : cliente.getListaTelefones()) {
            telefone = telefone.replaceAll(" ", "").replaceAll("-", "");
            if (Uteis.validarTelefoneCelular(telefone)) {
                telefones.add(telefone);
            }
        }
        dto.setTelefones(telefones);

//        List<AnamnesePerguntaRespostaDTO> parQRespostas;
        Anamnese questionarioParq = anamneseService.consultarParq(chave, usuario.getCodigo(), getViewUtils());
        questionarioParq.setPerguntas(anamneseService.obterPerguntasAnamnese(chave, questionarioParq.getCodigo()));
        List<ItemAvaliacaoFisica> itemParq = obterItensAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, true, avaliacao);
        dto.setParQRespostas(new ArrayList<AnamnesePerguntaRespostaDTO>());
        if (!UteisValidacao.emptyList(itemParq)) {
            ItemAvaliacaoFisica i = itemParq.get(0);
            List<PerguntaAnamnese> perguntas = anamneseService.obterPerguntasAnamnese(chave, questionarioParq.getCodigo());
            for (PerguntaAnamnese pa : perguntas) {
                RespostaCliente respostaCliente = anamneseService.obterRespostaPerguntasAnamnese(
                        chave, pa.getCodigo(), i.getCliente().getCodigo(), i.getCodigo());
                if (respostaCliente != null) {
                    dto.getParQRespostas().add(new AnamnesePerguntaRespostaDTO(respostaCliente));
                }
            }
        }

        ConfiguracaoSistemaService cfs = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
        boolean usarPressaoSistolicaDiastolica = cfs.consultarPorTipo(chave, ConfiguracoesEnum.USAR_PRESSAO_SISTOLICA_DIASTOLICA).getValorAsBoolean();

//        AvaliacaoPesoAlturaDTO pesoAltura;
        String pressaoArterial = "";
        String pressaoSistolica = "";
        String pressaoDiastolica = "";
        ItemAvaliacaoFisica itemPressao = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL, null, avaliacao);
        if (itemPressao != null) {
            pressaoArterial = itemPressao.getResult();
        }
        if (usarPressaoSistolicaDiastolica) {
            ItemAvaliacaoFisica itemPressaoSistolica = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL_SISTOLICA, null, avaliacao);
            if (itemPressaoSistolica != null) {
                pressaoSistolica = itemPressaoSistolica.getResult();
            }
            ItemAvaliacaoFisica itemPressaoDiastolica = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL_DIASTOLICA, null, avaliacao);
            if (itemPressaoDiastolica != null) {
                pressaoDiastolica = itemPressaoDiastolica.getResult();
            }
        }
        ItemAvaliacaoFisica itemFC = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.FREQUENCIA_CARDIACA, null, avaliacao);
        dto.setPesoAltura(new AvaliacaoPesoAlturaDTO(avaliacao, pressaoArterial, pressaoSistolica, pressaoDiastolica,
                itemFC == null || itemFC.getResult().isEmpty() ? 0 : Integer.parseInt(itemFC.getResult())));

//        AvaliacaoDobrasDTO dobras;
        dto.setDobras(new AvaliacaoDobrasDTO(avaliacao));
//        AvaliacaoPerimetriaDTO perimetria;
        dto.setPerimetria(new AvaliacaoPerimetriaDTO(avaliacao, po));
//        AvaliacaoComposicaoDTO composicao;
        dto.setComposicao(new AvaliacaoComposicaoDTO(po));
//        AvaliacaoFlexibilidadeDTO flexibilidade;
        Flexibilidade flexibilidade = carregarFlexibilidade(chave, avaliacao);
        dto.setFlexibilidade(new AvaliacaoFlexibilidadeDTO(flexibilidade));
        //        AvaliacaoPosturaDTO postura;
        AvaliacaoPostural avaliacaoPostural = consultarPostural(chave, avaliacao);
        if (avaliacaoPostural != null) {
            List<ItemAvaliacaoPostural> itens = consultarItens(chave, avaliacaoPostural);
            dto.setPostura(new AvaliacaoPosturaDTO(avaliacaoPostural, itens));
        }

//        AvaliacaoRMLDTO rml;

        ResultadoResistenciaEnum resultAbdomen = obterResultadoResistencia(chave, cliente, obterEnumResistenciaPorConfiguracao(chave, cliente.getSexo(), TipoResistenciaMuscular.ABDOMEN), avaliacao);
        ResultadoResistenciaEnum resultBraco = obterResultadoResistencia(chave, cliente, obterEnumResistenciaPorConfiguracao(chave, cliente.getSexo(), TipoResistenciaMuscular.BRACO), avaliacao);

        dto.setRml(new AvaliacaoRMLDTO(cliente.getFlexaoBraco(),
                cliente.getFlexaoAbdomen(),
                RMLLinha.obterPorIdade(cliente.getIdade()),
                resultBraco == null ? null : resultBraco.getColuna(),
                resultAbdomen == null ? null : resultAbdomen.getColuna()));
//        AvaliacaoVo2DTO vo2;
        ItemAvaliacaoFisica ventItem = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.VENTILOMETRIA, null, avaliacao);

        if (avaliacao.getPeso() != 0) {
            if (ventItem != null && ventItem.getVentilometria() != null) {
                dto.setVo2(new AvaliacaoVo2DTO(avaliacao, ventItem.getVentilometria()));
            } else {
                dto.setVo2(new AvaliacaoVo2DTO(avaliacao));
            }
            calcularVo2Astrand(avaliacao, cliente.isSexoMasculino());
            dto.getVo2().setAstrandVo2Maximo(avaliacao.getVo2MaxAstrand());
            dto.getVo2().setAstrandVo2Estimado(avaliacao.getVo2Astrand());
        }
        if (cliente.getIdade() != null && ventItem != null && !UteisValidacao.emptyNumber(ventItem.getCodigo()) && ventItem.getVentilometria() != null) {
            dto.setVo2(new AvaliacaoVo2DTO(avaliacao, ventItem.getVentilometria()));
            dto.getVo2().setProtocolo(avaliacao.getProtocoloVo());

            ResultadoVO2 resultadoVO2 = montarLinhaCalunaEnum(calcularVo212Minutos(avaliacao, cliente.isSexoMasculino(), cliente.getIdade()));
            if (resultadoVO2 != null) {
                dto.getVo2().setCaminhadaCorridaVo2Max(avaliacao.getVo2Max12());
                dto.getVo2().setCaminhadaCorridaColuna(CaminhadaCorridaColunaEnum.getInstance(resultadoVO2.getResult().trim()));
                final String categoria = resultadoVO2.getCategoria();
                if (categoria.equalsIgnoreCase("muito.fraca") || categoria.equalsIgnoreCase("superior")) {
                    dto.getVo2().setCaminhadaCorridaLinha(CaminhadaCorridaLinhaEnum.getInstance(categoria));
                } else {
                    dto.getVo2().setCaminhadaCorridaLinha(CaminhadaCorridaLinhaEnum.valueOf(categoria));
                }
            }
            resultadoVO2 = montarLinhaCalunaEnum(calcularVoAerobicoDeBanco(avaliacao, cliente.isSexoMasculino(), cliente.getIdade()));
            if (resultadoVO2 != null) {
                dto.getVo2().setAerobicoBancoVo2Max(avaliacao.getVoMaxAerobico());
                dto.getVo2().setAerobicobancoColuna(AerobicobancoColunaEnum.getInstance(resultadoVO2.getResult().trim()));
                dto.getVo2().setAerobicoBancoLinha(AerobicoBancoLinhaEnum.getInstance(resultadoVO2.getCategoria()));
            }
            // Converter tempo do teste 2400 para minutos
            if (!UteisValidacao.emptyString(avaliacao.getTempo2400())) {
                dto.getVo2().setTeste2400MetrosTempo((double) Uteis.converterMinutosEmSegundos(avaliacao.getTempo2400()));
            }

            resultadoVO2 = montarLinhaCalunaEnum(calcularVo2Teste2400(avaliacao, cliente.isSexoMasculino(), cliente.getIdade()));
            if (resultadoVO2 != null) {
                dto.getVo2().setTeste2400MetrosVo2Max(avaliacao.getVo2Max2400());
                dto.getVo2().setTeste2400MetrosColuna(Teste2400MetrosColunaEnum.getInstance(resultadoVO2.getResult().trim()));
                final String categoria = resultadoVO2.getCategoria();
                if (categoria.equalsIgnoreCase("muito.fraca")) {
                    dto.getVo2().setTeste2400MetrosLinha(Teste2400MetrosLinhaEnum.MUITA_FRACA);
                } else {
                    dto.getVo2().setTeste2400MetrosLinha(Teste2400MetrosLinhaEnum.valueOf(categoria));
                }

            }
            if (dto.getVo2() != null) {
                // Calcular QUEENS COLLEGE
                if (!UteisValidacao.emptyNumber(avaliacao.getFcQueens())) {
                    calcularVo2Queens(avaliacao, cliente.isSexoMasculino());
                }
                dto.getVo2().setCollegeVo2Maximo(avaliacao.getVo2MaxQueens());
                // Calcular ASTRAND CICLOERGOMETRO
                if (avaliacao.getPeso() != 0) {
                    calcularVo2Astrand(avaliacao, cliente.isSexoMasculino());
                    dto.getVo2().setAstrandVo2Maximo(avaliacao.getVo2MaxAstrand());
                    dto.getVo2().setAstrandVo2Estimado(avaliacao.getVo2Astrand());
                }
            }


        }
        if (avaliacao.getCategoriaPercentualGordura() != null) {
            dto.setComposicaoNota(avaliacao.getCategoriaPercentualGordura().toString());
        }
        dto.setImcNota(avaliacao.getCategoriaAvaliacaoIMC().toString());
        dto.setCardioNota(viewUtils.getLabelInternacionalizada(avaliacao.getResultadoRiscoCA(), "PT"));
//        AvaliacaoSomatotipiaDTO somatotipia;
        dto.setSomatotipia(new AvaliacaoSomatotipiaDTO(avaliacao, po));
//        AvaliacaoMetaRecomendacoesDTO metaRecomendacoes;
        dto.setMetaRecomendacoes(new AvaliacaoMetaRecomendacoesDTO(avaliacao));
        return dto;
    }

    public AvaliacaoFisicaDTO toDTO(String chave, AvaliacaoFisica avaliacao, Usuario usuario) throws Exception {
        if (avaliacao == null) {
            return null;
        }

        ClienteSintetico cliente = avaliacao.getCliente();
        AvaliacaoFisicaDTO dto = new AvaliacaoFisicaDTO();
        dto.setId(avaliacao.getCodigo());
        setarHora(avaliacao);
        dto.setDataAvaliacao(avaliacao.getDataAvaliacao().getTime());

        dto.setCliente(new ClienteSinteticoDTO(cliente));

        if (avaliacao.getDataProxima() != null) {
            dto.setDataProxima(avaliacao.getDataProxima().getTime());
        }


        if (avaliacao.getResponsavelLancamento_codigo() != null && !UteisValidacao.emptyNumber(avaliacao.getResponsavelLancamento_codigo())) {
            Usuario usu = usuarioService.obterPorId(chave, avaliacao.getResponsavelLancamento_codigo());
            dto.setAvaliador(usu == null?
                    new AvaliadorDTO(0, "") :
                    new AvaliadorDTO(usu.getProfessor().getCodigo(), usu.getProfessor().getNome()));
        }
//        AnamneseTO anamneseSelecionada;//        List<AnamnesePerguntaRespostaDTO> anamneseRespostas;
        List<ItemAvaliacaoFisica> itemAnamnse = obterItensAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, false, avaliacao);
        dto.setAnamneseRespostas(new ArrayList<AnamnesePerguntaRespostaDTO>());
        if (!UteisValidacao.emptyList(itemAnamnse)) {
            ItemAvaliacaoFisica i = itemAnamnse.get(0);
            Anamnese anamnese = i.getAnamnese();
            anamnese.setPerguntas(anamneseService.obterPerguntasAnamnese(chave, anamnese.getCodigo()));
            for (PerguntaAnamnese pa : anamnese.getPerguntas()) {
                if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                        || pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                    pa.getPergunta().setOpcoes(anamneseService.obterOpcoes(chave, pa.getPergunta().getCodigo()));
                }

                RespostaCliente respostaCliente = anamneseService.obterRespostaPerguntasAnamnese(
                        chave, pa.getCodigo(), i.getCliente().getCodigo(), i.getCodigo());
                if (respostaCliente != null) {
                    dto.getAnamneseRespostas().add(new AnamnesePerguntaRespostaDTO(respostaCliente));
                }
            }
            dto.setAnamneseSelecionada(new AnamneseTO(anamnese));
        }
//        List<String> objetivos;
        ItemAvaliacaoFisica itemObjetivos = obterItemAvaliacaoFisica(chave,
                cliente.getCodigo(), ItemAvaliacaoFisicaEnum.OBJETIVOS, null, avaliacao);
        if (itemObjetivos != null) {
            String[] splitobjs = itemObjetivos.getResult().split("\\|#\\|");
            dto.setObjetivos(Arrays.asList(splitobjs));
        } else {
            dto.setObjetivos(new ArrayList<String>());
        }
//        AvaliacaoAlunoBIDTO alunoBI;
        List<Double> pesos;
        try (ResultSet rs = avaliacaoFisicaDao.createStatement(chave, "select dataavaliacao, peso from avaliacaofisica  \n" +
                "where dataavaliacao::date <= '" + Uteis.getDataAplicandoFormatacao(avaliacao.getDataAvaliacao(), "yyyy-MM-dd") + "' and cliente_codigo = " +
                cliente.getCodigo() +
                "order by dataavaliacao desc limit 4")) {
            pesos = new ArrayList<Double>();
            while (rs.next()) {
                pesos.add(rs.getDouble("peso"));
            }
        }

        PesoOsseo po = obterPesoOsseo(chave, cliente.getCodigo(), avaliacao);
        double altura = avaliacao.getAltura();
        if (Double.isNaN(altura) || altura <= 0 || altura > 3.0) {
            altura = 0;
        }
        if (po == null) {
            po = new PesoOsseo();
            po.setAltura(altura);
            po.setPeso(avaliacao.getPeso());
            po.setPercentualGordura(avaliacao.getPercentualGordura());
            po = calcularPesoOsseo(po, cliente.isSexoMasculino());
        }
        dto.setAlunoBI(new AvaliacaoAlunoBIDTO(avaliacao, pesos, po));
        /* cria a lista de emails para enviar avaliacao */
        dto.setEmails(cliente.getListaEmails());
        /* Cria lista de telefones celulares para enviar avaliacao */
        List<String> telefones = new ArrayList<>();
        for (String telefone : cliente.getListaTelefones()) {
            telefone = telefone.replaceAll(" ", "").replaceAll("-", "");
            if (Uteis.validarTelefoneCelular(telefone)) {
                telefones.add(telefone);
            }
        }
        dto.setTelefones(telefones);

//        List<AnamnesePerguntaRespostaDTO> parQRespostas;
        if(usuario != null) {
            Anamnese questionarioParq = anamneseService.consultarParq(chave, usuario.getCodigo(), getViewUtils());
            questionarioParq.setPerguntas(anamneseService.obterPerguntasAnamnese(chave, questionarioParq.getCodigo()));
            List<ItemAvaliacaoFisica> itemParq = obterItensAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, true, avaliacao);
            dto.setParQRespostas(new ArrayList<AnamnesePerguntaRespostaDTO>());
            if (!UteisValidacao.emptyList(itemParq)) {
                ItemAvaliacaoFisica i = itemParq.get(0);
                List<PerguntaAnamnese> perguntas = anamneseService.obterPerguntasAnamnese(chave, questionarioParq.getCodigo());
                for (PerguntaAnamnese pa : perguntas) {
                    RespostaCliente respostaCliente = anamneseService.obterRespostaPerguntasAnamnese(
                            chave, pa.getCodigo(), i.getCliente().getCodigo(), i.getCodigo());
                    if (respostaCliente != null) {
                        dto.getParQRespostas().add(new AnamnesePerguntaRespostaDTO(respostaCliente));
                    }
                }
            }
        }

        ConfiguracaoSistemaService cfs = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
        boolean usarPressaoSistolicaDiastolica = cfs.consultarPorTipo(chave, ConfiguracoesEnum.USAR_PRESSAO_SISTOLICA_DIASTOLICA).getValorAsBoolean();

//        AvaliacaoPesoAlturaDTO pesoAltura;
        String pressaoArterial = "";
        String pressaoSistolica = "";
        String pressaoDiastolica = "";
        ItemAvaliacaoFisica itemPressao = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL, null, avaliacao);
        if (itemPressao != null) {
            pressaoArterial = itemPressao.getResult();
        }
        if (usarPressaoSistolicaDiastolica) {
            ItemAvaliacaoFisica itemPressaoSistolica = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL_SISTOLICA, null, avaliacao);
            if (itemPressaoSistolica != null) {
                pressaoSistolica = itemPressaoSistolica.getResult();
            }
            ItemAvaliacaoFisica itemPressaoDiastolica = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL_DIASTOLICA, null, avaliacao);
            if (itemPressaoDiastolica != null) {
                pressaoDiastolica = itemPressaoDiastolica.getResult();
            }
        }
        ItemAvaliacaoFisica itemFC = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.FREQUENCIA_CARDIACA, null, avaliacao);
        dto.setPesoAltura(new AvaliacaoPesoAlturaDTO(avaliacao, pressaoArterial, pressaoSistolica, pressaoDiastolica,
                itemFC == null || itemFC.getResult().isEmpty() ? 0 : Integer.parseInt(itemFC.getResult())));

//        AvaliacaoDobrasDTO dobras;
        dto.setDobras(new AvaliacaoDobrasDTO(avaliacao));
//        AvaliacaoPerimetriaDTO perimetria;
        dto.setPerimetria(new AvaliacaoPerimetriaDTO(avaliacao, po));
//        AvaliacaoComposicaoDTO composicao;
        dto.setComposicao(new AvaliacaoComposicaoDTO(po));
//        AvaliacaoFlexibilidadeDTO flexibilidade;
        Flexibilidade flexibilidade = carregarFlexibilidade(chave, avaliacao);
        dto.setFlexibilidade(new AvaliacaoFlexibilidadeDTO(flexibilidade));
        //        AvaliacaoPosturaDTO postura;
        AvaliacaoPostural avaliacaoPostural = consultarPostural(chave, avaliacao);
        if (avaliacaoPostural != null) {
            List<ItemAvaliacaoPostural> itens = consultarItens(chave, avaliacaoPostural);
            dto.setPostura(new AvaliacaoPosturaDTO(avaliacaoPostural, itens));
        }

//        AvaliacaoRMLDTO rml;
        ResultadoResistenciaEnum resultAbdomen = obterResultadoResistencia(chave, cliente, obterEnumResistenciaPorConfiguracao(chave, cliente.getSexo(), TipoResistenciaMuscular.ABDOMEN), avaliacao);
        ResultadoResistenciaEnum resultBraco = obterResultadoResistencia(chave, cliente, obterEnumResistenciaPorConfiguracao(chave, cliente.getSexo(), TipoResistenciaMuscular.BRACO), avaliacao);

        dto.setRml(new AvaliacaoRMLDTO(cliente.getFlexaoBraco(),
                cliente.getFlexaoAbdomen(),
                RMLLinha.obterPorIdade(cliente.getIdade()),
                resultBraco == null ? null : resultBraco.getColuna(),
                resultAbdomen == null ? null : resultAbdomen.getColuna()));
//        AvaliacaoVo2DTO vo2;
        ItemAvaliacaoFisica ventItem = obterItemAvaliacaoFisica(chave, cliente.getCodigo(), ItemAvaliacaoFisicaEnum.VENTILOMETRIA, null, avaliacao);


        if (avaliacao.getPeso() != 0) {
            if(ventItem != null && ventItem.getVentilometria() != null) {
                dto.setVo2(new AvaliacaoVo2DTO(avaliacao, ventItem.getVentilometria()));
            } else {
                dto.setVo2(new AvaliacaoVo2DTO(avaliacao));
            }
            calcularVo2Astrand(avaliacao, cliente.isSexoMasculino());
            dto.getVo2().setAstrandVo2Maximo(avaliacao.getVo2MaxAstrand());
            dto.getVo2().setAstrandVo2Estimado(avaliacao.getVo2Astrand());
        }
        if (cliente.getIdade() != null && ventItem != null && !UteisValidacao.emptyNumber(ventItem.getCodigo()) && ventItem.getVentilometria() != null) {
            dto.setVo2(new AvaliacaoVo2DTO(avaliacao, ventItem.getVentilometria()));
            dto.getVo2().setProtocolo(avaliacao.getProtocoloVo());

            ResultadoVO2 resultadoVO2 = montarLinhaCalunaEnum(calcularVo212Minutos(avaliacao, cliente.isSexoMasculino(), cliente.getIdade()));
            if (resultadoVO2 != null) {
                dto.getVo2().setCaminhadaCorridaVo2Max(avaliacao.getVo2Max12());
                dto.getVo2().setCaminhadaCorridaColuna(CaminhadaCorridaColunaEnum.getInstance(resultadoVO2.getResult().trim()));
                final String categoria = resultadoVO2.getCategoria();
                if (categoria.equalsIgnoreCase("muito.fraca") || categoria.equalsIgnoreCase("superior")) {
                    dto.getVo2().setCaminhadaCorridaLinha(CaminhadaCorridaLinhaEnum.getInstance(categoria));
                } else {
                    dto.getVo2().setCaminhadaCorridaLinha(CaminhadaCorridaLinhaEnum.valueOf(categoria));
                }
            }
            resultadoVO2 = montarLinhaCalunaEnum(calcularVoAerobicoDeBanco(avaliacao, cliente.isSexoMasculino(), cliente.getIdade()));
            if (resultadoVO2 != null) {
                dto.getVo2().setAerobicoBancoVo2Max(avaliacao.getVoMaxAerobico());
                dto.getVo2().setAerobicobancoColuna(AerobicobancoColunaEnum.getInstance(resultadoVO2.getResult().trim()));
                dto.getVo2().setAerobicoBancoLinha(AerobicoBancoLinhaEnum.getInstance(resultadoVO2.getCategoria()));
            }
            // Converter tempo do teste 2400 para minutos
            if(!UteisValidacao.emptyString(avaliacao.getTempo2400())){
                dto.getVo2().setTeste2400MetrosTempo((double) Uteis.converterMinutosEmSegundos(avaliacao.getTempo2400()));
            }

            resultadoVO2 = montarLinhaCalunaEnum(calcularVo2Teste2400(avaliacao, cliente.isSexoMasculino(), cliente.getIdade()));
            if (resultadoVO2 != null) {
                dto.getVo2().setTeste2400MetrosVo2Max(avaliacao.getVo2Max2400());
                dto.getVo2().setTeste2400MetrosColuna(Teste2400MetrosColunaEnum.getInstance(resultadoVO2.getResult().trim()));
                final String categoria = resultadoVO2.getCategoria();
                if (categoria.equalsIgnoreCase("muito.fraca")) {
                    dto.getVo2().setTeste2400MetrosLinha(Teste2400MetrosLinhaEnum.MUITA_FRACA);
                } else {
                    dto.getVo2().setTeste2400MetrosLinha(Teste2400MetrosLinhaEnum.valueOf(categoria));
                }

            }
            if(dto.getVo2() != null) {
                // Calcular QUEENS COLLEGE
                if (!UteisValidacao.emptyNumber(avaliacao.getFcQueens())){
                    calcularVo2Queens(avaliacao, cliente.isSexoMasculino());
                }
                dto.getVo2().setCollegeVo2Maximo(avaliacao.getVo2MaxQueens());
                // Calcular ASTRAND CICLOERGOMETRO
                if (avaliacao.getPeso() != 0) {
                    calcularVo2Astrand(avaliacao, cliente.isSexoMasculino());
                    dto.getVo2().setAstrandVo2Maximo(avaliacao.getVo2MaxAstrand());
                    dto.getVo2().setAstrandVo2Estimado(avaliacao.getVo2Astrand());
                }
            }


        }
        if(avaliacao.getCategoriaPercentualGordura() != null) {
            dto.setComposicaoNota(avaliacao.getCategoriaPercentualGordura().toString());
        }
        if (avaliacao.getCategoriaAvaliacaoIMC() != null) {
            dto.setImcNota(avaliacao.getCategoriaAvaliacaoIMC().toString());
        }
        dto.setCardioNota(viewUtils.getLabelInternacionalizada(avaliacao.getResultadoRiscoCA(), "PT"));
//        AvaliacaoSomatotipiaDTO somatotipia;
        dto.setSomatotipia(new AvaliacaoSomatotipiaDTO(avaliacao, po));
//        AvaliacaoMetaRecomendacoesDTO metaRecomendacoes;
        dto.setMetaRecomendacoes(new AvaliacaoMetaRecomendacoesDTO(avaliacao));
        return dto;
    }

    /**
     * @param resultadoVO2List
     * @return linha e coluna selecionado(Categoria e Result)
     */
    private ResultadoVO2 montarLinhaCalunaEnum(List<ResultadoVO2> resultadoVO2List) {
        ResultadoVO2 resultadoVO2 = null;
        for (ResultadoVO2 VO2 : resultadoVO2List) {
            if (VO2.getResult() != null) {
                resultadoVO2 = VO2;
                break;
            }
        }
        return resultadoVO2;
    }

    public void enviarEmailAvaliacao(Integer empresaId,
                                     final String ctx,
                                     final Integer avaliacaoid,
                                     final String email,
                                     Usuario usuario,
                                     HttpServletRequest request, ServletContext servletContext, IdiomaBancoEnum language) throws Exception {

        AvaliacaoFisica avaliacaoFisica = obterPorId(ctx, avaliacaoid);
        Flexibilidade flexibilidade = obterFlexibilidade(ctx, avaliacaoFisica.getCodigo());
        PesoOsseo pesoOsseo = obterPesoOsseo(ctx, avaliacaoFisica.getCliente().getCodigo(), avaliacaoFisica);
        ItemAvaliacaoFisica ventItem = obterItemAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(),
                ItemAvaliacaoFisicaEnum.VENTILOMETRIA, null, avaliacaoFisica);
        Ventilometria ventilometria = new Ventilometria();

        if (ventItem != null && !UteisValidacao.emptyNumber(ventItem.getCodigo())) {
            ventilometria = ventItem.getVentilometria();
        }
        Anamnese anamnese = null;
        Anamnese questionarioParq = null;

        AnamneseService as = (AnamneseService) UtilContext.getBean(AnamneseService.class);
        questionarioParq = as.consultarParq(ctx, 0, (ViewUtils) UtilContext.getBean(ViewUtils.class));
        questionarioParq.setPerguntas(as.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo()));
        List<ItemAvaliacaoFisica> parqs = obterItensAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, true,
                avaliacaoFisica);
        if (!UteisValidacao.emptyList(parqs)) {
            obterRespostas(ctx, questionarioParq, parqs.get(0));
        }
        List<ItemAvaliacaoFisica> anamnesesAluno = obterItensAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(),
                ItemAvaliacaoFisicaEnum.ANAMNESE, 1, false, avaliacaoFisica);
        if (!UteisValidacao.emptyList(anamnesesAluno)) {
            anamnese = anamnesesAluno.get(0).getAnamnese();
            List<PerguntaAnamnese> perguntaAnamneseList = anamneseService.obterPerguntasAnamnese(ctx, anamnese.getCodigo());
            List <PerguntaAnamnese> lista = new ArrayList<>();
            if(!UteisValidacao.emptyList(perguntaAnamneseList)) {
                for (PerguntaAnamnese pa : perguntaAnamneseList) {
                    if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                            || pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                        pa.getPergunta().setOpcoes(anamneseService.obterOpcoes(ctx, pa.getPergunta().getCodigo()));
                    }
                    lista.add(new PerguntaAnamnese(pa));
                }
            }
            anamnese.setPerguntas(lista);
            obterRespostas(ctx, anamnese, anamnesesAluno.get(0));
        }

        JSONObject json = new JSONObject();
        json.put("k", ctx);
        json.put("a", avaliacaoFisica.getCodigo());

        String urlAplicacao = Aplicacao.getProp(Aplicacao.discoveryUrls)+"/redir"+"/"+ctx+"/treino?"+"m=imprimiravaliacao&j=" + Uteis.encriptar(json.toString(), "cript0p4r4msint");

        UteisEmail u = new UteisEmail();
        String pdf = gerarPDFAvaliacaoFisica(ctx, avaliacaoFisica, flexibilidade, pesoOsseo, ventilometria, anamnese,
                questionarioParq, usuario, viewUtils, request, servletContext, true, true, "PT");
        if (SuperControle.independente(ctx)) {
            ConfigsEmail cfgEmail = cfgsService.obterConfiguracoes(ctx);
            u.preencherConfiguracaoEmailPadrao(cfgEmail);
        } else {
            Empresa empresa = empresaService.obterPorIdZW(ctx, avaliacaoFisica.getCliente().getEmpresa());
            String nomeEmpresa = empresa == null ? "" : empresa.getNome();

            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            JSONObject configsEmail = integracaoWS.configsEmail(url);
            u.preencherConfiguracaoEmailPadrao(configsEmail, nomeEmpresa);
        }
        File arquivo = new File(pdf);
        Long megabytes = arquivo == null || !arquivo.exists() ? null : ((arquivo.length() / 1024) / 1024);
        if(megabytes != null && megabytes < 4l){
            u.addAnexo("AvaliacaoFisica_Treino_" + avaliacaoFisica.getCliente().getNome() + ".pdf", arquivo);
            urlAplicacao = null;
        }

        Map<String, Object> paramsConteudoEmailImpressao = prepareParamsConteudoEmailImpressao(avaliacaoFisica, usuario, servletContext, ctx);
        String a = novoModeloConteudoEmailAvaliacao(paramsConteudoEmailImpressao);
        u.enviarEmail(email, avaliacaoFisica.getCliente().getNome(), a, "", "Avaliação Física");
    }

    @Override
    public void enviarComparativoSpa(List<Integer> avaliacoes, HttpServletRequest request, ServletContext servletContext) throws Exception {

        String ctx = sessaoService.getUsuarioAtual().getChave();
        Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
        ClienteSintetico cliente = new ClienteSintetico();

        List<AvaliacaoFisica> listaAvaliacao = new ArrayList<>();
        for (Integer i : avaliacoes) {
            AvaliacaoFisica avaliacaoFisica = new AvaliacaoFisica();
            avaliacaoFisica = obterPorId(ctx, i);
            if (cliente.getNome() == null) {
                cliente = avaliacaoFisica.getCliente();
            }
            listaAvaliacao.add(avaliacaoFisica);
        }

        String pdf = gerarPDFComparativo(
                ctx, listaAvaliacao, usuario, UtilContext.getBean(ViewUtils.class), request,
                null, true);

        enviarEmailComparativoAvaliacao(ctx, pdf, cliente,
                usuario.getEmpresaLogada() == null ? "" : usuario.getEmpresaLogada().getNome(),
                request, servletContext, usuario);
    }

    public List<PerguntaResponseTO> obterPerguntasParQ(String ctx, Integer codigoAnamnese) throws Exception {
        if (ctx == null) {
            ctx = sessaoService.getUsuarioAtual().getChave();
        }

        // Se foi fornecido um código específico de anamnese, consulta apenas essa anamnese
        if (codigoAnamnese != null) {
            List<PerguntaResponseTO> listRet = new ArrayList<>();
            List<PerguntaAnamnese> perguntas = anamneseService.obterPerguntasAnamnese(ctx, codigoAnamnese);
            if (perguntas != null) {
                for (PerguntaAnamnese pergunta : perguntas) {
                    listRet.add(new PerguntaResponseTO(pergunta.getPergunta(), pergunta.getCodigo()));
                }
            }
            return listRet;
        }

        // Comportamento original quando codigoAnamnese é null
        List<Anamnese> anamneseList = anamneseDao.listaTodasAtivasEParq(ctx);
        boolean encontrouParqCom10Perguntas = false;

        if (anamneseList != null) {
            for (Anamnese anamnese : anamneseList) {
                if (anamnese != null) {
                    List<PerguntaAnamnese> perguntas = anamneseService.obterPerguntasAnamnese(ctx, anamnese.getCodigo());
                    if (perguntas != null && perguntas.size() == 10) {
                        encontrouParqCom10Perguntas = true;
                        break;
                    }
                }
            }
        }

        if (!encontrouParqCom10Perguntas) {
            ViewUtils bean = UtilContext.getBean(ViewUtils.class);
            anamneseService.gravarParqPadrao10Perguntas(ctx, bean);
            anamneseList = anamneseDao.listaTodasAtivasEParq(ctx);
        }

        List<PerguntaResponseTO> listRet = new ArrayList<>();
        for (Anamnese anamnese : anamneseList) {
            if (anamnese != null && Boolean.TRUE.equals(anamnese.getParq()) && Boolean.TRUE.equals(anamnese.getAtiva())) {
                List<PerguntaAnamnese> perguntas = anamneseService.obterPerguntasAnamnese(ctx, anamnese.getCodigo());
                if (perguntas != null && perguntas.size() == 10) {
                    for (PerguntaAnamnese pergunta : perguntas) {
                        listRet.add(new PerguntaResponseTO(pergunta.getPergunta(), pergunta.getCodigo()));
                    }
                    break;
                }
            }
        }
        return listRet;
    }

    @Override
    public AvaliacaoFisicaDTO inserir(AvaliacaoFisicaDTOUpdate avaliacaoFisicaDTO, Integer codigoCliente) throws ServiceException {
        final UsuarioSimplesDTO usuarioTO = sessaoService.getUsuarioAtual();
        final String ctx = usuarioTO.getChave();
        final Usuario usuario = usuarioService.obterPorId(ctx, usuarioTO.getId());
        ClienteSintetico clienteSintetico = clienteSinteticoService.obterPorCodigo(ctx, codigoCliente);
        AvaliacaoFisica avaliacaoFisica = gravarAvaliacaoFisica(ctx, codigoCliente, avaliacaoFisicaDTO, clienteSintetico, usuario, null, true);
        inserirAlterar(ctx, usuarioTO, avaliacaoFisicaDTO, clienteSintetico, avaliacaoFisica, estaAlterando);

        try {
            AvaliacaoFisicaDTO avaliacaoFisicaRetorno = toDTO(ctx, avaliacaoFisica, usuario);
            if (avaliacaoFisicaRetorno != null) {
                avaliacaoFisicaRetorno.setResultadoParq(obterResultadoQuestionarioParq(ctx, avaliacaoFisica.getCliente()));
                clienteSintetico.setParq(obterResultadoQuestionarioParq(ctx, avaliacaoFisica.getCliente()));
                clienteDao.update(ctx, clienteSintetico);
                atualizarResultadoEvolucaoAluno(ctx, clienteSintetico);
            }
            incluirLog(ctx, avaliacaoFisica.getCodigo().toString(),
                    avaliacaoFisica.getCliente().getCodigo().toString(), "",
                    avaliacaoFisica.getDescricaoParaLog(null),
                    "INCLUSÃO", "INCLUSÃO DE AVALIAÇÃO FÍSICA [Origem: "+avaliacaoFisicaDTO.getOrigemAvaliacao()+"] ", EntidadeLogEnum.AVALIACAOFISICA, "Avaliação Física", sessaoService, logDao, null, null);
            Usuario usuarioCliente = usuarioService.consultarPorCliente(ctx, clienteSintetico.getCodigo());

            boolean clienteTemUsuario = usuarioCliente != null && !UteisValidacao.emptyNumber(usuarioCliente.getCodigo());
            if(clienteTemUsuario) {
                PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, "Nova avaliação física disponível", "Acompanhe a sua evolução e compare os resultados.", usuarioCliente.getUserName(), "");

                avaliacaoFisicaApiService.salvaAvaliacaoFisicaApiExternaAsync(ctx, usuarioCliente.getEmpresaZW(), usuarioCliente.getCodigo(), avaliacaoFisica);
            }

            return avaliacaoFisicaRetorno;
        } catch (Exception e) {
            Uteis.logar(e, AvaliacaoFisicaImpl.class);
            throw new ServiceException(e);
        }
    }

    public void notificarProtocolo(final ProtocolosAvaliacaoFisicaEnum protocolo, String ctx, ClienteSintetico clienteSintetico) {
        notificarRecursoEmpresa(protocolo.getRecursoSistema(), ctx, clienteSintetico);
    }

    public void notificarRecursoEmpresa(final RecursoSistema recurso, String ctx, ClienteSintetico clienteSintetico) {
        try {
            Empresa empresa = empresaService.obterPorIdZW(ctx, clienteSintetico.getEmpresa());
            EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                    recurso,
                    null,
                    ctx,
                    false,
                    Aplicacao.isTrue(Aplicacao.usarUrlRecursoEmpresa),
                    empresa.getCodZW(),
                    "GYMPASS",
                    empresa.getNome(),
                    "",
                    "",
                    ""
            );
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaImpl.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    @Override
    public AvaliacaoFisicaDTO inserir(AvaliacaoFisicaDTOUpdate avaliacaoFisicaDTO, Integer codigoCliente, Integer idProduto) throws ServiceException {
        final UsuarioSimplesDTO usuarioTO = sessaoService.getUsuarioAtual();
        final String ctx = usuarioTO.getChave();
        final Usuario usuario = usuarioService.obterPorId(ctx, usuarioTO.getId());
        ClienteSintetico clienteSintetico = clienteSinteticoService.obterPorCodigo(ctx, codigoCliente);
        AvaliacaoFisica avaliacaoFisica = gravarAvaliacaoFisica(ctx, codigoCliente, avaliacaoFisicaDTO, clienteSintetico, usuario, null, true);
        inserirAlterar(ctx, usuarioTO, avaliacaoFisicaDTO, clienteSintetico, avaliacaoFisica, estaAlterando);
        IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        try {
            String produtoVigente = integracaoWS.verificarAlunoTemProdutoVigenteRetornandoQuantidade(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, clienteSintetico.getCodigoCliente(),
                    idProduto);
            JSONArray produtos = new JSONArray(produtoVigente);
            Integer produtoLancado = 0;
            for(int i = 0; i < produtos.length(); i++){
                List<AvaliacaoFisica> avLancada = obterAvaliacaoMovProduto(ctx, produtos.getJSONObject(i).getInt("produtoId"));
                if(UteisValidacao.emptyList(avLancada) || produtos.getJSONObject(i).getInt("quantidade") > avLancada.size()){
                    produtoLancado = produtos.getJSONObject(i).getInt("produtoId");
                }
            }
            if (produtoLancado != 0) {
                avaliacaoFisica.setMovProduto(produtoLancado);
                avaliacaoFisica = alterar(ctx, avaliacaoFisica);
            }
            AvaliacaoFisicaDTO avaliacaoFisicaRetorno = toDTO(ctx, avaliacaoFisica, usuario);
            if (avaliacaoFisicaRetorno != null) {
                avaliacaoFisicaRetorno.setResultadoParq(obterResultadoQuestionarioParq(ctx, avaliacaoFisica.getCliente()));
                clienteSintetico.setParq(obterResultadoQuestionarioParq(ctx, avaliacaoFisica.getCliente()));
                clienteDao.update(ctx, clienteSintetico);
                atualizarResultadoEvolucaoAluno(ctx, clienteSintetico);

                Usuario usuarioCliente = usuarioService.consultarPorCliente(ctx, clienteSintetico.getCodigo());
                boolean clienteTemUsuario = usuarioCliente != null && !UteisValidacao.emptyNumber(usuarioCliente.getCodigo());

                if(clienteTemUsuario) {
                    PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, "Nova avaliação física disponível", "Acompanhe a sua evolução e compare os resultados.", usuarioCliente.getUserName(), "");
                    avaliacaoFisicaApiService.salvaAvaliacaoFisicaApiExternaAsync(ctx, usuarioCliente.getEmpresaZW(), usuarioCliente.getCodigo(), avaliacaoFisica);
                }
            }
            return avaliacaoFisicaRetorno;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public AvaliacaoFisicaDTO alterar(AvaliacaoFisicaDTOUpdate avaliacaoFisicaDTO, Integer codigoAvaliacaoFisica) throws ServiceException {
        final UsuarioSimplesDTO usuarioTO = sessaoService.getUsuarioAtual();
        final String ctx = usuarioTO.getChave();

        AvaliacaoFisica avaliacaoFisicaAntesAlteracao;
        AvaliacaoFisica avaliacaoFisica;
        try {
            avaliacaoFisica = obterPorId(ctx, codigoAvaliacaoFisica);
            avaliacaoFisicaAntesAlteracao = UtilReflection.copy(avaliacaoFisica);
            calcularVo2Astrand(avaliacaoFisicaAntesAlteracao, avaliacaoFisicaAntesAlteracao.getCliente().isSexoMasculino());
            calcularVo2Queens(avaliacaoFisicaAntesAlteracao, avaliacaoFisicaAntesAlteracao.getCliente().isSexoMasculino());
            Integer fcRepouso = avaliacaoFisicaAntesAlteracao.getCliente().getFcRepouso();
            if (fcRepouso == null) {
                fcRepouso = 0;
            }
            calcularTabelFrequenciaCardiaca(avaliacaoFisicaAntesAlteracao, avaliacaoFisicaAntesAlteracao.getCliente(), fcRepouso);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        final Usuario usuario = usuarioService.obterPorId(ctx, usuarioTO.getId());
        ClienteSintetico clienteSintetico = clienteSinteticoService.obterPorCodigo(ctx, avaliacaoFisica.getCliente().getCodigo());

        estaAlterando = true;
        avaliacaoFisica = gravarAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), avaliacaoFisicaDTO, clienteSintetico, usuario, avaliacaoFisica, false);
        inserirAlterar(ctx, usuarioTO, avaliacaoFisicaDTO, clienteSintetico, avaliacaoFisica, estaAlterando);
        estaAlterando = false;

        try {
            AvaliacaoFisicaDTO avaliacaoFisicaRetorno = toDTO(ctx, avaliacaoFisica, usuario);
            if (avaliacaoFisicaRetorno != null) {
                avaliacaoFisicaRetorno.setResultadoParq(obterResultadoQuestionarioParq(ctx, avaliacaoFisica.getCliente()));
                clienteSintetico.setParq(obterResultadoQuestionarioParq(ctx, avaliacaoFisica.getCliente()));
                clienteDao.update(ctx, clienteSintetico);
                atualizarResultadoEvolucaoAluno(ctx, clienteSintetico);
            }
            incluirLog(ctx, avaliacaoFisica.getCodigo().toString(), avaliacaoFisica.getCliente().getCodigo().toString(), avaliacaoFisicaAntesAlteracao.getDescricaoParaLog(avaliacaoFisica),
                    avaliacaoFisica.getDescricaoParaLog(avaliacaoFisicaAntesAlteracao), "ALTERAÇÃO", "ALTERAÇÃO AVALIAÇÃO FÍSICA [Origem: "+avaliacaoFisicaDTO.getOrigemAvaliacao()+"]",
                    EntidadeLogEnum.AVALIACAOFISICA, "Avaliação Física", sessaoService, logDao, null, null);

            Usuario usuarioCliente = usuarioService.consultarPorCliente(ctx, clienteSintetico.getCodigo());
            boolean clienteTemUsuario = usuarioCliente != null && !UteisValidacao.emptyNumber(usuarioCliente.getCodigo());
            if(clienteTemUsuario) {
                PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, "Nova avaliação física disponível", "Acompanhe a sua evolução e compare os resultados.", usuario.getUserName(), "");
                avaliacaoFisicaApiService.salvaAvaliacaoFisicaApiExternaAsync(ctx, usuarioCliente.getEmpresaZW(), usuarioCliente.getCodigo(), avaliacaoFisica);
            }

            return avaliacaoFisicaRetorno;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private void enviaNotificacaoAppDoAluno(String ctx, ClienteSintetico clienteSintetico, Usuario usuario) {
        try {
            Usuario usuarioCliente = usuarioService.consultarPorCliente(ctx, clienteSintetico.getCodigo());
            boolean clienteTemUsuario = usuarioCliente != null && !UteisValidacao.emptyNumber(usuarioCliente.getCodigo());
            if(clienteTemUsuario) {
                PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, "Nova avaliação física disponível", "Acompanhe a sua evolução e compare os resultados.", usuario.getUserName(), "");
            }
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaImpl.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    @Override
    public AvaliacaoFisicaDTO alterarVo2(Integer codigoAvaliacaoFisica, AvaliacaoVo2AstrandDTO dados) throws ServiceException {
        final UsuarioSimplesDTO usuarioTO = sessaoService.getUsuarioAtual();
        final String ctx = usuarioTO.getChave();

        AvaliacaoFisica avaliacaoFisica;
        try {
            avaliacaoFisica = obterPorId(ctx, codigoAvaliacaoFisica);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        final Usuario usuario = usuarioService.obterPorId(ctx, usuarioTO.getId());
        ClienteSintetico clienteSintetico = clienteSinteticoService.obterPorCodigo(ctx, avaliacaoFisica.getCliente().getCodigo());

        avaliacaoFisica.setFcAstrand4(dados.getAstrandFrequencia4());
        avaliacaoFisica.setFcAstrand5(dados.getAstrandFrequencia5());
        avaliacaoFisica.setCargaAstrand(dados.getAstrandCarga());

        AvaliacaoFisica avaliacaoFisicaUpdate = alterar(ctx, avaliacaoFisica);
        // Setar os valores transients novamente
        avaliacaoFisicaUpdate.setDiametroTornozelo(avaliacaoFisica.getDiametroTornozelo());
        avaliacaoFisicaUpdate.setDiametroCotovelo(avaliacaoFisica.getDiametroCotovelo());
        avaliacaoFisicaUpdate.setDiametroJoelho(avaliacaoFisica.getDiametroJoelho());
        avaliacaoFisicaUpdate.setDiametroPunho(avaliacaoFisica.getDiametroPunho());
        avaliacaoFisica = avaliacaoFisicaUpdate;

        try {
            AvaliacaoFisicaDTO avaliacaoFisicaRetorno = toDTO(ctx, avaliacaoFisica, usuario);
            if (avaliacaoFisicaRetorno != null) {
                avaliacaoFisicaRetorno.setResultadoParq(obterResultadoQuestionarioParq(ctx, avaliacaoFisica.getCliente()));
                clienteSintetico.setParq(obterResultadoQuestionarioParq(ctx, avaliacaoFisica.getCliente()));
                clienteDao.update(ctx, clienteSintetico);
                atualizarResultadoEvolucaoAluno(ctx, clienteSintetico);
                PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, "Nova avaliação física disponível", "Acompanhe a sua evolução e compare os resultados.", usuario.getUserName(), "");
            }
            return avaliacaoFisicaRetorno;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void inserirAlterar(String ctx, UsuarioSimplesDTO usuarioTO, AvaliacaoFisicaDTOUpdate avaliacaoFisicaDTO, ClienteSintetico clienteSintetico, AvaliacaoFisica avaliacaoFisica, boolean estaAlterando) throws ServiceException {

        final Usuario usuario = usuarioService.obterPorId(ctx, usuarioTO.getId());
        notificarRecursoEmpresa(RecursoSistema.GRAVOU_AVALIACAO_TREINO_NOVO, ctx, clienteSintetico);
        notificarProtocolo(avaliacaoFisicaDTO.getDobras().getProtocolo(), ctx, clienteSintetico);
        if (estaAlterando) {
            gravarAvaliacaoPostural(ctx, avaliacaoFisicaDTO, avaliacaoFisica, false);
            gravarObjetivo(ctx, avaliacaoFisicaDTO, usuario, clienteSintetico, avaliacaoFisica, true);
            gravarResistencia(ctx, avaliacaoFisicaDTO, clienteSintetico, avaliacaoFisica, usuario, true);
            gravarAnamneseResposta(ctx, avaliacaoFisicaDTO, clienteSintetico, avaliacaoFisica, usuario, true);
            gravarAnamneseParq(ctx, clienteSintetico, avaliacaoFisicaDTO, usuario, avaliacaoFisica, true);
            gravarFCPressao(ctx, clienteSintetico, avaliacaoFisica, usuario, true);
            ItemAvaliacaoFisica ventItem = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.VENTILOMETRIA, null, avaliacaoFisica);
            if(ventItem != null) {
                gravarVentilometria(ctx, clienteSintetico, usuario, avaliacaoFisicaDTO.getVo2(), avaliacaoFisica, ventItem.getVentilometria());
            }else {
                gravarVentilometria(ctx, clienteSintetico, usuario, avaliacaoFisicaDTO.getVo2(), avaliacaoFisica, null);
            }
        } else {
            gravarAvaliacaoPostural(ctx, avaliacaoFisicaDTO, avaliacaoFisica, true);
            gravarObjetivo(ctx, avaliacaoFisicaDTO, usuario, clienteSintetico, avaliacaoFisica, false);
            gravarResistencia(ctx, avaliacaoFisicaDTO, clienteSintetico, avaliacaoFisica, usuario, false);
            gravarAnamneseResposta(ctx, avaliacaoFisicaDTO, clienteSintetico, avaliacaoFisica, usuario, false);
            gravarAnamneseParq(ctx, clienteSintetico, avaliacaoFisicaDTO, usuario, avaliacaoFisica, false);
            gravarFCPressao(ctx, clienteSintetico, avaliacaoFisica, usuario, false);
            gravarVentilometria(ctx, clienteSintetico, usuario, avaliacaoFisicaDTO.getVo2(), avaliacaoFisica, null);

        }
        itensParaGravar(ctx, avaliacaoFisicaDTO, usuario, clienteSintetico, avaliacaoFisica);

    }


    /**
     * Evitando duplicidade de linha.
     *
     * @param ctx                chave do banco correspondente.
     * @param avaliacaoFisicaDTO objeto do serviço.
     * @param usuario            usuário logado na sessão.
     * @param clienteSintetico   cliente cujo a avaliação física está sendo inserida ou alterada.
     * @param avaliacaoFisica
     */
    private void itensParaGravar(final String ctx, AvaliacaoFisicaDTOUpdate avaliacaoFisicaDTO, Usuario usuario, ClienteSintetico clienteSintetico, AvaliacaoFisica avaliacaoFisica) throws ServiceException {
        gravarPesoOsseo(ctx, usuario, clienteSintetico, avaliacaoFisica);
        gravarFlexibilidade(ctx, avaliacaoFisica, avaliacaoFisicaDTO);
    }

    private void setarHora(AvaliacaoFisica avaliacaoFisica) {
        try {
            if (avaliacaoFisica.getDataAvaliacao() != null
                    && Calendario.getHora(avaliacaoFisica.getDataAvaliacao(), "HH").equals("00")) {
                avaliacaoFisica.setDataAvaliacao(Calendario.getDataComHora(avaliacaoFisica.getDataAvaliacao(),
                        Integer.valueOf(Calendario.getHora(Calendario.hoje(), "HH"))));
            }
        }catch (Exception e){
            Logger.getLogger(AvaliacaoFisicaImpl.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    /**
     * @param ctx                chave do banco correspondente
     * @param avaliacaoFisicaDTO objeto do serviço.
     * @param clienteSintetico   cliente cujo a avaliação física está sendo inserida ou alterada.
     * @param usuario            usuário logado na sessão.
     * @param avaliacaoFisica
     */
    private AvaliacaoFisica gravarAvaliacaoFisica(final String ctx, Integer codigoCliente, AvaliacaoFisicaDTOUpdate avaliacaoFisicaDTO, ClienteSintetico clienteSintetico, Usuario usuario, AvaliacaoFisica avaliacaoFisica, Boolean novaAvaliacao) throws ServiceException {
        if (novaAvaliacao) {
            validarProdutoAvaliacao(ctx, clienteSintetico);
        }
        if (avaliacaoFisica == null) {
            avaliacaoFisica = new AvaliacaoFisica();
            avaliacaoFisica.setCodigo(null);
            avaliacaoFisica.setCliente(clienteSintetico);
        }
        avaliacaoFisica.setDataAvaliacao(new Date(avaliacaoFisicaDTO.getDataAvaliacao()));
        setarHora(avaliacaoFisica);
        if(avaliacaoFisicaDTO.getDataProxima() == null){
            avaliacaoFisica.setDataProxima(null);
        } else {
            avaliacaoFisica.setDataProxima(new Date(avaliacaoFisicaDTO.getDataProxima()));
        }

        try {
            if (estaAlterando == false) {
                AvaliacaoFisica avaliacaoVirgente = obterAvaliacaoVigente(ctx, codigoCliente);
                if (avaliacaoVirgente != null) {
                    avaliacaoFisica.setMetaPercentualGorduraAnterior(avaliacaoVirgente.getMetaPercentualGordura());
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }

        if (avaliacaoFisicaDTO.getMetaRecomendacoes() != null) {
            avaliacaoFisica.setMetaPercentualGordura(avaliacaoFisicaDTO.getMetaRecomendacoes().getPercentualGorduraProxima());
            avaliacaoFisica.setRecomendacoes(avaliacaoFisicaDTO.getMetaRecomendacoes().getObservacoesAvaliador());
        }

        if (avaliacaoFisicaDTO.getSomatotipia() != null) {
            avaliacaoFisica.setEndomorfia(avaliacaoFisicaDTO.getSomatotipia().getEndomorfia());
            avaliacaoFisica.setMesomorfia(avaliacaoFisicaDTO.getSomatotipia().getMesomorfia());
            avaliacaoFisica.setEctomorfia(avaliacaoFisicaDTO.getSomatotipia().getEctomorfia());
            avaliacaoFisica.setTriceps(avaliacaoFisicaDTO.getSomatotipia().getDobraTriceps());
            avaliacaoFisica.setSupraEspinhal(avaliacaoFisicaDTO.getSomatotipia().getDobraSupraEspinhal());
            avaliacaoFisica.setSubescapular(avaliacaoFisicaDTO.getSomatotipia().getDobraSubescapular());
            avaliacaoFisica.setPanturrilha(avaliacaoFisicaDTO.getSomatotipia().getDobraPanturrilha());
            avaliacaoFisica.setPanturrilhaDir(avaliacaoFisicaDTO.getSomatotipia().getPerimetroPanturrilhaDireita());
            avaliacaoFisica.setBracoContraidoDir(avaliacaoFisicaDTO.getSomatotipia().getPerimetroBracoContraidoDireito());
            avaliacaoFisica.setDiametroPunho(avaliacaoFisicaDTO.getSomatotipia().getDiametroPunho());
            avaliacaoFisica.setDiametroJoelho(avaliacaoFisicaDTO.getSomatotipia().getDiametroJoelho());
            avaliacaoFisica.setDiametroCotovelo(avaliacaoFisicaDTO.getSomatotipia().getDiametroCotovelo());
            avaliacaoFisica.setDiametroTornozelo(avaliacaoFisicaDTO.getSomatotipia().getDiametroTornozelo());
        }

        if (avaliacaoFisicaDTO.getVo2() != null) {
            avaliacaoFisica.setDistancia12(avaliacaoFisicaDTO.getVo2().getCaminhadaCorridaDistancia());

            double segundos = avaliacaoFisicaDTO.getVo2().getTeste2400MetrosTempo() == null ? 0 : avaliacaoFisicaDTO.getVo2().getTeste2400MetrosTempo();
            avaliacaoFisica.setTempo2400(Uteis.converterSegundosEmMinutos((int) segundos));
            avaliacaoFisica.setVo2Max2400(segundos == 0 ? 0.0 : ((2400 * 60 * 0.2) + 3.5) / (segundos));
            avaliacaoFisica.setVo2Max2400(Uteis.forcarCasasDecimais(4, avaliacaoFisica.getVo2Max2400() < 0.0 ? 0.0 : avaliacaoFisica.getVo2Max2400()));

            avaliacaoFisica.setVoMaxAerobico(avaliacaoFisicaDTO.getVo2().getAerobicoBancoFrequencia());
            avaliacaoFisica.setFcAstrand4(avaliacaoFisicaDTO.getVo2().getAstrandFrequencia4());
            avaliacaoFisica.setFcAstrand5(avaliacaoFisicaDTO.getVo2().getAstrandFrequencia5());
            avaliacaoFisica.setFcQueens(avaliacaoFisicaDTO.getVo2().getCollegeFrequencia());
            avaliacaoFisica.setCargaAstrand(avaliacaoFisicaDTO.getVo2().getAstrandCarga());
            avaliacaoFisica.setProtocoloVo(avaliacaoFisicaDTO.getVo2().getProtocolo());
        }

        if (avaliacaoFisicaDTO.getPerimetria() != null) {
            avaliacaoFisica.setPescoco(avaliacaoFisicaDTO.getPerimetria().getPescoco());
            avaliacaoFisica.setOmbro(avaliacaoFisicaDTO.getPerimetria().getOmbro());
            avaliacaoFisica.setToraxBusto(avaliacaoFisicaDTO.getPerimetria().getToraxBustoRelaxado());
            avaliacaoFisica.setBracoRelaxadoEsq(avaliacaoFisicaDTO.getPerimetria().getBracoRelaxadoEsq());
            avaliacaoFisica.setBracoRelaxadoDir(avaliacaoFisicaDTO.getPerimetria().getBracoRelaxadoDir());
            avaliacaoFisica.setBracoContraidoEsq(avaliacaoFisicaDTO.getPerimetria().getBracoContraidoEsq());
            avaliacaoFisica.setBracoContraidoDir(avaliacaoFisicaDTO.getPerimetria().getBracoContraidoDir());
            avaliacaoFisica.setAntebracoEsq(avaliacaoFisicaDTO.getPerimetria().getAntebracoEsq());
            avaliacaoFisica.setAntebracoDir(avaliacaoFisicaDTO.getPerimetria().getAntebracoDir());
            avaliacaoFisica.setCintura(avaliacaoFisicaDTO.getPerimetria().getCintura());
            avaliacaoFisica.setCircunferenciaAbdominal(avaliacaoFisicaDTO.getPerimetria().getCircunferenciaAbdominal());
            avaliacaoFisica.setQuadril(avaliacaoFisicaDTO.getPerimetria().getQuadril());
            avaliacaoFisica.setCoxaProximalEsq(avaliacaoFisicaDTO.getPerimetria().getCoxaProximalEsq());
            avaliacaoFisica.setCoxaProximalDir(avaliacaoFisicaDTO.getPerimetria().getCoxaProximalDir());
            avaliacaoFisica.setCoxaMediaEsq(avaliacaoFisicaDTO.getPerimetria().getCoxaMediaEsq());
            avaliacaoFisica.setCoxaMediaDir(avaliacaoFisicaDTO.getPerimetria().getCoxaMediaDir());
            avaliacaoFisica.setCoxaDistalEsq(avaliacaoFisicaDTO.getPerimetria().getCoxaDistalEsq());
            avaliacaoFisica.setCoxaDistalDir(avaliacaoFisicaDTO.getPerimetria().getCoxaDistalDir());
            avaliacaoFisica.setPanturrilhaEsq(avaliacaoFisicaDTO.getPerimetria().getPanturrilhaEsq());
            avaliacaoFisica.setPanturrilhaDir(avaliacaoFisicaDTO.getPerimetria().getPanturrilhaDir());

            // caso nao seja null o valor já vai vim preenchido pela somototipia pois sao os mesmos campos
            if(avaliacaoFisicaDTO.getSomatotipia() == null) {
                avaliacaoFisica.setDiametroPunho(avaliacaoFisicaDTO.getPerimetria().getDiametroPunho());
                avaliacaoFisica.setDiametroJoelho(avaliacaoFisicaDTO.getPerimetria().getDiametroJoelho());
                avaliacaoFisica.setDiametroCotovelo(avaliacaoFisicaDTO.getPerimetria().getDiametroCotovelo());
                avaliacaoFisica.setDiametroTornozelo(avaliacaoFisicaDTO.getPerimetria().getDiametroTornozelo());
            }
        }
        if (avaliacaoFisicaDTO.getPesoAltura() != null) {
            if (avaliacaoFisicaDTO.getPesoAltura().getPeso() != null) {
                avaliacaoFisica.setPeso(avaliacaoFisicaDTO.getPesoAltura().getPeso());
            }
            if (avaliacaoFisicaDTO.getPesoAltura().getAltura() != null) {
                avaliacaoFisica.setAltura(Uteis.adicionarCaractereApos1CasaDecimal(avaliacaoFisicaDTO.getPesoAltura().getAltura()));
            }
            avaliacaoFisica.setFcMaxima(avaliacaoFisicaDTO.getPesoAltura().getFrequenciaMax());
            if (clienteSintetico.getFc() != null) {
                clienteSintetico.getFc().setResult(avaliacaoFisicaDTO.getPesoAltura().getFrequencia().toString());
            }
            boolean usarPressaoSistolicaDiastolica = cfgsService.consultarPorTipo(ctx, ConfiguracoesEnum.USAR_PRESSAO_SISTOLICA_DIASTOLICA).getValorAsBoolean();
            if (usarPressaoSistolicaDiastolica) {
                clienteSintetico.getPressaoSistolica().setResult(avaliacaoFisicaDTO.getPesoAltura().getPressaoSistolica());
                clienteSintetico.getPressaoDiastolica().setResult(avaliacaoFisicaDTO.getPesoAltura().getPressaoDiastolica());
            } else {
                clienteSintetico.getPressao().setResult(avaliacaoFisicaDTO.getPesoAltura().getPressaoArterial());
            }
        }
        if (avaliacaoFisicaDTO.getDobras() != null) {
            avaliacaoFisica.setProtocolo(avaliacaoFisicaDTO.getDobras().getProtocolo());
            avaliacaoFisica.setAbdominal(avaliacaoFisicaDTO.getDobras().getAbdominal());
            avaliacaoFisica.setPeitoral(avaliacaoFisicaDTO.getDobras().getPeitoral());
            avaliacaoFisica.setCoxaMedial(avaliacaoFisicaDTO.getDobras().getCoxaMedial());
            avaliacaoFisica.setSupraIliaca(avaliacaoFisicaDTO.getDobras().getSupraIliaca());
            avaliacaoFisica.setBiceps(avaliacaoFisicaDTO.getDobras().getBiceps());
            avaliacaoFisica.setAxilarMedia(avaliacaoFisicaDTO.getDobras().getAxilarMedia());

            if(!UteisValidacao.emptyString(avaliacaoFisicaDTO.getDobras().getLogBalanca())){
                avaliacaoFisica.setLogBalanca(avaliacaoFisicaDTO.getDobras().getLogBalanca());
            }

            if (avaliacaoFisicaDTO.getDobras().getBioimpedancia() != null) {
                if (avaliacaoFisicaDTO.getDobras().getBioimpedancia().getImc() != null) {
                    avaliacaoFisica.setImc((Uteis.arredondarForcando2CasasDecimais(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getImc())));
                }
                if (avaliacaoFisicaDTO.getDobras().getBioimpedancia().getMassaGorda() != null) {
                    avaliacaoFisica.setMassaGorda((Uteis.arredondarForcando2CasasDecimais(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getMassaGorda())));
                }
                if (avaliacaoFisicaDTO.getDobras().getBioimpedancia().getPercentAgua() != null) {
                    avaliacaoFisica.setPercentualAgua((Uteis.arredondarForcando2CasasDecimais(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getPercentAgua())));
                }
                if (avaliacaoFisicaDTO.getDobras().getBioimpedancia().getIdadeMetabolica() != null) {
                    avaliacaoFisica.setIdadeMetabolica((Uteis.arredondarForcando2CasasDecimais(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getIdadeMetabolica())));
                }
                avaliacaoFisica.setPercentualMassaMagra(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getPercentMassaMagra());
                avaliacaoFisica.setGorduraIdeal(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getGorduraIdeal());
                avaliacaoFisica.setReatancia(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getReatancia());
                avaliacaoFisica.setNecessidadeFisica(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getNecFisica());
                avaliacaoFisica.setNecessidadeCalorica(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getNecCalorica());
                avaliacaoFisica.setGorduraVisceral(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getGorduraVisceral());
                if(avaliacaoFisicaDTO.getDobras().getBioimpedancia() != null &&
                        avaliacaoFisicaDTO.getDobras().getBioimpedancia().getPercentMassaGorda() != null){
                    avaliacaoFisica.setPercentualGordura(Uteis.arredondarForcando2CasasDecimais(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getPercentMassaGorda()));
                }
                avaliacaoFisica.setMassaMagra(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getMassaMagra());
                avaliacaoFisica.setPesoOsseo(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getOssos());
                avaliacaoFisica.setResistencia(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getResistencia());


                if (avaliacaoFisicaDTO.getDobras().getBioimpedancia().getTmb() != null && avaliacaoFisicaDTO.getDobras().getBioimpedancia().getTmb() > 0) {
                    avaliacaoFisica.setTmb(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getTmb());
                } else {
                    double peso = avaliacaoFisicaDTO.getPesoAltura().getPeso() != null ? avaliacaoFisicaDTO.getPesoAltura().getPeso() : 0.0;
                    double altura = avaliacaoFisicaDTO.getPesoAltura().getAltura() != null ? avaliacaoFisicaDTO.getPesoAltura().getAltura() * 100 : 0.0;
                    int idade = clienteSintetico.getIdade();
                    if (clienteSintetico.isSexoMasculino()) {
                        double tmb = (peso == 0 || altura == 0) ? 0 : 66 + (13.8 * peso) + (5 * altura) - (6.8 * idade);
                        avaliacaoFisica.setTmb(Uteis.arredondarForcando2CasasDecimais(tmb));
                    } else {
                        double tmb = (peso == 0 || altura == 0) ? 0 : 655 + (9.6 * peso) + (1.8 * altura) - (4.7 * idade);
                        avaliacaoFisica.setTmb(Uteis.arredondarForcando2CasasDecimais(tmb));
                    }
                }

                if (avaliacaoFisicaDTO.getDobras().getBioimpedancia().getResiduos() != null) {
                    avaliacaoFisica.setResidual(Uteis.arredondarForcando2CasasDecimais(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getResiduos()));
                } else {
                    double peso = avaliacaoFisicaDTO.getPesoAltura().getPeso() != null ? avaliacaoFisicaDTO.getPesoAltura().getPeso() : 0.0;
                    if (clienteSintetico.isSexoMasculino()) {
                        avaliacaoFisica.setResidual(Uteis.arredondarForcando2CasasDecimais(peso * 0.24));
                    } else {
                        avaliacaoFisica.setResidual(Uteis.arredondarForcando2CasasDecimais(peso * 0.21));
                    }
                }
                if (avaliacaoFisicaDTO.getDobras().getBioimpedancia().getMusculos() != null) {
                    avaliacaoFisica.setPesoMuscular(avaliacaoFisicaDTO.getDobras().getBioimpedancia().getMusculos());
                } else {
                    double peso = (avaliacaoFisicaDTO.getPesoAltura() != null && avaliacaoFisicaDTO.getPesoAltura().getPeso() != null)
                            ? avaliacaoFisicaDTO.getPesoAltura().getPeso() : 0.0;
                    double residual = (avaliacaoFisica.getResidual() != null) ? avaliacaoFisica.getResidual() : 0.0;
                    double pesoOsseo = (avaliacaoFisica.getPesoOsseo() != null) ? avaliacaoFisica.getPesoOsseo() : 0.0;
                    double percentualGordura = (avaliacaoFisica.getPercentualGordura() != null) ? avaliacaoFisica.getPercentualGordura() / 100.0 : 0.0;
                    avaliacaoFisica.setPesoMuscular(peso - residual - pesoOsseo - (peso * percentualGordura));
                }
            }
        }

        if (!UteisValidacao.emptyString(avaliacaoFisica.getAssinatura())) {
            try {
                salvarAssinatura(ctx, avaliacaoFisica);
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        }

        PesoOsseo pesoOsseo = obterPesoOsseo(ctx, avaliacaoFisica, clienteSintetico);
        calcularAvaliacao(pesoOsseo, avaliacaoFisica, avaliacaoFisicaDTO.getTemImportacaoBiosanny());
        try {
            if(avaliacaoFisica.getProtocolo().equals(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA)
                && !UteisValidacao.emptyString(avaliacaoFisica.getLogBalanca())
                && !UteisValidacao.emptyNumber(avaliacaoFisica.getPeso())
                && !UteisValidacao.emptyNumber(avaliacaoFisica.getResidual())
                && !UteisValidacao.emptyNumber(avaliacaoFisica.getPesoOsseo())
                && !UteisValidacao.emptyNumber(avaliacaoFisica.getPeso())
            ){
                avaliacaoFisica.setPesoMuscular(avaliacaoFisica.getPeso() - avaliacaoFisica.getResidual() - avaliacaoFisica.getPesoOsseo() - (avaliacaoFisica.getPeso() * (avaliacaoFisica.getPercentualGordura() / 100)));
            }
        } catch (Exception e){

        }
        if (avaliacaoFisica.getCodigo() == null) {
            avaliacaoFisica.setCodigo(null);
            avaliacaoFisica.setResponsavelLancamento_codigo(usuario.getCodigo());
            AvaliacaoFisica avaliacaoFisicaRetorno = inserir(ctx, avaliacaoFisica);
            // Setar os valores transients novamente
            avaliacaoFisicaRetorno.setDiametroTornozelo(avaliacaoFisica.getDiametroTornozelo());
            avaliacaoFisicaRetorno.setDiametroCotovelo(avaliacaoFisica.getDiametroCotovelo());
            avaliacaoFisicaRetorno.setDiametroJoelho(avaliacaoFisica.getDiametroJoelho());
            avaliacaoFisicaRetorno.setDiametroPunho(avaliacaoFisica.getDiametroPunho());
            avaliacaoFisica = avaliacaoFisicaRetorno;
        } else {
            AvaliacaoFisica avaliacaoFisicaRetorno = alterar(ctx, avaliacaoFisica);
            // Setar os valores transients novamente
            avaliacaoFisicaRetorno.setDiametroTornozelo(avaliacaoFisica.getDiametroTornozelo());
            avaliacaoFisicaRetorno.setDiametroCotovelo(avaliacaoFisica.getDiametroCotovelo());
            avaliacaoFisicaRetorno.setDiametroJoelho(avaliacaoFisica.getDiametroJoelho());
            avaliacaoFisicaRetorno.setDiametroPunho(avaliacaoFisica.getDiametroPunho());
            avaliacaoFisica = avaliacaoFisicaRetorno;
        }

        try {
            IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            if (!SuperControle.independente(ctx)) {
                integracaoWS.gravarUtilizacaoAvaliacaoFisica(ctx, clienteSintetico.getCodigoCliente(), avaliacaoFisica.getCodigo(), avaliacaoFisica.getDataAvaliacao());
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }

        if (avaliacaoFisica.getFcMaxima() != null
                && clienteSintetico.getFc() != null
                && !UteisValidacao.emptyString(clienteSintetico.getFc().getResult())) {
            clienteSintetico.setFcMaxima(avaliacaoFisica.getFcMaxima().intValue());
            clienteSintetico.setFcRepouso(Integer.valueOf(clienteSintetico.getFc().getResult()));
            try {
                calcularTabelFrequenciaCardiaca(avaliacaoFisica, clienteSintetico, Integer.valueOf(clienteSintetico.getFc().getResult()));
                clienteSinteticoService.atualizarDadosFC(ctx, avaliacaoFisica.getFcMaxima().intValue(), Integer.valueOf(clienteSintetico.getFc().getResult()), clienteSintetico.getCodigo());
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        }

        return avaliacaoFisica;
    }

    private void validarProdutoAvaliacao(String ctx, ClienteSintetico clienteSintetico) throws ServiceException {
        ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema cfgValidarProduto = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_PRODUTO_AVALIACAO);
        if (cfgValidarProduto.getValorAsBoolean()) {
            ConfiguracaoSistema cfgProduto = css.consultarPorTipo(ctx, ConfiguracoesEnum.PRODUTO_AVALIACAO);
            IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            if (cfgProduto.getValor().matches("[0-9]*")) {
                Integer idProduto = Integer.parseInt(cfgProduto.getValor());
                try {
                    String produtoVigente = integracaoWS.verificarAlunoTemProdutoVigenteRetornandoQuantidade(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, clienteSintetico.getCodigoCliente(), idProduto);
                    JSONArray produtos = new JSONArray(produtoVigente);
                    if (produtos.length() == 0) {
                        throw new ServiceException("aluno.nao.produto.vigente.avaliacao");
                    } else {
                        Integer produtoLancado = null;
                        for (int i = 0; i < produtos.length(); i++) {
                            List<AvaliacaoFisica> avLancada = UtilContext.getBean(AvaliacaoFisicaService.class).obterAvaliacaoMovProduto(ctx, produtos.getJSONObject(i).getInt("produtoId"));
                            if (UteisValidacao.emptyList(avLancada) || produtos.getJSONObject(i).getInt("quantidade") > avLancada.size()) {
                                produtoLancado = produtos.getJSONObject(i).getInt("produtoId");
                            }
                        }
                        if (produtoLancado == null) {
                            throw new ServiceException("produto.vigente.usado");
                        }
                    }
                } catch (Exception e) {
                    throw new ServiceException(e);
                }
            }
        }
    }

    /**
     * @param ctx                chave do banco correspondente
     * @param avaliacaoFisicaDTO objeto do serviço.
     * @param clienteSintetico   cleint
     * @param avaliacaoFisica    cliente cujo a avaliação física está sendo inserida ou alterada.
     * @param usuario            usuário logado na sessão.
     */
    private void gravarAnamneseResposta(final String ctx, AvaliacaoFisicaDTOUpdate avaliacaoFisicaDTO, ClienteSintetico clienteSintetico, AvaliacaoFisica avaliacaoFisica, Usuario usuario, Boolean editando) throws ServiceException {
        // salva anamnese pergunta resposta
        Anamnese anamnese = anamneseService.getAnamneseForId(ctx, avaliacaoFisicaDTO.getAnamneseSelecionadaId());
        if(anamnese == null){
            return;
        }
        Anamnese anamneseAntesAlteracao = UtilReflection.copy(anamnese);
        anamneseAntesAlteracao.setPerguntas(povoarRespostasAnamneseAntesAlteracao(ctx, avaliacaoFisica, anamnese));

        Collection<PerguntaAnamnese> perguntaAnamneseList = new ArrayList<PerguntaAnamnese>();
        for (AnamnesePerguntaRespostaDTOUpdate objAnamnese : avaliacaoFisicaDTO.getAnamneseRespostas()) {
            ((ArrayList<PerguntaAnamnese>) perguntaAnamneseList).add(preencherListaPerguntaResposta(ctx, objAnamnese, anamnese));
        }
        anamnese.setPerguntas((List<PerguntaAnamnese>) perguntaAnamneseList);

        if (editando) {
            try {
                anamneseService.alterarAnamnese(ctx, usuario, clienteSintetico, anamnese, false, avaliacaoFisica);
                incluirLog(ctx, avaliacaoFisica.getCodigo().toString(), anamnese.getCodigo().toString(), anamneseAntesAlteracao.getDescricaoParaLog(anamnese), anamnese.getDescricaoParaLog(anamneseAntesAlteracao),
                        "ALTERAÇÃO", "ALTERAÇÃO ANAMNESE", EntidadeLogEnum.ANAMNESE, "Anamnese", sessaoService, logDao, null, null);
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        } else {
            ItemAvaliacaoFisica itemAnamnese = adicionarItem(ctx, anamnese.getDescricao(), clienteSintetico,
                    usuario, ItemAvaliacaoFisicaEnum.ANAMNESE, anamnese, null, avaliacaoFisica);
            anamneseService.gravarAnamneseCliente(ctx, anamnese, clienteSintetico, itemAnamnese);
            incluirLog(ctx, avaliacaoFisica.getCodigo().toString(), anamnese.getCodigo().toString(), "", anamnese.getDescricaoParaLog(null),
                    "INCLUSÃO", "INCLUSÃO ANAMNESE", EntidadeLogEnum.ANAMNESE, "Anamnese", sessaoService, logDao, null, null);
        }
    }

    private List<PerguntaAnamnese> povoarRespostasAnamneseAntesAlteracao(String ctx, AvaliacaoFisica avaliacaoFisica, Anamnese anamnese) {
        List<PerguntaAnamnese> pergsRet = new ArrayList<>();
        ItemAvaliacaoFisica item = null;
        try {
            List<ItemAvaliacaoFisica> itemAnamnse = obterItensAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, false, avaliacaoFisica);
            if (itemAnamnse != null && !itemAnamnse.isEmpty()) {
                item = itemAnamnse.get(0);
            }
            if (item != null) {
                List<PerguntaAnamnese> pergs = anamneseService.obterPerguntasAnamnese(ctx, anamnese.getCodigo());
                for (PerguntaAnamnese pa : pergs) {
                    PerguntaAnamnese pp = UtilReflection.copy(pa);
                    if (pp.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                            || pp.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                        pp.getPergunta().setOpcoes(anamneseService.obterOpcoes(ctx, pp.getPergunta().getCodigo()));
                    }
                    RespostaCliente respostaCliente = anamneseService.obterRespostaPerguntasAnamnese(
                            ctx, pp.getCodigo(), item.getCliente().getCodigo(), item.getCodigo());
                    pp.setResposta(respostaCliente.getResposta());
                    pp.setComplemento(respostaCliente.getObs());
                    pergsRet.add(pp);
                }
                return pergsRet;
            } else {
                return pergsRet;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return pergsRet;
        }
    }

    /**
     * @param ctx              chave do banco correspondente.
     * @param clienteSintetico objeto do serviço.
     * @param clienteSintetico cliente cujo a avaliação física está sendo inserida ou alterada.
     * @param usuario          usuário logado na sessão.
     */
    private void gravarResistencia(final String ctx, AvaliacaoFisicaDTOUpdate avaliacaoFisicaDTO, ClienteSintetico clienteSintetico, AvaliacaoFisica avaliacaoFisica, Usuario usuario, Boolean editando) throws ServiceException {
        try {
            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema rmlOpcoes = css.consultarPorTipo(ctx, ConfiguracoesEnum.CFG_RML_OPCOES); // na data desta alteração estava sendo utilizado para diferenciar homem e mulher

            clienteSintetico.setFlexaoBraco(avaliacaoFisicaDTO.getRml().getFlexoesBracos());
            clienteSintetico.setFlexaoAbdomen(avaliacaoFisicaDTO.getRml().getAbdominais());
            if(editando) {
                clienteSinteticoService.alterar(ctx, clienteSintetico);
            }
            Integer flexaoAbdomen = clienteSintetico.getFlexaoAbdomen() == null ? 0 : clienteSintetico.getFlexaoAbdomen();
            Integer flexaoBraco = clienteSintetico.getFlexaoBraco() == null ? 0 : clienteSintetico.getFlexaoBraco();
            ResultadoResistenciaEnum resultAbdomen = calcularResistencia(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN, clienteSintetico.getIdade(), flexaoAbdomen);
            ResultadoResistenciaEnum resultBraco = calcularResistencia(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO, clienteSintetico.getIdade(), flexaoBraco);

            if (rmlOpcoes != null && rmlOpcoes.getValorAsBoolean()) {
                if (SexoEnum.M.getSexo().equals(clienteSintetico.getSexo())) {
                    resultAbdomen = calcularResistenciaHomem(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM, clienteSintetico.getIdade(), flexaoAbdomen, SexoEnum.M);
                    resultBraco = calcularResistenciaHomem(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_HOMEM, clienteSintetico.getIdade(), flexaoBraco, SexoEnum.M);
                }
                if (SexoEnum.F.getSexo().equals(clienteSintetico.getSexo())) {
                    resultAbdomen = calcularResistenciaMulher(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER, clienteSintetico.getIdade(), flexaoAbdomen, SexoEnum.F);
                    resultBraco = calcularResistenciaMulher(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_MULHER, clienteSintetico.getIdade(), flexaoBraco, SexoEnum.F);
                }
            }

            if (editando) {
                ItemAvaliacaoFisica itemAbdomen = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN, null, avaliacaoFisica);
                ItemAvaliacaoFisica itemBraco = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO, null, avaliacaoFisica);

                if (rmlOpcoes != null && rmlOpcoes.getValorAsBoolean()) {
                    if (SexoEnum.M.getSexo().equals(clienteSintetico.getSexo())) {
                        itemAbdomen = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM, null, avaliacaoFisica);
                        itemBraco = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_HOMEM, null, avaliacaoFisica);
                    }
                    if (SexoEnum.F.getSexo().equals(clienteSintetico.getSexo())) {
                        itemAbdomen = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER, null, avaliacaoFisica);
                        itemBraco = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_MULHER, null, avaliacaoFisica);
                    }
                }

                if (itemAbdomen == null || itemBraco == null) {
                    gravarResistencia(ctx, avaliacaoFisicaDTO, clienteSintetico, avaliacaoFisica, usuario, false);
                } else {

                    itemAbdomen.setResult(flexaoAbdomen + ";" + resultAbdomen.name());
                    alterarItem(ctx, itemAbdomen);
                    itemBraco.setResult(flexaoBraco + ";" + resultBraco.name());
                    alterarItem(ctx, itemBraco);
                }

            } else {
                ItemAvaliacaoFisicaEnum itemAbdomenEnum = ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN;
                ItemAvaliacaoFisicaEnum itemBracoEnum = ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO;

                if (rmlOpcoes != null && rmlOpcoes.getValorAsBoolean()) {
                    if (SexoEnum.M.getSexo().equals(clienteSintetico.getSexo())) {
                        itemAbdomenEnum = ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM;
                        itemBracoEnum = ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_HOMEM;
                    }
                    if (SexoEnum.F.getSexo().equals(clienteSintetico.getSexo())) {
                        itemAbdomenEnum = ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER;
                        itemBracoEnum = ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_MULHER;
                    }
                }

                if (resultAbdomen != null) {
                    adicionarItem(ctx, flexaoAbdomen + ";" + resultAbdomen.name(), clienteSintetico, usuario, itemAbdomenEnum, null, null, avaliacaoFisica);
                }
                if (resultBraco != null) {
                    adicionarItem(ctx, flexaoBraco + ";" + resultBraco.name(), clienteSintetico, usuario, itemBracoEnum, null, null, avaliacaoFisica);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }

    }

    /**
     * @param ctx                chave do banco correspondente.
     * @param clienteSintetico   cliente cujo a avaliação física está sendo inserida ou alterada.
     * @param avaliacaoFisicaDTO objeto do serviço.
     * @param usuario            usuário logado na sessão.
     */
    private void gravarAnamneseParq(final String ctx, ClienteSintetico clienteSintetico, AvaliacaoFisicaDTOUpdate avaliacaoFisicaDTO, Usuario usuario, AvaliacaoFisica avaliacaoFisica, Boolean editando) throws ServiceException {
        Anamnese questionarioParq = anamneseService.consultarParq(ctx, usuario.getCodigo(), getViewUtils());
        Collection<PerguntaAnamnese> perguntaAnamneseListParq = new ArrayList<PerguntaAnamnese>();
        for (AnamnesePerguntaRespostaDTOUpdate objAnamnese : avaliacaoFisicaDTO.getParQRespostas()) {
            ((ArrayList<PerguntaAnamnese>) perguntaAnamneseListParq).add(preencherListaPerguntaResposta(ctx, objAnamnese, questionarioParq));
        }
        questionarioParq.setPerguntas((List<PerguntaAnamnese>) perguntaAnamneseListParq);
        if (editando) {
            try {
                anamneseService.alterarAnamnese(ctx, usuario, clienteSintetico, questionarioParq, true, avaliacaoFisica);
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        } else {
            ItemAvaliacaoFisica itemAnamneseParq = this.adicionarItem(ctx, questionarioParq.getDescricao(), clienteSintetico,
                    usuario, ItemAvaliacaoFisicaEnum.ANAMNESE, questionarioParq, null, avaliacaoFisica);

            anamneseService.gravarAnamneseCliente(ctx, questionarioParq, clienteSintetico, itemAnamneseParq);
        }
    }

    private void gravarObjetivo(final String ctx, AvaliacaoFisicaDTOUpdate avaliacaoFisicaDTO, Usuario usuario, ClienteSintetico clienteSintetico, AvaliacaoFisica avaliacaoFisica, Boolean editando) throws ServiceException {
        // salva objetivos
        String os = "";
        for (String o : avaliacaoFisicaDTO.getObjetivos()) {
            os += "|#|".concat(o);
        }

        if (editando) {
            ItemAvaliacaoFisica itemObjetivos = obterItemAvaliacaoFisica(ctx,
                    clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.OBJETIVOS, null, avaliacaoFisica);
            if (itemObjetivos == null) {
                gravarObjetivo(ctx, avaliacaoFisicaDTO, usuario, clienteSintetico, avaliacaoFisica, false);
            } else {
                ItemAvaliacaoFisica itemObjetivosAntesAlteracao = UtilReflection.copy(itemObjetivos);
                itemObjetivos.setResult(os.replaceFirst("\\|\\#\\|", ""));
                alterarItem(ctx, itemObjetivosAntesAlteracao, itemObjetivos, avaliacaoFisica);
            }
        } else {
            this.adicionarItem(ctx, os.replaceFirst("\\|\\#\\|", ""), clienteSintetico,
                    usuario, ItemAvaliacaoFisicaEnum.OBJETIVOS, null, null, avaliacaoFisica);
        }

    }

    public PerguntaAnamnese preencherListaPerguntaResposta(String ctx, AnamnesePerguntaRespostaDTOUpdate objAnamnese, Anamnese anamnese) throws ServiceException {
        Pergunta pergunta = null;
        try {
            List<String> respostas = new ArrayList<>();
            PerguntaAnamnese perguntaAnamnese = null;
            if(objAnamnese != null && !UteisValidacao.emptyNumber(objAnamnese.getAnamnesePerguntaId())) {
                perguntaAnamnese = perguntaAnamneseDao.findById(ctx, objAnamnese.getAnamnesePerguntaId());
            }
            if(perguntaAnamnese != null && perguntaAnamnese.getPergunta() != null) {
                pergunta = perguntaDao.findById(ctx, perguntaAnamnese.getPergunta().getCodigo());
                if (pergunta.getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA) && objAnamnese.getResposta() != null && !objAnamnese.getResposta().equals("")) {
                    String[] resp1 = objAnamnese.getResposta().split(",");
                    for (String resp : resp1) {
                        respostas.add(resp);
                    }
                    perguntaAnamnese.setRespostas(respostas);
                } else {
                    perguntaAnamnese.setResposta(objAnamnese.getResposta());
                }
                perguntaAnamnese.setComplemento(objAnamnese.getObservacao());
                perguntaAnamnese.setPergunta(pergunta);
                perguntaAnamnese.setAnamnese(anamnese);
                perguntaAnamnese.setCodigo(objAnamnese.getAnamnesePerguntaId());
            }
            return perguntaAnamnese;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void gravarPesoOsseo(final String ctx, Usuario usuario, ClienteSintetico clienteSintetico, AvaliacaoFisica avaliacaoFisica) throws ServiceException {
        PesoOsseo pesoOsseo;
        try {
            pesoOsseo = obterPesoOsseo(ctx, clienteSintetico.getCodigo(), avaliacaoFisica);
            if (pesoOsseo == null || pesoOsseo.getCodigo() == null || pesoOsseo.getCodigo() == 0) {
                if (avaliacaoFisica != null) {
                    pesoOsseo = gerarPesoOsseo(avaliacaoFisica, clienteSintetico);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }

        pesoOsseo.setResponsavelLancamento_codigo(usuario.getCodigo());
        pesoOsseo.setDataLancamento(Calendario.hoje());
        pesoOsseo.setCliente(clienteSintetico);
        pesoOsseo.setAvaliacaoFisica(avaliacaoFisica);
        if (avaliacaoFisica.getProtocolo().equals(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA)) {
            pesoOsseo.setPesoOsseo(avaliacaoFisica.getPesoOsseo());
        }
        pesoOsseo.setDiametroPunho(avaliacaoFisica.getDiametroPunho());
        pesoOsseo.setDiametroFemur(avaliacaoFisica.getDiametroJoelho());
        pesoOsseo.setDiametroCotovelo(avaliacaoFisica.getDiametroCotovelo());
        pesoOsseo.setDiametroTornozelo(avaliacaoFisica.getDiametroTornozelo());

        pesoOsseo.setAltura(avaliacaoFisica.getAltura());
        pesoOsseo.setPeso(avaliacaoFisica.getPeso());
        pesoOsseo.setPercentualGordura(avaliacaoFisica.getPercentualGordura());
        pesoOsseo = calcularPesoOsseo(pesoOsseo, clienteSintetico.isSexoMasculino());
        if (avaliacaoFisica.getProtocolo().equals(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA)) {
            pesoOsseo.setPesoOsseo(avaliacaoFisica.getPesoOsseo());
        }

        inserirPesoOsseo(ctx, pesoOsseo);
    }

    public PesoOsseo obterPesoOsseo(final String chave, AvaliacaoFisica avaliacaoFisica, ClienteSintetico clienteSintetico) throws ServiceException {
        try {
            PesoOsseo pesoOsseo = new PesoOsseo();
            if(avaliacaoFisica.getCodigo() != null) {
                pesoOsseo = obterPesoOsseo(chave, clienteSintetico.getCodigo(), avaliacaoFisica);
                if (pesoOsseo == null || pesoOsseo.getCodigo() == null || pesoOsseo.getCodigo() == 0) {
                    if (avaliacaoFisica != null) {
                        pesoOsseo = gerarPesoOsseo(avaliacaoFisica, clienteSintetico);
                        pesoOsseoDao.insert(chave, pesoOsseo);
                    }
                }
            }
            return pesoOsseo;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void gravarFlexibilidade(final String ctx, AvaliacaoFisica avaliacaoFisica, AvaliacaoFisicaDTOUpdate dto) throws ServiceException {
        Flexibilidade flexibilidade;
        try {
            flexibilidade = obterFlexibilidade(ctx, avaliacaoFisica.getCodigo());
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        if (flexibilidade == null) {
            flexibilidade = new Flexibilidade();
        }

        AvaliacaoFlexibilidadeDTOUpdate flexibilidadeDTOUpdate = dto.getFlexibilidade();
        if (flexibilidadeDTOUpdate != null) {
            flexibilidade.setAlcance(flexibilidadeDTOUpdate.getAlcanceMaximo());
            flexibilidade.setObservacao(flexibilidadeDTOUpdate.getObservacao());
            flexibilidade.setAvaliacao(avaliacaoFisica);
            flexibilidade = calcularFlexibilidade(flexibilidade);
            if (!UteisValidacao.emptyString(flexibilidadeDTOUpdate.getMobilidadeOmbroEsquerdo())) {
                flexibilidade.setMobilidadeOmbroEsquerdo(ClassificacaoEnum.valueOf(flexibilidadeDTOUpdate.getMobilidadeOmbroEsquerdo()));
            }
            if (!UteisValidacao.emptyString(flexibilidadeDTOUpdate.getMobilidadeOmbroDireito())) {
                flexibilidade.setMobilidadeOmbroDireito(ClassificacaoEnum.valueOf(flexibilidadeDTOUpdate.getMobilidadeOmbroDireito()));
            }
            if (!UteisValidacao.emptyString(flexibilidadeDTOUpdate.getMobilidadeQuadrilEsquerdo())) {
                flexibilidade.setMobilidadeQuadrilEsquerdo(ClassificacaoEnum.valueOf(flexibilidadeDTOUpdate.getMobilidadeQuadrilEsquerdo()));
            }
            if (!UteisValidacao.emptyString(flexibilidadeDTOUpdate.getMobilidadeQuadrilDireito())) {
                flexibilidade.setMobilidadeQuadrilDireito(ClassificacaoEnum.valueOf(flexibilidadeDTOUpdate.getMobilidadeQuadrilDireito()));
            }
            if (!UteisValidacao.emptyString(flexibilidadeDTOUpdate.getMobilidadeJoelhoEsquerdo())) {
                flexibilidade.setMobilidadeJoelhoEsquerdo(ClassificacaoEnum.valueOf(flexibilidadeDTOUpdate.getMobilidadeJoelhoEsquerdo()));
            }
            if (!UteisValidacao.emptyString(flexibilidadeDTOUpdate.getMobilidadeJoelhoDireito())) {
                flexibilidade.setMobilidadeJoelhoDireito(ClassificacaoEnum.valueOf(flexibilidadeDTOUpdate.getMobilidadeJoelhoDireito()));
            }
            if (flexibilidadeDTOUpdate.getMobilidadeTornozeloEsquerdo() != null) {
                flexibilidade.setMobilidadeTornozeloEsquerdo(flexibilidadeDTOUpdate.getMobilidadeTornozeloEsquerdo());
            }
            if (flexibilidadeDTOUpdate.getMobilidadeTornozeloDireito() != null) {
                flexibilidade.setMobilidadeTornozeloDireito(flexibilidadeDTOUpdate.getMobilidadeTornozeloDireito());
            }
            if (!UteisValidacao.emptyString(flexibilidadeDTOUpdate.getObservacaoOmbro())) {
                flexibilidade.setObservacaoOmbro(flexibilidadeDTOUpdate.getObservacaoOmbro());
            }
            if (!UteisValidacao.emptyString(flexibilidadeDTOUpdate.getObservacaoQuadril())) {
                flexibilidade.setObservacaoQuadril(flexibilidadeDTOUpdate.getObservacaoQuadril());
            }
            if (!UteisValidacao.emptyString(flexibilidadeDTOUpdate.getObservacaoJoelho())) {
                flexibilidade.setObservacaoJoelho(flexibilidadeDTOUpdate.getObservacaoJoelho());
            }
            if (!UteisValidacao.emptyString(flexibilidadeDTOUpdate.getObservacaoTornozelo())) {
                flexibilidade.setObservacaoTornozelo(flexibilidadeDTOUpdate.getObservacaoTornozelo());
            }
            try {
                salvarFlexibilidade(ctx, flexibilidade);
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        }
    }

    public Flexibilidade calcularFlexibilidade(Flexibilidade flexibilidade) {
        if (flexibilidade.getAlcance() == null || flexibilidade.getAlcance() < 24) {
            flexibilidade.setClassificacao(ClassificacaoEnum.FRACA);
        } else if (flexibilidade.getAlcance() >= 24 && flexibilidade.getAlcance() <= 28) {
            flexibilidade.setClassificacao(ClassificacaoEnum.REGULAR);
        } else if (flexibilidade.getAlcance() >= 29 && flexibilidade.getAlcance() <= 33) {
            flexibilidade.setClassificacao(ClassificacaoEnum.MEDIA);
        } else if (flexibilidade.getAlcance() >= 34 && flexibilidade.getAlcance() <= 38) {
            flexibilidade.setClassificacao(ClassificacaoEnum.BOA);
        } else if (flexibilidade.getAlcance() > 38) {
            flexibilidade.setClassificacao(ClassificacaoEnum.EXCELENTE);
        }
        return flexibilidade;
    }

    public void gravarFCPressao(final String ctx, ClienteSintetico clienteSintetico, AvaliacaoFisica avaliacaoFisica, Usuario usuario, boolean editando) {
        try {
            if (clienteSintetico.getFc() != null
                    && clienteSintetico.getFc().getResult() != null
                    && !clienteSintetico.getFc().getResult().isEmpty()) {
            }

            if (editando) {
                ItemAvaliacaoFisica itemPressao = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL, null, avaliacaoFisica);
                ItemAvaliacaoFisica itemPressaoSistolica = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL_SISTOLICA, null, avaliacaoFisica);
                ItemAvaliacaoFisica itemPressaoDiastolica = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL_DIASTOLICA, null, avaliacaoFisica);
                ItemAvaliacaoFisica itemFC = obterItemAvaliacaoFisica(ctx, clienteSintetico.getCodigo(), ItemAvaliacaoFisicaEnum.FREQUENCIA_CARDIACA, null, avaliacaoFisica);
                if (itemFC == null || itemPressao == null || itemPressaoSistolica == null || itemPressaoDiastolica == null) {
                    gravarFCPressao(ctx, clienteSintetico, avaliacaoFisica, usuario, false);
                } else {
                    itemPressao.setResult(clienteSintetico.getPressao().getResult());
                    itemPressaoSistolica.setResult(clienteSintetico.getPressaoSistolica().getResult());
                    itemPressaoDiastolica.setResult(clienteSintetico.getPressaoDiastolica().getResult());
                    itemFC.setResult(clienteSintetico.getFc().getResult());
                    alterarItem(ctx, itemFC);
                    alterarItem(ctx, itemPressao);
                    alterarItem(ctx, itemPressaoSistolica);
                    alterarItem(ctx, itemPressaoDiastolica);
                }
            } else {
                ItemAvaliacaoFisica pressao = new ItemAvaliacaoFisica(ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL,
                        clienteSintetico.getPressao().getResult(), Calendario.hoje(), clienteSintetico, usuario.getCodigo(), avaliacaoFisica);
                gravarItem(ctx, pressao);

                ItemAvaliacaoFisica pressaoSistolica = new ItemAvaliacaoFisica(ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL_SISTOLICA,
                        clienteSintetico.getPressaoSistolica().getResult(), Calendario.hoje(), clienteSintetico, usuario.getCodigo(), avaliacaoFisica);
                gravarItem(ctx, pressaoSistolica);

                ItemAvaliacaoFisica pressaoDiastolica = new ItemAvaliacaoFisica(ItemAvaliacaoFisicaEnum.PRESSAO_ARTERIAL_DIASTOLICA,
                        clienteSintetico.getPressaoDiastolica().getResult(), Calendario.hoje(), clienteSintetico, usuario.getCodigo(), avaliacaoFisica);
                gravarItem(ctx, pressaoDiastolica);

                ItemAvaliacaoFisica fc = new ItemAvaliacaoFisica(ItemAvaliacaoFisicaEnum.FREQUENCIA_CARDIACA,
                        clienteSintetico.getFc().getResult(), Calendario.hoje(), clienteSintetico, usuario.getCodigo(), avaliacaoFisica);
                gravarItem(ctx, fc);
            }
        } catch (ServiceException e) {
            e.printStackTrace();
        }
    }

    public void gravarAvaliacaoPostural(String ctx, AvaliacaoFisicaDTOUpdate dto, AvaliacaoFisica avaliacaoFisica, final Boolean isNew) throws ServiceException {
        AvaliacaoPostural avaliacaoPostural = new AvaliacaoPostural();
        List<ItemAvaliacaoPostural> itens = new ArrayList<ItemAvaliacaoPostural>();

        if (dto.getPostura() != null) {
            List<ItemAvaliacaoPostural> itemsAvaliacaoPosturalLateral = new ArrayList<ItemAvaliacaoPostural>();

            if (dto.getPostura().getVisaoLateral() != null) {
                AnamnesePosturaLateralDTO posturaLateralDTO = dto.getPostura().getVisaoLateral();
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.ANTERVERSAO_QUADRIL, posturaLateralDTO.getAnterversaoQuadril()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.HIPERCIFOSE_TORACICA, posturaLateralDTO.getHipercifoseToracica()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.HIPERLORDOSE_CERVICAL, posturaLateralDTO.getHiperlordoseCervical()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.HIPERLORDOSE_LOMBAR, posturaLateralDTO.getHiperlordoseLombar()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.GENU_FLEXO, posturaLateralDTO.getJoelhoFlexo()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.GENU_RECURVADO, posturaLateralDTO.getJoelhoRecurvado()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.PROTUSAO_ABDOMINAL, posturaLateralDTO.getProtusaoAbdominal()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.PE_CALCANEO, posturaLateralDTO.getPeCalcaneo()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.PE_CAVO, posturaLateralDTO.getPeCavo()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.PE_EQUINO, posturaLateralDTO.getPeEquino()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.PE_PLANO, posturaLateralDTO.getPePlano()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.RETIFICACAO_CERVICAL, posturaLateralDTO.getRetificacaoCervical()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.RETIFICACAO_LOMBAR, posturaLateralDTO.getRetificacaoLombar()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.RETROVERSAO_QUADRIL, posturaLateralDTO.getRetroversaoQuadril()));
                itemsAvaliacaoPosturalLateral.add(new ItemAvaliacaoPostural(ItemPosturaEnum.ROTACAO_INTERNA_OMBROS, posturaLateralDTO.getRotacaoInternaOmbros()));
            }

            List<ItemAvaliacaoPostural> itemsAvaliacaoPosturalPosterior = new ArrayList<ItemAvaliacaoPostural>();
            if (dto.getPostura().getVisaoPosterior() != null) {
                AnamnesePosturalPosteriorDTO visaoPosterior = dto.getPostura().getVisaoPosterior();
                itemsAvaliacaoPosturalPosterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.DEPRESSAO_ESCAPULAR, visaoPosterior.getDepressaoEscapular()));
                itemsAvaliacaoPosturalPosterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.ENCURTAMENTO_TRAPEZIO, visaoPosterior.getEncurtamentoTrapezio()));
                itemsAvaliacaoPosturalPosterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.ESCOLIOSE_CERVICAL, visaoPosterior.getEscolioseCervical()));
                itemsAvaliacaoPosturalPosterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.ESCOLIOSE_LOMBAR, visaoPosterior.getEscolioseLombar()));
                itemsAvaliacaoPosturalPosterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.ESCOLIOSE_TORACICA, visaoPosterior.getEscolioseToracica()));
                itemsAvaliacaoPosturalPosterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.PROTACAO_ESCPULAR, visaoPosterior.getProtacaoEscapular()));
                itemsAvaliacaoPosturalPosterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.PE_VAGO, visaoPosterior.getPeValgo()));
                itemsAvaliacaoPosturalPosterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.PE_VARO, visaoPosterior.getPeVaro()));
                itemsAvaliacaoPosturalPosterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.RETRACAO_ESCAPULAR, visaoPosterior.getRetracaoEscapular()));
            }

            List<ItemAvaliacaoPostural> itemsAvaliacaoPosturalAnterior = new ArrayList<ItemAvaliacaoPostural>();
            if (dto.getPostura().getVisaoAnterior() != null) {
                AnamnesePosturalAnteriorDTO anterior = dto.getPostura().getVisaoAnterior();
                itemsAvaliacaoPosturalAnterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.GENU_VALGO, anterior.getJoelhoValgo()));
                itemsAvaliacaoPosturalAnterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.GENU_VARO, anterior.getJoelhoVaro()));
                itemsAvaliacaoPosturalAnterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.PE_ABDUTO, anterior.getPeAbduto()));
                itemsAvaliacaoPosturalAnterior.add(new ItemAvaliacaoPostural(ItemPosturaEnum.PE_ADUTO, anterior.getPeAduto()));
            }

            if (dto.getPostura().getAssimetrias() != null) {
                AnamnesePosturalAssimetriasDTO assimetrias = dto.getPostura().getAssimetrias();
                avaliacaoPostural.setOmbros(assimetrias.getOmbrosAssimetricos() == null ? AssimetriaEnum.NENHUMA_ELEVACAO : assimetrias.getOmbrosAssimetricos());
                avaliacaoPostural.setQuadril(assimetrias.getAssimetriaQuadril() == null ? AssimetriaEnum.NENHUMA_ELEVACAO : assimetrias.getAssimetriaQuadril());
            }

            itens.addAll(itemsAvaliacaoPosturalLateral);
            itens.addAll(itemsAvaliacaoPosturalPosterior);
            itens.addAll(itemsAvaliacaoPosturalAnterior);
            avaliacaoPostural.setObservacao(dto.getPostura().getObservacao());
            avaliacaoPostural.setAvaliacao(avaliacaoFisica);

            AvaliacaoPosturaDTOUpdate avaliacaoPosturaDTOUpdate = dto.getPostura();

            avaliacaoPostural.setKeyImgPosterior(avaliacaoPosturaDTOUpdate.getFrenteImageId());
            avaliacaoPostural.setKeyImgDireita(avaliacaoPosturaDTOUpdate.getDireitaImageId());
            avaliacaoPostural.setKeyImgEsquerda(avaliacaoPosturaDTOUpdate.getEsquerdaImageId());
            avaliacaoPostural.setKeyImgAnterior(avaliacaoPosturaDTOUpdate.getCostasImageId());
            try {
                if (avaliacaoPosturaDTOUpdate.getCostasImageUpload() != null && !avaliacaoPosturaDTOUpdate.getCostasImageUpload().equals("")) {
                    byte[] imgAnterior = Base64.decodeBase64(avaliacaoPosturaDTOUpdate.getCostasImageUpload());
                    if (imgAnterior != null) {
                        if (ResizeImage.isReduzirImagem(imgAnterior, 1000)) {
                            byte[] reduzirImgAnterior = ResizeImage.reduzirImagem(imgAnterior, 30);
                            byte[] comprimirImgAnterior = ResizeImage.comprimirImagem(reduzirImgAnterior, 0.3f);
                            avaliacaoPostural.setImgAnteriorByte(comprimirImgAnterior);
                        } else {
                            avaliacaoPostural.setImgAnteriorByte(imgAnterior);
                        }
                    }
                }
                if (avaliacaoPosturaDTOUpdate.getEsquerdaImageUpload() != null && !avaliacaoPosturaDTOUpdate.getEsquerdaImageUpload().equals("")) {
                    byte[] imgEsquerda = Base64.decodeBase64(avaliacaoPosturaDTOUpdate.getEsquerdaImageUpload());
                    if (imgEsquerda != null) {
                        if (ResizeImage.isReduzirImagem(imgEsquerda, 1000)) {
                            byte[] reduzirImgEsquerda = ResizeImage.reduzirImagem(imgEsquerda, 30);
                            byte[] comprimirImgEsquerda = ResizeImage.comprimirImagem(reduzirImgEsquerda, 0.3f);
                            avaliacaoPostural.setImgEsquerdaByte(comprimirImgEsquerda);
                        } else {
                            avaliacaoPostural.setImgEsquerdaByte(imgEsquerda);
                        }
                    }
                }
                if (avaliacaoPosturaDTOUpdate.getFrenteImageUpload() != null && !avaliacaoPosturaDTOUpdate.getFrenteImageUpload().equals("")) {
                    byte[] imgPosterior = Base64.decodeBase64(avaliacaoPosturaDTOUpdate.getFrenteImageUpload());
                    if (imgPosterior != null) {
                        if (ResizeImage.isReduzirImagem(imgPosterior, 1000)) {
                            byte[] reduzirImgPosterior = ResizeImage.reduzirImagem(imgPosterior, 30);
                            byte[] comprimirImgPosterior = ResizeImage.comprimirImagem(reduzirImgPosterior, 0.3f);
                            avaliacaoPostural.setImgPosteriorByte(comprimirImgPosterior);
                        } else {
                            avaliacaoPostural.setImgPosteriorByte(imgPosterior);
                        }
                    }
                }
                if (avaliacaoPosturaDTOUpdate.getDireitaImageUpload() != null && !avaliacaoPosturaDTOUpdate.getDireitaImageUpload().equals("")) {
                    byte[] imgDireita = Base64.decodeBase64(avaliacaoPosturaDTOUpdate.getDireitaImageUpload());
                    if (imgDireita != null) {
                        if (ResizeImage.isReduzirImagem(imgDireita, 1000)) {
                            byte[] reduzirImgDireita = ResizeImage.reduzirImagem(imgDireita, 30);
                            byte[] comprimirImgDireita = ResizeImage.comprimirImagem(reduzirImgDireita, 0.3f);
                            avaliacaoPostural.setImgDireitaByte(comprimirImgDireita);
                        } else {
                            avaliacaoPostural.setImgDireitaByte(imgDireita);
                        }
                    }
                }
                avaliacaoPostural.setAvaliacao(avaliacaoFisica);
                AvaliacaoPostural avaliacaoPosturalLast = consultarPostural(ctx, avaliacaoPostural.getAvaliacao());
                if (avaliacaoPosturalLast != null && !isNew) {
                    avaliacaoPostural.setCodigo(avaliacaoPosturalLast.getCodigo());
                }
                deleteItensPostural(ctx, avaliacaoPostural);
                gravarAvaliacaoPostural(ctx, avaliacaoPostural, itens);
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        }
    }

    public void gravarVentilometria(final String ctx, ClienteSintetico clienteSintetico, Usuario usuario, AvaliacaoVo2DTO vo2, AvaliacaoFisica avaliacaoFisica, Ventilometria ventilometria) throws ServiceException {
        try {
            if (ventilometria == null) {
                ventilometria = new Ventilometria();
                ventilometria.setVo2max(vo2.getVo2Max());
                ventilometria.setLimiarVentilatorio(vo2.getLimiarVentilatorioI());
                ventilometria.setLimiarVentilatorio2(vo2.getLimiarVentilatorioII());
                ventilometria.setCliente(clienteSintetico);
                adicionarItem(ctx, "", clienteSintetico, usuario, ItemAvaliacaoFisicaEnum.VENTILOMETRIA, null, ventilometria, avaliacaoFisica);
            } else {
                ventilometria.setVo2max(vo2.getVo2Max());
                ventilometria.setLimiarVentilatorio(vo2.getLimiarVentilatorioI());
                ventilometria.setLimiarVentilatorio2(vo2.getLimiarVentilatorioII());
                ventilometria.setCliente(clienteSintetico);
                alterarVentilometria(ctx, ventilometria);
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void calcularAvaliacao(PesoOsseo pesoOsseo, AvaliacaoFisica avaliacaoFisica, boolean temImportacaoBiosanny) throws ServiceException {
            pesoOsseo = pesoOsseo == null ? new PesoOsseo() : pesoOsseo;
            calcularAvalicaoFisica(avaliacaoFisica,
                    pesoOsseo,
                    avaliacaoFisica.getProtocolo(),
                    temImportacaoBiosanny);

    }

    @Override
    public void enviarEmailComparativoAvaliacao(String key, String pdf, ClienteSintetico cliente, String nomeEmpresa, HttpServletRequest request, ServletContext servletContext, final Usuario usuario) throws Exception {

        File arquivo = new File(pdf);
        //obter configurações do envio de email
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        JSONObject configsEmail = integracaoWS.configsEmail(url);
        UteisEmail u = new UteisEmail();

        u.addAnexo("AvaliacaoFisica_ZWTreino_"
                + cliente.getNome() + ".pdf", arquivo);

        u.preencherConfiguracaoEmailPadrao(configsEmail, nomeEmpresa);

        String a = conteudoEmailAvaliacao(cliente.getPrimeiroNome(), null, null, null, usuario, null);
        List<String> listaEmails = cliente.getListaEmails();
        String[] emails = new String[listaEmails.size()];
        emails = listaEmails.toArray(emails);
        for (String e : emails) {
            u.enviarEmail(e, cliente.getNome(), a, "", "Avaliação Física");
        }
    }

    @Override
    public String comporUrlPdf(final String ctx, final Integer codAvaliacao, final HttpServletRequest request, Boolean isNewTreino, String language) throws Exception {
        Usuario usuario = null;
        if (isNewTreino) {
            final UsuarioSimplesDTO usuarioTO = sessaoService.getUsuarioAtual();
            usuario = usuarioService.obterPorId(ctx, usuarioTO.getId());
        }

        AvaliacaoFisica avaliacaoFisica = obterPorId(ctx, codAvaliacao);
        Flexibilidade flexibilidade = obterFlexibilidade(ctx, avaliacaoFisica.getCodigo());
        PesoOsseo pesoOsseo = obterPesoOsseo(ctx, avaliacaoFisica.getCliente().getCodigo(), avaliacaoFisica);
        ItemAvaliacaoFisica ventItem = obterItemAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(),
                ItemAvaliacaoFisicaEnum.VENTILOMETRIA, null, avaliacaoFisica);
        Ventilometria ventilometria = null;
        if (ventItem != null && !UteisValidacao.emptyNumber(ventItem.getCodigo())) {
            ventilometria = ventItem.getVentilometria();
        }
        Anamnese anamnese = null;
        Anamnese questionarioParq = null;

        questionarioParq = anamneseService.consultarParq(ctx, 0, getViewUtils());
        questionarioParq.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo()));
        List<ItemAvaliacaoFisica> parqs = obterItensAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, true,
                avaliacaoFisica);
        if (!UteisValidacao.emptyList(parqs)) {
            obterRespostas(ctx, questionarioParq, parqs.get(0));
        }
        List<ItemAvaliacaoFisica> anamnesesAluno = obterItensAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(),
                ItemAvaliacaoFisicaEnum.ANAMNESE, 1, false, avaliacaoFisica);
        if (!UteisValidacao.emptyList(anamnesesAluno)) {
            anamnese = anamnesesAluno.get(0).getAnamnese();
        }
        if(anamnese != null){
            List<PerguntaAnamnese> perguntaAnamneseList = anamneseService.obterPerguntasAnamnese(ctx, anamnese.getCodigo());
            List <PerguntaAnamnese> lista = new ArrayList<>();
            if(!UteisValidacao.emptyList(perguntaAnamneseList)) {
                for (PerguntaAnamnese pa : perguntaAnamneseList) {
                    if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                            || pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                        pa.getPergunta().setOpcoes(anamneseService.obterOpcoes(ctx, pa.getPergunta().getCodigo()));
                    }
                    lista.add(new PerguntaAnamnese(pa));
                }
            }
            anamnese.setPerguntas(lista);
            obterRespostas(ctx, anamnese, anamnesesAluno.get(0));
        }

        return gerarPDFAvaliacaoFisica(ctx, avaliacaoFisica,
                flexibilidade,
                pesoOsseo, ventilometria,
                anamnese, questionarioParq,
                usuario, getViewUtils(),
                request, sc, false, true, language);
    }

    @Override
    public AvaliacaoFisicaDTO obterAvaliacaoFisica(final Integer id) throws ServiceException {
        final UsuarioSimplesDTO usuarioTO = sessaoService.getUsuarioAtual();
        final String ctx = usuarioTO.getChave();
        final Usuario usuario = usuarioService.obterPorId(ctx, usuarioTO.getId());
        AvaliacaoFisica avaliacaoFisica;
        try {
            avaliacaoFisica = obterPorId(ctx, id);
            return this.getToDTO(ctx, avaliacaoFisica, usuario);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void enviarComparativoAvaliacaoIntegrada(final String key, List<ItemAvaliacaoFisica> avaliacoes, String nomeEmpresa, Usuario usuario,
                                                    String[] destinatarios,
                                                    ServletContext context) throws Exception {


        List<ItemAvaliacaoFisica> itensAvaliacaoFisica = new ArrayList<ItemAvaliacaoFisica>();
        for (int i = 0; avaliacoes.size() > i; i++) {
            ItemAvaliacaoFisica itemAvaliacaoFisicaComp = montarVisualizacao(key, avaliacoes.get(i).getCodigo());
            itensAvaliacaoFisica.add(itemAvaliacaoFisicaComp);
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        AvaliacaoFisicaIntegradaPDF.criarPDFComparacaoAvaliacaoIntegrada(key, usuario, itensAvaliacaoFisica,
                UtilContext.getBean(ViewUtils.class),
                context, outputStream);
        byte[] bytes = outputStream.toByteArray();

        //construct the pdf body part
        DataSource dataSource = new ByteArrayDataSource(bytes, "application/pdf");
        MimeBodyPart pdfBodyPart = new MimeBodyPart();
        pdfBodyPart.setDataHandler(new DataHandler(dataSource));
        pdfBodyPart.setFileName("avaliacaointegrada-" + nomeEmpresa.trim().replaceAll(" ", "_")
                + itensAvaliacaoFisica.get(0).getCliente().getNome().trim().replaceAll(" ", "_") + ".pdf");

        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        JSONObject configsEmail = integracaoWS.configsEmail(url);
        UteisEmail u = new UteisEmail();
        u.preencherConfiguracaoEmailPadrao(configsEmail, nomeEmpresa);

        String a = "Avaliação Física " + (UteisValidacao.emptyString(nomeEmpresa) ? "" : ("- " + nomeEmpresa));
        for (String e : destinatarios) {
            u.enviarEmail(e, itensAvaliacaoFisica.get(0).getCliente().getNome(),
                    a,
                    "", pdfBodyPart,
                    a);
        }
    }

    @Override
    public boolean isAvaliacaoVigente(final String ctx, AvaliacaoFisica object) throws ServiceException {
        try {

            Date dataUltimaAvaliacao = null;
            StringBuilder sql = new StringBuilder();
            sql.append("select dataavaliacao from avaliacaofisica  \n");
            sql.append("where cliente_codigo = ").append(object.getCliente().getCodigo());
            sql.append(" order by dataavaliacao desc limit 1 ");
            List listOfObjects = avaliacaoFisicaDao.listOfObjects(ctx, sql.toString());
            for (Object o : listOfObjects) {
                dataUltimaAvaliacao = (Date) o;
            }

            return dataUltimaAvaliacao == null || Calendario.maiorOuIgual(object.getDataAvaliacao(),dataUltimaAvaliacao);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void atualizarResultadoEvolucaoAluno(String ctx, ClienteSintetico cliente) {
        try {
            List<AvaliacaoFisica> avaliacoesCliente = obterAvaliacaoCliente(ctx, cliente.getCodigo());
            if(!avaliacoesCliente.isEmpty()){
                AvaliacaoFisica primeiraAvaliacao = avaliacoesCliente.get(avaliacoesCliente.size() - 1);
                AvaliacaoFisica atualAvaliacao = avaliacoesCliente.get(0);

                cliente.setPesoInicio(primeiraAvaliacao.getPeso());
                cliente.setPesoAtual(atualAvaliacao.getPeso());

                cliente.setPercentualGorduraInicio(primeiraAvaliacao.getPercentualGordura());
                cliente.setPercentualGorduraAtual(atualAvaliacao.getPercentualGordura());

                if(UteisValidacao.emptyNumber(primeiraAvaliacao.getMassaMagra()) && !UteisValidacao.emptyNumber(primeiraAvaliacao.getPercentualMassaMagra())){
                    cliente.setMassaMagraInicio((primeiraAvaliacao.getPeso()*primeiraAvaliacao.getPercentualMassaMagra())/100.0);
                } else {
                    cliente.setMassaMagraInicio(primeiraAvaliacao.getMassaMagra());
                }

                if(UteisValidacao.emptyNumber(atualAvaliacao.getMassaMagra()) && !UteisValidacao.emptyNumber(atualAvaliacao.getPercentualMassaMagra())){
                    cliente.setMassaMagraAtual((atualAvaliacao.getPeso()*atualAvaliacao.getPercentualMassaMagra())/100.0);
                } else {
                    cliente.setMassaMagraAtual(atualAvaliacao.getMassaMagra());
                }


                clienteSinteticoService.alterar(ctx, cliente);
            }
        } catch (ServiceException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.sc = servletContext;
    }

    public List<SugestaoHorarioPersonalJSON> sugerirHorariosApp(final String chave, final Date data, final Integer empresa)
            throws Exception {
        List<SugestaoHorarioPersonalJSON> horarios = new ArrayList<>();
        TipoEventoService tes = UtilContext.getBean(TipoEventoService.class);
        List<TipoEvento> tipoEventos = tes.obterTodos(chave, true);
        for (TipoEvento tipoEvento : tipoEventos) {
            HorarioPersonalAgendaJSON sugestoes = ages.sugestoesHorarioApp(chave, tipoEvento, data, empresa);
            HorarioPersonalJSON horario = new HorarioPersonalJSON();
            horario.setInicio(sugestoes.getInicio());
            horario.setFim(sugestoes.getFim());
            if (sugestoes.getInicio() > 0){
                horarios.add(new SugestaoHorarioPersonalJSON(tipoEvento.getCodigo(), tipoEvento.getNome(), horario, sugestoes.getAgendamentoId(), sugestoes.getProfessorId()));
            }
        }
        return horarios;
    }

    public Map<String, String> restaurarRespostas(String ctx, String codigosPergunta) throws Exception{
        /*SIGA ESSES PASSOS PARA DESCOBRIR AS PERGUNTAS DELETADAS
        -- 1 - liste os dias em que houveram perguntas deletadas e quais foram os usuarios
        select
        to_char(TO_TIMESTAMP(c.timestamp/1000), 'yyyy-mm-dd HH24:MI:SS') as dtalteracao, c.username, pa.*
                from
        pergunta_aud pa
        inner join customrevisionentity c on c.id = pa.rev
        where
        pa.revtype = 2
        order by
        dtalteracao desc

        -- 2 - liste quais foram as respostas afetadas - select in perguntaanmnese x resposta cliente usando auditoria - 5929
        select
        pa.codigo, p.anamnese_codigo, r.resposta
        from
        respostacliente_aud r
        inner join perguntaanamnese_aud p on p.codigo = r.perguntaanamnese_codigo
        inner join pergunta_aud pa on pa.codigo = p.pergunta_codigo
        inner join pergunta p2 on p2.descricao = pa.descricao
        inner join perguntaanamnese p3 on p3.pergunta_codigo = p2.codigo
        where
        p.pergunta_codigo in (461, 475, 474, 473, 472, 471, 470, 469, 468, 467, 466, 465, 464, 463, 462)
        and p3.anamnese_codigo = p.anamnese_codigo
        group by
        1,2,3*/

        Map<String, String> retorno = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select p2.tipopergunta, p3.codigo as perguntaanamnese, \n");
        sql.append("p2.codigo as novapergunta, p.anamnese_codigo, \n");
        sql.append("        r.resposta, r.codigo, r.avaliacao_codigo, \n");
        sql.append("        r.cliente_codigo, r.itemavaliacao_codigo, \n");
        sql.append("        r.obs \n");
        sql.append("from \n");
        sql.append("respostacliente_aud r \n");
        sql.append("inner join perguntaanamnese_aud p on p.codigo = r.perguntaanamnese_codigo \n");
        sql.append("inner join pergunta_aud pa on pa.codigo = p.pergunta_codigo \n");
        sql.append("inner join pergunta p2 on p2.descricao = pa.descricao \n");
        sql.append("inner join perguntaanamnese p3 on p3.pergunta_codigo = p2.codigo \n");
        sql.append("left join respostacliente r2 on r2.itemavaliacao_codigo = r.itemavaliacao_codigo and r2.perguntaanamnese_codigo = p3.codigo \n");
        sql.append("where \n");
        sql.append("p.pergunta_codigo in (").append(codigosPergunta).append(") \n");
        sql.append("and p3.anamnese_codigo = p.anamnese_codigo \n");
        sql.append("and (r.obs <> '' or r.resposta <> '') \n");
        sql.append("and r2.codigo is null \n");
        sql.append("group by \n");
        sql.append("1,2,3,4,5,6,7,8,9, 10 \n");

        try (ResultSet rs = avaliacaoFisicaDao.createStatement(ctx, sql.toString())) {

            Map<Integer, ClienteSintetico> mapclientes = new HashMap<>();
            Map<Integer, PerguntaAnamnese> mapPerguntaAnamnese = new HashMap<>();
            Map<Integer, ItemAvaliacaoFisica> mapItemAvaliacaoFisica = new HashMap<>();

            while (rs.next()) {
                if (mapPerguntaAnamnese.get(rs.getInt("perguntaanamnese")) == null) {
                    PerguntaAnamnese perguntaanamnese = perguntaAnamneseDao.findById(ctx, rs.getInt("perguntaanamnese"));
                    mapPerguntaAnamnese.put(rs.getInt("perguntaanamnese"), perguntaanamnese);
                }

                if (mapItemAvaliacaoFisica.get(rs.getInt("itemavaliacao_codigo")) == null) {
                    ItemAvaliacaoFisica itemAvaliacaoFisica = itemDao.findById(ctx, rs.getInt("itemavaliacao_codigo"));
                    mapItemAvaliacaoFisica.put(rs.getInt("itemavaliacao_codigo"), itemAvaliacaoFisica);
                }

                if (mapclientes.get(rs.getInt("cliente_codigo")) == null) {
                    mapclientes.put(rs.getInt("cliente_codigo"), clienteDao.findById(ctx, rs.getInt("cliente_codigo")));
                }

                TiposPerguntaEnum tiposPergunta = UteisValidacao.emptyNumber(rs.getInt("tipopergunta")) ?
                        TiposPerguntaEnum.TEXTUAL :
                        TiposPerguntaEnum.getFromOrdinal(rs.getInt("tipopergunta"));

                RespostaCliente resposta = new RespostaCliente();
                resposta.setCliente(mapclientes.get(rs.getInt("cliente_codigo")));
                resposta.setPerguntaAnamnese(mapPerguntaAnamnese.get(rs.getInt("perguntaanamnese")));
                resposta.setItemAvaliacao(mapItemAvaliacaoFisica.get(rs.getInt("itemavaliacao_codigo")));
                resposta.setObs(rs.getString("obs"));
                if (!UteisValidacao.emptyString(rs.getString("resposta")) &&
                        (tiposPergunta.equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA) || tiposPergunta.equals(TiposPerguntaEnum.SIMPLES_ESCOLHA))) {

                    try (ResultSet rsOpcoes = perguntaDao.createStatement(ctx, "select o2.codigo from opcaopergunta_aud o\n" +
                            "inner join opcaopergunta o2 on o.opcao = o2.opcao \n" +
                            "where o.codigo in (" + rs.getString("resposta").replace("_", ",") + ")\n" +
                            "and o2.pergunta_codigo = " + rs.getInt("novapergunta"))) {
                        resposta.setResposta("");
                        while (rsOpcoes.next()) {
                            resposta.setResposta(resposta.getResposta() + "_" + rsOpcoes.getInt("codigo"));
                        }
                    }
                    resposta.setResposta(resposta.getResposta().replaceFirst("_", ""));
                } else {
                    resposta.setResposta(rs.getString("resposta"));
                }
                resposta = respostaClienteDao.insert(ctx, resposta);
                retorno.put(resposta.getPerguntaAnamnese().getPergunta().getDescricao() + "-" + resposta.getCodigo(), resposta.getResposta());
            }
        }
        return retorno;
    }

    @Override
    public List<RespostaCliente> obterRespostasCliente(final String ctx, final Integer cliente, final Integer respostaClienteParQ) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT obj FROM RespostaCliente obj WHERE obj.cliente.codigo = :cliente AND obj.respostaClienteParQ.codigo = :codigoRespostaClienteParQ \n");
            HashMap params = new HashMap<String, Object>();
            sql.append(" ORDER by obj.codigo");
            params.put("cliente", cliente);
            params.put("codigoRespostaClienteParQ", respostaClienteParQ);
            return respostaClienteDao.findByParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<RespostaClienteTO> respostaClienteToTO(List<RespostaCliente> listaRc) throws ServiceException {
        List<RespostaClienteTO> respostaClienteTOS = new ArrayList<>();
        for (RespostaCliente rc : listaRc) {
            RespostaClienteTO rcTO = new RespostaClienteTO();
            rcTO.setCodigo(rc.getCodigo());
            rcTO.setCodigoCliente(rc.getCliente().getCodigo());
            rcTO.setCodigoRespostaClienteParq(rc.getRespostaClienteParQ().getCodigo());
            rcTO.setCodigoPergunta(rc.getPerguntaAnamnese().getCodigo());
            rcTO.setResposta(rc.getResposta());
            rcTO.setObs(rc.getObs());
            respostaClienteTOS.add(rcTO);
        }
        return respostaClienteTOS;
    }

    public String consultarClientesParQAssinaturaDigital(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request, Integer diasParaVencimentoParq, String todos) throws ServiceException {
        try {
            // garantir que retorno somente os dados referentes aos vigentes;
            JSONObject retorno = new JSONObject();
            retorno.put("assinados", parQDao.consultarClientesParQAssinadoV2(ctx, empresaZw, nomeOuMatricula, request, null));
            retorno.put("nrAssinados", parQDao.consultarNrTotalClientesParQAssinadoV2(ctx, empresaZw, null));
            JSONArray listaNaoAssinados = parQDao.consultarClientesParQNaoAssinadoV2(ctx, empresaZw, nomeOuMatricula, request, todos);
            retorno.put("naoAssinados", listaNaoAssinados);
            retorno.put("nrNaoAssinados", parQDao.consultarNrTotalClientesParQNaoAssinadoV2(ctx, empresaZw));
            retorno.put("parqPositivo", parQDao.consultarClientesParQPositivoV2(ctx, empresaZw, nomeOuMatricula, request));
            retorno.put("nrPositivos", parQDao.consultarNrTotalClientesParQPositivosV2(ctx, empresaZw));
            if (!UteisValidacao.emptyNumber(diasParaVencimentoParq)) {
                retorno.put("assinadosvencidos", parQDao.consultarClientesParQAssinadoV2(ctx, empresaZw, nomeOuMatricula, request, diasParaVencimentoParq));
                retorno.put("nrAssinadosVencidos", parQDao.consultarNrTotalClientesParQAssinadoV2(ctx, empresaZw, diasParaVencimentoParq));
            } else {
                retorno.put("assinadosvencidos", new JSONArray());
                retorno.put("nrAssinadosVencidos", 0);
            }
            retorno.put("status", "sucesso");
            return retorno.toString();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public boolean isParqAlunoAssinado(String ctx, Integer empresaZw, String matricula, HttpServletRequest request) throws ServiceException {
        try {
            return parQDao.consultarClientesParQAssinadoV2(ctx, empresaZw, matricula, request, null).length() > 0;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public String obterMatAlunosParq(String ctx, Integer empresaZw, String tipoConsulta, Integer diasParaVencimentoParq) throws ServiceException {
        // Consulta utilizada para relatório geral de clientes no adm, filtro parq assinado e não assinado
        try {
            JSONObject listRetorno = new JSONObject();
            listRetorno.put("assinado", "");
            listRetorno.put("vencido", "");
            listRetorno.put("nao_assinado", "");

            switch (tipoConsulta) {
                case "assinado":
                    listRetorno.put("assinado", obterMatAlunosParq(ctx, empresaZw, true, null));
                    break;
                case "vencido":
                    if (!UteisValidacao.emptyNumber(diasParaVencimentoParq)) {
                        listRetorno.put("vencido", obterMatAlunosParq(ctx, empresaZw, true, diasParaVencimentoParq));
                    }
                    break;
                case "nao_assinado":
                    listRetorno.put("nao_assinado", obterMatAlunosParq(ctx, empresaZw, false, null));
                    break;
                case "todos":
                    listRetorno.put("assinado", obterMatAlunosParq(ctx, empresaZw, true, null));
                    if (!UteisValidacao.emptyNumber(diasParaVencimentoParq)) {
                        listRetorno.put("vencido", obterMatAlunosParq(ctx, empresaZw, true, diasParaVencimentoParq));
                    }
                    listRetorno.put("nao_assinado", obterMatAlunosParq(ctx, empresaZw, false, null));
                    break;
            }
            return listRetorno.toString();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private String obterMatAlunosParq(String ctx, Integer empresaZw, boolean isAssinado, Integer diasParaVencimentoParq) throws Exception {
        String listAlunos = "";
        StringBuilder sql = new StringBuilder();

        if (isAssinado) {
            sql.append("SELECT distinct(cs.matricula) \n");
            sql.append("FROM clientesintetico cs \n");
            sql.append("INNER JOIN respostaclienteparq rc ON rc.cliente_codigo = cs.codigo \n");
        } else {
            sql.append("SELECT distinct(cs.matricula) \n");
            sql.append("FROM clientesintetico cs \n");
            sql.append("LEFT JOIN LATERAL (\n" +
                    "    SELECT rc.urlassinatura\n" +
                    "    FROM respostaclienteparq rc\n" +
                    "    WHERE rc.cliente_codigo = cs.codigo\n" +
                    "    ORDER BY rc.dataresposta DESC\n" +
                    "    LIMIT 1\n" +
                    ") rc ON true \n");
        }
        sql.append("WHERE 1=1 \n");
        if (!isAssinado) {
            sql.append("AND (rc.urlassinatura IS NULL OR TRIM(rc.urlassinatura) = '') \n");
        } else {
            sql.append("AND rc.urlassinatura IS NOT null \n");
            sql.append("AND rc.urlassinatura <> '' \n");
        }
        if (!UteisValidacao.emptyNumber(empresaZw)) {
            sql.append("AND cs.empresa = ").append(empresaZw).append(" \n");
        }
        if (isAssinado && !UteisValidacao.emptyNumber(diasParaVencimentoParq)) {
            sql.append("AND rc.dataresposta + INTERVAL '").append(diasParaVencimentoParq).append(" days' < current_date \n");
        }
        try (ResultSet rs = parQDao.createStatement(ctx, sql.toString())) {
            while (rs.next()) {
                listAlunos += "," + rs.getInt("matricula");
            }
        }

        return listAlunos.length() > 1 ? listAlunos.substring(1) : listAlunos;
    }

    public RMLConfigDTO obterTabelaRML(Integer alunoId) throws ServiceException {
        RMLConfigDTO configDTO = new RMLConfigDTO();
        ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);

        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            ConfiguracaoSistema rmlOpcoes = css.consultarPorTipo(chave, ConfiguracoesEnum.CFG_RML_OPCOES);
            ConfiguracaoSistema rmlSeparado = css.consultarPorTipo(chave, ConfiguracoesEnum.CFG_RML_SEPARADO);

            try (ResultSet rs = clienteDao.createStatement(chave, "select sexo from clientesintetico where codigo = " + alunoId)) {
                if (rs.next()) {
                    String sexo = rs.getString("sexo");
                    sexo = UteisValidacao.emptyString(sexo) ? "F" : sexo;
                    String sufixo = sexo.equals(SexoEnum.M.getSexo()) ? "_HOMEM" : "_MULHER";

                    for (RMLEnum r : RMLEnum.values()) {
                        if (rmlOpcoes.getValorAsBoolean() && !rmlSeparado.getValorAsBoolean() && !UteisValidacao.emptyString(sufixo)) {
                            RMLValoresDTO valor = new RMLValoresDTO();
                            if (r.name().contains(sufixo) && sufixo.equals("_HOMEM")) {
                                valor.setLinha(r.getFaixa());
                                valor.setFaixaIdade(r.getFaixa().getFaixaIdade());
                                valor.setExcelente(r.getExcelente() + "+");
                                valor.setAcimaMedia(r.getAcimaMedia() + " - " + (r.getExcelente() - 1));
                                valor.setMedia(r.getMedia() + " - " + (r.getAcimaMedia() - 1));
                                valor.setAbaixoMedia(r.getAbaixoMedia() + " - " + (r.getMedia() - 1));
                                valor.setFraco(r.getFraco() + " - " + (r.getAbaixoMedia() - 1));

                                if (r.name().contains("BRACO")) {
                                    configDTO.getBracos().add(valor);
                                } else {
                                    configDTO.getAbdomen().add(valor);
                                }
                            } else if (r.name().contains(sufixo) && sufixo.equals("_MULHER")) {
                                valor.setLinha(r.getFaixa());
                                valor.setFaixaIdade(r.getFaixa().getFaixaIdade());
                                valor.setExcelente(r.getExcelente() + "+");
                                valor.setAcimaMedia(r.getAcimaMedia() + " - " + (r.getExcelente() - 1));
                                valor.setMedia(r.getMedia() + " - " + (r.getAcimaMedia() - 1));
                                valor.setAbaixoMedia(r.getAbaixoMedia() + " - " + (r.getMedia() - 1));
                                valor.setFraco(r.getFraco() + " - " + (r.getAbaixoMedia() - 1));

                                if (r.name().contains("BRACO")) {
                                    configDTO.getBracos().add(valor);
                                } else {
                                    configDTO.getAbdomen().add(valor);
                                }
                            }
                        } else {
                            if (!rmlOpcoes.getValorAsBoolean() && rmlSeparado.getValorAsBoolean() && !r.name().contains("_HOMEM") && !r.name().contains("_MULHER")) {
                                RMLValoresDTO valor = new RMLValoresDTO();
                                valor.setLinha(r.getFaixa());
                                valor.setFaixaIdade(r.getFaixa().getFaixaIdade());
                                valor.setExcelente(r.getExcelente() + "+");
                                valor.setAcimaMedia(r.getAcimaMedia() + " - " + (r.getExcelente() - 1));
                                valor.setMedia(r.getMedia() + " - " + (r.getAcimaMedia() - 1));
                                valor.setAbaixoMedia(r.getAbaixoMedia() + " - " + (r.getMedia() - 1));
                                valor.setFraco(r.getFraco() + " - " + (r.getAbaixoMedia() - 1));

                                if (r.name().contains("BRACO")) {
                                    configDTO.getBracos().add(valor);
                                } else {
                                    configDTO.getAbdomen().add(valor);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return configDTO;
    }

    @Override
    public void processoAjustarResponsavel(final String ctx) {

        try {
            try(Connection bdZW = conexaoZWService.conexaoZw(ctx);
                ResultSet rs = avaliacaoFisicaDao.createStatement(ctx, "select codigo from avaliacaofisica where responsavellancamento_codigo = 0")){
                List<Integer> avaliacoes = new ArrayList<>();
                while(rs.next()){
                    avaliacoes.add(rs.getInt("codigo"));
                }

                Map<String, String> responsaveis = new HashMap<>();
                Map<String, Integer> usernameCodigo = new HashMap<>();
                Set<String> usuarios = new HashSet<>();
                for(Integer cod : avaliacoes){
                    try(ResultSet rsUsuarioAntigo = avaliacaoFisicaDao.createStatement(ctx,"select u.username, u.usuariozw from avaliacaofisica_aud aa " +
                            "inner join usuario u on u.codigo = aa.responsavellancamento_codigo " +
                            "where aa.codigo = " + cod + " order by rev limit 1")){
                        String usuario = "-";
                        if(rsUsuarioAntigo.next()){
                            usuarios.add(rsUsuarioAntigo.getString("username"));
                            int usuariozw = rsUsuarioAntigo.getInt("usuariozw");
                            if(usuariozw > 0){
                                usuario = rsUsuarioAntigo.getString("username");
                                usernameCodigo.put(usuario, usuariozw);
                            } else {
                                try(ResultSet rsUserCod = ConexaoZWServiceImpl.criarConsulta(
                                            "select codigo from usuario where username ilike '" +
                                                    rsUsuarioAntigo.getString("username") +
                                                    "'", bdZW)){
                                    if(rsUserCod.next()){
                                        usuario = rsUsuarioAntigo.getString("username");
                                        usernameCodigo.put(usuario, rsUserCod.getInt("codigo"));
                                    }
                                }

                            }

                        }
                        String avaliacoesUsuario = responsaveis.get(usuario);
                        if(avaliacoesUsuario == null){
                            avaliacoesUsuario = "";
                        }
                        avaliacoesUsuario += "," + cod;
                        responsaveis.put(usuario, avaliacoesUsuario);
                    }
                }

                String naoEncontrados = responsaveis.get("-");
                if(naoEncontrados != null){
                    avaliacaoFisicaDao.executeNativeSQL(ctx, "update avaliacaofisica set responsavellancamento_codigo = 2 " +
                            " where codigo in (" + naoEncontrados.replaceFirst(",", "") + ");");
                }

                for(String username : usuarios){
                    System.out.println("--" + username);
                    if(responsaveis.get(username) != null){
                        String avaliacoesUsuario = responsaveis.get(username);
                        avaliacaoFisicaDao.executeNativeSQL(ctx, "update avaliacaofisica set responsavellancamento_codigo = " +
                                usernameCodigo.get(username) + " where codigo in (" + avaliacoesUsuario.replaceFirst(",", "") + ");");
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public String processoRemoverAvaliacaoPosturalDuplicada(final String ctx, boolean todas, Integer codigoAvaliacaoPostural) throws Exception {
        if (UteisValidacao.emptyString(ctx)) {
            throw new ServiceException("A chave não foi localizada!");
        }

        if (!todas && UteisValidacao.emptyNumber(codigoAvaliacaoPostural)) {
            throw new ServiceException("Se você informar que não é para verificar todas avaliações posturais, é obrigatório informar o código da avaliação postural a ser removido!");
        }
        Integer countSuccess = 0;
        try {
            if (!todas && !UteisValidacao.emptyNumber(codigoAvaliacaoPostural)) {
                // SE NÃO É PRA VALIDAR TODAS, E FOI INFORMADO O CÓDIGO DA AVALIACAOPOSTURAL A SER REMOVIDA, REMOVER E RETORNAR
                posturalDao.executeNative(ctx, "delete from itemavaliacaopostural where avaliacaopostural_codigo = " + codigoAvaliacaoPostural);
                posturalDao.executeNative(ctx, "delete from avaliacaopostural where codigo = " + codigoAvaliacaoPostural);
                return "Avaliação Postural de código " + codigoAvaliacaoPostural + " foi removida com sucesso!";
            }
            // CONSULTAR TODAS AVALIACOESPOSTURAL DUPLICADAS PARA MESMA AVALIACAOFISICA
            String sql = "select avaliacao_codigo, count(avaliacao_codigo) as quantidade \n" +
                         "from avaliacaopostural \n" +
                         "group by avaliacao_codigo \n" +
                         "having count(avaliacao_codigo) > 1;";
            try (ResultSet rsConsulta = posturalDao.createStatement(ctx, sql)) {
                while (rsConsulta.next()) {
                    // OBTER TODOS REGISTROS DA AVALIACOESPOSTURAL PELO AVALIACAO_CODIGO
                    Integer codAvPosturalRemover = null;
                    sql = "select * from avaliacaopostural a where avaliacao_codigo = " + rsConsulta.getInt("avaliacao_codigo") + " order by codigo asc;";
                    try (ResultSet rsAux = posturalDao.createStatement(ctx, sql)) {
                        while (rsAux.next()) {
                            // REMOVER APENAS 1 DOS REGISTROS EM QUE TIVER COM VALORES NULL, SE TODOS REGISTROS ESTIVEREM COM VALORES NULL INDIFERE QUAL SEJA O REGISTRO A SER EXCLUÍDO
                            if (UteisValidacao.emptyString(rsAux.getString("keyimganterior")) &&
                                    UteisValidacao.emptyString(rsAux.getString("keyimgdireita")) &&
                                    UteisValidacao.emptyString(rsAux.getString("keyimgesquerda")) &&
                                    UteisValidacao.emptyString(rsAux.getString("keyimgposterior")) &&
                                    UteisValidacao.emptyString(rsAux.getString("observacao")) &&
                                    UteisValidacao.emptyNumber(rsAux.getInt("ombros")) &&
                                    UteisValidacao.emptyNumber(rsAux.getInt("quadril"))) {
                                codAvPosturalRemover = rsAux.getInt("codigo");
                            }
                        }
                    }
                    if (!UteisValidacao.emptyNumber(codAvPosturalRemover)) {
                        posturalDao.executeNative(ctx, "delete from itemavaliacaopostural where avaliacaopostural_codigo = " + codAvPosturalRemover);
                        posturalDao.executeNative(ctx, "delete from avaliacaopostural where codigo = " + codAvPosturalRemover);
                        countSuccess++;
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, AvaliacaoFisicaImpl.class);
            return "ERRO: Ocorreu um erro durante o processo: " + e.toString();
        }
        return "SUCESSO: Foram removidos " + countSuccess + " registros de avaliacaopostural duplicados!";
    }

    public JSONObject consultarParQsAluno(String ctx, Integer empresaZw, Integer codigoCliente, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            JSONArray alunosParQ = parQDao.alunoParQValido(ctx, empresaZw, codigoCliente);

            int totalElements = alunosParQ.length();
            int maxResults = paginadorDTO.getSize() == null ? 20 : paginadorDTO.getSize().intValue();
            int page = paginadorDTO.getPage() == null ? 0 : paginadorDTO.getPage().intValue();

            page = Math.max(page, 0);

            int start = page * maxResults;
            int end = Math.min(start + maxResults, totalElements);

            if (start >= totalElements) {
                start = totalElements - maxResults;
                start = Math.max(start, 0);
            }

            JSONArray paginatedResults = new JSONArray();
            for (int i = start; i < end; i++) {
                paginatedResults.put(alunosParQ.getJSONObject(i));
            }

            paginadorDTO.setQuantidadeTotalElementos((long) totalElements);
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) page);

            JSONObject retorno = new JSONObject();
            retorno.put("alunos", paginatedResults);
            retorno.put("totalElementos", paginadorDTO.getQuantidadeTotalElementos());
            retorno.put("size", paginadorDTO.getSize());
            retorno.put("page", paginadorDTO.getPage());
            retorno.put("status", "sucesso");

            return retorno;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

}

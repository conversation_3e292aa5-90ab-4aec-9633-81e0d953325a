package br.com.pacto.service.impl.turma;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.turma.AmbienteDTO;
import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.controller.json.turma.NivelTurmaDTO;
import br.com.pacto.controller.json.turma.TurmaResponseDTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.turma.TurmaDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AulaExcecoes;
import br.com.pacto.service.intf.turma.TurmaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static br.com.pacto.objeto.Uteis.incluirLog;

@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class TurmaServiceImpl implements TurmaService {

    @Autowired
    private SessaoService sessaoService;

    @Autowired
    private TurmaDao turmaDao;

    @Autowired
    private LogDao logDao;

    @Override
    public TurmaResponseDTO save(TurmaResponseDTO turmaDTO) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        TurmaResponseDTO turma = turmaDao.save(ctx, turmaDTO);
        if (turmaDTO.getLinkVideos() != null) {
            for (TurmaVideoDTO t : turmaDTO.getLinkVideos()) {
                t.setTurma_codigo(turmaDTO.getId());
                t = turmaDao.saveTurmaVideo(ctx, t);
            }
            turma.setLinkVideos(turmaDTO.getLinkVideos());
        }

        if (turma.getModalidade() != null && !UteisValidacao.emptyNumber(turma.getModalidade().getId()) && UteisValidacao.emptyString(turma.getModalidade().getNome())) {
            turma.getModalidade().setNome(turmaDao.obterNomeModalidadePorCodigo(ctx, turma.getModalidade().getId()));
        }

        incluirLog(ctx, turma.getId().toString(), "", "", getDescricaoTurmaParaLog(turma, null), "INCLUSÃO", "INCLUSÃO DE TURMA", EntidadeLogEnum.TURMA, "Turma", sessaoService, logDao, null, null);
        return turma;
    }

    @Override
    public TurmaResponseDTO update(TurmaResponseDTO turmaDTO) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        TurmaResponseDTO turmaAtes = turmaDao.obterTurma(ctx, turmaDTO.getId());
        turmaAtes.setLinkVideos(turmaDao.obterListaTurmaVideo(ctx, turmaDTO.getId()));
        validarVigenciaTurmaAlunosNaTurma(turmaDTO, ctx, turmaAtes);
        TurmaResponseDTO turma = turmaDao.update(ctx, turmaDTO);

        for (TurmaVideoDTO t : turmaAtes.getLinkVideos()) {
            if (!turmaDTO.getLinkVideos().contains(new TurmaVideoDTO(t))) {
                turmaDao.excluirTurmaVideo(ctx, t);
            }
        }
        if (turmaDTO.getLinkVideos() != null && turmaDTO.getLinkVideos().size() > 0) {
            for (TurmaVideoDTO t : turmaDTO.getLinkVideos()) {
                t.setTurma_codigo(turmaDTO.getId());
                TurmaVideoDTO turmaDto = turmaDao.obterTurmaVideo(ctx, t.getId());
                if (turmaDto != null) {
                    turmaDao.updateTurmaVideo(ctx, t);
                } else {
                    t = turmaDao.saveTurmaVideo(ctx, t);
                }
            }
        }
        registrarLogAlteracaoTurma(ctx, turmaAtes, turma);
        return turma;
    }

    private void registrarLogAlteracaoTurma(String ctx, TurmaResponseDTO turmaAtes, TurmaResponseDTO turma) throws ParseException {
        turmaAtes.setDataInicial(Calendario.getData(Calendario.getDate("MM/dd/yyyy", turmaAtes.getDataInicial()), "dd/MM/yyyy"));
        turmaAtes.setDataFinal(Calendario.getData(Calendario.getDate("MM/dd/yyyy", turmaAtes.getDataFinal()), "dd/MM/yyyy"));
        turma.setDataInicial(Calendario.getData(new Date(Long.parseLong(turma.getDataInicial())), "dd/MM/yyyy"));
        turma.setDataFinal(Calendario.getData(new Date(Long.parseLong(turma.getDataFinal())), "dd/MM/yyyy"));

        if (turmaAtes.getModalidade() != null && !UteisValidacao.emptyNumber(turmaAtes.getModalidade().getId()) && UteisValidacao.emptyString(turmaAtes.getModalidade().getNome())) {
            turmaAtes.getModalidade().setNome(turmaDao.obterNomeModalidadePorCodigo(ctx, turmaAtes.getModalidade().getId()));
        }

        if (turma.getModalidade() != null && !UteisValidacao.emptyNumber(turma.getModalidade().getId()) && UteisValidacao.emptyString(turma.getModalidade().getNome())) {
            turma.getModalidade().setNome(turmaDao.obterNomeModalidadePorCodigo(ctx, turma.getModalidade().getId()));
        }

        incluirLog(ctx, turma.getId().toString(), "",
                getDescricaoTurmaParaLog(turmaAtes, turma), getDescricaoTurmaParaLog(turma, turmaAtes),
                "ALTERAÇÃO", "ALTERAÇÃO DE TURMA", EntidadeLogEnum.TURMA, "Turma", sessaoService, logDao, null, null);
        turma.setDataInicial(String.valueOf(Calendario.getDate("dd/MM/yyyy", turma.getDataInicial()).getTime()));
        turma.setDataFinal(String.valueOf(Calendario.getDate("dd/MM/yyyy", turma.getDataFinal()).getTime()));
    }

    private void validarVigenciaTurmaAlunosNaTurma(TurmaResponseDTO turmaDTO, String ctx, TurmaResponseDTO turmaAtes) throws Exception {
        if (turmaAtes != null && !Calendario.igual(Calendario.getDate("MM/dd/yyyy", turmaAtes.getDataFinal()), new Date(Long.parseLong(turmaDTO.getDataFinal())))) {
            if (new Date(Long.parseLong(turmaDTO.getDataFinal())).before(Calendario.hoje()) && turmaDao.existemAlunosNaTurma(ctx, turmaDTO)) {
                throw new ServiceException("Não é possível alterar a vigência da turma, existem alunos ativos.");
            }
        }
    }

    @Override
    public List<HorarioTurmaResponseDTO> saveOrUpdateHorario(List<HorarioTurmaResponseDTO> horarioDTO, Integer empresaZwId) {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<HorarioTurmaResponseDTO> horariosSalvos = new ArrayList<>();
        horarioDTO.forEach(h -> {
            try {
                if (h != null && (h.getCodigo() == null || h.getCodigo() == 0)) {
                    HorarioTurmaResponseDTO horarioTurmaResponseDTO = turmaDao.saveHorario(ctx, h);
                    horariosSalvos.add(horarioTurmaResponseDTO);
                    sincronizarHorarioTurmaMgb(ctx, empresaZwId, horarioTurmaResponseDTO.getCodigo());
                    incluirLog(ctx, h.getCodigo().toString(), h.getTurma().toString(), "", getDescricaoHorarioParaLog(h, null), "INCLUSÃO", "INCLUSÃO DE HORARIO TURMA", EntidadeLogEnum.HORARIO_TURMA, "Horario Turma", sessaoService, logDao, null, null);
                } else {
                    HorarioTurmaResponseDTO horarioAntes = turmaDao.obterHorarioTurma(ctx, h.getCodigo());
                    HorarioTurmaResponseDTO horario = turmaDao.updateHorario(ctx, h);
                    sincronizarHorarioTurmaMgb(ctx, empresaZwId, horario.getCodigo());
                    incluirLog(ctx, horario.getCodigo().toString(), horario.getTurma().toString(),
                            getDescricaoHorarioParaLog(horarioAntes, horario), getDescricaoHorarioParaLog(horario, horarioAntes),
                            "ALTERAÇÃO", "ALTERAÇÃO DE HORARIO TURMA", EntidadeLogEnum.HORARIO_TURMA, "Horario Turma", sessaoService, logDao, null, null);
                }
                turmaDao.saveHorarioCapacidadeCategoria(ctx, h);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        });
        return horariosSalvos;
    }

    private void sincronizarHorarioTurmaMgb(String ctx, Integer empresaZwId, Integer codigoHorarioTurma) {
        try {
            chamadaZwSincronizarHorarioMgb(ctx, "/prest/mgb/sync-horario-turma-mgb", empresaZwId, codigoHorarioTurma);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("#### [TurmaServiceImpl.sincronizarHorarioMgb] ERRO AO SINCRONIZAR HORARIO MGB ERRO: " +ex.getMessage());
        }
    }

    @Override
    public HorarioTurmaResponseDTO saveHorario(HorarioTurmaResponseDTO horarioDTO) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        HorarioTurmaResponseDTO horario = turmaDao.saveHorario(ctx, horarioDTO);
        incluirLog(ctx, horario.getCodigo().toString(), horario.getTurma().toString(), "", getDescricaoHorarioParaLog(horario, null), "INCLUSÃO", "INCLUSÃO DE HORARIO TURMA", EntidadeLogEnum.HORARIO_TURMA, "Horario Turma", sessaoService, logDao, null, null);
        return horario;
    }

    @Override
    public HorarioTurmaResponseDTO updateHorario(HorarioTurmaResponseDTO horarioDTO) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        HorarioTurmaResponseDTO horarioAntes = turmaDao.obterHorarioTurma(ctx, horarioDTO.getCodigo());
        HorarioTurmaResponseDTO horario = turmaDao.updateHorario(ctx, horarioDTO);
        incluirLog(ctx, horario.getCodigo().toString(), horario.getTurma().toString(),
                getDescricaoHorarioParaLog(horarioAntes, horario), getDescricaoHorarioParaLog(horario, horarioAntes),
                "ALTERAÇÃO", "ALTERAÇÃO DE HORARIO TURMA", EntidadeLogEnum.HORARIO_TURMA, "Horario Turma", sessaoService, logDao, null, null);
        return horario;
    }

    @Override
    public AmbienteDTO saveAmbiente(AmbienteDTO ambienteDTO) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        validacoesAmbiente(ctx, ambienteDTO);
        return turmaDao.saveAmbiente(ctx, ambienteDTO);
    }

    private void validacoesAmbiente(String ctx, AmbienteDTO ambienteDTO) throws Exception {
        if (UteisValidacao.emptyString(ambienteDTO.getNome())) {
            throw new ServiceException("O campo \"Nome\" deve ser informado");
        }
        if (turmaDao.existsAmbienteByDescricao(ctx, ambienteDTO.getNome())) {
            throw new ServiceException("Já existe um ambiente com este mesmo nome.");
        }
        if (UteisValidacao.emptyNumber(ambienteDTO.getTipoAmbiente())) {
            throw new ServiceException("O campo \"Tipo de ambiente\" deve ser informado");
        }
    }

    @Override
    public NivelTurmaDTO saveNivelTurma(NivelTurmaDTO nivelTurmaDTO) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (turmaDao.existsNivelTurmaByDescricao(ctx, nivelTurmaDTO.getNome())) {
            throw new ServiceException("Já existe um nivel turma com este mesmo nome.");
        }
        return turmaDao.saveNivelTurma(ctx, nivelTurmaDTO);
    }

    @Override
    public List<TurmaResponseDTO> listarTurmas(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort(paginadorDTO.getSort());
            } else {
                paginadorDTO.setSort("nome,ASC");
            }

            return turmaDao.obterTurmas(ctx, null, empresaId, paginadorDTO, filtros);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcecoes.ERRO_lISTAR, e);
        }
    }

    public TurmaResponseDTO obterTurma(Integer codigo) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        TurmaResponseDTO turmaResponseDTO = turmaDao.obterTurma(ctx, codigo);
        turmaResponseDTO.setLinkVideos(turmaDao.obterListaTurmaVideo(ctx, codigo));
        return turmaResponseDTO;
    }

    public List<HorarioTurmaResponseDTO> listarHorariosTurma(JSONObject filtros, PaginadorDTO paginadorDTO, Integer turma) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return turmaDao.listarHorariosTurma(ctx, filtros, paginadorDTO, turma);
    }

    @Override
    public String validarExisteAlunosHorarioTurma(Integer id) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        HorarioTurmaResponseDTO dto = turmaDao.obterHorarioTurma(ctx, id);
        //procura por alunos matriculados na turma
        Long alunoMatriculadoHorario = turmaDao.consultarPorHorarioTurmaPorPeriodoCount(ctx, dto.getCodigo(), Calendario.hoje(), Calendario.hoje());
        if (alunoMatriculadoHorario.intValue() != 0) {
            return "Há alunos matriculados no horário de turma de " + dto.getDiaSemana_Apresentar() + " das " + dto.getHoraInicial() + " às " + dto.getHoraFinal() + " com o professor(a) " + dto.getProfessor() + ". Por favor, transfira os alunos para outro horário antes de excluir esse horário.";
        }

        turmaDao.nrAlunosReposicao(ctx, dto, Calendario.semanaAtualSQL(Calendario.hoje()));
        if (dto.getNrAlunoEntraramPorReposicao() != 0 || dto.getNrAlunoSairamPorReposicao() != 0) {
            return "Existem reposições para o horário. Acesse a consulta de turmas e apague as reposições para excluir esse horário.";
        }

        //procura por alunos do historico que nao estao nesse horario mais
        Long alunoMatriculadoHorarioHistorico = turmaDao.consultarPorHorarioTurmaCount(ctx, dto.getCodigo());
        boolean existeFaltaCreditoTreino = turmaDao.existeControleCredito(ctx, dto.getCodigo());
        boolean existemReposicoesFutura = turmaDao.existemReposicoesParaHorarioTurma(ctx, dto, true);
        if (existemReposicoesFutura) {
            return "Existem reposições futuras para este horário";
        }

        boolean existeModHorarioTurma = turmaDao.existeContratoModalidadeHorarioTurma(ctx, dto.getCodigo());
        boolean existemReposicoes = turmaDao.existemReposicoesParaHorarioTurma(ctx, dto, false);
        boolean existeAulaDesmarcadaSemRepor = turmaDao.existeAulaDesmarcadaSemReporParaHorarioTurma(ctx, dto.getCodigo());
        if ((alunoMatriculadoHorarioHistorico.intValue() == 0 && !existeFaltaCreditoTreino && !existemReposicoes && !existeModHorarioTurma && !existeAulaDesmarcadaSemRepor)) {
            return "EXCLUIR";
        } else {
            return "INATIVAR";
        }
    }

    @Override
    public String removerHorarioTurma(Integer id, Integer empresaZwId) throws Exception {
        String retorno = validarExisteAlunosHorarioTurma(id);
        String ctx = sessaoService.getUsuarioAtual().getChave();
        HorarioTurmaResponseDTO dto = turmaDao.obterHorarioTurma(ctx, id);

        if (retorno.equals("EXCLUIR") || retorno.equals("INATIVAR")) {
            if (retorno.equals("EXCLUIR")) {
                //horario novo sem alunos
                chamadaZwSincronizarHorarioMgb(ctx, "/prest/mgb/deletar-turma-mgb", empresaZwId, id);
                turmaDao.excluirHorarioTurma(ctx, dto);
            } else {
                //horario com alunos em historico
                chamadaZwSincronizarHorarioMgb(ctx, "/prest/mgb/inativar-turma-mgb", empresaZwId, id);
                dto.setSituacao("IN");
                dto.setHorarioDisponivelVenda(false);
                dto.setDataSaiuTurma(Calendario.getData("MM/dd/yyyy HH:mm:ss"));
                turmaDao.desativarHorarioTurma(ctx, dto);
            }
        } else {
            return retorno;
        }

        incluirLog(ctx, dto.getCodigo().toString(), dto.getTurma().toString(),
                null, getDescricaoHorarioParaLog(dto, null),
                "EXCLUSÃO", "EXCLUSÃO DE HORARIO TURMA", EntidadeLogEnum.HORARIO_TURMA, "Horario Turma", sessaoService, logDao, null, null);
        return "sucess";
    }

    public String chamadaZwSincronizarHorarioMgb(String ctx, String endpoint, Integer empresaZwId, Integer codigoHorarioTurma) throws Exception {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("chave", ctx);
            params.put("empresa", empresaZwId.toString());
            params.put("codigoHorarioTurma", codigoHorarioTurma.toString());

            String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            String urlParams = ExecuteRequestHttpService.obterUrlComParams(url + endpoint, params, "UTF-8");
            String msgRetorno = ExecuteRequestHttpService.executeRequestGET(urlParams);

            System.out.println("#### RETORNO CHAMADA_ZW_MGB: " + msgRetorno);
            if (msgRetorno.startsWith("Sucesso")) {
                return msgRetorno;
            } else {
                throw new ServiceException(msgRetorno);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao tentar inativar/deletar o horário no MGB " + e.getMessage());
            return "";
        }
    }

    public List<TurmaResponseDTO> obterTurmas(Integer codTurmaEdit, Integer empresa, PaginadorDTO paginadorDTO, JSONObject filtros) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return turmaDao.obterTurmas(ctx, codTurmaEdit, empresa, paginadorDTO, filtros);
    }

    private String getDescricaoTurmaParaLog(TurmaResponseDTO v1, TurmaResponseDTO v2) {

        try {
            StringBuilder log = new StringBuilder();

            if (v2 == null) {
                log.append(UtilReflection.difference(v1, null));
            } else {
                log.append(UtilReflection.difference(v1, v2));
            }

            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    private String getDescricaoHorarioParaLog(HorarioTurmaResponseDTO v1, HorarioTurmaResponseDTO v2) {

        try {
            StringBuilder log = new StringBuilder();

            if (v2 == null) {
                log.append(UtilReflection.difference(v1, null));
            } else {
                log.append(UtilReflection.difference(v1, v2));
            }

            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    @Override
    public void syncTurmaMgb(Integer codEmprezaZw, Integer codTurma) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        CompletableFuture.runAsync(() -> {
            try {

                Map<String, String> params = new HashMap<>();
                params.put("chave", ctx);
                params.put("empresa", codEmprezaZw.toString());
                params.put("codTurma", codTurma.toString());

                String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                String urlParams = ExecuteRequestHttpService.obterUrlComParams(
                        url + "/prest/mgb/sync-turma-mgb",
                        params,
                        "UTF-8"
                );

                String msgRetorno = ExecuteRequestHttpService.executeRequestGET(urlParams);
                Uteis.logarDebug("#### CTX: " + ctx + " RETORNO SINCRONIZACAO TURMA COM MGB: " + msgRetorno);

            } catch (Exception ex) {
                ex.printStackTrace();
                Uteis.logarDebug("#### Erro ao sincronizar turma com MGB: " + ex.getMessage());
            }
        });
    }
}

package br.com.pacto.service.impl.processo;

import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.dao.intf.avaliacao.ParQDao;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoSistemaDao;
import br.com.pacto.dao.intf.pessoa.PessoaDao;
import br.com.pacto.dto.SincronizacaoAlunosResultadoDTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.cliente.ClienteSinteticoServiceImpl;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.processo.ProcessosService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProcessosServiceImpl implements ProcessosService {

    @Autowired
    private ConfiguracaoSistemaDao configuracaoDao;
    @Autowired
    private ParQDao parQDao;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private PessoaDao pessoaDao;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;

    public ClienteSinteticoDao getClienteSinteticoDao() {
        return this.clienteSinteticoDao;
    }

    @Override
    public String createUnaccent(String ctx) throws Exception {
        try {
            try (ResultSet rs = configuracaoDao.createStatement(ctx, "select exists (select * from pg_extension where upper(extname) like 'UNACCENT');")) {
                if (rs.next() && rs.getBoolean("exists")) {
                    return "Extensão unaccent já existe neste banco";
                } else {
                    configuracaoDao.executeNativeSQL(ctx, "create extension unaccent;");
                    return "Extensão unaccent criada com sucesso";
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, ProcessosService.class);
            throw new ServiceException(e);
        }
    }

    @Override
    public List<String> replicarAssinaturaContratoParaParq(String ctx, Integer empresaZW, Boolean apenasValidar) throws Exception {
        System.out.println("INICIANDO PROCESSO DE REPLICAR ASSINATURA CONTRATO PARA ASSINATURA PARQ REPETIDAS PARA CHAVE: " + ctx);
        // consulta para identificar todas assinaturas parq repetidas
        StringBuilder sql = new StringBuilder();
        sql.append("WITH repetidos AS ( \n");
        sql.append("    SELECT urlassinatura, COUNT(*) AS total, COUNT(DISTINCT cliente_codigo) AS diferentes \n");
        sql.append("    FROM respostaclienteparq \n");
        sql.append("    GROUP BY urlassinatura \n");
        sql.append("    HAVING COUNT(*) > 1 AND COUNT(DISTINCT cliente_codigo) > 1 \n");
        sql.append(") \n");
        sql.append("SELECT c.nome, c.codigo as codigoclientetr, c.matricula, c.codigocliente as codigoclientezw, c.situacao, r.codigo, r.urlassinatura \n");
        sql.append("FROM respostaclienteparq r \n");
        sql.append("JOIN repetidos rep ON r.urlassinatura = rep.urlassinatura \n");
        sql.append("INNER JOIN clientesintetico c on c.codigo = r.cliente_codigo \n");
        sql.append("WHERE c.empresa = ").append(empresaZW).append(" \n");
        sql.append("ORDER by r.cliente_codigo, r.dataresposta desc");

        Map<Integer, JSONObject> mapAlunosAssinaturaParq = new HashMap<Integer, JSONObject>();
        List<String> listaLogRetorno = new ArrayList<>();
        listaLogRetorno.add("#### ALUNOS COM ASSINATURA REPETIDA ###############################################################");
        String assinaturaRepetida = "";
        String listaMatriculas = "";
        Integer qt = 0;

        try (ResultSet rs = parQDao.createStatement(ctx, sql.toString())) {
            while (rs.next()) {
                JSONObject objeto = new JSONObject();
                objeto.put("nome", rs.getString("nome"));
                objeto.put("matricula", rs.getString("matricula"));
                objeto.put("codigoclientetr", rs.getString("codigoclientetr"));
                objeto.put("codigoclientezw", rs.getString("codigoclientezw"));
                objeto.put("situacao", rs.getString("situacao"));
                objeto.put("codigoRespostaParq", rs.getString("codigo"));
                if (UteisValidacao.emptyString(assinaturaRepetida)) {
                    assinaturaRepetida = rs.getString("urlassinatura");
                }
                listaMatriculas += "," + rs.getString("matricula");
                listaLogRetorno.add(rs.getString("nome") + " - MAT: " + rs.getString("matricula") + " - SITUACAO: " + rs.getString("situacao"));
                mapAlunosAssinaturaParq.put(rs.getInt("codigoclientezw"), objeto);
                qt++;
            }
        }

        listaMatriculas = listaMatriculas.replaceFirst(",", "");
        System.out.println("MATRICULAS: " + listaMatriculas);
        listaLogRetorno.add("#### TOTAL: " + qt);
        listaLogRetorno.add("###################################################################################################");
        listaLogRetorno.add("");

        if (UteisValidacao.emptyString(listaMatriculas)) {
            listaLogRetorno.add("#### NÃO FOI LOCALIZADO NENHUM ALUNO COM ASSINATURA PARQ REPETIDA, PROCESSO ENCERRADO #############");
        } else {
            if (apenasValidar) {
                listaLogRetorno.add("#### ALUNOS COM ASSINATURA REPETIDA E POSSUEM CONTRATO ASSINADO ###############################");
            } else {
                listaLogRetorno.add("#### ALUNOS COM ASSINATURA DO CONTRATO REPLICADA PARA A ASSINATURA DO PARQ ####################");
            }

            // consulta para identificar contratos assinados dos alunos que estão com a assinatura parq repetida
            StringBuilder sqlZw = new StringBuilder();
            sqlZw.append("SELECT DISTINCT ON (cli.codigo) cli.codigo AS codigo_cliente, pes.nome, cli.codigomatricula, cad.assinatura, cad.lancamento,  con.datalancamento \n");
            sqlZw.append("FROM contratoassinaturadigital cad \n");
            sqlZw.append("INNER JOIN contrato con ON con.codigo = cad.contrato \n");
            sqlZw.append("INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n");
            sqlZw.append("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n");
            sqlZw.append("WHERE cli.codigomatricula IN (").append(listaMatriculas).append(") \n");
            sqlZw.append("ORDER BY cli.codigo, cad.lancamento DESC, con.datalancamento DESC");
            qt = 0;

            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlZw.toString(), conZW)) {
                    while (rs.next()) {
                        if (apenasValidar) {
                            listaLogRetorno.add("APENAS VALIDAÇÃO! - " + rs.getString("nome") + " - MAT: " + rs.getString("codigomatricula"));
                        } else {
                            try {
                                // atualizar a assinatura do parq com a assinatura do contrato
                                Integer codCliTR = Integer.valueOf(mapAlunosAssinaturaParq.get(rs.getInt("codigo_cliente")).getString("codigoclientetr"));
                                String assinaturaContrato = rs.getString("assinatura");
                                parQDao.executeNativeSQL(ctx, "update respostaclienteparq set urlassinatura = '" + assinaturaContrato + "' where cliente_codigo = " + codCliTR + " and urlassinatura = '" + assinaturaRepetida + "'");
                                listaLogRetorno.add("ASSINATURA REPLICADA COM SUCESSO! - " + rs.getString("nome") + " - MAT: " + rs.getString("codigomatricula"));
                            } catch (Exception e) {
                                listaLogRetorno.add("ERRO AO TENTAR ATUALIZAR A ASSINATURA DO: " + rs.getString("nome") + " - MAT: " + rs.getString("codigomatricula"));
                                Uteis.logar(e, ProcessosServiceImpl.class);
                            }
                        }
                        qt++;
                    }
                }
            }

            listaLogRetorno.add("#### TOTAL: " + qt);
            listaLogRetorno.add("###################################################################################################");

            if (!apenasValidar) {
                qt = 0;
                listaLogRetorno.add("");
                listaLogRetorno.add("#### ALUNOS COM ASSINATURA REPETIDA MAS NÃO POSSUEM CONTRATO ASSINADO PARA REPLICAR A ASSINATURA E NÃO FORAM ATUALIZADOS ###############################");
                try (ResultSet rs = parQDao.createStatement(ctx, sql.toString())) {
                    while (rs.next()) {
                        listaLogRetorno.add(rs.getString("nome") + " - MAT: " + rs.getString("matricula") + " - SITUACAO: " + rs.getString("situacao"));
                        qt++;
                    }
                }
                listaLogRetorno.add("#### TOTAL: " + qt);
                listaLogRetorno.add("###################################################################################################");
            }
        }

        System.out.println("FINALIZADO PROCESSO DE REPLICAR ASSINATURA CONTRATO PARA ASSINATURA PARQ REPETIDAS PARA CHAVE: " + ctx);
        return listaLogRetorno;
    }

    @Override
    public String forcarSincronizacaoFotoKeyPessoaZwTr(String ctx) throws Exception {
        Uteis.logarDebug("#### INICIANDO PROCESSO DE SINCRONIZAR FOTO PESSOA ENTRE ZW E TREINO PARA CHAVE: " + ctx);
        // consultar todos registros de pessoa que fotokey não seja null e não seja vazio
        StringBuilder sqlZw = new StringBuilder();
        sqlZw.append(
                "select p.nome, p.fotokey, c.codigo as codclientezw, c.codigomatricula \n" +
                "from pessoa p \n" +
                "inner join cliente c on c.pessoa = p.codigo \n" +
                "where p.fotokey is not null \n" +
                "and p.fotokey <> '';"
        );
        Integer qtTotal = 0;
        Integer qtSucesso = 0;
        Integer qtIgual = 0;
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rsZw = ConexaoZWServiceImpl.criarConsulta(sqlZw.toString(), conZW)) {
                while (rsZw.next()) {
                    qtTotal++;
                    try {
                        // consultar registro de pessoa do banco do treino pelo codigo de cliente do banco do zw
                        Integer codClienteZw = rsZw.getInt("codclientezw");
                        String fotoKeyPessoaZw = rsZw.getString("fotokey");

                        StringBuilder sqlTr = new StringBuilder();
                        sqlTr.append(
                                "select p.codigo as codPessoaTR, p.fotokey\n" +
                                "from pessoa p \n" +
                                "inner join clientesintetico c on c.pessoa_codigo = p.codigo\n" +
                                "where c.codigocliente = " + codClienteZw + "; \n"
                        );

                        try (ResultSet rsTR = pessoaDao.createStatement(ctx, sqlTr.toString())) {
                            while (rsTR.next()) {
                                Integer codPessoaTr = rsTR.getInt("codPessoaTR");
                                String fotoKeyPessoaTr = rsTR.getString("fotokey");
                                // se for diferente atualizar com a fotoKeyPessoaZw no banco do treino
                                if (!fotoKeyPessoaZw.equals(fotoKeyPessoaTr)) {
                                    pessoaDao.executeNativeSQL(ctx, "update pessoa set fotokey = '" + fotoKeyPessoaZw + "' where codigo = " + codPessoaTr);
                                    qtSucesso++;
                                } else {
                                    qtIgual++;
                                }
                            }
                        }

                    } catch (Exception e) {
                        Uteis.logar(e, ProcessosServiceImpl.class);
                    }
                }
            }
        }

        Uteis.logarDebug("#### FINALIZANDO PROCESSO DE SINCRONIZAR FOTO PESSOA ENTRE ZW E TREINO PARA CHAVE: " + ctx);
        return "Processo concluído! - Total pessoas zw verificado: " + qtTotal + " - Total sucesso: " + qtSucesso + " - Total fotokey igual entre zw e treino: " + qtIgual;
    }

    // ### INÍCIO PROCESSO PARA VERIFICAR E SINCRONIZAR ALUNOS ENTRE BANCO ZW E TREINO #################################
    @Override
    public SincronizacaoAlunosResultadoDTO validarSincronizacaoTodosAlunosZwTr(String ctx, Integer empresaIdZw, boolean considerarVisitante) throws Exception {
        if (SuperControle.independente(ctx)) {
            throw new ServiceException("Operação 'validarSincronizacaoTodosAlunosZwTr' não permitida para clientes de Treino Independente");
        }

        if (empresaIdZw == null) {
            throw new ServiceException("ID da empresa ZW não pode ser nulo");
        }

        long startTime = System.currentTimeMillis();
        Uteis.logarDebug("**********************************************************************************");
        Uteis.logarDebug("SINCRONIZACAO validarSincronizacaoTodosAlunosZwTr - Iniciando validacao para empresa: " + empresaIdZw + " e chave: " + ctx);

        // Contadores para o relatório final
        int totalVerificados = 0;
        int jaAtualizados = 0;
        int foramAtualizados = 0;
        int foramImportados = 0;
        List<String> logs = new ArrayList<>();

        try {
            // 1. Carregar todos os clientes do banco ZW em memória para performance
            Map<Integer, ClienteZWData> clientesZW = carregarClientesZW(ctx, empresaIdZw, considerarVisitante);

            // 2. Carregar todos os clientes do banco TR em memória para performance
            Map<Integer, ClienteTRData> clientesTR = carregarClientesTR(ctx, empresaIdZw);

            // 3. Carregar cache de professores específico da empresa para evitar conflitos entre bancos
            Map<Integer, Integer> cacheProfessores = carregarCacheProfessoresPorEmpresa(ctx, empresaIdZw);

            if (considerarVisitante) {
                Uteis.logarDebug("SINCRONIZACAO validarSincronizacaoTodosAlunosZwTr - Carregados " + clientesZW.size() +
                        " clientes ZW (todas situacoes) e " + clientesTR.size() + " clientes TR (todas situacoes) ");
            } else {
                Uteis.logarDebug("SINCRONIZACAO validarSincronizacaoTodosAlunosZwTr - Carregados " + clientesZW.size() +
                        " clientes ZW (sem visitantes) e " + clientesTR.size() + " clientes TR (com visitantes)");
            }

            // 4. Processar cada cliente ZW
            for (Map.Entry<Integer, ClienteZWData> entryZW : clientesZW.entrySet()) {
                Integer codigoCliente = entryZW.getKey();
                ClienteZWData clienteZW = entryZW.getValue();
                totalVerificados++;

                try {
                    // Verificar se existe no banco TR
                    ClienteTRData clienteTR = clientesTR.get(codigoCliente);

                    if (clienteTR == null) {
                        Uteis.logarDebug("Importando aluno zw para treino: #" + foramImportados + " - matricula: "  + clienteZW.matricula );
                        try {
                            // foram utilizados os mesmos paramêtros que são passados no processo antigo de importação de alunos
                            clienteSinteticoService.addAlunoV2(
                                    ctx,
                                    clienteZW.matricula,
                                    codigoCliente,
                                    clienteZW.colaborador,
                                    false,
                                    0,
                                    clienteZW.empresa,
                                    false
                            );
                            foramImportados++;
                            continue;
                        } catch (Exception e) {
                            logs.add("O aluno com matrícula " + clienteZW.matricula +
                                    " (código " + codigoCliente + ") existe no ZW mas não no TR e não foi importado com sucesso: " + e.getMessage());
                            continue;
                        }
                    }

                    // Verificar se precisa atualizar
                    String tipoAtualizacao = determinarTipoAtualizacao(clienteZW, clienteTR);

                    if (tipoAtualizacao != null) {
                        // Precisa atualizar
                        try {
                            sincronizarAlunoZwTrOtimizado(ctx, clienteTR.codigo, clienteZW.colaborador,
                                    clienteZW.situacao, tipoAtualizacao, cacheProfessores);
                            foramAtualizados++;
                        } catch (Exception e) {
                            logs.add("O aluno com matrícula " + clienteZW.matricula +
                                    " não teve a verificação/atualização concluída com sucesso: " + e.getMessage());
                        }
                    } else {
                        // Já está atualizado
                        jaAtualizados++;
                    }

                } catch (Exception e) {
                    logs.add("O aluno com matrícula " + clienteZW.matricula +
                            " não teve a verificação/atualização concluída com sucesso: " + e.getMessage());
                }
            }

            // 5. Montar resposta DTO
            SincronizacaoAlunosResultadoDTO resultado = new SincronizacaoAlunosResultadoDTO();
            resultado.setTotal(totalVerificados + " alunos foram verificados");
            resultado.setJaAtualizados(jaAtualizados + " alunos já estavam atualizados");
            resultado.setForamAtualizados(foramAtualizados + " alunos estavam desatualizados e foram atualizados com sucesso");
            resultado.setImportados(foramImportados + " alunos estavam fora do treino e foram importados com sucesso");
            resultado.setLogs(logs);

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            Uteis.logarDebug("SINCRONIZACAO validarSincronizacaoTodosAlunosZwTr - Concluida em " + duration + "ms. " +
                    "Total: " + totalVerificados + ", Atualizados: " + foramAtualizados +
                    ", Ja sincronizados: " + jaAtualizados + ", importados: " + foramImportados + ", Erros: " + logs.size());

            return resultado;

        } catch (Exception e) {
            Uteis.logar(e, ProcessosServiceImpl.class);
            throw new ServiceException("Erro durante sincronização validarSincronizacaoTodosAlunosZwTr: " + e.getMessage(), e);
        }
    }

    // Classes auxiliares para otimização de performance
    private static class ClienteZWData {
        Integer codigo;
        Integer matricula;
        Integer empresa;
        String situacao;
        Integer colaborador;

        ClienteZWData(Integer codigo, Integer matricula, Integer empresa, String situacao, Integer colaborador) {
            this.codigo = codigo;
            this.matricula = matricula;
            this.empresa = empresa;
            this.situacao = situacao;
            this.colaborador = colaborador;
        }
    }

    private static class ClienteTRData {
        Integer codigo;
        Integer codigoCliente;
        Integer matricula;
        Integer empresa;
        String situacao;
        Integer codigoColaborador;

        ClienteTRData(Integer codigo, Integer codigoCliente, Integer matricula, Integer empresa, String situacao, Integer codigoColaborador) {
            this.codigo = codigo;
            this.codigoCliente = codigoCliente;
            this.matricula = matricula;
            this.empresa = empresa;
            this.situacao = situacao;
            this.codigoColaborador = codigoColaborador;
        }
    }

    // Métod0 otimizado para carregar todos os clientes ZW
    private Map<Integer, ClienteZWData> carregarClientesZW(String ctx, Integer empresaIdZw, boolean considerarVisitante) throws Exception {
        Map<Integer, ClienteZWData> clientesZW = new HashMap<>();

        String sqlZW = "select c.codigo, c.codigomatricula, c.empresa, c.situacao, v.colaborador " +
                "from cliente c " +
                "left join vinculo v on v.cliente = c.codigo and v.tipovinculo = 'TW' " +
                "where c.empresa = ? " +
                (considerarVisitante ? "" : "and c.situacao <> 'VI' ") +
                "order by c.codigo, v.codigo desc";

        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             PreparedStatement ps = conZW.prepareStatement(sqlZW)) {

            ps.setInt(1, empresaIdZw);
            try (ResultSet rs = ps.executeQuery()) {
                Integer ultimoCodigo = null;

                while (rs.next()) {
                    Integer codigo = rs.getInt("codigo");

                    // Como ordenamos por codigo e v.codigo desc, pegamos apenas o primeiro vínculo (mais recente)
                    if (ultimoCodigo == null || !ultimoCodigo.equals(codigo)) {
                        ClienteZWData cliente = new ClienteZWData(
                                codigo,
                                rs.getInt("codigomatricula"),
                                rs.getInt("empresa"),
                                rs.getString("situacao"),
                                (Integer) rs.getObject("colaborador")
                        );
                        clientesZW.put(codigo, cliente);
                        ultimoCodigo = codigo;
                    }
                }
            }
        }

        return clientesZW;
    }

    // Métod0 otimizado para carregar todos os clientes TR
    private Map<Integer, ClienteTRData> carregarClientesTR(String ctx, Integer empresaIdZw) throws Exception {
        Map<Integer, ClienteTRData> clientesTR = new HashMap<>();

        String sqlTR = "select c.codigo, c.matricula, c.empresa, c.situacao, p.codigocolaborador, c.codigocliente " +
                "from clientesintetico c " +
                "left join professorsintetico p on p.codigo = c.professorsintetico_codigo " +
                "where c.empresa = ?";

        try (Connection conn = getClienteSinteticoDao().getConnection(ctx);
             PreparedStatement ps = conn.prepareStatement(sqlTR)) {

            ps.setInt(1, empresaIdZw);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    Integer codigoCliente = rs.getInt("codigocliente");

                    ClienteTRData cliente = new ClienteTRData(
                            rs.getInt("codigo"),
                            rs.getInt("codigocliente"),
                            rs.getInt("matricula"),
                            rs.getInt("empresa"),
                            rs.getString("situacao"),
                            (Integer) rs.getObject("codigocolaborador")
                    );
                    clientesTR.put(codigoCliente, cliente);
                }
            }
        }

        return clientesTR;
    }

    // Métod0 para determinar o tipo de atualização necessária
    private String determinarTipoAtualizacao(ClienteZWData clienteZW, ClienteTRData clienteTR) {
        boolean situacaoDesatualizada = false;
        boolean vinculoDesatualizado = false;

        // Comparar situação
        String situacaoZW = clienteZW.situacao != null ? clienteZW.situacao : "";
        String situacaoTR = clienteTR.situacao != null ? clienteTR.situacao : "";
        if (!situacaoZW.equals(situacaoTR)) {
            situacaoDesatualizada = true;
        }

        // Comparar colaborador/professor
        String colaboradorZWStr = clienteZW.colaborador != null ? clienteZW.colaborador.toString() : "";
        String colaboradorTRStr = clienteTR.codigoColaborador != null ? clienteTR.codigoColaborador.toString() : "";
        if (!colaboradorZWStr.equals(colaboradorTRStr)) {
            vinculoDesatualizado = true;
        }

        // Determinar tipo de atualização
        if (situacaoDesatualizada && vinculoDesatualizado) {
            return "AMBOS";
        } else if (situacaoDesatualizada) {
            return "SITUACAO";
        } else if (vinculoDesatualizado) {
            return "VINCULO_PROFESSOR";
        }

        return null; // Não precisa atualizar
    }

    // Versão otimizada do métod0 de sincronização para processamento em lote
    private void sincronizarAlunoZwTrOtimizado(String ctx, Integer codigoClienteSintetico, Integer codColaborador,
                                               String situacaoAluno, String tipoAtualizacao,
                                               Map<Integer, Integer> cacheProfessores) throws Exception {

        Integer codProfessorTW = null;

        // Buscar professor apenas se necessário
        if ((tipoAtualizacao.equalsIgnoreCase("VINCULO_PROFESSOR") || tipoAtualizacao.equalsIgnoreCase("AMBOS"))) {
            if (codColaborador != null) {
                codProfessorTW = buscarCodigoProfessorPorColaborador(codColaborador, cacheProfessores);
            }
            // Se codColaborador é null, codProfessorTW permanece null (remove vínculo)
        }

        // Executar atualização
        executarAtualizacaoOtimizada(ctx, codigoClienteSintetico, codProfessorTW, situacaoAluno, tipoAtualizacao);
    }

    // Métod0 otimizado para carregar cache de professores por empresa (evita conflitos entre bancos)
    private Map<Integer, Integer> carregarCacheProfessoresPorEmpresa(String ctx, Integer empresaIdZw) throws Exception {
        Map<Integer, Integer> cacheProfessores = new HashMap<>();

        String sqlProfessores = "SELECT ps.codigo, ps.codigocolaborador " +
                "FROM professorsintetico ps " +
                "INNER JOIN empresa e ON e.codigo = ps.empresa_codigo " +
                "WHERE e.codzw = ? AND ps.codigocolaborador IS NOT NULL";

        try (Connection conn = getClienteSinteticoDao().getConnection(ctx);
             PreparedStatement ps = conn.prepareStatement(sqlProfessores)) {

            ps.setInt(1, empresaIdZw);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    Integer codigoProfessor = rs.getInt("codigo");
                    Integer codigoColaborador = rs.getInt("codigocolaborador");
                    cacheProfessores.put(codigoColaborador, codigoProfessor);
                }
            }
        }

        Uteis.logarDebug("SINCRONIZACAO validarSincronizacaoTodosAlunosZwTr - Cache de professores carregado: " + cacheProfessores.size() + " professores");
        return cacheProfessores;
    }

    // Métod0 para buscar código do professor usando o cache local da empresa
    private Integer buscarCodigoProfessorPorColaborador(Integer codColaborador, Map<Integer, Integer> cacheProfessores) throws Exception {
        Integer codigoProfessor = cacheProfessores.get(codColaborador);

        if (codigoProfessor == null) {
            throw new ServiceException("Professor sintético não encontrado para o código do colaborador: " + codColaborador);
        }

        return codigoProfessor;
    }

    // Métod0 otimizado para executar atualizações
    private void executarAtualizacaoOtimizada(String ctx, Integer codigoClienteSintetico, Integer codProfessorTW,
                                              String situacaoAluno, String tipoAtualizacao) throws Exception {

        try (Connection conn = getClienteSinteticoDao().getConnection(ctx)) {
            String sql = "";

            switch (tipoAtualizacao.toUpperCase()) {
                case "SITUACAO":
                    sql = "UPDATE clientesintetico SET situacao = ? WHERE codigo = ?";
                    try (PreparedStatement ps = conn.prepareStatement(sql)) {
                        ps.setString(1, situacaoAluno);
                        ps.setInt(2, codigoClienteSintetico);

                        int rowsAffected = ps.executeUpdate();
                        if (rowsAffected == 0) {
                            throw new ServiceException("Nenhum registro foi atualizado para o cliente: " + codigoClienteSintetico);
                        }
                    }
                    break;

                case "VINCULO_PROFESSOR":
                    sql = "UPDATE clientesintetico SET professorsintetico_codigo = ? WHERE codigo = ?";
                    try (PreparedStatement ps = conn.prepareStatement(sql)) {
                        if (codProfessorTW != null) {
                            ps.setInt(1, codProfessorTW);
                        } else {
                            ps.setNull(1, java.sql.Types.INTEGER);
                        }
                        ps.setInt(2, codigoClienteSintetico);

                        int rowsAffected = ps.executeUpdate();
                        if (rowsAffected == 0) {
                            throw new ServiceException("Nenhum registro foi atualizado para o cliente: " + codigoClienteSintetico);
                        }
                    }
                    break;

                case "AMBOS":
                    sql = "UPDATE clientesintetico SET professorsintetico_codigo = ?, situacao = ? WHERE codigo = ?";
                    try (PreparedStatement ps = conn.prepareStatement(sql)) {
                        if (codProfessorTW != null) {
                            ps.setInt(1, codProfessorTW);
                        } else {
                            ps.setNull(1, java.sql.Types.INTEGER);
                        }
                        ps.setString(2, situacaoAluno);
                        ps.setInt(3, codigoClienteSintetico);

                        int rowsAffected = ps.executeUpdate();
                        if (rowsAffected == 0) {
                            throw new ServiceException("Nenhum registro foi atualizado para o cliente: " + codigoClienteSintetico);
                        }
                    }
                    break;

                default:
                    throw new ServiceException("Tipo de atualização inválido: " + tipoAtualizacao);
            }
        }
    }

    // ### FIM PROCESSO PARA VERIFICAR E SINCRONIZAR ALUNOS ENTRE BANCO ZW E TREINO ####################################


}

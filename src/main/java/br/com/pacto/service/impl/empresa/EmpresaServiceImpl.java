/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.empresa;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.base.jpa.MigracaoDadosJPAService;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.EmpresaBasicaResponseTO;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.colaborador.EmailTO;
import br.com.pacto.bean.colaborador.SituacaoColaboradorEnum;
import br.com.pacto.bean.colaborador.TelefoneTO;
import br.com.pacto.bean.colaborador.TipoUsuarioColaboradorEnum;
import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.empresa.CreateClientDTO;
import br.com.pacto.controller.json.empresa.EmpresaDTO;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.controller.json.empresa.ResponseActiveEmpresaDTO;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.objeto.Aplicacao;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.LoginService;
import br.com.pacto.security.service.SessaoService;
import org.apache.http.client.methods.CloseableHttpResponse;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.EmpresaExcecoes;
import br.com.pacto.service.impl.usuario.UsuarioServiceImpl;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.empresa.ConfiguracaoConvenioEmpresaVO;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.memcached.CachedManagerInterfaceFacade;
import br.com.pacto.service.intf.perfil.PerfilService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.JSFUtilities;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import servicos.integracao.adm.AdmWSConsumer;
import servicos.integracao.adm.client.EmpresaWS;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@Service
@Qualifier(value = "empresaService")
public class EmpresaServiceImpl implements EmpresaService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private MigracaoDadosJPAService migracaoService;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private ProfessorSinteticoService pfService;
    @Autowired
    private PerfilService perfilService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private CachedManagerInterfaceFacade memcached;
    @Autowired
    private ConexaoZWService conexaoZWService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public EmpresaDao getEmpresaDao() {
        return empresaDao;
    }

    @Override
    public EmpresaJSON validarChave(final String ctx) throws ServiceException {
        try {
            EmpresaJSON empresa = new EmpresaJSON();
            Map<String, Object> dados = EntityManagerFactoryService.getPropsKey(ctx);
            empresa.setId(dados.get("chave").toString());
            empresa.setNome((String) dados.get("nomeBD").toString());
            empresa.setNome(empresa.getNome().replace("bdmusc", "").toUpperCase());
            empresa.setTelefone("");
            return empresa;
        } catch (Exception ex) {
            Logger.getLogger(EmpresaServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void persistirEmpresasZW(final String ctx, List<EmpresaWS> empresas) throws ServiceException {
        if (empresas != null && !empresas.isEmpty()) {
            for (EmpresaWS eWS : empresas) {
                Empresa e = obterPorIdZW(ctx, eWS.getCodigo());
                if (e == null) {
                    e = new Empresa();
                    e = Empresa.copyAttributes(e, eWS);
                    inserir(ctx, e);
                } else {
                    e = Empresa.copyAttributes(e, eWS);
                    alterar(ctx, e);
                }
            }
        }
    }

    @Override
    public Empresa persistirEmpresasZW(final String ctx, final Integer idEmpresaWS,
            final Integer idUsuarioZW) throws ServiceException {
        if (idEmpresaWS != null && idEmpresaWS.intValue() > 0
                && idUsuarioZW != null && idUsuarioZW.intValue() > 0) {
            if (obterPorIdZW(ctx, idEmpresaWS) == null) {
                UtilContext.getBean(AdmWSConsumer.class).obterEmpresasZW(ctx, idUsuarioZW);
            }
            return obterPorIdZW(ctx, idEmpresaWS);
        }
        return null;
    }

    @Override
    public Empresa inserir(final String ctx, Empresa object) throws ServiceException {
        try {
            validarDados(ctx, object);
            return getEmpresaDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Empresa alterar(final String ctx, Empresa object) throws ServiceException {
        try {
            validarDados(ctx, object);
            return getEmpresaDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public EmpresaDTO alterarEmpDTO(final String ctx, EmpresaDTO object) throws ServiceException {
        try {
            if ( object.getKeyImgEmpresa() != null && !object.getKeyImgEmpresa().equals("") ) {
                object.setKeyImgEmpresa(uploadLogoEmp(ctx, object.getCodigo() ,object.getKeyImgEmpresa()));
            } else if ( object.getKeyImgEmpresa() == null || object.getKeyImgEmpresa().equals("") ) {
                deletLastLogoEmp(ctx, object);
            }
            Empresa empresa = toEmpresa(ctx, object);
            validarDados(ctx, empresa);
            empresa = getEmpresaDao().update(ctx, empresa);
            object = toEmpresaDTO(empresa);

            return object;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Empresa obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getEmpresaDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public EmpresaDTO obterPorIdEmpDTO(final String ctx, Integer id) throws ServiceException {
        try {
            Empresa empresa = getEmpresaDao().findById(ctx, id);
            EmpresaDTO empresaDTO = toEmpresaDTO(empresa);

            return empresaDTO;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Empresa toEmpresa(final String ctx, EmpresaDTO empresaDTO) throws ServiceException {
        try {
            Empresa empresa = getEmpresaDao().findById(ctx, empresaDTO.getCodigo());
            empresa.setNome(empresaDTO.getNome());
            empresa.setTimeZoneDefault(empresaDTO.getTimeZoneDefault());
            empresa.setKeyImgEmpresa(empresaDTO.getKeyImgEmpresa());

            return empresa;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public EmpresaDTO toEmpresaDTO(Empresa empresa) throws ServiceException {
        try {
            EmpresaDTO empresaDTO = new EmpresaDTO();
            empresaDTO.setCodigo(empresa.getCodigo());
            empresaDTO.setNome(empresa.getNome());
            empresaDTO.setTimeZoneDefault(empresa.getTimeZoneDefault());

            if ( empresa.getKeyImgEmpresa() != null && !empresa.getKeyImgEmpresa().equals("") ) {
                empresaDTO.setKeyImgEmpresa("https://cdn1.pactorian.net/".concat(empresa.getKeyImgEmpresa()));
            } else {
                empresaDTO.setKeyImgEmpresa("");
            }

            return empresaDTO;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public String uploadLogoEmp(final String key, int codigo, String img) throws Exception {
        Empresa empresa = getEmpresaDao().findById(key, codigo);
        img = img.replace("hhttps://cdn1.pactorian.net/", "");
        String result = img;
        String lastKey = empresa.getKeyImgEmpresa() != null ? empresa.getKeyImgEmpresa() : "";

        if ( !lastKey.toLowerCase().equals(img.toLowerCase()) ) {
            byte[] imgEmp = Base64.decodeBase64(img);

            try {
                if ( (imgEmp != null && !imgEmp.equals("")) && (lastKey != null && !lastKey.equals("")) ) {
                    deleteLogoEmpresa(lastKey);
                }
                result = (salvarLogoEmpresaComByte(key, imgEmp, empresa.getCodigo().toString().concat("empresa").concat(String.valueOf(Calendario.hoje().getTime()))));
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        }

        return result;
    }

    public void deletLastLogoEmp(final String key, EmpresaDTO empresaDTO) throws Exception {
        Empresa empresa = getEmpresaDao().findById(key, empresaDTO.getCodigo());
        String lastKey = empresa.getKeyImgEmpresa();

        if ( lastKey != null && !lastKey.equals("") ) {
            deleteLogoEmpresa(lastKey);
        }
    }

    public String salvarLogoEmpresaComByte(String key, byte[] obj, String identificador) throws Exception {
        return MidiaService.getInstanceWood().uploadObjectFromByteArray(key, MidiaEntidadeEnum.FOTO_EMPRESA, identificador, obj);
    }

    public void deleteLogoEmpresa(final String key) throws ServiceException {
        try {
            MidiaService.getInstanceWood().deleteObject(key);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public Empresa obterPorIdZW(final String ctx, Integer id) throws ServiceException {
        try {
            return getEmpresaDao().findObjectByAttribute(ctx, "codZW", id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Empresa> obterTodos(final String ctx) throws ServiceException {
        try {
            return getEmpresaDao().findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(String ctx, Empresa object) throws ServiceException {
        try {
            getEmpresaDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    
    public Empresa obterEmpresaTreinoIndependente(final String ctx) throws ServiceException {
        try {
            List<Empresa> empresas = obterTodos(ctx);
            if(empresas == null || empresas.isEmpty()){
                Empresa e = empresaDao.insert(ctx, new Empresa("EMPRESA"));
                e.setCodZW(e.getCodigo());
                return e;
            }else{
                return empresas.get(0);
            }
            
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<EmpresaBasicaResponseTO> listarEmpresas() throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<Empresa> empresas = empresaDao.findAll(ctx);
            List<EmpresaBasicaResponseTO> ret = new ArrayList<>();
            for (Empresa empresa : empresas) {
                ret.add(new EmpresaBasicaResponseTO(empresa));
            }
            return ret;
        } catch (Exception ex) {
            throw new ServiceException(EmpresaExcecoes.ERRO_BUSCAR_EMPRESAS, ex);
        }
    }

    public void validarDados(final String ctx, Empresa object) throws ServiceException {
        if(SuperControle.independente(ctx)) {
            if ( StringUtils.isBlank(object.getNome())) {
                if (!JSFUtilities.isJSFContext()) {
                    throw new ServiceException(EmpresaExcecoes.VALIDACAO_NOME_EMPRESAS);
                }
                throw new ValidacaoException("validacao.nome");
            }

            if (StringUtils.isBlank(object.getTimeZoneDefault())) {
                if (!JSFUtilities.isJSFContext()) {
                    throw new ServiceException(EmpresaExcecoes.VALIDACAO_TIME_ZONE_EMPRESAS);
                }
                throw new ValidacaoException("validacao.timezone");
            }
        }
    }

    @Override
    public void inicializarEmpresa(String ctx, String nomeEmpresa, ColaboradorTO colaborador,
                                   HttpServletRequest request) throws ServiceException{
        try {
            migracaoService.atualizar(ctx);

            Integer perfil = null;
            List<Perfil> perfis = perfilService.obterTodos(ctx);
            for(Perfil p : perfis){
                if(p.getNome().equals("COORDENADOR")){
                    atualizarPermissoesDeAcessoCoordenador(ctx, p);
                    perfil = p.getCodigo();
                }
            }

            colaborador.setPerfilUsuarioID(perfil == null ? perfis.get(0).getCodigo() : perfil);

            Empresa empresa = new Empresa();
            List<Empresa> empresas = empresaDao.findAll(ctx);
            if (UteisValidacao.emptyList(empresas)) {
                empresa.setNome(nomeEmpresa);
                empresa.setEmail(colaborador.getAppUserName());
                empresa = empresaDao.insert(ctx, empresa);

                empresa.setCodZW(empresa.getCodigo());
                empresa = empresaDao.update(ctx, empresa);
            } else {
                empresa = empresas.get(0);
            }

            pfService.cadastrarColaborador(ctx, colaborador, empresa.getCodigo(), request);
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private void atualizarPermissoesDeAcessoCoordenador(String ctx, Perfil perfilCoordenador) throws ServiceException {

        List<Permissao> permissoes = new ArrayList<>();
        for (RecursoEnum recurso : RecursoEnum.values()) {
            boolean existe = false;
            for (Permissao permissao : perfilCoordenador.getPermissoes()) {
                if (permissao.getRecurso().equals(recurso)) {
                    existe = true;
                }
            }
            if (!existe) {
                permissoes.add(new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            }
        }

        perfilCoordenador.getPermissoes().addAll(permissoes);


        perfilService.alterar(ctx, perfilCoordenador);
    }

    @Override
    public ResponseActiveEmpresaDTO ativarEmpresa(String ctx, CreateClientDTO colaborador,
                                                  HttpServletRequest request) throws ServiceException {
        try {
            ColaboradorTO colaboradorTO = new ColaboradorTO();
            colaboradorTO.setNome(colaborador.getNome());
            colaboradorTO.setUsarApp(true);
            colaboradorTO.setAppUserName(colaborador.getEmail());
            colaboradorTO.setAppPassword(colaborador.getSenha());
            colaboradorTO.setSituacao(SituacaoColaboradorEnum.ATIVO);
            colaboradorTO.setTipoUsuario(TipoUsuarioColaboradorEnum.COORDENADOR);
            colaboradorTO.getEmails().add(new EmailTO(colaborador.getEmail()));
            if (StringUtils.isNotBlank(colaborador.getCelular())) {
                colaboradorTO.getFones().add(new TelefoneTO(colaborador.getCelular()));
            }
            inicializarEmpresa(ctx, "Empresa", colaboradorTO, request);

            ResponseActiveEmpresaDTO ret = new ResponseActiveEmpresaDTO();
            ret.setToken(UtilContext.getBean(LoginService.class).login(ctx, colaboradorTO.getAppUserName(), colaboradorTO.getAppPassword(), true));
            ret.setEmpresaId(obterEmpresaTreinoIndependente(ctx).getCodigo());

            return ret;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(EmpresaExcecoes.ERRO_ATIVAR_EMPRESA, e);
        }

    }

    @Override
    public String health(String chave) throws Exception {
        return empresaDao.health(chave);
    }

    @Override
    public Empresa obterPorCodFinanceiro(final String ctx, Integer codFinanceiro) throws ServiceException {
        try {
            return getEmpresaDao().findObjectByAttribute(ctx, "cod_empresafinanceiro", codFinanceiro);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public String obterFusoHorarioEmpresa(String ctx, Integer empresa) throws ServiceException {
        String timeZone = null;
        if(UteisValidacao.emptyNumber(empresa)){ // pega o menor hora para verificação. Isso porque o retira ficha não avalia empresa
            List<Empresa> listaEmpresas = obterTodos(ctx);
            for(Empresa empresaVO: listaEmpresas){
                if(timeZone == null){
                    timeZone = empresaVO.getTimeZoneDefault();
                } else{
                    if(empresaVO.getTimeZoneDefault() != null){
                        if(Calendario.menorComHora(Calendario.hoje(empresaVO.getTimeZoneDefault()), Calendario.hoje(timeZone))){
                            timeZone = empresaVO.getTimeZoneDefault();
                        }
                    }
                }
            }

        } else {
            timeZone = EntityManagerFactoryService.getTimeZoneEmpresa(ctx,empresa);
            if(timeZone == null) {
                Empresa empresaZW = obterPorIdZW(ctx, empresa);
                if(empresaZW.getTimeZoneDefault() != null && !empresaZW.getTimeZoneDefault().isEmpty()){
                    timeZone = empresaZW.getTimeZoneDefault();
                    EntityManagerFactoryService.setTimeZoneEmpresa(ctx,empresa, timeZone);
                }

            }
        }
        return timeZone == null ? TimeZoneEnum.Brazil_East.getId() : timeZone;
    }

    public void reverterAlteracoesProjetoUsuario(String ctx) throws Exception{
        String sql = "update configgympass set usuariolancou_codigo = (select codigo from usuario where usuariozw = usuariolancou_codigo limit 1);\n" +
                "update avaliacaofisica set responsavellancamento_codigo = (select codigo from usuario where usuariozw = responsavellancamento_codigo limit 1);\n" +
                "update itemavaliacaofisica set responsavellancamento_codigo = (select codigo from usuario where usuariozw = responsavellancamento_codigo limit 1);\n" +
                "update pesoosseo set responsavellancamento_codigo = (select codigo from usuario where usuariozw = responsavellancamento_codigo limit 1);\n" +
                "update auladiaexclusao set usuario_codigo = (select codigo from usuario where usuariozw = usuario_codigo limit 1);\n" +
                "update alunofavorito set usuario_codigo = (select codigo from usuario where usuariozw = usuario_codigo limit 1);\n" +
                "update clienteobservacao set usuario_codigo =  (select codigo from usuario where usuariozw = usuario_codigo limit 1);\n" +
                "update liberacaocheckin set usuarioliberou_codigo = (select codigo from usuario where usuariozw = usuarioliberou_codigo limit 1); \n" +
                "update agendamento set usuarioultalteracao_codigo = (select codigo from usuario where usuariozw = usuarioultalteracao_codigo limit 1); \n" +
                "update configuracaosistemausuario set usuario_codigo = (select codigo from usuario where usuariozw = usuario_codigo limit 1); \n" +
                "update professorsubstituido set usuariosubstituiu_codigo = (select codigo from usuario where usuariozw = usuariosubstituiu_codigo limit 1); \n" +
                "update linkpredefinido set usuario_codigo = (select codigo from usuario where usuariozw = usuario_codigo limit 1); \n" +
                "update geracaorankingprofessores set responsavellancamento_codigo = (select codigo from usuario where usuariozw = responsavellancamento_codigo limit 1); \n" +
                "update versao set numero = 103;";

        empresaDao.executeNative(ctx, sql);
    }

    @Override
    public Empresa obterPorAributo(String ctx, String atributo, Object valor) throws ServiceException {
        try {
            String identificador = "empresa-".concat(atributo).concat("-").concat(valor.toString().replaceAll(" ", "0_"));
            Empresa empresa = memcached.ler(ctx, identificador);
            if (empresa != null) {
                return empresa;
            }
            empresa = getEmpresaDao().findObjectByAttribute(ctx, atributo, valor);

            if (empresa != null) {
                cachearEmpresa(ctx, identificador, empresa);
            }
            return empresa;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<String> obterTotens(String ctx, Integer empresa) throws Exception {
        List<String> totens = new ArrayList<String>();
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String sql = "SELECT totem FROM ConfiguracaoEmpresaTotem";
            if(empresa != null){
                sql += " where empresa = " + empresa;
            }
            sql += " group by totem";
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {
                while (rs.next()) {
                    totens.add(rs.getString("totem"));
                }
            }
        }
        return totens;
    }

    @Override
    public ConfiguracaoConvenioEmpresaVO obterConfiguracaoConvenioEmpresa(String ctx, Integer codEmpresa) throws Exception {
        ConfiguracaoConvenioEmpresaVO configuracaoConvenioEmpresaVO = new ConfiguracaoConvenioEmpresaVO();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM empresa e ");
        sql.append("WHERE e.codigo = ").append(codEmpresa);

        try(Connection conZW = conexaoZWService.conexaoZw(ctx)){
            try(ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)){
                if(rs.next()){
                    configuracaoConvenioEmpresaVO.setGeraAutorizacaoCobrancaAutomaticaBloqueada(rs.getBoolean("gerarautcobrancacomcobautbloqueada"));

                    Integer convenioCobrancaCartao = rs.getInt("conveniocobrancacartao");
                    Integer convenioCobrancaPix = rs.getInt("conveniocobrancapix");
                    Integer convenioCobrancaBoleto = rs.getInt("conveniocobrancaboleto");


                    StringBuilder sqlConvenio = new StringBuilder();
                    sqlConvenio.append("SELECT * FROM conveniocobranca c ");
                    sqlConvenio.append("WHERE c.codigo IN (").append(convenioCobrancaCartao).append(",").append(convenioCobrancaPix).append(",").append(convenioCobrancaBoleto).append(")");

                    try(ResultSet rsConvenio = ConexaoZWServiceImpl.criarConsulta(sqlConvenio.toString(), conZW)){
                        while(rsConvenio.next()){
                            if(rsConvenio.getInt("codigo") == convenioCobrancaCartao){
                                configuracaoConvenioEmpresaVO.setConvenioCartaoDeCredito(rsConvenio.getString("descricao"));
                            }else if(rsConvenio.getInt("codigo") == convenioCobrancaPix){
                                configuracaoConvenioEmpresaVO.setConvenioPix(rsConvenio.getString("descricao"));
                            }else if(rsConvenio.getInt("codigo") == convenioCobrancaBoleto){
                                configuracaoConvenioEmpresaVO.setConvenioBoleto(rsConvenio.getString("descricao"));
                            }
                        }
                    }
                }
            }
        }

        return configuracaoConvenioEmpresaVO;
    }

    private void cachearEmpresa(String ctx, String identificador, Empresa empresa) {
        try {
            memcached.gravar(ctx, identificador, empresa);
        } catch (Exception e) {
            Uteis.logar(e, UsuarioServiceImpl.class);
        }
    }

    public List<EmpresaBasicaResponseTO> obterUnidadesAtivas() throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<Empresa> empresas = empresaDao.findAll(ctx);
        List<EmpresaBasicaResponseTO> unidadesAtivas = new ArrayList<>();

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String sql = "SELECT codigo, nome FROM empresa WHERE ativa = true";

            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {
                while (rs.next()) {
                    int codigoZw = rs.getInt("codigo");
                    String nomeZw = rs.getString("nome");

                    for (Empresa empresa : empresas) {
                        if (empresa.getCodZW() != null && empresa.getCodZW().equals(codigoZw)) {
                            EmpresaBasicaResponseTO unidade = new EmpresaBasicaResponseTO();
                            unidade.setId(empresa.getCodZW());
                            unidade.setNome(nomeZw);
                            unidadesAtivas.add(unidade);
                            break;
                        }
                    }
                }
            }
        }
        return unidadesAtivas;
    }

    public String obterSiglaEstado(String chave, int empresa) {
        try {
            String endpoint = "/prest/estado?&key=" + chave + "&empresa=" + empresa + "&op=sigla";
            JSONObject jsonObject = chamadaZW(chave, endpoint, empresa, null);
            return jsonObject.getJSONObject("return").getString("sigla");
        } catch (Exception ex) {
            return "";
        }
    }

    public JSONObject chamadaZW(String chave, String endpoint, Integer empresa, List<NameValuePair> parametros) throws Exception {
        final String url = Aplicacao.getProp(chave, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        if (parametros != null) {
            params = parametros;
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    @Override
    public List<Empresa> obterTodosComCodZW(final String ctx) throws ServiceException {
        try {
            return getEmpresaDao().findListByAttributes(ctx, new String[]{"codzw > "},
                    new Object[]{0}, "codigo", 0);

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

}

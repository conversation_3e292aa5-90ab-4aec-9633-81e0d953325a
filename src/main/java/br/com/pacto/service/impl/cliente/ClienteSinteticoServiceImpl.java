/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.cliente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.AvaliacaoProfessor;
import br.com.pacto.bean.cliente.AcaoAlunoEnum;
import br.com.pacto.bean.cliente.AlunoCatalogoResponseTO;
import br.com.pacto.bean.cliente.AvaliacaoCatalogoAlunoDTO;
import br.com.pacto.bean.cliente.ClienteAcompanhamento;
import br.com.pacto.bean.cliente.ClienteAcompanhamentoAvaliacao;
import br.com.pacto.bean.cliente.ClienteAcompanhamentoJSON;
import br.com.pacto.bean.cliente.ClienteConsulta;
import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.bean.cliente.ClientePesquisa;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.cliente.HistoricoNivelCliente;
import br.com.pacto.bean.cliente.HistoricoPresenca;
import br.com.pacto.bean.cliente.PerfilDISC;
import br.com.pacto.bean.cliente.RetrospectivaCache;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.pessoa.Email;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.OrigemExecucaoEnum;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamento;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.bean.usuario.StatusEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.controller.json.acompanhamento.ClienteAcompanhamentoAvaliacaoDTO;
import br.com.pacto.controller.json.agendamento.PerfilDISCDTO;
import br.com.pacto.controller.json.agendamento.PerfilDISCVO;
import br.com.pacto.controller.json.aluno.AlunoDTO;
import br.com.pacto.controller.json.aluno.AlunoObservacaoDTO;
import br.com.pacto.controller.json.aluno.AlunoOlympiaDTO;
import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import br.com.pacto.controller.json.aluno.AlunoZWDTO;
import br.com.pacto.controller.json.aluno.AlunoZWResponseDTO;
import br.com.pacto.controller.json.aluno.AtestadoResponseTO;
import br.com.pacto.controller.json.aluno.AvaliacaoAlunoDTO;
import br.com.pacto.controller.json.aluno.AvaliacaoAlunoResponseDTO;
import br.com.pacto.controller.json.aluno.AvaliacaoProfessorDTO;
import br.com.pacto.controller.json.aluno.AvaliacaoProfessorVO;
import br.com.pacto.controller.json.aluno.ClienteObservacaoAnexosDTO;
import br.com.pacto.controller.json.aluno.ClienteObservacaoDTO;
import br.com.pacto.controller.json.aluno.ContratoZWDTO;
import br.com.pacto.controller.json.aluno.DetalheTreinoAlunoDTO;
import br.com.pacto.controller.json.aluno.FiltroAlunoJSON;
import br.com.pacto.controller.json.aluno.FiltroLinhaTempoDTO;
import br.com.pacto.controller.json.aluno.LinhaDeTempoAlunoResponseDTO;
import br.com.pacto.controller.json.aluno.NivelAlunoResponseTO;
import br.com.pacto.controller.json.aluno.ObservacaoArquivoUploadDTO;
import br.com.pacto.controller.json.aluno.PlanoZWDTO;
import br.com.pacto.controller.json.aluno.ProgramaTreinoAlunoResponseDTO;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.aluno.SituacaoContratoZWEnum;
import br.com.pacto.controller.json.aluno.TelefoneDTO;
import br.com.pacto.controller.json.aluno.TipoContratoZWEnum;
import br.com.pacto.controller.json.aluno.UsuarioSimplesAlunoDTO;
import br.com.pacto.controller.json.aulaDia.AulaAlunoDTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.gestao.ClienteDadosTotalPassDTO;
import br.com.pacto.controller.json.gestao.ConfigTotalPassDTO;
import br.com.pacto.controller.json.gestao.LogTotalPassDTO;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.notificacao.PushMobileRunnable;
import br.com.pacto.controller.json.usuario.AlunoAppInfoDTO;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoProfessorDao;
import br.com.pacto.dao.intf.avaliacao.ParQDao;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.dao.intf.cliente.ClienteAcompanhamentoAvaliacaoDao;
import br.com.pacto.dao.intf.cliente.ClienteAcompanhamentoDao;
import br.com.pacto.dao.intf.cliente.ClienteObservacaoDao;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.cliente.HistoricoNivelClienteDao;
import br.com.pacto.dao.intf.cliente.HistoricoPresencaDao;
import br.com.pacto.dao.intf.cliente.PerfilDISCDao;
import br.com.pacto.dao.intf.cliente.RetrospectivaCacheDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.nivel.NivelDao;
import br.com.pacto.dao.intf.notificacao.NotificacaoDao;
import br.com.pacto.dao.intf.pessoa.PessoaDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoAndamentoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoFichaDao;
import br.com.pacto.dao.intf.serie.TreinoRealizadoDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.dao.intf.wod.ScoreTreinoDao;
import br.com.pacto.email.UteisEmail;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.cliente.StatusClienteEnum;
import br.com.pacto.enumerador.cliente.TiposAcompanhamentoEnum;
import br.com.pacto.enumerador.cliente.TiposConsultaCatalogoAlunoEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.aparelho.NivelServiceImpl;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.configuracoes.ConfiguracaoSistemaServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AlunoExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.CatalogoAlunoExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.ProgramaTreinoExcecoes;
import br.com.pacto.service.impl.programa.ProgramaTreinoServiceImpl;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.cliente.BrindesVO;
import br.com.pacto.service.intf.cliente.ClientePesquisaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.cliente.HistoricoPresencasVO;
import br.com.pacto.service.intf.cliente.RetrospectivaAnoVO;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.linhadotempo.LinhaDoTempoService;
import br.com.pacto.service.intf.memcached.CachedManagerInterfaceFacade;
import br.com.pacto.service.intf.notificacao.NotificacaoAulaAgendadaService;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.pessoa.PessoaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.treinoindependente.TreinoIndependenteService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.notificador.ServicoNotificacaoPush;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.UtilS3Base64Img;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import br.com.pacto.util.impl.AuditUtilities;
import br.com.pacto.util.impl.JSFUtilities;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.FiltrosLinhaTempo;
import br.com.pacto.util.json.LinhaDoTempoTO;
import br.com.pacto.util.json.TipoLinhaEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.hibernate.Hibernate;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.ui.ModelMap;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.ServletContextAware;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import servicos.integracao.zw.beans.UsuarioZW;
import servicos.integracao.zw.json.AddClienteJSON;
import servicos.integracao.zw.json.AtestadoClienteJSON;
import servicos.integracao.zw.json.ColetorJSON;
import servicos.integracao.zw.json.VinculoJSON;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.DatatypeConverter;
import javax.xml.ws.WebServiceException;
import java.io.IOException;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.security.SecureRandom;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static br.com.pacto.bean.configuracoes.ConfiguracoesEnum.HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR;
import static br.com.pacto.bean.configuracoes.ConfiguracoesEnum.TEMPO_APROVACAO_AUTOMATICA;
import static br.com.pacto.objeto.Aplicacao.totalPassApi;
import static br.com.pacto.objeto.Aplicacao.totalPassApiValidate;
import static br.com.pacto.objeto.Aplicacao.urlApiStaging;
import static br.com.pacto.objeto.Aplicacao.urlValidateStaging;
import static br.com.pacto.objeto.Uteis.incluirLog;
import static java.sql.DriverManager.getConnection;
import static org.apache.commons.lang3.StringUtils.isBlank;


/**
 * <AUTHOR>
 */
@Service
public class ClienteSinteticoServiceImpl implements ClienteSinteticoService, ServletContextAware {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    private ProgramaTreinoFichaDao programaTreinoFichaDao;
    @Autowired
    private ProgramaTreinoDao programaTreinoDao;
    @Autowired
    private TreinoRealizadoDao treinoRealizadoDao;
    @Autowired
    private AvaliacaoFisicaService avaliacaoFisicaService;
    @Autowired
    private ProgramaTreinoService programaService;
    @Autowired
    private HistoricoNivelClienteDao historicoNivelClienteDao;
    @Autowired
    private ClienteAcompanhamentoDao clienteAcompanhamentoDao;
    @Autowired
    private ScoreTreinoDao scoreTreinoDao;
    @Autowired
    private ProfessorSinteticoService professorSintetico;
    @Autowired
    private ClienteObservacaoDao clienteObservacaoDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private PessoaService pessoaService;
    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    private NotificacaoDao notificacaoDao;
    @Autowired
    private AvaliacaoProfessorDao avaliacaoProfessorDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private LinhaDoTempoService linhaDoTempoService;
    @Autowired
    private NivelDao nivelDao;
    @Autowired
    private TreinoIndependenteService independenteService;
    @Autowired
    private FotoService fotoService;
    @Autowired
    private PessoaDao pessoaDao;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    private ServletContext context;
    @Autowired
    private ClientePesquisaService clientePesquisaService;
    @Autowired
    private ProgramaTreinoService treinoService;
    @Autowired
    private CachedManagerInterfaceFacade memCachedService;
    @Autowired
    private ParQDao parQDao;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private ProfessorSinteticoDao professorSinteticoDao;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private LogDao logDao;
    @Autowired
    private NivelServiceImpl nivelService;
    @Autowired
    private PerfilDISCDao perfilDISCDao;;
    @Autowired
    private NotificacaoAulaAgendadaService notificacaoAulaAgendadaService;
    @Autowired
    private ProgramaTreinoDao programatreinoDao;
    @Autowired
    private RetrospectivaCacheDao retrospectivaCacheDao;
    @Autowired
    private ProgramaTreinoService programaTreinoService;
    @Autowired
    private ClienteAcompanhamentoAvaliacaoDao clienteAcompanhamentoAvaliacaoDao;
    @Autowired
    private HistoricoPresencaDao historicoPresencaDao;

    private static final Boolean ISVALIDATE = false;
    private static final String AUTORIZADO_TOTALPASS = "204";
    private static final String BLOQUEADO_TOTALPASS_CHECKIN = "422";


    Integer maxResults = 50;
    Integer indiceInicial = 0;

    public ClienteAcompanhamentoAvaliacaoDao getClienteAcompanhamentoAvaliacaoDao() {
        return this.clienteAcompanhamentoAvaliacaoDao;
    }

    public ProfessorSinteticoService getProfessorSintetico() {
        return professorSintetico;
    }

    public void setProfessorSintetico(ProfessorSinteticoService professorSintetico) {
        this.professorSintetico = professorSintetico;
    }

    public HistoricoNivelClienteDao getHistoricoNivelClienteDao() {
        return historicoNivelClienteDao;
    }

    public void setHistoricoNivelClienteDao(HistoricoNivelClienteDao historicoNivelClienteDao) {
        this.historicoNivelClienteDao = historicoNivelClienteDao;
    }

    public NotificacaoDao getNotificacaoDao() {
        return notificacaoDao;
    }

    public UsuarioDao getUsuarioDao() {
        return this.usuarioDao;
    }

    public AvaliacaoProfessorDao getAvaliacaoProfessorDao() {
        return avaliacaoProfessorDao;
    }

    public void setAvaliacaoProfessorDao(AvaliacaoProfessorDao avaliacaoProfessorDao) {
        this.avaliacaoProfessorDao = avaliacaoProfessorDao;
    }

    public ClienteAcompanhamentoDao getClienteAcompanhamentoDao() {
        return clienteAcompanhamentoDao;
    }

    public void setClienteAcompanhamentoDao(ClienteAcompanhamentoDao clienteAcompanhamentoDao) {
        this.clienteAcompanhamentoDao = clienteAcompanhamentoDao;
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public ClienteSinteticoDao getClienteSinteticoDao() {
        return this.clienteSinteticoDao;
    }

    public void setClienteSinteticoDao(ClienteSinteticoDao clienteSinteticoDao) {
        this.clienteSinteticoDao = clienteSinteticoDao;
    }

    private static final int MAXIMO_ITEMS_LINHA_DO_TEMPO = 100;

    public ClienteSintetico alterar(final String ctx, ClienteSintetico object) throws ServiceException {
        try {
            object.setVersao(object.getVersao() + 1);
            criarPessoaSimples(ctx, object);
            ClienteSintetico csUpdated = getClienteSinteticoDao().update(ctx, object);
            getClienteSinteticoDao().refresh(ctx, csUpdated);
            getClientePesquisaService().inserir(ctx, csUpdated);
            atualizarDadoSintetico(ctx, csUpdated);
            memCachedService.invalidarCache(ctx, null, object.getMatricula());
            return csUpdated;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void atualizarDadoSintetico(String ctx, ClienteSintetico c) {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "update clientesintetico c \n" +
                    "set terminoUltimoPrograma = (select max(dataterminoprevisto) from programatreino where cliente_codigo = c.codigo)" +
                    " where c.codigo = " + c.getCodigo());
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoService.class);
        }
    }

    public ClienteSintetico alterarAlgunsCampos(final String ctx,
                                                ClienteSintetico object, String[] campos, Object[] values) throws ServiceException {
        try {
            campos = ArrayUtils.add(campos, "versao");
            object.setVersao(object.getVersao() + 1);
            values = ArrayUtils.add(values, object.getVersao());
            getClienteSinteticoDao().updateAlgunsCampos(ctx, campos, values,
                    new String[]{"codigo"}, new Object[]{object.getCodigo()});
            memCachedService.invalidarCache(ctx, null, object.getMatricula());
            return object;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public void incrementarVersao(final String ctx,
                                  int codigoCliente) throws ServiceException {
        try {
            getClienteSinteticoDao().executeNativeSQL(ctx, "Update ClienteSintetico set versao = versao + 1 where codigoCliente = " + codigoCliente);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public ClienteSintetico alterar(final String ctx, ClienteSintetico clienteZW, ClienteSintetico clienteTreino) throws ServiceException {
        try {
            ProfessorSintetico profAntesAlteracao = professorSinteticoService.consultarProfessorCarteiraPorCliente(ctx, clienteTreino.getCodigo());
            //forçar atualizar o cache
            Integer codMatriculaAtual = clienteTreino.getMatricula();
            clienteTreino.setVersao(clienteTreino.getVersao() + 1);
            clienteTreino = getClienteSinteticoDao().update(ctx, clienteTreino);
            //salvar informações que foram incluídas pelo Treino
            clienteZW.setCodigo(clienteTreino.getCodigo());
            clienteZW.setNivelAluno(clienteTreino.getNivelAluno());
            clienteZW.setProfessorSintetico(clienteTreino.getProfessorSintetico());
            clienteZW.setGrupo(clienteTreino.getGrupo());
            clienteZW.setBadges(clienteTreino.getBadges());
            clienteZW.setObjetivos(clienteTreino.getObjetivos());
            clienteZW.setDadosAvaliacao(clienteTreino.getDadosAvaliacao());
            clienteZW.setVersao(clienteTreino.getVersao());
            clienteZW.setNrTreinosPrevistos(clienteTreino.getNrTreinosPrevistos());
            clienteZW.setNrTreinosRealizados(clienteTreino.getNrTreinosRealizados());
            clienteZW.setPessoa(clienteTreino.getPessoa());
            ClienteSintetico csUpdated = getClienteSinteticoDao().update(ctx, clienteZW);

            inserirLogAlteracaoProfessorAluno(ctx, profAntesAlteracao, clienteZW.getProfessorSintetico(), clienteZW.getMatricula(), "SINCRONIZAÇÃO ALUNO - ZW");

            getClienteSinteticoDao().refresh(ctx, csUpdated);
            if (!csUpdated.getMatricula().equals(codMatriculaAtual)) {
                ClientePesquisa clientePesquisa = getClientePesquisaService().consultarPorMatricula(ctx, codMatriculaAtual);
                if (clientePesquisa != null) {
                    clientePesquisaService.excluir(ctx, clientePesquisa);
                }
            }
            getClientePesquisaService().inserir(ctx, csUpdated);
            memCachedService.invalidarCache(ctx, null, csUpdated.getMatricula());
            return csUpdated;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, ClienteSintetico object) throws ServiceException {
        try {
            excluirHistoricoNivel(ctx, object.getCodigo());
            excluirNotificacao(ctx, object.getCodigo());
            excluirResultadosWods(ctx, object);
            excluirUsuario(ctx, object.getCodigo());
            getClienteSinteticoDao().delete(ctx, object);
            getClientePesquisaService().excluirPorMatricula(ctx, object.getMatricula());
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void excluirResultadosWods(String ctx, ClienteSintetico cs) throws Exception {
        List<ScoreTreino> resultados = scoreTreinoDao.findListByAttributes(ctx, new String[]{"usuario.cliente.codigo"}, new Object[]{cs.getCodigo()}, null, 1, 0);
        if (!UteisValidacao.emptyNumber(resultados.size())) {
            scoreTreinoDao.deleteComParam(ctx, new String[]{"usuario.codigo"}, new Object[]{resultados.get(0).getUsuario().getCodigo()});

        }

    }

    private void excluirUsuario(String ctx, Integer cliente) throws ServiceException {
        try {
            getUsuarioDao().deleteComParam(ctx, new String[]{"cliente.codigo"}, new Object[]{cliente});
        } catch (Exception ex) {
            throw new ServiceException(ex);

        }
    }

    private void excluirNotificacao(String ctx, Integer cliente) throws ServiceException {
        try {
            getNotificacaoDao().deleteComParam(ctx, new String[]{"cliente.codigo"}, new Object[]{cliente});
        } catch (Exception ex) {
            throw new ServiceException(ex);

        }
    }

    public ClienteSintetico inserir(final String ctx, ClienteSintetico object) throws ServiceException {
        return inserir(ctx, object, null);
    }

    public ClienteSintetico inserir(final String ctx, ClienteSintetico object, String fotoKey) throws ServiceException {
        try {
            if (object != null) {
                try {
                    Uteis.logar(null, "[ClienteSintetico.inserir] " + new JSONObject(object).toString());
                } catch (StackOverflowError | Exception e) {
                    Uteis.logarDebug("[ClienteSintetico.inserir] Erro ao gerar JSON do objeto para log: " + e.getClass().getSimpleName());
                }
            } else {
                Uteis.logar(null, "[ClienteSintetico.inserir] ClienteSintetico object is null");
            }

            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgAulasExperimentais = css.consultarPorTipo(ctx, ConfiguracoesEnum.NR_AULA_EXPERIMENTAL_ALUNO);
            Uteis.logar(null, "[ClienteSintetico.inserir] cfgAulasExperimentais: " + cfgAulasExperimentais);

            criarPessoaSimples(ctx, object);
            Uteis.logar(null, "[ClienteSintetico.inserir] criarPessoaSimples: ok...");

            object.setNrAulasExperimentais(cfgAulasExperimentais.getValorAsInteger());
            if (object.getPessoa() != null && !br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                object.getPessoa().setFotoKey(Aplicacao.saveImageLocal(context, ctx, fotoKey, false, null, false));
            }

            ClienteSintetico csADD = getClienteSinteticoDao().insert(ctx, object);
            Uteis.logar(null, "[ClienteSintetico.inserir] getClienteSinteticoDao().insert: ok...");
            getClientePesquisaService().inserir(ctx, csADD);
            Uteis.logar(null, "[ClienteSintetico.inserir] getClientePesquisaService().inserir: ok...");
            return csADD;
        } catch (Exception ex) {
            Logger.getLogger(ClienteSinteticoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex);
        }
    }

    public void criarPessoaSimples(final String ctx, ClienteSintetico object) throws Exception {
        if (object.getPessoa() == null || (object.getPessoa().getCodigo() == null || object.getPessoa().getCodigo() == 0)) {
            Pessoa pessoa = new Pessoa();
            pessoa.setNome(object.getNome());
            Email email = new Email(object.getEmail(), pessoa);
            pessoa.getEmails().add(email);
            Telefone telefone = new Telefone(object.getTelefones(), pessoa);
            pessoa.getTelefones().add(telefone);
            object.setPessoa(pessoaService.inserirPessoa(ctx, pessoa));
        }
    }

    public ClienteSintetico obterObjetoPorParam(final String ctx, String queryS, Map<String, Object> params)
            throws ServiceException {
        try {
            return getClienteSinteticoDao().findObjectByParam(ctx, queryS, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ClienteSintetico obterPorAtributo(final String ctx, final String atributo, final Object valor)
            throws ServiceException {
        try {
            return getClienteSinteticoDao().findObjectByAttribute(ctx, atributo, valor);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ClienteSintetico obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getClienteSinteticoDao().obterPorId(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClienteSintetico obterPorCodigoPessoaZW(final String ctx, Integer codigoPessoa) throws ServiceException {
        try {
            return getClienteSinteticoDao().findObjectByAttribute(ctx, "codigoPessoa", codigoPessoa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ClienteSintetico> obterPorParam(final String ctx, String queryS, Map<String, Object> params, Integer... index)
            throws ServiceException {
        try {
            return getClienteSinteticoDao().findByParam(ctx, queryS, params, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ClienteSintetico> obterPorParam(final String ctx, String queryS, Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getClienteSinteticoDao().findByParam(ctx, queryS, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ClienteAcompanhamento> obterClienteAcompanhamentoPorParam(final String ctx, String queryS, Map<String, Object> params, Integer... index)
            throws ServiceException {
        try {
            return getClienteAcompanhamentoDao().findByParam(ctx, queryS, params, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ClienteSintetico> obterTodos(final String ctx, final Integer empresaZW, final Integer... index) throws ServiceException {
        try {
            return getClienteSinteticoDao().findListByAttributes(ctx, new String[]{"empresa"}, new Integer[]{empresaZW}, "nome", index.length > 0 ? index[0] : 0, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ClienteSintetico> consultarPorProfessor(final String ctx, Integer professor,
                                                        Integer empresaZW, boolean apenasNome) throws ServiceException {
        try {
            if (empresaZW == null || empresaZW == 0) {
                return apenasNome ? getClienteSinteticoDao().findObjectsByAttributesSimple(ctx, new String[]{"codigo", "nome"},
                        new String[]{"professorSintetico.codigo"}, new Object[]{professor}, "nome", 0)
                        : getClienteSinteticoDao().findListByAttributes(ctx, new String[]{"professorSintetico.codigo"},
                        new Object[]{professor}, "nome", 0);
            }
            return apenasNome ? getClienteSinteticoDao().findObjectsByAttributesSimple(ctx, new String[]{"codigo", "nome"},
                    new String[]{"professorSintetico.codigo", "empresa"}, new Object[]{professor, empresaZW},
                    "nome", 0)
                    : getClienteSinteticoDao().findListByAttributes(ctx,
                    new String[]{"professorSintetico.codigo", "empresa"}, new Object[]{professor, empresaZW}, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    /**
     * @param ctx
     * @param pessoaProfessor
     * @param empresaZW
     * @param apenasNome
     * @param index           parametro que traz o maxResult na posição [0] e firstResult na posição [1]
     * @return
     * @throws ServiceException
     * <AUTHOR> Siqueira
     */
    @Override
    public List<ClienteSintetico> consultarPorCodigoPessoaProfessor(final String ctx, Integer pessoaProfessor,
                                                                    Integer empresaZW, String filtroNome, boolean apenasNome, Integer... index) throws ServiceException {
        try {
            StringBuilder hql = new StringBuilder();
            if (apenasNome) {
                hql.append("SELECT new ClienteSintetico(codigo, nome) FROM ClienteSintetico obj where 0 = 0 ");
            } else {
                hql.append("SELECT obj FROM ClienteSintetico obj where 0 = 0 ");
            }
            if (!UteisValidacao.emptyNumber(pessoaProfessor)) {
                hql.append("AND professorSintetico.codigoPessoa = :professorSinteticocodigoPessoa ");
            }
            if (!UteisValidacao.emptyNumber(empresaZW)) {
                hql.append("AND empresa = :empresa ");
            }
            if (!UteisValidacao.emptyString(filtroNome)) {
                hql.append("AND upper(nome) like :filtroNome ");
            }
            hql.append(" AND(obj.ativo is true or obj.ativo is null) ");
            hql.append("order by nome asc ");
            Map<String, Object> params = new HashMap<String, Object>();

            if (!UteisValidacao.emptyNumber(pessoaProfessor)) {
                params.put("professorSinteticocodigoPessoa", pessoaProfessor);
            }
            if (!UteisValidacao.emptyNumber(empresaZW)) {
                params.put("empresa", empresaZW);
            }
            if (!UteisValidacao.emptyString(filtroNome)) {
                params.put("filtroNome", "%" + filtroNome.toUpperCase() + "%");
            }

            return getClienteSinteticoDao().findByParam(ctx, hql.toString(), params, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public boolean existeAlunoVinculadoProfessor(final String ctx, Integer professor) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" professorsintetico_codigo  = ").append(professor);
            return getClienteSinteticoDao().existsWithParam(ctx, sql);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ClienteSintetico consultarPorCpf(final String ctx, final String cpf) throws Exception {
        ClienteSintetico cliente = null;
        try {
            String s = "SELECT obj FROM ClienteSintetico obj where obj.CPF = :cpf or obj.CPF = :cpfNR";
            Map<String, Object> params = new HashMap();
            params.put("cpf", Uteis.removerMascara(cpf));
            params.put("cpfNR", Uteis.aplicarMascara(cpf, "999.999.999-99"));
            List<ClienteSintetico> clienteSinteticos = getClienteSinteticoDao().findByParam(ctx, s, params);
            if (!UteisValidacao.emptyList(clienteSinteticos)) {
                cliente = clienteSinteticos.get(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cliente;
    }

    @Override
    public List<AvaliacaoProfessorDTO> avaliaProfessores(List<AvaliacaoProfessorDTO> avaliacaoProfessorDTOS, String ctx) throws Exception {
        AvaliacaoProfessor avaliacaoProfessor = new AvaliacaoProfessor();
        List<AvaliacaoProfessorDTO> avalicoesValidadas = new ArrayList<>();
        List<AvaliacaoProfessorDTO> avaliacoesAdicionadas = new ArrayList<>();
        for (AvaliacaoProfessorDTO avaliacaoProfessorDTO : avaliacaoProfessorDTOS) {
            avalicoesValidadas.add(getAvaliacaoProfessorDTOValidada(ctx, avaliacaoProfessor, avaliacaoProfessorDTO));
        }
        for (AvaliacaoProfessorDTO avaliacaoProfessorDTO : avalicoesValidadas) {
            avaliacaoProfessor = createOrUpdateAvaliacaoProfessor(ctx, avaliacaoProfessor, avaliacaoProfessorDTO);
            AvaliacaoProfessorDTO avaliacaoProfessorDTOAdicionado = avaliacaoProfessorToDTO(avaliacaoProfessor);
            avaliacoesAdicionadas.add(avaliacaoProfessorDTOAdicionado);
        }
        return avaliacoesAdicionadas;
    }

    @Override
    public AvaliacaoProfessorDTO avaliaProfessor(AvaliacaoProfessorDTO avaliacaoProfessorDTO, String ctx) throws Exception {
        AvaliacaoProfessor avaliacaoProfessor = new AvaliacaoProfessor();
        avaliacaoProfessorDTO = getAvaliacaoProfessorDTOValidada(ctx, avaliacaoProfessor, avaliacaoProfessorDTO);
        avaliacaoProfessor = createOrUpdateAvaliacaoProfessor(ctx, avaliacaoProfessor, avaliacaoProfessorDTO);

        if(avaliacaoProfessorDTO.getNotaAvaliada() <= 3) {
            ServicoNotificacaoPush.enviaNotificacaoAvaliacao(ctx, avaliacaoProfessor.getEmpresa(), false, avaliacaoProfessor.getProfessorSintetico().getNome(), empresaService.obterPorId(ctx, avaliacaoProfessor.getEmpresa()).getNome());
        }
        return avaliacaoProfessorToDTO(avaliacaoProfessor);
    }

    public List<AvaliacaoProfessorDTO> buscaAvaliacao(String codProfessores, String ctx) throws Exception {
        List<AvaliacaoProfessorDTO> avaliacaoProfessorDTOS = new ArrayList<>();
        List<AvaliacaoProfessor> avaliacaoProfessors = new ArrayList<>();
        if (codProfessores != null && !codProfessores.equals("")) {
            avaliacaoProfessors = avaliacaoProfessorDao.findByCodProfessor(codProfessores, ctx);
        } else {
            avaliacaoProfessors = avaliacaoProfessorDao.findAll(ctx);
        }
        for (AvaliacaoProfessor avaliacaoProfessor : avaliacaoProfessors) {
            avaliacaoProfessorDTOS.add(avaliacaoProfessorToDTO(avaliacaoProfessor));
        }
        return avaliacaoProfessorDTOS;
    }

    private AvaliacaoProfessorDTO avaliacaoProfessorToDTO(AvaliacaoProfessor avaliacaoProfessor) {
        AvaliacaoProfessorDTO avaliacaoProfessorDTOAdicionado = new AvaliacaoProfessorDTO();
        avaliacaoProfessorDTOAdicionado.setCodigo(avaliacaoProfessor.getCodigo());
        avaliacaoProfessorDTOAdicionado.setCodUsuario(avaliacaoProfessor.getUsuario().getCodigo());
        avaliacaoProfessorDTOAdicionado.setProfessorCodigo(avaliacaoProfessor.getProfessorSintetico().getCodigo());
        avaliacaoProfessorDTOAdicionado.setProfessorNome(avaliacaoProfessor.getProfessorSintetico().getNome());
        avaliacaoProfessorDTOAdicionado.setDataUpdate(avaliacaoProfessor.getDataUpdate());
        avaliacaoProfessorDTOAdicionado.setDataRegistro(avaliacaoProfessor.getDataRegistro());
        avaliacaoProfessorDTOAdicionado.setEmpresa(avaliacaoProfessor.getEmpresa());
        avaliacaoProfessorDTOAdicionado.setClienteUsername(avaliacaoProfessor.getUsuario().getUserName());
        return avaliacaoProfessorDTOAdicionado;
    }

    private AvaliacaoProfessor createOrUpdateAvaliacaoProfessor(String ctx, AvaliacaoProfessor avaliacaoProfessor, AvaliacaoProfessorDTO avaliacaoProfessorDTO) throws Exception {
        if (avaliacaoProfessorDTO.getCodigo() == null || avaliacaoProfessorDTO.getCodigo() == 0) {
            avaliacaoProfessor.setDataRegistro(new Date(System.currentTimeMillis()));
            avaliacaoProfessor.setNotaAvaliada(avaliacaoProfessorDTO.getNotaAvaliada());
            avaliacaoProfessor.setProfessorSintetico(getProfessorSintetico().obterPorId(ctx, avaliacaoProfessorDTO.getProfessorCodigo()));
            avaliacaoProfessor.setUsuario(getUsuarioDao().findById(ctx, avaliacaoProfessorDTO.getCodUsuario()));
            avaliacaoProfessor.setEmpresa(avaliacaoProfessorDTO.getEmpresa());
            avaliacaoProfessor = getAvaliacaoProfessorDao().insert(ctx, avaliacaoProfessor);
        } else {
            avaliacaoProfessor = getAvaliacaoProfessorDao().findById(ctx, avaliacaoProfessorDTO.getCodigo());
            avaliacaoProfessor.setDataUpdate(new Date(System.currentTimeMillis()));
            avaliacaoProfessor.setNotaAvaliada(avaliacaoProfessorDTO.getNotaAvaliada());
            avaliacaoProfessor = getAvaliacaoProfessorDao().update(ctx, avaliacaoProfessor);
        }
        return avaliacaoProfessor;
    }

    @Override
    public AvaliacaoProfessorVO novaAvaliacaoProfessor(AvaliacaoProfessorDTO avaliacaoProfessorDTO, String ctx) throws Exception {
        AvaliacaoProfessor avaliacaoProfessor = new AvaliacaoProfessor();
        avaliacaoProfessorDTO = getAvaliacaoProfessorDTOValidadaV2(ctx, avaliacaoProfessorDTO);
        avaliacaoProfessor = createAvaliacaoProfessor(ctx, avaliacaoProfessor, avaliacaoProfessorDTO);

        if(avaliacaoProfessorDTO.getNotaAvaliada() <= 3) {
            ServicoNotificacaoPush.enviaNotificacaoAvaliacao(ctx, avaliacaoProfessor.getEmpresa(), false, avaliacaoProfessor.getProfessorSintetico().getNome(), empresaService.obterPorId(ctx, avaliacaoProfessor.getEmpresa()).getNome());
        }

        return avaliacaoProfessorToVO(avaliacaoProfessor, ctx);
    }

    @Override
    public List<AvaliacaoProfessorVO> buscaAvaliacaoCliente(Integer codUsuario, String ctx) throws Exception {
        List<AvaliacaoProfessor> avaliacaoProfessors = avaliacaoProfessorDao.findByCodUsuario(ctx, codUsuario);

        List<AvaliacaoProfessorVO> avaliacaoProfessorVOS = new ArrayList<>();
        for (AvaliacaoProfessor avaliacaoProfessor : avaliacaoProfessors) {
            avaliacaoProfessorVOS.add(avaliacaoProfessorToVO(avaliacaoProfessor, ctx));
        }
        return avaliacaoProfessorVOS;
    }

    @Override
    public String consultaModalidadeDiaria(String ctx, String matricula, Date dia) {
        String modalidades = "";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String sql = "SELECT STRING_AGG(aad.modalidade :: text, '|') AS codigos " +
                    "FROM aulaavulsadiaria aad " +
                    "INNER JOIN periodoacessocliente pac ON aad.codigo = pac.aulaavulsadiaria " +
                    "INNER JOIN cliente c ON c.codigo = aad.cliente " +
                    "WHERE c.codigomatricula = ? " +
                    "AND aad.modalidade IS NOT NULL " +
                    "AND aad.modalidade <> 0 " +
                    "AND ? BETWEEN pac.datainicioacesso AND pac.datafinalacesso";
            try (PreparedStatement ps = conZW.prepareStatement(sql)) {
                ps.setInt(1, Integer.parseInt(matricula));
                ps.setDate(2, Uteis.getDataJDBC(dia));
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        modalidades = rs.getString("codigos") != null ? rs.getString("codigos") : "";
                    }
                }
            } catch (NumberFormatException e) {
                Logger.getLogger(ClienteSinteticoServiceImpl.class.getName()).log(Level.SEVERE, "Erro ao converter matrícula para inteiro: " + matricula, e);
                return "";
            }
        } catch (Exception e) {
            System.out.println("Erro ao consultar modalidades diárias: " + e.getMessage());
        }

        return modalidades;
    }

    private AvaliacaoProfessorVO avaliacaoProfessorToVO(AvaliacaoProfessor avaliacaoProfessor, String ctx) throws Exception {
        AvaliacaoProfessorVO avaliacaoProfessorVO = new AvaliacaoProfessorVO();
        avaliacaoProfessorVO.setCodigo(avaliacaoProfessor.getCodigo());
        avaliacaoProfessorVO.setCodUsuario(avaliacaoProfessor.getUsuario().getCodigo());
        avaliacaoProfessorVO.setProfessorCodigo(avaliacaoProfessor.getProfessorSintetico().getCodigo());
        avaliacaoProfessorVO.setProfessorNome(avaliacaoProfessor.getProfessorSintetico().getNome());
        avaliacaoProfessorVO.setDataUpdate(avaliacaoProfessor.getDataUpdate());
        avaliacaoProfessorVO.setDataRegistro(avaliacaoProfessor.getDataRegistro());
        avaliacaoProfessorVO.setEmpresa(avaliacaoProfessor.getEmpresa());
        avaliacaoProfessorVO.setClienteUsername(avaliacaoProfessor.getUsuario().getUserName());
        avaliacaoProfessorVO.setNotaAvaliada(avaliacaoProfessor.getNotaAvaliada());
        avaliacaoProfessorVO.setComentario(avaliacaoProfessor.getComentario());
        Usuario usuarioProfessor = usuarioService.consultarPorProfessor(ctx, avaliacaoProfessor.getProfessorSintetico().getCodigo());
        String fotoProfessor = usuarioProfessor != null && usuarioProfessor.getUsuarioZW() != null ?
                Aplicacao.obterUrlFotoDaNuvem(usuarioService.urlFotoAppTreino(ctx, usuarioProfessor.getUsuarioZW())): avaliacaoProfessor.getProfessorSintetico().getFotoKey();
        avaliacaoProfessorVO.setFotoProfessor(fotoProfessor);
        return avaliacaoProfessorVO;
    }

    private AvaliacaoProfessor createAvaliacaoProfessor(String ctx, AvaliacaoProfessor avaliacaoProfessor, AvaliacaoProfessorDTO avaliacaoProfessorDTO) throws Exception {
        avaliacaoProfessor.setDataRegistro(new Date(System.currentTimeMillis()));
        avaliacaoProfessor.setNotaAvaliada(avaliacaoProfessorDTO.getNotaAvaliada());
        avaliacaoProfessor.setProfessorSintetico(getProfessorSintetico().obterPorId(ctx, avaliacaoProfessorDTO.getProfessorCodigo()));
        avaliacaoProfessor.setUsuario(getUsuarioDao().findById(ctx, avaliacaoProfessorDTO.getCodUsuario()));
        avaliacaoProfessor.setEmpresa(avaliacaoProfessorDTO.getEmpresa());
        avaliacaoProfessor.setComentario(avaliacaoProfessorDTO.getComentario());
        avaliacaoProfessor = getAvaliacaoProfessorDao().insert(ctx, avaliacaoProfessor);

        return avaliacaoProfessor;
    }

    private AvaliacaoProfessorDTO  getAvaliacaoProfessorDTOValidada(String ctx, AvaliacaoProfessor avaliacaoProfessor, AvaliacaoProfessorDTO avaliacaoProfessorDTO) throws Exception {
        if (avaliacaoProfessorDTO.getEmpresa() == null || avaliacaoProfessorDTO.getEmpresa() == 0) {
            throw new ServiceException("É necessário informar a empresa da avaliação do aluno " + avaliacaoProfessorDTO.getClienteUsername() != null ? avaliacaoProfessorDTO.getClienteUsername() : avaliacaoProfessorDTO.getCodUsuario().toString());
        }
        Empresa empresa = empresaService.obterPorAributo(ctx, "codigo", avaliacaoProfessorDTO.getEmpresa());
        if (empresa == null) {
            throw new ServiceException("Não foi possível encontrar a empresa de código: " + avaliacaoProfessorDTO.getEmpresa());
        }

        Usuario usuario = null;
        if(avaliacaoProfessorDTO.getCodUsuario() != null && avaliacaoProfessorDTO.getCodUsuario() != 0) {
            usuario = usuarioService.obterPorAtributo(ctx, "codigo", avaliacaoProfessorDTO.getCodUsuario());
        } else {
            usuario = usuarioService.obterPorAtributo(ctx, "username", avaliacaoProfessorDTO.getClienteUsername());
        }
        if (usuario == null) {
            String erro = avaliacaoProfessorDTO.getCodUsuario() != null && avaliacaoProfessorDTO.getCodUsuario() != 0 ? avaliacaoProfessorDTO.getCodUsuario().toString() : avaliacaoProfessorDTO.getClienteUsername();
            throw new ServiceException("Não foi possível encontrar o usuario informado: " + erro);
        }

        ProfessorSintetico professorSintetico = null;
        if (avaliacaoProfessorDTO.getProfessorCodigo() != null && avaliacaoProfessorDTO.getProfessorCodigo() != 0) {
            professorSintetico = professorSinteticoService.obterPorAtributo(ctx, "codigo", avaliacaoProfessorDTO.getProfessorCodigo());
        } else {
            professorSintetico = professorSinteticoService.obterPorAtributo(ctx, "nome", avaliacaoProfessorDTO.getProfessorNome());
        }
        if (professorSintetico == null) {
            String erro = avaliacaoProfessorDTO.getProfessorCodigo() != null && avaliacaoProfessorDTO.getProfessorCodigo() != 0 ? avaliacaoProfessorDTO.getCodigo().toString() : avaliacaoProfessorDTO.getProfessorNome();
            throw new ServiceException("Não foi possível encontrar o professor informado: " + erro);
        }
        avaliacaoProfessorDTO.setProfessorCodigo(professorSintetico.getCodigo());

        if ((avaliacaoProfessorDTO.getCodigo() != null && avaliacaoProfessorDTO.getCodigo() != 0)) {
            avaliacaoProfessor = getAvaliacaoProfessorDao().findById(ctx, avaliacaoProfessorDTO.getCodigo());
            if (avaliacaoProfessor == null || avaliacaoProfessor.getCodigo() == null) {
                throw new ServiceException("Não foi possível encontrar a avaliação de código " + avaliacaoProfessorDTO.getCodigo());
            }
        }

        return avaliacaoProfessorDTO;
    }

    private AvaliacaoProfessorDTO  getAvaliacaoProfessorDTOValidadaV2(String ctx, AvaliacaoProfessorDTO avaliacaoProfessorDTO) throws Exception {
        if (avaliacaoProfessorDTO.getNotaAvaliada() < 0 || avaliacaoProfessorDTO.getNotaAvaliada() > 5) {
            throw new ServiceException("A nota avaliada deve ser de 0 a 5.");
        }
        if (avaliacaoProfessorDTO.getEmpresa() == null || avaliacaoProfessorDTO.getEmpresa() == 0) {
            throw new ServiceException("É necessário informar a empresa da avaliação do aluno " + avaliacaoProfessorDTO.getClienteUsername() != null ? avaliacaoProfessorDTO.getClienteUsername() : avaliacaoProfessorDTO.getCodUsuario().toString());
        }
        Empresa empresa = empresaService.obterPorAributo(ctx, "codigo", avaliacaoProfessorDTO.getEmpresa());
        if (empresa == null) {
            throw new ServiceException("Não foi possível encontrar a empresa de código: " + avaliacaoProfessorDTO.getEmpresa());
        }
        String usuarioString = avaliacaoProfessorDTO.getCodUsuario() != null && avaliacaoProfessorDTO.getCodUsuario() != 0 ? avaliacaoProfessorDTO.getCodUsuario().toString() : avaliacaoProfessorDTO.getClienteUsername();
        Usuario usuario = null;

        try {
            if(avaliacaoProfessorDTO.getCodUsuario() != null && avaliacaoProfessorDTO.getCodUsuario() != 0) {
                usuario = usuarioService.consultaPorId(ctx, avaliacaoProfessorDTO.getCodUsuario());
            } else {
                usuario = usuarioService.validarUsuarioMovelApp(ctx, avaliacaoProfessorDTO.getClienteUsername(), true);
            }
            if (usuario == null || usuario.getCliente() == null) {
                throw new ServiceException("Não foi possível encontrar o usuario informado: " + usuarioString);
            }
        } catch (Exception e) {
            throw new ServiceException("Não foi possível encontrar o usuario informado: " + usuarioString);
        }
        avaliacaoProfessorDTO.setCodUsuario(usuario.getCodigo());
        avaliacaoProfessorDTO.setClienteUsername(usuario.getUserName());

        ProfessorSintetico professorSintetico = null;
        if (avaliacaoProfessorDTO.getProfessorCodigo() != null && avaliacaoProfessorDTO.getProfessorCodigo() != 0) {
            professorSintetico = professorSinteticoService.obterPorAtributo(ctx, "codigo", avaliacaoProfessorDTO.getProfessorCodigo());
        } else {
            professorSintetico = professorSinteticoService.obterPorAtributo(ctx, "nome", avaliacaoProfessorDTO.getProfessorNome());
        }
        if (professorSintetico == null) {
            String erro = avaliacaoProfessorDTO.getProfessorCodigo() != null && avaliacaoProfessorDTO.getProfessorCodigo() != 0 ? avaliacaoProfessorDTO.getCodigo().toString() : avaliacaoProfessorDTO.getProfessorNome();
            throw new ServiceException("Não foi possível encontrar o professor informado: " + erro);
        }
        avaliacaoProfessorDTO.setProfessorCodigo(professorSintetico.getCodigo());
        avaliacaoProfessorDTO.setProfessorNome(professorSintetico.getNome());

        if ((avaliacaoProfessorDTO.getCodigo() != null && avaliacaoProfessorDTO.getCodigo() != 0)) {
            if (getAvaliacaoProfessorDao().findById(ctx, avaliacaoProfessorDTO.getCodigo()) == null ||
                    getAvaliacaoProfessorDao().findById(ctx, avaliacaoProfessorDTO.getCodigo()).getCodigo() == null) {
                throw new ServiceException("Não foi possível encontrar a avaliação de código " + avaliacaoProfessorDTO.getCodigo());
            }
        }

        List<AvaliacaoProfessor> avaliacaoProfessors = getAvaliacaoProfessorDao()
                .findByCodProfessorECodUsuario(ctx, avaliacaoProfessorDTO.getCodUsuario(), avaliacaoProfessorDTO.getProfessorCodigo());
        for(AvaliacaoProfessor avaliacaoProfessor1 : avaliacaoProfessors) {
            Date trintaDiasAntesDataAtual = new Date(System.currentTimeMillis() - 2592000000L);
            if(avaliacaoProfessor1.getDataRegistro().after(trintaDiasAntesDataAtual)) {
                throw new ServiceException("Já existe uma avaliação para o professor " + professorSintetico.getNome() + " e o aluno " + usuario.getUserName() + " feita nos últimos 30 dias.");
            }
        }

        return avaliacaoProfessorDTO;
    }

    @Override
    public List<ClienteSintetico> consultarPorMatriculaOuNome(final String ctx,
                                                              final Integer empresaZW, final Integer professor, final String filtro, final Integer maxResults) throws ServiceException {

        List<String> attrs = new ArrayList<String>();
        List<Object> values = new ArrayList<Object>();
        if (empresaZW != null) {
            attrs.add("empresa");
            values.add(empresaZW);
        }
        if (professor != null) {
            attrs.add("professorSintetico.codigo");
            values.add(professor);
        }
        try {
            if (filtro != null && (filtro.replaceAll("[^0-9]", "").length() == 10 ||
                    filtro.replaceAll("[^0-9]", "").length() == 11)) {
                try {
                    String s = "SELECT obj FROM ClienteSintetico obj where obj.CPF = :cpf or obj.CPF = :cpfNR";
                    Map<String, Object> params = new HashMap();
                    params.put("cpf", Uteis.removerMascara(filtro));
                    params.put("cpfNR", Uteis.aplicarMascara(filtro, "999.999.999-99"));
                    List<ClienteSintetico> clienteSinteticos = getClienteSinteticoDao().findByParam(ctx, s, params);
                    if (!UteisValidacao.emptyList(clienteSinteticos)) {
                        return clienteSinteticos;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                attrs.add("codigoAcesso");
                values.add(filtro);
                String[] atributos = new String[attrs.size()];
                atributos = attrs.toArray(atributos);
                return getClienteSinteticoDao().findListByAttributes(ctx,
                        atributos, values.toArray(), "nome", maxResults);
            } else {
                Integer matricula = Integer.valueOf(filtro);
                attrs.add("matricula");
                values.add(matricula);
                String[] atributos = new String[attrs.size()];
                atributos = attrs.toArray(atributos);
                return getClienteSinteticoDao().findListByAttributes(ctx,
                        atributos, values.toArray(), "nome", maxResults);
            }

        } catch (NumberFormatException ne) {
            try {

                StringBuilder hql = new StringBuilder();
                Map<String, Object> params = new HashMap<>();


                hql.append("SELECT obj FROM ClienteSintetico obj ");
                StringBuilder where = new StringBuilder();
                if (!isBlank(filtro)) {
                    where.append("where");
                    hql.append(where + " upper(obj.nome) like :nome");
                    params.put("nome", "%" + filtro.toUpperCase().trim() + "%");
                }

                if (empresaZW != null) {
                    if (where.length() > 0) {
                        hql.append(" and (obj.empresa) = :empresa");
                    } else {
                        hql.append(" where (obj.empresa) = :empresa");
                    }
                    params.put("empresa", empresaZW);
                }


                hql.append(" ORDER BY obj.nome ASC");

                return getClienteSinteticoDao().findByParam(ctx, hql.toString(), params, maxResults);
            } catch (Exception ex) {
                throw new ServiceException(ex.getMessage());
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Map<String, ClienteSintetico> consultarPorMatriculasOuNomes(final String contexto, final Integer empresaZW,
                                                                       final Integer professor, final List<Integer> matriculas,
                                                                       final List<String> nomes, final Integer maxResults,
                                                                       final HttpServletRequest request) throws ServiceException {

        final Boolean matriculasVazias = CollectionUtils.isEmpty(matriculas);
        final Boolean nomesVazios = CollectionUtils.isEmpty(nomes);
        if (matriculasVazias && nomesVazios) {
            throw new ServiceException("Os nomes e as matriculas não podem ser nulos simultaneamente.");
        }

        try {

            final Map<String, ClienteSintetico> clientes = new HashMap<>();
            for (ClienteSintetico clienteSintetico :
                    getClienteSinteticoDao().findByEmpresaProfessorNomesMatriculasIn(contexto, empresaZW, professor, nomes, matriculas, maxResults)) {


                if (clienteSintetico.getFotoKeyApp() == null || clienteSintetico.getFotoKeyApp().isEmpty()) {
                    clienteSintetico.setUrlFoto(fotoService.carregarFoto(
                            contexto,
                            "",
                            clienteSintetico.getCodigoPessoa(),
                            false,
                            request));
                } else {
                    if (!clienteSintetico.getFotoKeyApp().contains("http")) {
                        clienteSintetico.setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(clienteSintetico.getFotoKeyApp()));
                    } else {
                        clienteSintetico.setUrlFoto(clienteSintetico.getFotoKeyApp());
                    }
                }

                if (matriculas.contains(clienteSintetico.getMatricula())) {
                    clientes.put(String.valueOf(clienteSintetico.getMatricula()), clienteSintetico);
                } else if (nomes.contains(clienteSintetico.getNome())) {
                    clientes.put(clienteSintetico.getNome(), clienteSintetico);
                }
            }
            return clientes;
        } catch (Exception e) {
            throw new ServiceException("Ocorreu um problema ao buscar os alunos na base.", e);
        }
    }

    @Override
    public void atualizarNivelAluno(final String ctx, Integer nivel, ClienteSintetico cliente) throws ServiceException {
        try {
            UsuarioSimplesDTO usuario = sessaoService.getUsuarioAtual();
            cliente.setNivelAluno(nivel == null || nivel == 0 ? null : new Nivel(nivel));
            alterar(ctx, cliente);
            Date alteracao = Calendario.hoje();
            HistoricoNivelCliente ultimoHistorico = consultarUltimoHistoricoNivel(ctx, cliente.getCodigo());
            if (ultimoHistorico != null) {
                ultimoHistorico.setFim(alteracao);
                getHistoricoNivelClienteDao().update(ctx, ultimoHistorico);
            }
            if (nivel != null && nivel >= 0) {
                HistoricoNivelCliente novoHistorico = new HistoricoNivelCliente();
                novoHistorico.setCliente(cliente);
                novoHistorico.setNivel(cliente.getNivelAluno());
                novoHistorico.setInicio(alteracao);
                if (usuario != null) {
                    novoHistorico.setUsuario(usuario.getId(), usuario.getUsername());
                }
                getHistoricoNivelClienteDao().insert(ctx, novoHistorico);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public JSONArray todosAlunos(String key) throws Exception {
        List<ClienteSintetico> findAll = clienteSinteticoDao.findAll(key);
        findAll = Ordenacao.ordenarLista(findAll, "nome");
        JSONArray array = new JSONArray();
        for (ClienteSintetico cli : findAll) {
            JSONObject obj = new JSONObject();
            obj.put("matricula", cli.getMatricula());
            obj.put("codigopessoa", cli.getCodigoPessoa());
            obj.put("nome", cli.getNome());
            obj.put("professor", cli.getProfessorSintetico() == null ? 0 : cli.getProfessorSintetico().getCodigoColaborador());
            obj.put("situacao", cli.getSituacao());
            array.put(obj);
        }
        return array;
    }

    @Override
    public void atualizarProfessorAluno(final String ctx,
                                        IntegracaoCadastrosWSConsumer integracaoWS,
                                        Integer professor, ClienteSintetico cliente, Integer codigoUsuario, HttpServletRequest request) throws ServiceException {
        try {
            Integer pa = cliente.getProfessorSintetico() == null ? 0
                    : cliente.getProfessorSintetico().getCodigo();
            professor = professor == null ? 0 : professor;
            ProfessorSintetico ps = professorSintetico.obterPorId(ctx, professor);
            ProfessorSintetico professorAtual = professorSintetico.obterPorId(ctx, pa);

            // se ambos são null ou igual, não realizar nenhuma alteração a seguir:
            if ((professorAtual == null && ps == null) || Objects.equals(professorAtual, ps)) {
                return;
            }

            if (ps == null || ps.getCodigo() == null) {
                cliente.setProfessorSintetico(null);
            }
            cliente.setProfessorSintetico(ps);
            alterar(ctx, cliente);

            if (integracaoWS != null) {
                final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                String retornoVinculo = integracaoWS.gerarVinculoProfessorAluno(url, ctx, cliente.getCodigoCliente(),
                        (ps == null ? null : ps.getCodigoColaborador()), codigoUsuario);
                if (!retornoVinculo.toUpperCase().contains("SUCESSO")) {
                    ProfessorSintetico psAtual = professorSintetico.obterPorId(ctx, pa);
                    cliente.setProfessorSintetico(psAtual);
                    alterar(ctx, cliente);
                    throw new ValidacaoException(retornoVinculo.replaceFirst("ERRO: ", ""));
                }
            }

            inserirLogAlteracaoProfessorAluno(ctx, professorAtual, ps, cliente.getMatricula(), null);

            ProgramaTreino programaAtual = programaService.consultarUltimoTreinoDataBaseAluno(ctx, cliente, null);
            if (programaAtual != null) {
                programaAtual.setProfessorCarteira(ps);
                programaService.alterar(ctx, programaAtual);
                programaService.atualizarVersaoPrograma(ctx, programaAtual);
                if (request != null) {
                    programaService.notificarOuvintes(ctx,
                            programaAtual.getCliente() == null ? null : programaAtual.getCliente().getCodigo(),
                            programaAtual.getCodigoColaborador(), request);
                } else {
                    programaService.notificarOuvintes(ctx, programaAtual);
                }

            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public void inserirLogAlteracaoProfessorAluno(final String ctx, ProfessorSintetico profAntesAlteracao, ProfessorSintetico novoProfessor, Integer matriculaAluno, String username) throws ServiceException {
        try {
            if (!Objects.equals(profAntesAlteracao, novoProfessor)) {
                String nomeProfessorAtual = profAntesAlteracao != null ? profAntesAlteracao.getNome() : "";
                String nomeNovoProfessor = novoProfessor != null ? novoProfessor.getNome() : "";
                if (UteisValidacao.emptyString(username)) {
                    if (sessaoService != null && sessaoService.getUsuarioAtual() != null) {
                        username = sessaoService.getUsuarioAtual().getUsername();
                    } else {
                        username = "USUÁRIO SEM SESSÃO";
                    }
                }

                incluirLog(
                        ctx,
                        matriculaAluno.toString(),
                        "",
                        nomeProfessorAtual,
                        nomeNovoProfessor,
                        "ALTERAÇÃO",
                        "ALTERAÇÃO DE PROFESSOR DO ALUNO",
                        EntidadeLogEnum.ALUNO,
                        "Professor do Aluno",
                        username,
                        logDao,
                        null, null);
            }
        } catch (Exception e) {
            Uteis.logar(e, ClienteSinteticoServiceImpl.class);
        }
    }

    public HistoricoNivelCliente consultarUltimoHistoricoNivel(final String ctx, final Integer cliente) throws ServiceException {
        try {
            String sql = "SELECT obj FROM HistoricoNivelCliente obj WHERE obj.fim = null and obj.cliente.codigo = :cliente";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("cliente", cliente);
            return getHistoricoNivelClienteDao().findObjectByParam(ctx, sql, p);
        } catch (Exception ex) {
            throw new ServiceException(ex);

        }
    }

    public void excluirHistoricoNivel(final String ctx, final Integer cliente) throws ServiceException {
        try {
            getHistoricoNivelClienteDao().deleteComParam(ctx, new String[]{"cliente.codigo"}, new Object[]{cliente});
        } catch (Exception ex) {
            throw new ServiceException(ex);

        }
    }

    public void iniciarAcompanhamento(final String ctx, final ClienteSintetico cliente, final ProfessorSintetico professor, TiposAcompanhamentoEnum tiposAcompanhamentoEnum) throws ServiceException {
        try {
            ClienteAcompanhamento cliAcomp = obterAcompanhamento(ctx, cliente);
            if (cliAcomp == null || cliAcomp.getCodigo() == null || cliAcomp.getCodigo().equals(0)) {
                cliAcomp = new ClienteAcompanhamento();
                cliAcomp.setCliente(cliente);
                cliAcomp.setProfessor(professor);
                cliAcomp.setInicio(Calendario.hoje());
                cliAcomp.setTipoAcompanhamentoCliente(tiposAcompanhamentoEnum.getCodigo());
                getClienteAcompanhamentoDao().insert(ctx, cliAcomp);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void avaliarAcompanhamento(final String ctx,
                                      final ClienteAcompanhamentoAvaliacaoDTO avaliacaoDTO,
                                      final Usuario usuarioLogado) throws ServiceException {
        try {
            final ClienteAcompanhamento acompanhamento = getClienteAcompanhamentoDao().findById(ctx, avaliacaoDTO.getAcompanhamentoId());

            if (acompanhamento == null) {
                throw new ServiceException("O acompanhamento que você está tentando avaliar não foi encontrado.");
            }

            if (!acompanhamento.getCliente().getCodigo().equals(avaliacaoDTO.getClienteSinteticoId())) {
                throw new ServiceException("Operação não permitida. Este acompanhamento pertence a outro usuário.");
            }

            if (getClienteAcompanhamentoAvaliacaoDao().findByAcompanhamentoId(ctx, avaliacaoDTO.getAcompanhamentoId()) != null) {
                throw new ServiceException("Este acompanhamento já foi avaliado anteriormente.");
            }

            final Date dataInicioAcompanhamento = acompanhamento.getInicio();
            final Date dataAtual = Calendario.hoje();

            if (!Calendario.isMesmoDia(dataInicioAcompanhamento, dataAtual)) {
                throw new ServiceException("O prazo para avaliação expirou. A avaliação deve ser feita até as 23:59 do dia do treino.");
            }

            ClienteAcompanhamentoAvaliacao novaAvaliacao = new ClienteAcompanhamentoAvaliacao();
            novaAvaliacao.setClienteAcompanhamento(acompanhamento);
            novaAvaliacao.setNota(avaliacaoDTO.getNota());
            novaAvaliacao.setComentario(avaliacaoDTO.getComentario());
            novaAvaliacao.setDataAvaliacao(dataAtual);

            getClienteAcompanhamentoAvaliacaoDao().insert(ctx, novaAvaliacao);

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void finalizarAcompanhamento(final String ctx,
                                        final ClienteSintetico cliente,
                                        final ProfessorSintetico professor,
                                        final Integer programaId,
                                        final Integer fichaId) throws ServiceException {
        try {
            ClienteAcompanhamento cliAcomp = obterAcompanhamento(ctx, cliente);
            if (cliAcomp != null || cliAcomp.getCodigo() == null || cliAcomp.getCodigo().equals(0)) {
                cliAcomp.setFim(Calendario.hoje());
                getClienteAcompanhamentoDao().update(ctx, cliAcomp);
                ProgramaTreino pt = programaService.obterPorId(ctx, programaId);
                ProgramaTreinoFicha ptf = programaService.obterProgramaTreinoFicha(ctx, programaId, fichaId);
                if (pt != null && ptf != null) {
                    programaService.inserirTreinoRealizadoSemSerie(
                            ctx,
                            pt,
                            fichaId.toString(),
                            Calendario.getData(cliAcomp.getInicio(), Calendario.MASC_DATAHORA),
                            Calendario.getData(cliAcomp.getFim(), Calendario.MASC_DATAHORA),
                            OrigemExecucaoEnum.ACOMP_PROFESSOR,
                            ptf, null, null, null);
                    programaService.resetarCampoSerieRealizadaDaSerie(ctx, fichaId);
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ClienteSintetico> consultarPorAcompanhadosProfessor(final String ctx, Integer professor, String filtroNome, Date inicio, Date fim, Integer... index) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            query.append(" SELECT cliente FROM ClienteAcompanhamento obj INNER JOIN obj.cliente cliente INNER JOIN obj.professor prof ");
            query.append("WHERE obj.inicio between :inicio and :fim ");
            if (professor != null && !UteisValidacao.emptyNumber(professor)) {
                query.append(" and prof.codigo = :professor");
            }
            if (filtroNome != null && !UteisValidacao.emptyString(filtroNome)) {
                query.append("AND upper(cliente.nome) like :filtroNome ");
            }
            query.append(" AND ( cliente.ativo is true or cliente.ativo is null ) ");
            query.append(" order by cliente.nome ");
            HashMap<String, Object> p = new HashMap<String, Object>();
            if (professor != null && !UteisValidacao.emptyNumber(professor)) {
                p.put("professor", professor);
            }
            p.put("inicio", Calendario.getDataComHoraZerada(inicio));
            p.put("fim", Calendario.getDataComHora(fim, "23:59:59"));
            if (filtroNome != null && !UteisValidacao.emptyString(filtroNome)) {
                p.put("filtroNome", "%" + filtroNome.toUpperCase() + "%");
            }
            if (index == null) {
                return obterPorParam(ctx, query.toString(), p);
            } else {
                return obterPorParam(ctx, query.toString(), p, index);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ClienteSintetico> consultarPorAgendadosProfessor(final String ctx,
                                                                 final Integer empresaZW, final Integer professor, String filtroNome, Date inicio, Date fim, Integer... index) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            query.append(" SELECT distinct cliente FROM Agendamento obj ");
            query.append(" INNER JOIN obj.cliente cliente ");
            query.append("WHERE cliente.empresa = ").append(empresaZW).
                    append(" and obj.inicio between :inicio and :fim ");
            HashMap<String, Object> p = new HashMap<String, Object>();
            if (professor != null) {
                query.append(" AND obj.professor.codigo = :professor");
                p.put("professor", professor);
            }
            if (!UteisValidacao.emptyString(filtroNome)) {
                query.append("AND upper(cliente.nome) like :filtroNome");
            }
            query.append(" AND(cliente.ativo is true or cliente.ativo is null) ");
            query.append(" order by cliente.nome ");
            p.put("inicio", Calendario.getDataComHoraZerada(inicio));
            p.put("fim", Calendario.getDataComHora(fim, "23:59:59"));
            if (!UteisValidacao.emptyString(filtroNome)) {
                p.put("filtroNome", "%" + filtroNome.toUpperCase() + "%");
            }
            List<ClienteSintetico> resultado = obterPorParam(ctx, query.toString(), p, index);
            return resultado;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ClienteSintetico> consultarPorDesacompanhadosProfessor(final String ctx,
                                                                       final Integer empresaZW, final Integer professor, String filtroNome, Date inicio, Date fim,
                                                                       boolean somenteNaAcademia, PaginadorDTO paginadorDTO, String filter, HttpServletRequest request, Integer... index) throws ServiceException {
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema duracaoNaAcademia = css.consultarPorTipo(ctx, ConfiguracoesEnum.DURACAO_ALUNO_NA_ACADEMIA);

            boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
            JSONObject objJson = new JSONObject();
            if (filter != null) {
                filter = Uteis.retirarAcentuacaoRegex(filter);
                objJson = new JSONObject(filter);
            }

            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append("SELECT obj FROM ClienteSintetico obj ");
            query.append(" WHERE empresa = :empresa ");
            p.put("empresa", empresaZW);
            query.append("and obj.situacao in ('AT') ");
            query.append(" and obj.codigo NOT IN (");
            query.append(" SELECT distinct objA.cliente.codigo FROM ClienteAcompanhamento objA ");
            query.append(" WHERE objA.inicio between :inicio and :fim )");
            if (somenteNaAcademia) {
                query.append(" AND obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta ");
                query.append(" WHERE sta.dataInicioEvento between :inicio and :fim AND sta.dataFimEvento IS NULL)");
            }
            if (professor != null && !UteisValidacao.emptyNumber(professor)) {
                query.append(" AND obj.professorSintetico.codigo = :professor ");
                p.put("professor", professor);
            }
            if (filtroNome != null && !UteisValidacao.emptyString(filtroNome)) {
                query.append("AND upper(obj.nome) like :filtroNome ");
            }
            if (buscaRapida) {
                query.append(" AND ((");
                String pesquisaCliente = objJson.getString("quicksearchValue");
                query.append(" UPPER(obj.nome) LIKE UPPER(CONCAT(:nomeCliente,'%')))");
                query.append(" OR (");
                String pesquisaProfessor = objJson.getString("quicksearchValue");
                query.append(" UPPER(obj.professorSintetico.nome) LIKE UPPER(CONCAT(:nomeProfesor,'%')))");
                p.put("nomeCliente", pesquisaCliente.replaceAll(" ", "%"));
                p.put("nomeProfesor", pesquisaProfessor.replaceAll(" ", "%"));
                try {
                    Integer pesquisaMatricula = Integer.parseInt(objJson.getString("quicksearchValue"));
                    query.append(" OR (obj.matricula = :matricula) ");
                    p.put("matricula", pesquisaMatricula);
                } catch (Exception e) { }
                query.append(") ");
            }
            query.append(" AND(obj.ativo is true or obj.ativo is null) ");
            query.append(" AND obj.professorSintetico.nome is not null ");
            query.append(" ORDER BY obj.nome ");
            p.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger()));
//            p.put("inicio", Calendario.getDataComHoraZerada(inicio));
            p.put("fim", Calendario.getDataComHora(fim, "23:59:59"));
            if (filtroNome != null && !UteisValidacao.emptyString(filtroNome)) {
                p.put("filtroNome", "%" + filtroNome.toUpperCase() + "%");
            }

            List<ClienteSintetico> clientes = new ArrayList<>();
            if (index == null) {
                clientes = obterPorParam(ctx, query.toString(), p);
            } else {
                clientes = obterPorParam(ctx, query.toString(), p, index);
            }

            if (request != null) {
                for (ClienteSintetico cs : clientes) {
                    String fotoKey = obterFotoKeyClienteZW(ctx, cs.getMatricula());
                    cs.setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(fotoKey));
                }
            }

            if (paginadorDTO != null) {
                Integer quantidade = consultarQuantidadePorDesacompanhadosProfessor(ctx, empresaZW, professor, Calendario.hoje(), Calendario.hoje());
                return montarPaginadorDTO(ctx, query, p, paginadorDTO, clienteSinteticoDao, null, professor, true, quantidade);
            }

            return clientes;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private String obterFotoKeyClienteZW(String ctx, Integer matricula) {
        String fotoKey = "";
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                StringBuilder sql = new StringBuilder();
                sql.append("select p.fotokey \n");
                sql.append("from pessoa p \n");
                sql.append("inner join cliente c on c.pessoa = p.codigo \n");
                sql.append("where c.codigomatricula = ").append(matricula);
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                    if (rs.next()) {
                        fotoKey = rs.getString("fotokey");
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao tentar obter fotokey do cliente ZW: " + ex.getMessage());
        }
        return fotoKey;
    }

    @Override
    public List<ClienteSintetico> consultarPorAcompanhadosProfessor(final String ctx,
                                                                    final Integer empresaZW, final Integer professor, String filtroNome, Date inicio, Date fim,
                                                                    boolean somenteNaAcademia, PaginadorDTO paginadorDTO, String filter, HttpServletRequest request, Integer... index) throws ServiceException {
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema duracaoNaAcademia = css.consultarPorTipo(ctx, ConfiguracoesEnum.DURACAO_ALUNO_NA_ACADEMIA);

            boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
            JSONObject objJson = new JSONObject();
            if (filter != null) {
                filter = Uteis.retirarAcentuacaoRegex(filter);
                objJson = new JSONObject(filter);
            }

            StringBuilder query = new StringBuilder();
            query.append("SELECT obj FROM ClienteAcompanhamento obj ");
            query.append("INNER JOIN obj.cliente cliente ");
            query.append("INNER JOIN obj.professor prof ");
            query.append("WHERE obj.inicio between :inicio AND :fim ");
            query.append("AND obj.fim is null ");
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("inicio", Calendario.getDataComHoraZerada(inicio));
            p.put("fim", Calendario.getDataComHora(fim, "23:59:59"));

            if (professor != null && !UteisValidacao.emptyNumber(professor)) {
                query.append(" AND obj.professor.codigo = :professor ");
                p.put("professor", professor);
            }
            if (filtroNome != null && !UteisValidacao.emptyString(filtroNome)) {
                query.append("AND upper(obj.nome) like :filtroNome ");
            }
            query.append("ORDER BY obj.codigo DESC ");
//            if (buscaRapida) {
//                query.append(" AND ((");
//                String pesquisaCliente = objJson.getString("quicksearchValue");
//                query.append(" UPPER(obj.nome) LIKE UPPER(CONCAT(:nomeCliente,'%')))");
//                query.append(" OR (");
//                String pesquisaProfessor = objJson.getString("quicksearchValue");
//                query.append(" UPPER(obj.professorSintetico.nome) LIKE UPPER(CONCAT(:nomeProfesor,'%')))");
//                p.put("nomeCliente", pesquisaCliente.replaceAll(" ", "%"));
//                p.put("nomeProfesor", pesquisaProfessor.replaceAll(" ", "%"));
//                try {
//                    Integer pesquisaMatricula = Integer.parseInt(objJson.getString("quicksearchValue"));
//                    query.append(" OR (obj.matricula = :matricula) ");
//                    p.put("matricula", pesquisaMatricula);
//                } catch (Exception e) { }
//                query.append(") ");
//            }
//            query.append(" AND(obj.ativo is true or obj.ativo is null) ");
//            query.append(" AND obj.professorSintetico.nome is not null ");
//            query.append(" ORDER BY obj.nome ");
//            p.put("inicio", Calendario.getDataComHoraZerada(inicio));
//            p.put("fim", Calendario.getDataComHora(fim, "23:59:59"));
//            if (filtroNome != null && !UteisValidacao.emptyString(filtroNome)) {
//                p.put("filtroNome", "%" + filtroNome.toUpperCase() + "%");
//            }

            List<ClienteSintetico> clientes = new ArrayList<>();
            List<ClienteAcompanhamento> clienteAcompanhamento = new ArrayList<>();


            if (index == null) {
                clienteAcompanhamento = obterClienteAcompanhamentoPorParam(ctx, query.toString(), p);
            } else {
                clienteAcompanhamento = obterClienteAcompanhamentoPorParam(ctx, query.toString(), p, index);
            }

            for(ClienteAcompanhamento cliAcompanhamento : clienteAcompanhamento){
                clientes.add(cliAcompanhamento.getCliente());
            }

            if (request != null) {
                for (ClienteSintetico cs : clientes) {
                    String fotoKey = obterFotoKeyClienteZW(ctx, cs.getMatricula());
                    cs.setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(fotoKey));
                }
            }
            if (paginadorDTO != null) {
                Integer quantidade = consultarQuantidadeEmAcompanhamentoProfessor(ctx, empresaZW, professor, Calendario.hoje(), Calendario.hoje());
                return montarPaginadorClienteAcompanhamentoDTO(ctx, query, p, paginadorDTO, clienteAcompanhamentoDao, null, professor, true, quantidade);
            }

            return clientes;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private List montarPaginadorDTO(String key, StringBuilder hql, Map<String, Object> param, PaginadorDTO paginadorDTO, DaoGenerico dao, Integer count, Integer idProfessor, boolean pesquisaNative, Integer quantidade) throws Exception {
        maxResults = paginadorDTO.getSize() == null ? 999999 : paginadorDTO.getSize().intValue();
        indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        paginadorDTO.setQuantidadeTotalElementos((long) quantidade);
        List<ClienteSintetico> list = dao.findByParam(key, hql.toString(), param, maxResults, indiceInicial);

        return list;
    }

    private List montarPaginadorClienteAcompanhamentoDTO(String key, StringBuilder hql, Map<String, Object> param, PaginadorDTO paginadorDTO, DaoGenerico dao, Integer count, Integer idProfessor, boolean pesquisaNative, Integer quantidade) throws Exception {
        maxResults = paginadorDTO.getSize() == null ? 999999 : paginadorDTO.getSize().intValue();
        indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        paginadorDTO.setQuantidadeTotalElementos((long) quantidade);
        List<ClienteAcompanhamento> list = dao.findByParam(key, hql.toString(), param, maxResults, indiceInicial);
        List<ClienteSintetico> clientes = new ArrayList<>();
        for(ClienteAcompanhamento cliAcompanhamento : list){
            clientes.add(cliAcompanhamento.getCliente());
        }
        return clientes;
    }

    @Override
    public Integer consultarQuantidadePorDesacompanhadosProfessor(final String ctx,
                                                                  final Integer empresaZW, final Integer professor,
                                                                  Date inicio, Date fim) throws ServiceException {
        try {

            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema duracaoNaAcademia = css.consultarPorTipo(ctx, ConfiguracoesEnum.DURACAO_ALUNO_NA_ACADEMIA);
            inicio = Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger());

            StringBuilder query = new StringBuilder();
            query.append("SELECT count(obj) as total FROM ClienteSintetico obj inner join ProfessorSintetico ps on\tobj.professorSintetico_codigo = ps.codigo ");
            query.append(" WHERE empresa = ").append(empresaZW);
            query.append(" 	and obj.situacao in ('AT')");
            query.append(" and obj.codigo NOT IN (");
            query.append(" SELECT distinct objA.cliente_codigo FROM ClienteAcompanhamento objA ");
            query.append(" WHERE objA.inicio between '");
            query.append(Uteis.getDataAplicandoFormatacao(Calendario.getDataComHoraZerada(inicio), "yyyy-MM-dd HH:mm")).append("' \n and '")
                    .append(Uteis.getDataAplicandoFormatacao(Calendario.getDataComHora(inicio, "23:59"), "yyyy-MM-dd HH:mm")).append("')");
            query.append(" AND obj.codigo IN (SELECT u.cliente_codigo FROM StatusPessoa sta inner join usuario u on u.codigo = sta.usuario_codigo ");
            query.append(" WHERE sta.dataInicioEvento between '");
            query.append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd HH:mm")).append("' \n and '")
                    .append(Uteis.getDataAplicandoFormatacao(Calendario.getDataComHora(inicio, "23:59"), "yyyy-MM-dd HH:mm"));
            query.append("' AND sta.dataFimEvento IS NULL)");

            if (professor != null && !UteisValidacao.emptyNumber(professor)) {
                query.append(" AND ps.codigo = ").append(professor);
            }
            query.append(" AND(obj.ativo is true or obj.ativo is null) ");
            query.append(" AND ps.nome is not null ");

            try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, query.toString())) {
                return rs.next() ? rs.getInt("total") : 0;
            }

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Integer consultarQuantidadeEmAcompanhamentoProfessor(final String ctx,
                                                                final Integer empresaZW, final Integer professor,
                                                                Date inicio, Date fim) throws ServiceException {
        try {

            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema duracaoNaAcademia = css.consultarPorTipo(ctx, ConfiguracoesEnum.DURACAO_ALUNO_NA_ACADEMIA);
//            inicio = Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger());

            StringBuilder query = new StringBuilder();
            query.append("SELECT count(obj) as total FROM ClienteAcompanhamento obj ");
            query.append(" inner join ProfessorSintetico ps on obj.professor_codigo  = ps.codigo ");
            query.append(" WHERE ps.empresa_codigo = ").append(empresaZW);
            query.append(" AND obj.inicio between '");
            query.append(Uteis.getDataAplicandoFormatacao(Calendario.getDataComHoraZerada(inicio), "yyyy-MM-dd HH:mm")).append("' \n and '")
            .append(Uteis.getDataAplicandoFormatacao(Calendario.getDataComHora(inicio, "23:59"), "yyyy-MM-dd HH:mm")).append("' and obj.fim is null ");
            if (professor != null && !UteisValidacao.emptyNumber(professor)) {
                query.append(" AND obj.professor_codigo = "+professor);

            }

            try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, query.toString())) {
                return rs.next() ? rs.getInt("total") : 0;
            }

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ClienteSintetico> consultarAlunosSemObservacaoXDias(final String ctx, final Integer empresaZW,
                                                                    final Integer professor, String filtroNome, Integer xdias, Integer... index) throws ServiceException {
        try {
            Date diaLimite = Uteis.somarDias(Calendario.hoje(), -1 * xdias);
            StringBuilder query = new StringBuilder();
            query.append("SELECT obj FROM ClienteSintetico obj ");
            query.append(" WHERE empresa = ").append(empresaZW);
            query.append(" and obj.codigo NOT IN (");
            query.append(" SELECT distinct obj.cliente.codigo FROM ClienteObservacao obj ");
            query.append(" WHERE obj.dataObservacao > :dia )");
            HashMap<String, Object> p = new HashMap<String, Object>();
            if (professor != null) {
                query.append(" AND obj.professorSintetico.codigo = :professor ");
                p.put("professor", professor);
            }
            if (!UteisValidacao.emptyString(filtroNome)) {
                query.append("AND upper(obj.nome) like :filtroNome ");
            }
            query.append(" AND(obj.ativo is true or obj.ativo is null) ");
            query.append(" ORDER BY obj.nome ");
            p.put("dia", Calendario.getDataComHoraZerada(diaLimite));
            if (!UteisValidacao.emptyString(filtroNome)) {
                p.put("filtroNome", "%" + filtroNome.toUpperCase() + "%");
            }
            return obterPorParam(ctx, query.toString(), p, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public String concluirCadastro(String ctx, ClienteSintetico clienteEscolhido,
                                   List<VinculoJSON> vinculos, IntegracaoCadastrosWSConsumer integracaoWS,
                                   String email, String senha, Integer novoProfessor, boolean usarAplicativoAgora, Integer codigoUsuario, String nomeEmpresa) throws ValidacaoException, ServiceException {
        return concluirCadastro(ctx, clienteEscolhido, vinculos, integracaoWS, email, senha, novoProfessor, usarAplicativoAgora, codigoUsuario, true, nomeEmpresa);
    }

    @Override
    public String concluirCadastro(String ctx, ClienteSintetico clienteEscolhido,
                                   List<VinculoJSON> vinculos, IntegracaoCadastrosWSConsumer integracaoWS,
                                   String email, String senha, Integer novoProfessor, boolean usarAplicativoAgora, Integer codigoUsuario,
                                   boolean obrigarProfessor, String nomeEmpresa) throws ValidacaoException, ServiceException {
        try {
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            if (!usarAplicativoAgora) {
                email = clienteEscolhido.getMatricula().toString();
                SecureRandom random = new SecureRandom();
                senha = new BigInteger(130, random).toString(32);
            }
            Usuario usuario = usuarioService.consultarPorUserName(ctx, email);
            if (usuario != null
                    && ((usuario.getCliente() != null && usuario.getCliente().getCodigo() != null && !usuario.getCliente().getMatricula().equals(clienteEscolhido.getMatricula()))
                    || (usuario.getProfessor() != null && usuario.getProfessor().getCodigo() != null && usuario.getProfessor().getCodigo() != 0))) {
                throw new ValidacaoException(getViewUtils().getMensagem("username.em.uso") + " "
                        + (usuario.getCliente() == null ? usuario.getProfessor() == null ? ""
                        : "Colaborador: " + usuario.getProfessor().getNome() : usuario.getCliente().getNome()));
            }
            String retorno;
            if (UteisValidacao.emptyString(SuperControle.getNomeAppParaEmail(ctx)) || SuperControle.getNomeAppParaEmail(ctx).equals("false")) {
                retorno = integracaoWS.gerarUsuarioMovelAluno(url, ctx, email, senha,
                        clienteEscolhido.getCodigoCliente(), SuperControle.getNomeAppParaEmail(ctx), SuperControle.getAppUrlEmail(ctx), nomeEmpresa);
            } else {
                retorno = integracaoWS.gerarUsuarioMovelAluno(url, ctx, email, senha,
                        clienteEscolhido.getCodigoCliente(), SuperControle.getNomeAppParaEmail(ctx), SuperControle.getAppUrlEmail(ctx), SuperControle.getNomeAppParaEmail(ctx));
            }

            if (retorno.contains("ERRO")) {
                throw new ValidacaoException(retorno.replaceFirst("ERRO: ", ""));
            }

            ClienteSintetico aluno = obterPorAtributo(ctx, "codigoCliente", clienteEscolhido.getCodigoCliente());
            if (aluno == null) {
                throw new ValidacaoException(getViewUtils().getMensagem("cliente.naoencontrado"));
            }
            ProfessorSintetico professorAntesAlterar = aluno.getProfessorSintetico();
            aluno.setUltimaVisita(clienteEscolhido.getUltimaVisita());
            aluno.setCargo(clienteEscolhido.getCargo());
            aluno.setFreePass(clienteEscolhido.isFreePass());
            aluno.setTelefones(clienteEscolhido.getTelefones());
            aluno.setEndereco(clienteEscolhido.getEndereco());
            aluno.setCidade(clienteEscolhido.getCidade());
            aluno.setBairro(clienteEscolhido.getBairro());
            aluno.setEstadoCivil(clienteEscolhido.getEstadoCivil());
            aluno.setCPF(clienteEscolhido.getCPF());
            aluno.setRG(clienteEscolhido.getRG());
            aluno.setUF(clienteEscolhido.getUF());
            aluno.setUF(clienteEscolhido.getUF());
            aluno.setSituacao(clienteEscolhido.getSituacao());
            aluno.setSituacaoContrato(clienteEscolhido.getSituacaoContrato());

            if (novoProfessor != null && novoProfessor > 0) {
                String retornoVinculo = integracaoWS.gerarVinculoProfessorAluno(url, ctx, clienteEscolhido.getCodigoCliente(), novoProfessor, codigoUsuario);
                if (!retornoVinculo.toUpperCase().contains("SUCESSO")) {
                    throw new ValidacaoException(retornoVinculo.replaceFirst("ERRO: ", ""));
                }
                clienteEscolhido.setProfessorSintetico(new ProfessorSintetico());
                clienteEscolhido.getProfessorSintetico().setCodigoColaborador(novoProfessor);
                Uteis.logar(null, retornoVinculo);
            }
            ProfessorSintetico professorTreino = getProfessorSintetico().obterProfessorTreino(ctx, clienteEscolhido, vinculos, obrigarProfessor);
            clienteEscolhido.setProfessorSintetico(professorTreino);
            aluno.setProfessorSintetico(professorTreino);
            aluno.setEmail(aluno.getEmail() == null || aluno.getEmail().isEmpty() ? email : aluno.getEmail());
            alterar(ctx, aluno);
            inserirLogAlteracaoProfessorAluno(ctx, professorAntesAlterar, professorTreino, aluno.getMatricula(), null);

            Map<String, String> dadosEvento = new HashMap<String, String>();

            dadosEvento.put("NomeAluno", aluno.getNome());

            if (usarAplicativoAgora) {
                adicionarUsuarioServicoDescobrir(ctx, email);
            }
            return retorno;
        } catch (WebServiceException wse) {
            if (wse.getCause() instanceof SocketTimeoutException) {
                throw new ServiceException("Serviço de importação de aluno indisponível temporariamente, tente novmente");
            }
            throw new ServiceException(wse.getMessage());
        } catch (ValidacaoException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void adicionarUsuarioServicoDescobrir(String ctx, String email) throws IOException {
        try {
            if (email.contains("@")) {
                String url = String.format("%s/prest/empresa/%s/inserirUsuario", Aplicacao.getProp(Aplicacao.urlOAMD), ctx);
                Map<String, String> params = new HashMap<String, String>();
                params.put("email", email);
                ExecuteRequestHttpService.executeRequest(url, params);
            }
        } catch (Exception e) {
            Uteis.logar(e, ClienteSinteticoServiceImpl.class);
        }
    }

    @Override
    public ClienteSintetico consultarSimplesPorMatricula(String ctx, Integer matricula) throws ServiceException {
        try {
            List<ClienteSintetico> lista = getClienteSinteticoDao().findObjectsByAttributesSimple(ctx, new String[]{"codigo", "nome"},
                    new String[]{"matricula"}, new Object[]{matricula},
                    "nome", 0);
            return lista == null || lista.isEmpty() ? null : lista.get(0);
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    public ModelMap consultarTreinosRealizadosPorDiaSemana(String ctx, Long dataInicial, Long dataFinal, Integer codigoAluno, String username) throws ServiceException {

        ModelMap modelMap = new ModelMap();
        for (DiasSemana d : DiasSemana.values()) {
            modelMap.addAttribute(d.getMin().toUpperCase(), 0);
        }
        List<TreinoRealizado> treinoRealizados = null;
        try {
            treinoRealizados = treinoService.obterTreinosRealizados(ctx, null, new Date(dataInicial), new Date(dataFinal), codigoAluno);
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (TreinoRealizado t : treinoRealizados) {
            try {
                DiasSemana diaSemana = DiasSemana.getDiaSemana(Uteis.obterDiaSemanaData(t.getDataInicio()));
                String sigla = diaSemana.getMin().toUpperCase();
                modelMap.put(sigla, (Integer) modelMap.get(sigla) + 1);

            } catch (Exception e) {
                throw new ServiceException(e);
            }
        }
        return modelMap;
    }

    public ClienteSintetico consultarSimplesPorCodigoCliente(String ctx, Integer codigoCliente) throws ServiceException {
        try {
            List<ClienteSintetico> lista = getClienteSinteticoDao().findObjectsByAttributesSimple(ctx, new String[]{"codigo", "nome"},
                    new String[]{"codigoCliente"}, new Object[]{codigoCliente},
                    "nome", 0);
            return lista == null || lista.isEmpty() ? null : lista.get(0);
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public List<ClienteSintetico> consultarNaAcademia(final String ctx,
                                                      final Integer empresaZW, final Integer professor, String filtroNome, final Date inicio, StatusClienteEnum status, Integer... index) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            Map<String, Object> params = new HashMap<String, Object>();
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);

            ConfiguracaoSistema duracaoNaAcademia = css.consultarPorTipo(ctx, ConfiguracoesEnum.DURACAO_ALUNO_NA_ACADEMIA);

            query.append("SELECT obj FROM ClienteSintetico obj ");
            query.append(" WHERE obj.codigo IN (SELECT sta.usuario.cliente.codigo FROM StatusPessoa sta ");
            query.append(" WHERE sta.dataInicioEvento between :inicio AND :fim AND sta.dataFimEvento IS NULL\n");
            query.append(" AND sta.empresa.codigo = :empresa)");
            if (professor != null) {
                query.append(" AND obj.professorSintetico.codigo = :professor ");
                params.put("professor", professor);
            }
            if (!UteisValidacao.emptyString(filtroNome)) {
                query.append("AND upper(obj.nome) like :filtroNome ");
            }
            if (status != null && status.equals(StatusClienteEnum.NA_ACADEMIA_TREINO_VENCIDO)) {
                query.append(" and obj.codigo IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE :hoje > p.dataTerminoPrevisto) ");
                query.append(" and obj.codigo NOT IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE :hoje <= p.dataTerminoPrevisto) ");
                params.put("hoje", Calendario.hoje());
            }
            if (status != null && status.equals(StatusClienteEnum.NA_ACADEMIA_TREINO_A_VENCER)) {
                ConfiguracaoSistema diasAntes = css.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
                Date depois = Uteis.somarCampoData(Calendario.hoje(), Calendar.DAY_OF_MONTH, diasAntes.getValorAsInteger());
                params.put("antes", Calendario.getDataComHoraZerada(Calendario.hoje()));
                params.put("depois", depois);
                query.append(" and obj.codigo IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE p.programaTreinoRenovado is null and p.dataTerminoPrevisto between :antes and :depois)  ");

            }

            if (status != null && status.equals(StatusClienteEnum.NA_ACADEMIA_SEM_TREINO)) {
                query.append(" and NOT EXISTS (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE obj.codigo = p.cliente.codigo)");
            }

            query.append(" ORDER BY obj.nome ");
            params.put("empresa", empresaZW);
            params.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger()));
            params.put("fim", Calendario.getDataComHora(inicio, "23:59:59"));
            if (!UteisValidacao.emptyString(filtroNome)) {
                params.put("filtroNome", "%" + filtroNome.toUpperCase() + "%");
            }
            List<ClienteSintetico> resultado = obterPorParam(ctx, query.toString(), params, index);
            Ordenacao.ordenarLista(resultado, "nome");
            return resultado;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ClienteSintetico> consultarSemTreino(final String ctx,
                                                     final Integer empresaZW, final Integer professor, String filtroNome, final Integer max, final Integer index) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            Map<String, Object> params = new HashMap<String, Object>();

            query.append("SELECT obj FROM ClienteSintetico obj ");
            query.append(" WHERE empresa = ").append(empresaZW).
                    append(" and NOT EXISTS (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE obj.codigo = p.cliente.codigo)");
            if (professor != null) {
                query.append(" AND obj.professorSintetico.codigo = :professor ");
                params.put("professor", professor);
            }
            if (!UteisValidacao.emptyString(filtroNome)) {
                query.append("AND upper(obj.nome) like :filtroNome ");
            }
            query.append(" AND(obj.ativo is true or obj.ativo is null) ");
            query.append(" ORDER BY obj.nome ");

            if (!UteisValidacao.emptyString(filtroNome)) {
                params.put("filtroNome", "%" + filtroNome.toUpperCase() + "%");
            }
            if (max == null || index == null) {
                return obterPorParam(ctx, query.toString(), params);
            } else {
                return obterPorParam(ctx, query.toString(), params, max, index);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ClienteSintetico> consultarTreinoVencido(final String ctx,
                                                         final Integer empresaZW, final Integer professor, String filtroNome, final Integer max, final Integer index) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            Map<String, Object> params = new HashMap<String, Object>();

            query.append("SELECT obj FROM ClienteSintetico obj ");
            query.append(" WHERE empresa = ").append(empresaZW);
            query.append(" and obj.codigo IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE :hoje > p.dataTerminoPrevisto and p.cliente.codigo is not null) ");
            query.append(" and obj.codigo NOT IN (SELECT p.cliente.codigo FROM ProgramaTreino p WHERE :hoje <= p.dataTerminoPrevisto and p.cliente.codigo is not null) ");
            if (professor != null) {
                query.append(" AND obj.professorSintetico.codigo = :professor ");
                params.put("professor", professor);
            }
            if (!UteisValidacao.emptyString(filtroNome)) {
                query.append("AND upper(obj.nome) like :filtroNome ");
            }
            query.append(" AND(obj.ativo is true or obj.ativo is null) ");
            query.append(" ORDER BY obj.nome ");
            params.put("hoje", Calendario.hoje());
            if (!UteisValidacao.emptyString(filtroNome)) {
                params.put("filtroNome", "%" + filtroNome.toUpperCase() + "%");
            }
            if (max == null || index == null) {
                return obterPorParam(ctx, query.toString(), params);
            } else {
                return obterPorParam(ctx, query.toString(), params, max, index);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ClienteSintetico> consultarSimplesPorNome(final String ctx,
                                                          final Integer empresaZW, final String nome) throws ServiceException {
        try {
            String query = "select new ClienteSintetico(codigo, nome) FROM ClienteSintetico WHERE empresa = " + empresaZW + " and lower(nome) LIKE '"
                    + (nome == null ? "" : nome.toLowerCase()) + "%' ORDER BY nome";
            return getClienteSinteticoDao().findByParam(ctx, query, new HashMap<String, Object>());
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public ClienteAcompanhamento obterAcompanhamento(final String ctx, final ClienteSintetico cliente) throws ServiceException {
        try {
            String sql = "SELECT obj FROM ClienteAcompanhamento obj "
                    + "WHERE obj.inicio between :inicio and :fim and obj.cliente.codigo = :cliente";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("inicio", Calendario.getDataComHoraZerada(Calendario.hoje()));
            p.put("fim", Calendario.getDataComHora(Calendario.hoje(), "23:59:59"));
            p.put("cliente", cliente.getCodigo());
            return getClienteAcompanhamentoDao().findObjectByParam(ctx, sql, p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ClienteSintetico> consultarTodosSoNomeCodigo(final String ctx,
                                                             final Integer empresaZW) throws ServiceException {
        try {
            return getClienteSinteticoDao().findObjectsByAttributesSimple(ctx, new String[]{"codigo", "nome"},
                    new String[]{"empresa"}, new Object[]{empresaZW}, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public boolean mostrarTodosAlunos(final Usuario usuario) {
        return (usuario.getTipo().equals(TipoUsuarioEnum.ROOT)
                || (usuario.isFuncionalidadeHabilitado(RecursoEnum.VER_ALUNOS_OUTRAS_CARTEIRAS.name())));
    }

    @Override
    public List<ClienteSintetico> filtrarStatus(final String ctx, Usuario usuario, StatusClienteEnum status, String filtroNome, Integer max, Integer index) throws ServiceException {
        List<ClienteSintetico> clientes = new ArrayList<ClienteSintetico>();
        try {
            if (status != null) {
                Integer[] indexResult = new Integer[]{max, index};
                switch (status) {
                    case MINHA_CARTEIRA:
                        clientes = consultarPorCodigoPessoaProfessor(ctx, usuario.getProfessor().getCodigoPessoa(),
                                usuario.getEmpresaZW(), filtroNome, false, indexResult);
                        break;
                    case ACOMPANHANDO:
                        clientes = consultarPorAcompanhadosProfessor(ctx, usuario.getProfessor().getCodigo(), filtroNome,
                                Calendario.hoje(), Calendario.hoje(), indexResult);
                        break;
                    case AGENDADOS:
                        clientes = consultarPorAgendadosProfessor(ctx,
                                usuario.getEmpresaZW(),
                                mostrarTodosAlunos(usuario) ? null : usuario.getProfessor().getCodigo(), filtroNome,
                                Calendario.hoje(), Calendario.hoje(), indexResult);
                        break;
                    case DESACOMPANHADOS:
                        ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
                        ConfiguracaoSistema cfgDesacompanhados = css.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_SEM_OBSERVACAO_CONSIDERAR_DESACOMPANHADO);
                        if (UteisValidacao.emptyNumber(cfgDesacompanhados.getValorAsInteger())) {
                            clientes = consultarPorDesacompanhadosProfessor(ctx,
                                    usuario.getEmpresaZW(),
                                    mostrarTodosAlunos(usuario) ? null : usuario.getProfessor().getCodigo(), filtroNome,
                                    Calendario.hoje(), Calendario.hoje(), true, null, null, null, indexResult);
                        } else {
                            clientes = consultarAlunosSemObservacaoXDias(ctx,
                                    usuario.getEmpresaZW(),
                                    usuario.getProfessor().getCodigo(), filtroNome,
                                    cfgDesacompanhados.getValorAsInteger(), indexResult);
                        }
                        break;
                    case NA_ACADEMIA:
                    case NA_ACADEMIA_TREINO_A_VENCER:
                    case NA_ACADEMIA_TREINO_VENCIDO:
                    case NA_ACADEMIA_SEM_TREINO:
                        Empresa empresa = empresaService.obterPorIdZW(ctx, usuario.getEmpresaZW());
                        clientes = consultarNaAcademia(ctx,
                                empresa.getCodigo(),
                                mostrarTodosAlunos(usuario) ? null : usuario.getProfessor().getCodigo(), filtroNome,
                                Calendario.hoje(), status, indexResult);
                        break;
                    case NA_ACADEMIA_MINHA_CARTEIRA:
                        clientes = consultarNaAcademia(ctx,
                                usuario.getEmpresaZW(),
                                usuario.getProfessor().getCodigo(), filtroNome,
                                Calendario.hoje(), status, indexResult);
                        break;
                    case SEM_TREINO:
                        clientes = consultarSemTreino(ctx,
                                usuario.getEmpresaZW(),
                                mostrarTodosAlunos(usuario) ? null : usuario.getProfessor().getCodigo(), filtroNome,
                                max, index);
                        break;
                    case TREINO_VENCIDO:
                        clientes = consultarTreinoVencido(ctx,
                                usuario.getEmpresaZW(),
                                mostrarTodosAlunos(usuario) ? null : usuario.getProfessor().getCodigo(), filtroNome, max, index);
                        break;
                    case TODOS:
                        if (mostrarTodosAlunos(usuario)) {
                            if (max != null && index != null) {
                                StringBuilder query = new StringBuilder();
                                query.append("SELECT obj FROM ClienteSintetico obj WHERE obj.empresa = :empresa ");
                                if (!UteisValidacao.emptyString(filtroNome)) {
                                    query.append("AND upper(obj.nome) like :filtroNome ");
                                }
                                query.append(" AND(obj.ativo is true or obj.ativo is null) ");
                                query.append("ORDER BY obj.nome");
                                Map<String, Object> params = new HashMap<String, Object>();
                                params.put("empresa", usuario.getEmpresaZW());
                                if (!UteisValidacao.emptyString(filtroNome)) {
                                    params.put("filtroNome", "%" + filtroNome.toUpperCase() + "%");
                                }
                                clientes = obterPorParam(ctx, query.toString(), params, max, index);
                            } else {
                                clientes = obterTodos(ctx, usuario.getEmpresaZW(), indexResult);
                            }
                        } else {
                            clientes = consultarPorCodigoPessoaProfessor(ctx, usuario.getProfessor().getCodigoPessoa(),
                                    usuario.getEmpresaZW(), filtroNome, false, indexResult);
                        }
                        break;
                }
            }
            return clientes;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ClienteSintetico> filtrarStatusApp(final String ctx, Usuario usuario, StatusClienteEnum status, String filtroNome, Integer max, Integer index, Integer empresaZW) throws ServiceException {
        List<ClienteSintetico> clientes = new ArrayList<ClienteSintetico>();
        try {
            if (status != null) {
                Integer[] indexResult = new Integer[]{max, index};
                switch (status) {
                    case MINHA_CARTEIRA:
                        clientes = consultarPorCodigoPessoaProfessor(ctx, usuario.getProfessor().getCodigoPessoa(),
                                empresaZW, filtroNome, false, indexResult);
                        break;
                    case ACOMPANHANDO:
                        clientes = consultarPorAcompanhadosProfessor(ctx, usuario.getProfessor().getCodigo(), filtroNome,
                                Calendario.hoje(), Calendario.hoje(), indexResult);
                        break;
                    case AGENDADOS:
                        clientes = consultarPorAgendadosProfessor(ctx,
                                empresaZW,
                                mostrarTodosAlunos(usuario) ? null : usuario.getProfessor().getCodigo(), filtroNome,
                                Calendario.hoje(), Calendario.hoje(), indexResult);
                        break;
                    case DESACOMPANHADOS:
                        ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
                        ConfiguracaoSistema cfgDesacompanhados = css.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_SEM_OBSERVACAO_CONSIDERAR_DESACOMPANHADO);
                        if (UteisValidacao.emptyNumber(cfgDesacompanhados.getValorAsInteger())) {
                            clientes = consultarPorDesacompanhadosProfessor(ctx,
                                    empresaZW,
                                    mostrarTodosAlunos(usuario) ? null : usuario.getProfessor().getCodigo(), filtroNome,
                                    Calendario.hoje(), Calendario.hoje(), true, null, null, null, indexResult);
                        } else {
                            clientes = consultarAlunosSemObservacaoXDias(ctx,
                                    empresaZW,
                                    usuario.getProfessor().getCodigo(), filtroNome,
                                    cfgDesacompanhados.getValorAsInteger(), indexResult);
                        }
                        break;
                    case NA_ACADEMIA:
                    case NA_ACADEMIA_TREINO_A_VENCER:
                    case NA_ACADEMIA_TREINO_VENCIDO:
                    case NA_ACADEMIA_SEM_TREINO:
                        Empresa empresa = empresaService.obterPorIdZW(ctx, empresaZW);
                        clientes = consultarNaAcademia(ctx,
                                empresa.getCodigo(),
                                mostrarTodosAlunos(usuario) ? null : usuario.getProfessor().getCodigo(), filtroNome,
                                Calendario.hoje(), status, indexResult);
                        break;
                    case NA_ACADEMIA_MINHA_CARTEIRA:
                        clientes = consultarNaAcademia(ctx,
                                empresaZW,
                                usuario.getProfessor().getCodigo(), filtroNome,
                                Calendario.hoje(), status, indexResult);
                        break;
                    case SEM_TREINO:
                        clientes = consultarSemTreino(ctx,
                                empresaZW,
                                mostrarTodosAlunos(usuario) ? null : usuario.getProfessor().getCodigo(), filtroNome,
                                max, index);
                        break;
                    case TREINO_VENCIDO:
                        clientes = consultarTreinoVencido(ctx,
                                empresaZW,
                                mostrarTodosAlunos(usuario) ? null : usuario.getProfessor().getCodigo(), filtroNome, max, index);
                        break;
                    case TODOS:
                        if (mostrarTodosAlunos(usuario)) {
                            if (max != null && index != null) {
                                StringBuilder query = new StringBuilder();
                                query.append("SELECT obj FROM ClienteSintetico obj WHERE obj.empresa = :empresa ");
                                if (!UteisValidacao.emptyString(filtroNome)) {
                                    query.append("AND (UNACCENT(UPPER(obj.nome)) LIKE UNACCENT(CONCAT(:nome,'%')))");
                                    query.append(" OR UNACCENT(UPPER(obj.nomeConsulta)) LIKE UNACCENT(CONCAT(:nome,'%'))");
                                    query.append(" OR obj.email LIKE :email)");
                                }
                                query.append(" AND(obj.ativo is true or obj.ativo is null) ");
                                query.append("ORDER BY obj.nome");
                                Map<String, Object> params = new HashMap<String, Object>();
                                params.put("empresa", empresaZW);
                                if (!UteisValidacao.emptyString(filtroNome)) {
                                    params.put("nome", filtroNome.toUpperCase());
                                    params.put("email", "%" + filtroNome + "%");
                                }
                                clientes = obterPorParam(ctx, query.toString(), params, max, index);
                            } else {
                                clientes = obterTodos(ctx, empresaZW, indexResult);
                            }
                        } else {
                            clientes = consultarPorCodigoPessoaProfessor(ctx, usuario.getProfessor().getCodigoPessoa(),
                                    empresaZW, filtroNome, false, indexResult);
                        }
                        break;
                }
            }
            return clientes;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void atualizarNrTreinosRealizados(final String ctx, final Integer codCliente,
                                             final Integer nrTreinos) throws ServiceException {
        try {
            clienteSinteticoDao.updateAlgunsCampos(ctx, new String[]{"nrtreinosrealizados"},
                    new Object[]{nrTreinos}, new String[]{"codigo"}, new Object[]{codCliente});
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClienteObservacao gravarObservacaoCliente(final String ctx, ClienteObservacao clienteObservacao) throws ServiceException {
        try {
            return clienteObservacaoDao.insert(ctx, clienteObservacao);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClienteObservacao gravarAlteracaoObservacaoCliente(final String ctx, ClienteSintetico cliente,
                                                              String obs, boolean avaliacaoFisica, Usuario usuario, Boolean importante, int codigo) throws ServiceException {
        try {
            ClienteObservacao clienteObservacao = new ClienteObservacao();
            clienteObservacao.setAvaliacaoFisica(avaliacaoFisica);
            clienteObservacao.setCliente(cliente);
            clienteObservacao.setDataObservacao(Calendario.hoje());
            clienteObservacao.setUsuario_codigo(usuario.getCodigo());
            clienteObservacao.setObservacao(obs);
            clienteObservacao.setImportante(importante);
            return clienteObservacaoDao.insert(ctx, clienteObservacao);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClienteObservacao alterarObservacaoCliente(final String ctx, ClienteObservacao obj) throws ServiceException {
        try {
            return clienteObservacaoDao.update(ctx, obj);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClienteObservacao consultarUltimaObservacaoCliente(final String ctx, ClienteSintetico cliente, boolean avaliacao) throws ServiceException {
        try {
            String query = "SELECT obj FROM ClienteObservacao obj WHERE obj.cliente.codigo = :cliente";
            if (avaliacao) {
                query += " AND obj.avaliacaoFisica IS TRUE";
            } else {
                query += " AND obj.avaliacaoFisica IS FALSE";
            }
            query += " ORDER BY obj.dataObservacao DESC";
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("cliente", cliente.getCodigo());
            List<ClienteObservacao> find = clienteObservacaoDao.findByParam(ctx, query, param, 1, 0);
            return find == null || find.isEmpty() ? null : find.get(0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ClienteObservacao> consultarObservacoesCliente(final String ctx, ClienteSintetico cliente, boolean avaliacao) throws ServiceException {
        try {
            String query = "SELECT obj FROM ClienteObservacao obj WHERE obj.cliente.codigo = :cliente";
            if (avaliacao) {
                query += " AND obj.avaliacaoFisica IS TRUE";
            } else {
                query += " AND obj.avaliacaoFisica IS FALSE";
            }
            query += " ORDER BY obj.dataObservacao DESC";
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("cliente", cliente.getCodigo());
            return clienteObservacaoDao.findByParam(ctx, query, param);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClienteSintetico obterPorCodigoCliente(final String ctx, final Integer codigo) throws ServiceException {
        try {
            String s = "select obj from ClienteSintetico obj where codigoCliente = :id";
            Map<String, Object> p = new HashMap();
            p.put("id", codigo);
            return obterObjetoPorParam(ctx, s, p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluirAluno(String ctx, Integer codigoCliente) throws ServiceException {
        excluirAluno(ctx, codigoCliente, null);
    }

    @Override
    public void excluirAluno(String ctx, Integer codigoCliente, ClienteSintetico clienteSintetico) throws ServiceException {
        try {
            if (clienteSintetico == null && !UteisValidacao.emptyNumber(codigoCliente)) {
                clienteSintetico = consultarSimplesPorCodigoCliente(ctx, codigoCliente);
            }
            if (clienteSintetico != null && !UteisValidacao.emptyNumber(clienteSintetico.getCodigo())) {
                Integer codigoSintetico = clienteSintetico.getCodigo();

                try (Connection conn = clienteSinteticoDao.getConnection(ctx)) {

                    executarDelete(conn, "DELETE FROM statuspessoa WHERE usuario_codigo IN (SELECT codigo FROM usuario WHERE cliente_codigo = ?)", codigoSintetico);
                    executarDelete(conn, "DELETE FROM notificacao WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM objetivoprograma WHERE programa_codigo IN (SELECT codigo FROM programatreino WHERE cliente_codigo = ?)", codigoSintetico);

                    excluirProgramaTreino(ctx, codigoSintetico);

                    executarDelete(conn, "DELETE FROM lembreteagendamento WHERE agendamento_codigo IN (SELECT codigo FROM agendamento WHERE cliente_codigo = ?)", codigoSintetico);
                    executarDelete(conn, "DELETE FROM agendamento WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM treinorealizado WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM clienteacompanhamento WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM historiconivelcliente WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM respostacliente WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM respostaclienteparq WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM itemavaliacaofisica WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM ventilometria WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM itemavaliacaopostural WHERE avaliacaopostural_codigo IN (SELECT codigo FROM avaliacaopostural WHERE avaliacao_codigo IN (SELECT codigo FROM avaliacaofisica WHERE cliente_codigo = ?))", codigoSintetico);
                    executarDelete(conn, "DELETE FROM avaliacaopostural WHERE avaliacao_codigo IN (SELECT codigo FROM avaliacaofisica WHERE cliente_codigo = ?)", codigoSintetico);
                    executarDelete(conn, "DELETE FROM flexibilidade WHERE avaliacao_codigo IN (SELECT codigo FROM avaliacaofisica WHERE cliente_codigo = ?)", codigoSintetico);
                    executarDelete(conn, "DELETE FROM pesoosseo WHERE avaliacaofisica_codigo IN (SELECT codigo FROM avaliacaofisica WHERE cliente_codigo = ?)", codigoSintetico);
                    executarDelete(conn, "DELETE FROM avaliacaofisica WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM historicorevisaoprogramatreino WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM clienteobservacao WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM comentariowod WHERE usuario_codigo IN (SELECT codigo FROM usuario WHERE cliente_codigo = ?)", codigoSintetico);
                    executarDelete(conn, "DELETE FROM scoretreino WHERE usuario_codigo IN (SELECT codigo FROM usuario WHERE cliente_codigo = ?)", codigoSintetico);
                    executarDelete(conn, "DELETE FROM usuarioemail WHERE usuario IN (SELECT codigo FROM usuario WHERE cliente_codigo = ?)", codigoSintetico);
                    executarDelete(conn, "DELETE FROM avaliacaoprofessor WHERE usuario_codigo IN (SELECT codigo FROM usuario WHERE cliente_codigo = ?)", codigoSintetico);
                    executarDelete(conn, "DELETE FROM usuario WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM historicopresenca WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM agendamentolocacaoproduto WHERE agendamento_codigo IN (SELECT codigo FROM agendamentolocacao WHERE cliente_codigo = ?)", codigoSintetico);
                    executarDelete(conn, "DELETE FROM agendamentolocacao WHERE cliente_codigo = ?", codigoSintetico);
                    executarDelete(conn, "DELETE FROM notificacaoaulaagendada WHERE cliente = ?", codigoSintetico);

                    getClienteSinteticoDao().delete(ctx, codigoSintetico);

                } catch (SQLException e) {
                    throw new ServiceException("Erro ao excluir aluno com código " + codigoSintetico, e);
                }
            }

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void executarDelete(Connection conn, String sql, int parametro) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, parametro);
            stmt.executeUpdate();
        }
    }

    @Override
    public void excluirAlunoMatricula(String ctx, Integer matricula) throws ServiceException {
        try {
            ClienteSintetico clienteSintetico = consultarPorMatricula(ctx, matricula.toString());
            if (clienteSintetico != null && clienteSintetico.getCodigo() != null) {
                deletarAlunoeRelacionamentos(ctx, clienteSintetico);
            }
            getClientePesquisaService().excluirPorMatricula(ctx, matricula);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }


    private void deletarAlunoeRelacionamentos(String ctx, ClienteSintetico clienteSintetico) throws ServiceException {
        try {
            Integer codigo = clienteSintetico.getCodigo();
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from statuspessoa where usuario_codigo in(select codigo from usuario where cliente_codigo = " + codigo + ")");
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from usuario where cliente_codigo = " + codigo);
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from notificacao where cliente_codigo = " + codigo);
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from objetivoprograma where programa_codigo in(select codigo from programatreino where cliente_codigo =" + codigo + ")");
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from programatreinoficha where programa_codigo in(select codigo from programatreino where cliente_codigo =" + codigo + ")");
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from programatreino where cliente_codigo = " + codigo);
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from lembreteagendamento where agendamento_codigo in (select codigo from agendamento where cliente_codigo = " + codigo + ")");
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from agendamento where cliente_codigo = " + codigo);
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from treinorealizado where cliente_codigo = " + codigo);
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from clienteacompanhamento where cliente_codigo = " + codigo);
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from historiconivelcliente where cliente_codigo = " + codigo);
            getClienteSinteticoDao().executeNativeSQL(ctx, "delete from clientesintetico where codigo = " + codigo);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void excluirProgramaTreino(String ctx, Integer codigo) throws Exception {

        ProgramaTreino programaTreino = new ProgramaTreino();
        List<ProgramaTreinoFicha> programasTreinoFicha = new ArrayList<ProgramaTreinoFicha>();
        programaTreino = getProgramaTreinoDao().findObjectByAttribute(ctx, "cliente_codigo", codigo);

        if (programaTreino != null) {

            programasTreinoFicha = getProgramaTreinoFichaDao().obterPorProgramaTreino(ctx, programaTreino.getCodigo());

            if (programasTreinoFicha != null && !programasTreinoFicha.isEmpty()) {

                for (ProgramaTreinoFicha programaTreinoFicha : programasTreinoFicha) {
                    getClienteSinteticoDao().executeNativeSQL(ctx, "delete from programatreinoficha_diasemana where programatreinoficha_codigo = " + programaTreinoFicha.getCodigo());
                    getProgramaTreinoFichaDao().delete(ctx, programaTreinoFicha);
                }

            }
            getProgramaTreinoDao().delete(ctx, programaTreino);
        }
    }

    @Override
    public String addAluno(final String ctx, final Integer matricula, final Integer professor, boolean usarAplicativo, Integer codigoUsuario, Integer empresa) throws Exception {
        return addAluno(ctx, matricula, professor, usarAplicativo, codigoUsuario, empresa);
    }

    @Override
    public String addAluno(final String ctx, final Integer matricula, final Integer professor, boolean usarAplicativo, Integer codigoUsuario, Integer empresa, boolean obrigarProfessor) throws Exception {
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        List<AddClienteJSON> clientes = integracaoWS.consultarClientesZW(url, ctx, empresa, "", "", matricula);
        if (clientes == null || clientes.isEmpty()) {
            throw new ServiceException("Seu cadastro não foi encontrado, procure a administração!");
        }
        ClienteSintetico cliente = integracaoWS.consultarClienteSintetico(url, ctx, clientes.get(0).getCodigoCliente());
        String userName = !usarAplicativo || cliente.getEmail() == null || cliente.getEmail().isEmpty()
                ? matricula.toString() : cliente.getEmail();
        EmpresaService empresaSer = UtilContext.getBean(EmpresaService.class);
        Empresa empresaCliente = empresaSer.obterPorId(cliente.getKey(), cliente.getEmpresa());
        return concluirCadastro(ctx, cliente, null, integracaoWS, userName, null, professor, usarAplicativo, codigoUsuario, obrigarProfessor,
                empresaCliente == null ? "false" : empresaCliente.getNome());
    }

    @Override
    public String addAlunoV2(final String ctx, final Integer matricula, final Integer codigoCliente, final Integer professor, boolean usarAplicativo, Integer codigoUsuario, Integer empresa, boolean obrigarProfessor) throws Exception {
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);

        ClienteSintetico cliente = consultarClienteSinteticoOtimizadoBDZW(ctx, codigoCliente);
        String userName = !usarAplicativo || cliente.getEmail() == null || cliente.getEmail().isEmpty() ? matricula.toString() : cliente.getEmail();
        EmpresaService empresaSer = UtilContext.getBean(EmpresaService.class);
        Empresa empresaCliente = empresaSer.obterPorId(cliente.getKey(), cliente.getEmpresa());

        return concluirCadastro(ctx, cliente, null, integracaoWS, userName, null, professor, usarAplicativo, codigoUsuario, obrigarProfessor,
                empresaCliente == null ? "false" : empresaCliente.getNome());
    }

    /*
    * Métod0 consultarClienteSinteticoOtimizadoBDZW criado para substituir integracaoWS.consultarClientesZW e consultar diretamente no banco zw
    */
    @Override
    public ClienteSintetico consultarClienteSinteticoOtimizadoBDZW(String ctx, Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT scdw.*, t.numero, t2.numero as residencial, e.bairro, e.endereco, c.nome as nomeCidade, ");
        sql.append("p.estadocivil, p.rg, p.cfp, s.sigla ");
        sql.append("FROM SituacaoClienteSinteticoDW scdw ");
        sql.append("LEFT JOIN pessoa p ON scdw.codigopessoa = p.codigo ");
        sql.append("LEFT JOIN telefone t ON t.pessoa = p.codigo AND t.tipotelefone = 'CE' ");
        sql.append("LEFT JOIN telefone t2 ON t2.pessoa = p.codigo AND t2.tipotelefone = 'RE' ");
        sql.append("LEFT JOIN endereco e ON e.pessoa = p.codigo ");
        sql.append("LEFT JOIN cidade c ON p.cidade = c.codigo ");
        sql.append("LEFT JOIN estado s ON p.estado = s.codigo ");
        sql.append("WHERE scdw.codigocliente = ?");

        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             PreparedStatement ps = conZW.prepareStatement(sql.toString())) {

            ps.setInt(1, codigoCliente);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    JSONObject jsonData = construirJSONClienteSinteticoBDZW(rs);
                    ClienteSintetico clienteSinteticoImportar = new ClienteSintetico(jsonData);
                    clienteSinteticoImportar.setKey(ctx);
                    String usuarioMovel = consultarUsuarioMovelPorClienteBdZw(ctx, codigoCliente);

                    if (!UteisValidacao.emptyString(usuarioMovel)) {
                        clienteSinteticoImportar.setEmail(usuarioMovel);
                    } else {
                        // Ajustar fluxo para ficar igual ao que está no integracaoCadastrosWS do ZW metod0 consultarClienteSintetico
                        String email = consultarEmailPorClienteBdZw(ctx, codigoCliente);
                        clienteSinteticoImportar.setEmail(email);
                    }

                    return clienteSinteticoImportar;
                }
            }
        }
        return null;
    }

    private JSONObject construirJSONClienteSinteticoBDZW(ResultSet rs) throws Exception {
        JSONObject json = new JSONObject();

        // Campos obrigatórios
        json.put("codigoCliente", new JSONArray().put(rs.getInt("codigocliente")));
        json.put("codigoPessoa", new JSONArray().put(rs.getInt("codigopessoa")));
        json.put("matricula", new JSONArray().put(rs.getInt("matricula")));
        json.put("nomeCliente", new JSONArray().put(rs.getString("nomecliente")));
        json.put("situacao", new JSONArray().put(rs.getString("situacao")));
        json.put("empresaCliente", new JSONArray().put(rs.getInt("empresacliente")));

        // Dados pessoais
        json.put("cpf", new JSONArray().put(rs.getString("cfp") != null ? rs.getString("cfp") : ""));
        json.put("rg", new JSONArray().put(rs.getString("rg") != null ? rs.getString("rg") : ""));
        json.put("estadoCivil", new JSONArray().put(rs.getString("estadocivil") != null ? rs.getString("estadocivil") : ""));
        json.put("dataNascimento", new JSONArray().put(rs.getString("datanascimento")));
        json.put("idade", new JSONArray().put(rs.getInt("idade")));
        json.put("sexoCliente", new JSONArray().put(rs.getString("sexocliente")));
        json.put("cargo", new JSONArray().put(rs.getString("cargo") != null ? rs.getString("cargo") : ""));

        // Endereço e contato
        json.put("endereco", new JSONArray().put(rs.getString("endereco") != null ? rs.getString("endereco") : ""));
        json.put("bairro", new JSONArray().put(rs.getString("bairro") != null ? rs.getString("bairro") : ""));
        json.put("cidade", new JSONArray().put(rs.getString("nomeCidade") != null ? rs.getString("nomeCidade") : ""));
        json.put("UF", new JSONArray().put(rs.getString("sigla") != null ? rs.getString("sigla") : ""));

        // Telefones
        String telefone = rs.getString("numero");
        String telefoneRes = rs.getString("residencial");
        json.put("telefonesCliente", new JSONArray().put(telefone != null ? telefone : ""));
        json.put("telefonesConsulta", new JSONArray().put(telefone != null ? telefone : ""));

        // Dados do contrato
        json.put("codigoContrato", new JSONArray().put(rs.getString("codigoContrato") != null ? rs.getString("codigoContrato") : ""));
        json.put("duracaoContratoMeses", new JSONArray().put(rs.getString("duracaoContratoMeses") != null ? rs.getString("duracaoContratoMeses") : ""));
        json.put("situacaoContrato", new JSONArray().put(rs.getString("situacaocontrato") != null ? rs.getString("situacaocontrato") : ""));
        json.put("valorFaturadoContrato", new JSONArray().put(rs.getString("valorFaturadoContrato") != null ? rs.getString("valorFaturadoContrato") : ""));
        json.put("valorPagoContrato", new JSONArray().put(rs.getString("valorPagoContrato") != null ? rs.getString("valorPagoContrato") : ""));
        json.put("valorParcAbertoContrato", new JSONArray().put(rs.getString("valorParcAbertoContrato") != null ? rs.getString("valorParcAbertoContrato") : ""));
        json.put("saldoContaCorrenteCliente", new JSONArray().put(rs.getString("saldoContaCorrenteCliente") != null ? rs.getString("saldoContaCorrenteCliente") : ""));
        json.put("dataVigenciaDe", new JSONArray().put(rs.getString("dataVigenciaDe") != null ? rs.getString("dataVigenciaDe") : ""));
        json.put("dataVigenciaAte", new JSONArray().put(rs.getString("dataVigenciaAte") != null ? rs.getString("dataVigenciaAte") : ""));
        json.put("dataVigenciaAteAjustada", new JSONArray().put(rs.getString("dataVigenciaAteAjustada") != null ? rs.getString("dataVigenciaAteAjustada") : ""));
        json.put("dataLancamentoContrato", new JSONArray().put(rs.getString("dataLancamentoContrato") != null ? rs.getString("dataLancamentoContrato") : ""));
        json.put("dataRenovacaoContrato", new JSONArray().put(rs.getString("dataRenovacaoContrato") != null ? rs.getString("dataRenovacaoContrato") : ""));
        json.put("dataRematriculaContrato", new JSONArray().put(rs.getString("dataRematriculaContrato") != null ? rs.getString("dataRematriculaContrato") : ""));
        json.put("dataUltimoBV", new JSONArray().put(rs.getString("dataUltimoBV") != null ? rs.getString("dataUltimoBV") : ""));
        json.put("dataMatricula", new JSONArray().put(rs.getString("datamatricula") != null ? rs.getString("datamatricula") : ""));
        json.put("dataCadastro", new JSONArray().put(rs.getString("datacadastro") != null ? rs.getString("datacadastro") : ""));
        json.put("dataUltimoAcesso", new JSONArray().put(rs.getString("dataultimoacesso") != null ? rs.getString("dataultimoacesso") : ""));
        json.put("ultimaVisita", new JSONArray().put(rs.getString("ultimavisita") != null ? rs.getString("ultimavisita") : ""));

        json.put("diasAssiduidadeUltRematriculaAteHoje", new JSONArray().put(rs.getString("diasAssiduidadeUltRematriculaAteHoje") != null ? rs.getString("diasAssiduidadeUltRematriculaAteHoje") : ""));
        json.put("faseAtualCRM", new JSONArray().put(rs.getString("faseAtualCRM") != null ? rs.getString("faseAtualCRM") : ""));
        json.put("dataUltimoContatoCRM", new JSONArray().put(rs.getString("dataUltimoContatoCRM") != null ? rs.getString("dataUltimoContatoCRM") : ""));
        json.put("responsavelUltimoContatoCRM", new JSONArray().put(rs.getString("responsavelUltimoContatoCRM") != null ? rs.getString("responsavelUltimoContatoCRM") : ""));
        json.put("codigoUltimoContatoCRM", new JSONArray().put(rs.getString("codigoUltimoContatoCRM") != null ? rs.getString("codigoUltimoContatoCRM") : ""));

        json.put("tipoPeriodoAcesso", new JSONArray().put(rs.getString("tipoPeriodoAcesso") != null ? rs.getString("tipoPeriodoAcesso") : ""));
        json.put("dataInicioPeriodoAcesso", new JSONArray().put(rs.getString("dataInicioPeriodoAcesso") != null ? rs.getString("dataInicioPeriodoAcesso") : ""));
        json.put("dataFimPeriodoAcesso", new JSONArray().put(rs.getString("dataFimPeriodoAcesso") != null ? rs.getString("dataFimPeriodoAcesso") : ""));
        json.put("diasAcessoSemanaPassada", new JSONArray().put(rs.getString("diasAcessoSemanaPassada") != null ? rs.getString("diasAcessoSemanaPassada") : ""));
        json.put("diasAcessoSemana2", new JSONArray().put(rs.getString("diasAcessoSemana2") != null ? rs.getString("diasAcessoSemana2") : ""));
        json.put("diasAcessoSemana3", new JSONArray().put(rs.getString("diasAcessoSemana3") != null ? rs.getString("diasAcessoSemana3") : ""));
        json.put("diasAcessoSemana4", new JSONArray().put(rs.getString("diasAcessoSemana4") != null ? rs.getString("diasAcessoSemana4") : ""));
        json.put("vezesporsemana", new JSONArray().put(rs.getString("vezesporsemana") != null ? rs.getString("vezesporsemana") : ""));
        json.put("diasAcessoUltimoMes", new JSONArray().put(rs.getString("diasAcessoUltimoMes") != null ? rs.getString("diasAcessoUltimoMes") : ""));
        json.put("diasAcessoMes2", new JSONArray().put(rs.getString("diasAcessoMes2") != null ? rs.getString("diasAcessoMes2") : ""));
        json.put("diasAcessoMes3", new JSONArray().put(rs.getString("diasAcessoMes3") != null ? rs.getString("diasAcessoMes3") : ""));
        json.put("diasAcessoMes4", new JSONArray().put(rs.getString("diasAcessoMes4") != null ? rs.getString("diasAcessoMes4") : ""));
        json.put("mediaDiasAcesso4Meses", new JSONArray().put(rs.getString("mediaDiasAcesso4Meses") != null ? rs.getString("mediaDiasAcesso4Meses") : ""));

        // Campos adicionais que podem ser necessários
        json.put("profissao", new JSONArray().put(rs.getString("profissao") != null ? rs.getString("profissao") : ""));
        json.put("colaboradores", new JSONArray().put(""));
        json.put("modalidades", new JSONArray().put(rs.getString("modalidades") != null ? rs.getString("modalidades") : ""));
        json.put("descricoesModalidades", new JSONArray().put(rs.getString("descricoesModalidades") != null ? rs.getString("descricoesModalidades") : ""));
        json.put("situacaoContratoOperacao", new JSONArray().put(rs.getString("situacaoContratoOperacao") != null ? rs.getString("situacaoContratoOperacao") : ""));
        json.put("freePassInicio", new JSONArray().put(rs.getString("freePassInicio") != null ? rs.getString("freePassInicio") : ""));
        json.put("freePassInicio", new JSONArray().put(rs.getString("freePassFim") != null ? rs.getString("freePassFim") : ""));

        return json;
    }

    @Override
    public ClienteSintetico consultarPorMatricula(String ctx, String matricula) throws ServiceException {
        try {
            return getClienteSinteticoDao().findObjectByAttribute(ctx, "matricula", Integer.valueOf(matricula));
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public JSONObject consultarPassivo(String ctx, Integer codigoPassivo) throws ServiceException {
        try {
            String s = "SELECT * from passivo where codigo = " + codigoPassivo;
            try (Connection con = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(s, con)) {
                    if (rs.next()) {
                        JSONObject json = new JSONObject();
                        json.put("codigo", rs.getInt("codigo"));
                        json.put("nome", !UteisValidacao.emptyString(Uteis.getSobrenome(rs.getString("nome"))) ? rs.getString("nome") : (rs.getString("nome").trim() + " (CRM)"));
                        json.put("telefone", (!UteisValidacao.emptyString(rs.getString("telefonecelular")) ? rs.getString("telefonecelular") :
                                (!UteisValidacao.emptyString(rs.getString("telefoneresidencial")) ? rs.getString("telefoneresidencial") : rs.getString("telefonetrabalho"))));
                        json.put("responsavelCadastro", rs.getInt("responsavelcadastro"));
                        json.put("dia", rs.getDate("dia"));
                        json.put("observacaoCliente", rs.getString("observacao"));
                        json.put("usuarioResponsavel", rs.getInt("colaboradorresponsavel"));
                        json.put("email", rs.getString("email"));
                        json.put("unidade", rs.getInt("empresa"));
                        json.put("origemSistema", rs.getInt("origemsistema"));
                        return json;
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public Integer consultarMetaAgendamentoPresencialAluno(String ctx, Integer matriculaCliente, Date dia) throws ServiceException {
        try {
            String s = " select fmd.codigo from fecharmetadetalhado fmd\n" +
                    " inner join fecharmeta fm on fm.codigo = fmd.fecharmeta \n" +
                    " inner join cliente c on c.codigo = fmd.cliente \n" +
                    " where c.codigomatricula = "+matriculaCliente+"\n" +
                    " and fm.dataregistro between '"+Uteis.getDataJDBC(dia)+" 00:00:00' and '"+Uteis.getDataJDBC(dia)+" 23:59:59'\n" +
                    " and fm.identificadormeta = 'AG'";
            try (Connection con = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(s, con)) {
                    if (rs.next()) {
                        return rs.getInt("codigo");
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }


    @Override
    public JSONObject consultarIndicado(String ctx, Integer codigoIndicado) throws ServiceException {
        try {
            String s = "SELECT i.*, icao.responsavelcadastro, icao.observacao, icao.dia, icao.colaboradorresponsavel from indicado i" +
                    " inner join indicacao icao on icao.codigo = i.indicacao" +
                    " where i.codigo = " + codigoIndicado;
            try (Connection con = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(s, con)) {
                    if (rs.next()) {
                        JSONObject json = new JSONObject();
                        json.put("codigo", rs.getInt("codigo"));
                        json.put("nome", !UteisValidacao.emptyString(Uteis.getSobrenome(rs.getString("nomeindicado"))) ? rs.getString("nomeindicado") : (rs.getString("nomeindicado").trim() + " (CRM)"));
                        json.put("telefone", (!UteisValidacao.emptyString(rs.getString("telefoneindicado")) ? rs.getString("telefoneindicado") : rs.getString("telefone")));
                        json.put("responsavelCadastro", rs.getInt("responsavelcadastro"));
                        json.put("dia", rs.getDate("dia"));
                        json.put("observacaoCliente", rs.getString("observacao"));
                        json.put("usuarioResponsavel", rs.getInt("colaboradorresponsavel"));
                        json.put("email", rs.getString("email"));
                        json.put("unidade", rs.getInt("empresa"));
                        json.put("origemSistema", rs.getInt("origemsistema"));
                        json.put("cpf", rs.getString("cpf"));
                        return json;
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public String autorizaAcessoTotalPass(ClienteDadosTotalPassDTO clienteDadosTotalPassDTO, String ctx) throws ServiceException {
        try {

            if (clienteDadosTotalPassDTO.getMatricula() == null || clienteDadosTotalPassDTO.getMatricula().equals("")) {
                throw new ServiceException("Matricula não informada");
            }

            try {
                ClienteSintetico cliente = consultarPorMatricula(ctx, clienteDadosTotalPassDTO.getMatricula());
            } catch (Exception e) {
                throw new ServiceException("Cliente de matrícula " + clienteDadosTotalPassDTO.getMatricula() + " não encontrado!");
            }

            ConfigTotalPassDTO configTotalPass = getConfigTotalPassDTO(clienteDadosTotalPassDTO, ctx);

            System.out.println("Verificando se possui acesso!");
            Boolean jaPossuiAcesso = verificaSeJaPossuiAcessoRegistrado(clienteDadosTotalPassDTO, ctx);

            consultaCPFClienteZWCasoNaoInformado(clienteDadosTotalPassDTO, ctx);

            validarInformacoesTotalPass(jaPossuiAcesso, configTotalPass, clienteDadosTotalPassDTO);
            LogTotalPassDTO logTotalPassDTO = consumirAPI("APP", configTotalPass, clienteDadosTotalPassDTO);

            salvaLogTotalPass(ctx, logTotalPassDTO);

            if (logTotalPassDTO.getResposta().equals(AUTORIZADO_TOTALPASS)) {

                if (!jaPossuiAcesso) {

                    //criar periodo acesso no ZW
                    String sqlInsertPeriodoAcesso = "INSERT INTO periodoacessocliente (pessoa, datainicioacesso, datafinalacesso, tipoacesso, tipototalpass) VALUES (" +
                            logTotalPassDTO.getPessoa() + "," +
                            "now()" + "," +
                            "now()" + ",'" +
                            "PL'," +
                            true + ")";

                    try (Connection con = conexaoZWService.conexaoZw(ctx)) {
                        try (PreparedStatement insert = con.prepareStatement(sqlInsertPeriodoAcesso)){
                            insert.executeUpdate();
                        }
                    }
                }

                return "Total Pass lançado com sucesso!";

            } else if (logTotalPassDTO.getResposta().equals((BLOQUEADO_TOTALPASS_CHECKIN))) {
                throw new ServiceException(" Por favor realize o check-in na TotalPass.");
            } else {
                throw new ServiceException("TotalPass não validou o acesso.");
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private void salvaLogTotalPass(String ctx, LogTotalPassDTO logTotalPassDTO) throws Exception {
        //criar log total pass e salvar no zw
        String sqlInsertLogTotalPass = "INSERT INTO logtotalpass (apikey, usuario, tipo, uri, resposta, tempo_resposta, pessoa," +
                "dataregistro, empresa, ip, json, origem, respostaapi) " +
                "VALUES ('" + logTotalPassDTO.getApikey() + "'," +
                logTotalPassDTO.getUsuario() + ",'" +
                logTotalPassDTO.getTipo() + "','" +
                logTotalPassDTO.getUri() + "','" +
                logTotalPassDTO.getResposta() + "'," +
                logTotalPassDTO.getTempoResposta() + "," +
                logTotalPassDTO.getPessoa() + ",'" +
                Uteis.getDataAplicandoFormatacao(logTotalPassDTO.getDataregistro(), "yyyy-MM-dd HH:mm:ss") + "'," +
                logTotalPassDTO.getEmpresa() + ",'" +
                logTotalPassDTO.getIp() + "','" +
                logTotalPassDTO.getJson() + "','" +
                logTotalPassDTO.getOrigem() + "', '" +
                logTotalPassDTO.getRespostaApi() + "')";

        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (PreparedStatement insert = con.prepareStatement(sqlInsertLogTotalPass)) {
                insert.executeUpdate();
            }
        }
    }

    private void consultaCPFClienteZWCasoNaoInformado(ClienteDadosTotalPassDTO clienteDadosTotalPassDTO, String ctx) throws Exception {
        if (UteisValidacao.emptyString(clienteDadosTotalPassDTO.getCpf())) {
            String sqlCPF = "SELECT cpf FROM pessoa WHERE codigo = " + clienteDadosTotalPassDTO.getPessoa();
            try (Connection con = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlCPF, con)) {
                    if (rs.next()) {
                        clienteDadosTotalPassDTO.setCpf(rs.getString("cpf"));
                    }
                }
            }
        }
    }

    private Boolean verificaSeJaPossuiAcessoRegistrado(ClienteDadosTotalPassDTO clienteDadosTotalPassDTO, String ctx) throws Exception {
        String dataAtual = Uteis.getDataAplicandoFormatacao(new Date(System.currentTimeMillis()), "yyyy-MM-dd");
        String sqlPeriodoAcesso = "SELECT * FROM periodoacessocliente WHERE pessoa = " + clienteDadosTotalPassDTO.getPessoa() +
                " and datainicioacesso >= '" + dataAtual +
                "' and datafinalacesso <= '" + dataAtual + "' and tipototalpass = true";

        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlPeriodoAcesso, con)) {
                if (rs.next()) {
                    return true;
                }
            }
        }
        return false;
    }

    private ConfigTotalPassDTO getConfigTotalPassDTO(ClienteDadosTotalPassDTO clienteDadosTotalPassDTO, String ctx) throws Exception {
        ConfigTotalPassDTO configTotalPass = null;
        //consulta zw configtotalpass
        String sqlConfigTotalPass = "SELECT * FROM configtotalpass WHERE empresa_codigo = " + clienteDadosTotalPassDTO.getEmpresa();
        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlConfigTotalPass, con)) {
                if (rs.next()) {
                    configTotalPass = new ConfigTotalPassDTO();
                    configTotalPass.setCodigo(rs.getInt("codigo"));
                    configTotalPass.setEmpresa_codigo(rs.getInt("empresa_codigo"));
                    configTotalPass.setNome(rs.getString("nome"));
                    configTotalPass.setCodigoTotalPass(rs.getString("codigototalpass"));
                    configTotalPass.setUsuarioLancou(rs.getInt("usuariolancou_codigo"));
                    configTotalPass.setInativo(rs.getBoolean("inativo"));
                    configTotalPass.setPermitirWod(rs.getBoolean("permitirwod"));
                    configTotalPass.setDataLancamento(rs.getDate("datalancamento"));
                    configTotalPass.setLimiteDeAcessosPorDia(rs.getInt("limitedeacessospordia"));
                    configTotalPass.setLimiteDeAulasPorDia(rs.getInt("limitedeaulaspordia"));
                    configTotalPass.setApikey(rs.getString("apikey"));
                }
            }
        }
        return configTotalPass;
    }

    private LogTotalPassDTO consumirAPI(String origem, ConfigTotalPassDTO configTotalPass, ClienteDadosTotalPassDTO clienteDadosTotalPassDTO) throws UnknownHostException {

        LogTotalPassDTO registroEntrada = new LogTotalPassDTO();
        InetAddress enderecoIP = InetAddress.getLocalHost();
        registroEntrada.setPessoa(clienteDadosTotalPassDTO.getPessoa());

        registroEntrada.setDataRegistroInstant(Instant.now());
        registroEntrada.setTempoResposta(new Long(0));
        registroEntrada.setEmpresa(clienteDadosTotalPassDTO.getEmpresa());
        registroEntrada.setOrigem(origem);
        registroEntrada.setJson(null);
        registroEntrada.setIp(enderecoIP.getHostAddress());
        if (ISVALIDATE && configTotalPass.getApikey() != null && configTotalPass.getApikey().startsWith("TESTES_")) {
            registroEntrada.setTipo("VALIDATE EM TESTE");
        }
        if (ISVALIDATE && configTotalPass.getApikey() != null && !configTotalPass.getApikey().startsWith("TESTES_")) {
            registroEntrada.setTipo("VALIDATE EM PRODUÇÃO");
        }
        if (!ISVALIDATE && configTotalPass.getApikey() != null && !configTotalPass.getApikey().startsWith("TESTES_")) {
            registroEntrada.setTipo("QUEIMA EM PRODUÇÃO");
        }
        if (!ISVALIDATE && configTotalPass.getApikey() != null && configTotalPass.getApikey().startsWith("TESTES_")) {
            registroEntrada.setTipo("QUEIMA EM TESTE");
        }

        try {

            String uri = ISVALIDATE ? Aplicacao.getProp(totalPassApiValidate) : Aplicacao.getProp(totalPassApi);
            if (configTotalPass.getApikey() != null && configTotalPass.getApikey().startsWith("TESTES_")) {
                configTotalPass.setApikey(configTotalPass.getApikey().replace("TESTES_", ""));
                uri = ISVALIDATE ? Aplicacao.getProp(urlValidateStaging) : Aplicacao.getProp(urlApiStaging);
            }
            System.out.println(uri);
            HttpPost httpPost = new HttpPost(uri);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("x-api-key", configTotalPass.getApikey());

            JsonObject requestBody = new JsonObject();
            JsonObject data = new JsonObject();
            JsonObject attributes = new JsonObject();

            attributes.addProperty("type", "cpf");
            attributes.addProperty("identifier", clienteDadosTotalPassDTO.getCpf());
            attributes.addProperty("service_provider_code", configTotalPass.getCodigoTotalPass());

            data.addProperty("type", "string");
            data.add("attributes", attributes);

            requestBody.add("data", data);

            httpPost.setEntity(new StringEntity(requestBody.toString()));

            Instant startTime = Instant.now();

            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPost);

            Instant endTime = Instant.now();

            int responseCode = response.getStatusLine().getStatusCode();
            Duration elapsedTime = Duration.between(startTime, endTime);

            registroEntrada.setUri(uri);
            registroEntrada.setApikey(configTotalPass.getApikey());
            registroEntrada.setTempoResposta(elapsedTime.toMillis());

            String jsonEntrada = (requestBody.toString());
            registroEntrada.setJson(jsonEntrada);

            try {
                String responseApi = EntityUtils.toString(response.getEntity());
                registroEntrada.setRespostaApi(responseApi);
            } catch (Exception e) {
               e.printStackTrace();
                System.out.println("Erro ao converter resposta da API TotalPass");
            }
            registroEntrada.setResposta(String.valueOf(responseCode));
            return registroEntrada;
        } catch (IOException e) {
            e.printStackTrace();
        }


        return null;
    }

    private void validarInformacoesTotalPass(Boolean jaPossuiAcesso, ConfigTotalPassDTO configTotalPass, ClienteDadosTotalPassDTO clienteDadosTotalPassDTO) throws ServiceException {

        System.out.println("Validando informações TotalPass");
        if (configTotalPass == null || configTotalPass.getCodigoTotalPass().equals("")) {
            throw new ServiceException("Informe as configurações para TotalPass na tela de integrações.");
        }
        if (UteisValidacao.emptyString(clienteDadosTotalPassDTO.getCpf())) {
            throw new ServiceException("Cadastre o cpf para realizar validação com TotalPass.");
        } else {
            clienteDadosTotalPassDTO.setCpf(clienteDadosTotalPassDTO.getCpf().replaceAll("[-.\\s]", ""));
        }
        if (clienteDadosTotalPassDTO.getEmpresa() == null && UteisValidacao.emptyNumber(clienteDadosTotalPassDTO.getEmpresa())) {
            throw new ServiceException("Nenhuma empresa logada.");
        }
        if (UteisValidacao.emptyString(configTotalPass.getApikey()) || UteisValidacao.emptyString(configTotalPass.getCodigoTotalPass()) || configTotalPass.getInativo()) {
            throw new ServiceException("Informe as configurações para TotalPass na tela de integrações.");
        }
        if (jaPossuiAcesso) {
            throw new ServiceException("A validação nesta empresa já foi realizada hoje");
        }
        System.out.println("Validações realizadas com sucesso.");
    }

    @Override
    public List<ClienteSintetico> consultarPorNome(final String ctx, final String nome) throws ServiceException {
        try {
            String s = "select obj from ClienteSintetico obj where nome = :nome";
            Map<String, Object> p = new HashMap();
            p.put("nome", nome.toString());
            return obterPorParam(ctx, s, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }


    }

    @Override
    public ClienteSintetico obterPorCodigo(String ctx, Integer codigo) throws ServiceException {
        try {
            String s = "select obj from ClienteSintetico obj where codigo = :codigo";
            Map<String, Object> p = new HashMap();
            p.put("codigo", codigo);
            return obterObjetoPorParam(ctx, s, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void baixarFotoAluno(String ctx, HttpServletRequest request, String codigopessoa, String fotoKey, Boolean atualizarFotoApp) throws Exception {
        ClienteSintetico cliente = consultarPorCodigoPessoa(ctx, codigopessoa);
        fotoService.migracaoFotoPessoa(context, ctx, cliente.getCodigoPessoa(), fotoKey);
        if (atualizarFotoApp) {
            cliente.setFotoKeyApp(fotoKey+ "?time=" + Calendario.hoje().getTime());
            for(Usuario u : cliente.getUsuarios()){
                u.setFotoKeyApp(fotoKey);
                u.setVersaoFotoApp(u.getVersaoFotoApp() + 1);
                getUsuarioDao().update(ctx, u);
            }
        }
        cliente.setDataAtualizacaoFoto(Calendario.hoje());
        getClienteSinteticoDao().update(ctx, cliente);
    }

    @Override
    public List<String> deletarAlunosForaZW(String ctx) {
        List<String> alunos = new ArrayList<String>();
        try {
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            NotificacaoService ns = (NotificacaoService) UtilContext.getBean(NotificacaoService.class);
            ProgramaTreinoService ps = (ProgramaTreinoService) UtilContext.getBean(ProgramaTreinoService.class);
            List<Integer> list = clienteSinteticoDao.listOfObjects(ctx, "select matricula from ClienteSintetico");
            for (Integer cod : list) {
                try {
                    List<AddClienteJSON> clienteJSONs = integracaoWS.consultarClientesZW(url, ctx, null, null, null, cod);
                    if (clienteJSONs == null
                            || clienteJSONs.isEmpty()) {
                        ClienteSintetico sintetico = consultarPorMatricula(ctx, cod.toString());
                        if (sintetico.getCodigoCliente() == null || sintetico.getCodigoCliente() == 0) {
                            continue;
                        }
                        List<Notificacao> notificacaos = ns.obterUltimasCliente(ctx, sintetico.getCodigo(), 0);
                        for (Notificacao n : notificacaos) {
                            ns.excluir(ctx, n);
                        }
                        List<ProgramaTreino> programaTreinos = ps.obterProgramasPorCliente(ctx, sintetico.getCodigo(), null, null, null, 0);
                        for (ProgramaTreino p : programaTreinos) {
                            ps.excluir(ctx, p, null, false);
                        }
                        excluir(ctx, sintetico);
                        alunos.add(sintetico.getMatricula().toString() + " - " + sintetico.getNome());
                    }
                } catch (Exception e) {

                }
            }
        } catch (Exception e) {

        }
        return alunos;
    }

    @Override
    public ClienteSintetico consultarPorCodigoPessoa(String ctx, String codigopessoa) throws ServiceException {
        try {
            return getClienteSinteticoDao().findObjectByAttribute(ctx, "codigopessoa", Integer.valueOf(codigopessoa));
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void excluirObservacaoCliente(String ctx, ClienteObservacao clienteObservacao) throws ServiceException {
        try {
            this.clienteObservacaoDao.delete(ctx, clienteObservacao);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void removerFotoAluno(String ctx, HttpServletRequest request, String codigopessoa, Boolean atualizarFotoApp) throws Exception {
        ClienteSintetico cliente = consultarPorCodigoPessoa(ctx, codigopessoa);
        Aplicacao.deletarFoto(ctx, cliente.getPessoa().getFotoKey(), false, request.getSession().getServletContext());
        cliente.getPessoa().setFotoKey("fotoPadrao.jpg");
        if (atualizarFotoApp) {
            cliente.setFotoKeyApp("fotoPadrao.jpg");
            for(Usuario u : cliente.getUsuarios()){
                u.setFotoKeyApp("fotoPadrao.jpg");
                u.setVersaoFotoApp(u.getVersaoFotoApp() + 1);
                getUsuarioDao().update(ctx, u);
            }
        }
        cliente.setDataAtualizacaoFoto(Calendario.hoje());
        getClienteSinteticoDao().update(ctx, cliente);
    }

    @Override
    public List<ClienteSintetico> consultarFotosAtualizar(final String ctx, Date dataHora) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            query.append("SELECT obj FROM ClienteSintetico obj ");
            query.append("WHERE dataAtualizacaoFoto >= :dataHora ");
            query.append("ORDER BY obj.nome ");

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("dataHora", dataHora);

            return obterPorParam(ctx, query.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void atualizarDadosFC(final String ctx, final Integer fcMaxima, final Integer fcRepouso, final Integer codigoCliente) throws Exception {
        clienteSinteticoDao.updateAlgunsCampos(ctx,
                new String[]{"fcRepouso", "fcMaxima"},
                new Object[]{fcRepouso, fcMaxima},
                new String[]{"codigo"}, new Object[]{codigoCliente});
    }

    public void executeNativeSQL(final String ctx, String sql) throws ServiceException {
        try {
            getClienteSinteticoDao().executeNativeSQL(ctx, sql);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClienteSintetico consultarPorCodigoExterno(String ctx, String codigoExterno) throws ServiceException {
        try {
            return getClienteSinteticoDao().findObjectByAttribute(ctx, "codigoExterno", codigoExterno);
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }


    public List<ClienteConsulta> consultaSimplificadaAlunos(String key, String param, Integer professor, Integer empresaZW, StatusClienteEnum status) throws Exception {
        List<ClienteConsulta> alunos = new ArrayList<ClienteConsulta>();

        StringBuilder sql = new StringBuilder();
        sql.append(" select n.nome as nivel, cli.nome, cli.codigo, codigopessoa, codigocliente from clientesintetico cli");
        sql.append(" left join nivel n on cli.nivelaluno_codigo = n.codigo ");

        try {
            Integer matricula = Integer.valueOf(param);
            sql.append(" where matricula = ").append(matricula);
        } catch (NumberFormatException ne) {
            sql.append(" where upper(cli.nome) like '").append(param.toUpperCase()).append("%' ");
        }
        if (!UteisValidacao.emptyNumber(professor)) {
            sql.append(" and professorSintetico_codigo =  ").append(professor);
        }
        if (!UteisValidacao.emptyNumber(empresaZW)) {
            sql.append(" and empresa =  ").append(empresaZW);
        }
        sql.append(" order by cli.nome");
        try (ResultSet rs = clienteSinteticoDao.createStatement(key, sql.toString())) {
            while (rs.next()) {
                ClienteConsulta cli = new ClienteConsulta(rs);

                StringBuilder sqlprograma = new StringBuilder();

                sqlprograma.append("select nome from ProgramaTreino obj where cliente_codigo = ").append(cli.getCodigo());
                sqlprograma.append("and ('").append(Calendario.getData(Calendario.hoje(), "yyyy-MM-dd HH:mm:ss")).append("'");
                sqlprograma.append(" between ").append("dataInicio and dataTerminoPrevisto");
                sqlprograma.append(" or cast(dataTerminoPrevisto as date) = '").append(Uteis.getDataFormatoBD(Calendario.getDataComHoraZerada(Calendario.hoje()))).append("') ");
                try (ResultSet rsprog = clienteSinteticoDao.createStatement(key, sqlprograma.toString())) {

                    if (rsprog.next()) {
                        cli.setProgramaAtual(rsprog.getString("nome"));
                    } else {
                        sqlprograma = new StringBuilder();
                        sqlprograma.append("SELECT pro.nome FROM ProgramaTreinoAndamento obj ");
                        sqlprograma.append(" INNER JOIN programatreino pro on obj.programa_codigo = pro.codigo ");
                        sqlprograma.append(" where pro.cliente_codigo = ").append(cli.getCodigo());
                        sqlprograma.append(" and ultimoTreino IS NOT NULL ");
                        sqlprograma.append(" order by ultimoTreino desc limit 1");

                        try (ResultSet rsult = clienteSinteticoDao.createStatement(key, sqlprograma.toString())) {

                            if (rsprog.next()) {
                                cli.setProgramaAtual(rsult.getString("nome"));
                            }
                        }

                    }
                }
                alunos.add(cli);
            }
        }

        return alunos;
    }

    @Override
    public List<AlunoCatalogoResponseTO> obterCatalogoAlunos(HttpServletRequest request, Integer empresaId, String filtroNome, TiposConsultaCatalogoAlunoEnum tipo) throws ServiceException {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            Usuario usuarioProfessor = usuarioService.obterPorId(chave, sessaoService.getUsuarioAtual().getId());
            if (usuarioProfessor == null || usuarioProfessor.getProfessor() == null) {
                throw new ServiceException(CatalogoAlunoExcecoes.PROFESSOR_NAO_ENCONTRADO);
            }
            usuarioProfessor.setEmpresaZW(empresaId);
            List<ClienteSintetico> clientes = filtrarStatus(chave, usuarioProfessor, tipo.getStatusClienteEnum(), filtroNome, 50, 0);
            List<AlunoCatalogoResponseTO> ret = new ArrayList<AlunoCatalogoResponseTO>();
            for (ClienteSintetico cliente : clientes) {
                Usuario usuarioCliente = usuarioService.consultarPorCliente(chave, cliente.getCodigo());

                ProgramaTreino programaAtual = null;

                programaAtual = programaService.consultarUltimoTreinoDataBaseAluno(chave, cliente, null);

                Usuario usuarioProfessorProgramaTreino = null;

                if (programaAtual != null && !UteisValidacao.emptyNumber(programaAtual.getCodigo())) {
                    ProgramaTreinoAndamento programaTreinoAndamento = UtilContext.getBean(ProgramaTreinoAndamentoDao.class)
                            .findObjectByAttributes(chave, new String[]{"programa.codigo"}, new Object[]{programaAtual.getCodigo()}, "codigo");
                    if (programaTreinoAndamento != null && !UteisValidacao.emptyNumber(programaTreinoAndamento.getCodigo())) {
                        programaAtual.setNrTreinosRealizados(programaTreinoAndamento.getNrTreinos());
                    }

                    if (programaAtual.getProfessorCarteira() != null) {
                        // Este endpoint não exige as fichas, atividades e séries
                        programaAtual.setProgramaFichas(null);
                        usuarioProfessorProgramaTreino = usuarioService.consultarPorProfessor(chave, programaAtual.getProfessorCarteira().getCodigo());
                    }
                }
                boolean resultadoParq = avaliacaoFisicaService.obterResultadoQuestionarioParq(chave, cliente);
                if (cliente.getPessoa() != null) {
                    cliente.setUrlFoto(fotoService.defineURLFotoPessoa(request, cliente.getPessoa().getFotoKey(), cliente.getCodigoPessoa(), false, chave, !br.com.pacto.controller.json.base.SuperControle.independente(chave)));
                }
                //TODO: tive que pesquisar a variável "programaAtual" novamente pois esta dando LazyInitializationException
                // na AtividadeResponseTO linha 35 e linha 40 quando tenta dar o get no objeto Atividade.
                AlunoCatalogoResponseTO retAluno = new AlunoCatalogoResponseTO(cliente, usuarioCliente, usuarioProfessor,
                        programaService.consultarUltimoTreinoDataBaseAluno(chave, cliente, null), usuarioProfessorProgramaTreino, resultadoParq, br.com.pacto.controller.json.base.SuperControle.independente(chave));
                if (programaAtual != null && programaAtual.getProfessorCarteira() != null && retAluno.getProfessor() != null) {
                    retAluno.getProfessor().setImageUri(Aplicacao.obterUrlFotoDaNuvem(programaAtual.getProfessorCarteira().getPessoa().getFotoKey()));
                }

                ret.add(retAluno);
            }
            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(CatalogoAlunoExcecoes.ERRO_CONSULTAR_CATALOGO_ALUNOS);
        }
    }

    @Override
    public List<NivelAlunoResponseTO> obterNiveisAluno() throws ServiceException {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            List<Nivel> niveis = nivelDao.findAll(chave);
            List<NivelAlunoResponseTO> ret = new ArrayList<>();
            for (Nivel nivel : niveis) {
                ret.add(new NivelAlunoResponseTO(nivel));
            }
            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_LISTAR_NIVEIS_ALUNO);
        }
    }

    @Override
    public AlunoResponseTO cadastrarAluno(String ctx, AlunoDTO alunoDTO, Integer empresaId) throws ServiceException {
        if (UteisValidacao.emptyString(ctx)) {
            ctx = sessaoService.getUsuarioAtual().getChave();
        }
        return cadastrarAluno(alunoDTO, empresaId, ctx, false);
    }

    @Override
    public AlunoResponseTO cadastrarAlunoPersonalFit(AlunoDTO alunoDTO, String chave) throws ServiceException {
        List<Empresa> empresas = empresaService.obterTodos(chave);
        if (empresas == null || empresas.isEmpty()) {
            throw new ServiceException("nao foi possivel encontrar pelo menos um registro de empresa na chave informada");
        }
        return cadastrarAluno(alunoDTO, empresas.get(0).getCodigo(), chave, false);
    }

    public AlunoResponseTO cadastrarAluno(AlunoDTO alunoDTO, Integer empresaId, String ctx, boolean treinoWeb) throws ServiceException {
        try {
            validarCamposC(ctx, alunoDTO);
            emailFoiUltizado(ctx, alunoDTO);

            if (alunoDTO.getSexo() == null) {
                throw new ServiceException((AlunoExcecoes.SEXO_ALUNO_NAO_INFORMADO));
            }
            ClienteSintetico cs = clienteSinteticoCadastro(alunoDTO);
            Pessoa pessoa = cs.getPessoa();
            pessoa = pessoaService.inserirPessoa(ctx, pessoa);

            if (treinoWeb || !UteisValidacao.emptyNumber(alunoDTO.getProfessorId())) {
                if (alunoDTO.getProfessorId() == null) {
                    throw new ServiceException(AlunoExcecoes.PROFESSOR_NAO_ENCONTRADO);
                }
                ProfessorSintetico ps = professorSintetico.obterPorId(ctx, alunoDTO.getProfessorId());
                cs.setProfessorSintetico(ps);
            }

            Empresa empresa = empresaService.obterPorId(ctx, empresaId);
            cs.setEmpresa(empresa.getCodigo());
            cs.setDataMatricula(Calendario.hoje());
            cs.getPessoa().setNome(cs.getPessoa().getNome().toUpperCase());
            cs.setSexo(cs.getPessoa().getSexo());
            cs.setNome(cs.getPessoa().getNome());
            if (StringUtils.isNotBlank(alunoDTO.getSituacaoAluno())) {
                cs.setSituacao(SituacaoAlunoEnum.valueOf(alunoDTO.getSituacaoAluno()).getCodigo());
            }
            cs.setCodigoPessoa(cs.getPessoa().getCodigo());
            cs.setDataNascimento(cs.getPessoa().getDataNascimento());
            cs.setCodigoExterno(alunoDTO.getCodigoExterno());
            cs = inserir(ctx, cs);

            Integer novaMatricula = cs.getCodigo();
            if (matriculaExiste(ctx, novaMatricula)) {
                novaMatricula = obterUltimaMatriculaCadastrada(ctx) + 1;
            }

            cs.setMatricula(novaMatricula);
            cs.setCodigoCliente(cs.getCodigo());
            atualizarNivelAluno(ctx, alunoDTO.getNivelId(), cs);
            Usuario usuario = new Usuario();
            usuario.setEmpresaZW(empresa.getCodigo());
            usuario.setTipo(TipoUsuarioEnum.ALUNO);
            usuario.setCliente(cs);
            usuario.setStatus(StatusEnum.ATIVO);
            SecureRandom random = new SecureRandom();
            String senha = alunoDTO.getAppPassword();
            if (senha == null || senha.isEmpty()) {
                senha = new BigInteger(130, random).toString(32);
                senha = senha.length() > 8 ? senha.substring(0, 8) : senha;
            }

            usuario.setSenha(Uteis.encriptar(senha));
            if (alunoDTO.getUsarApp()) {
                usuario.setNome(alunoDTO.getAppUsername());
                usuario.setUserName(alunoDTO.getAppUsername());
                usuarioService.inserir(ctx, usuario);
                if (treinoWeb) {
                    independenteService.enviarUsuarioMovelAluno(ctx, cs, senha);
                }
                adicionarUsuarioServicoDescobrir(ctx, alunoDTO.getAppUsername());

            } else {
                usuario.setNome(cs.getMatricula().toString());
                usuario.setUserName(cs.getMatricula().toString());
                usuarioService.inserir(ctx, usuario);
            }
            alterar(ctx, cs);
            if (!isBlank(alunoDTO.getImagemData()) && !isBlank(alunoDTO.getExtensaoImagem())) {
                salvarMidiaNuvem(ctx, alunoDTO, cs.getPessoa());
            }
            Usuario professor = usuarioService.consultarPorProfessor(ctx, alunoDTO.getProfessorId());
            return new AlunoResponseTO(cs, usuario, professor, null, null, null, null,
                    null, null, br.com.pacto.controller.json.base.SuperControle.independente(ctx), null, null);
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_CADASTRAR_ALUNO, e);
        }
    }

    @Override
    public AlunoObservacaoDTO gravarObservacao(Integer alunoId, AlunoObservacaoDTO observacaoDTO) throws ServiceException {
        try {

            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            ClienteObservacao clienteObservacao = new ClienteObservacao();
            ClienteSintetico clienteSintetico = obterPorCodigo(ctx, alunoId);
            acao(AcaoAlunoEnum.ADICIONAR_OBSERVACAO, clienteSintetico.getMatriculaString());
            clienteObservacao.setCliente(clienteSintetico);
            clienteObservacao.setDataObservacao(Calendario.hoje());
            clienteObservacao.setUsuario_codigo(usuario.getCodigo());
            clienteObservacao.setObservacao(observacaoDTO.getObservacao());
            clienteObservacao.setAvaliacaoFisica(false);
            clienteObservacao.setImportante(observacaoDTO.getImportante());


            if (UteisValidacao.emptyString(observacaoDTO.getObservacao())) {
                throw new ValidacaoException("É necessário preencher a observação.");
            }
            gravarObservacaoCliente(ctx, clienteObservacao);

            alterarObservacaoCliente(ctx, clienteObservacao);
            ClienteSinteticoService cs = UtilContext.getBean(ClienteSinteticoService.class);
            cs.alterar(ctx, clienteSintetico);

            return new AlunoObservacaoDTO(clienteObservacao);
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_CADASTRAR_OBSERVACAO, e);
        } finally {
            leaveAcao();
        }
    }

    @Override
    public AlunoObservacaoDTO gravarObservacaoAnexos(Integer alunoId, AlunoObservacaoDTO observacaoDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            ClienteObservacao clienteObservacao = new ClienteObservacao();
            clienteObservacao.setCodigo(observacaoDTO.getCodigo());
            clienteObservacao.setNomeArquivo(observacaoDTO.getNomeArquivo());
            clienteObservacao.setFormatoArquivo(observacaoDTO.getFormatoArquivo());
            ClienteSintetico clienteSintetico = obterPorCodigo(ctx, alunoId);
            acao(AcaoAlunoEnum.ADICIONAR_ANEXO, clienteSintetico.getMatriculaString());
            clienteObservacao.setCliente(clienteSintetico);
            clienteObservacao.setUsuario_codigo(usuario.getCodigo());
            clienteObservacao.setObservacao(observacaoDTO.getObservacao());
            clienteObservacao.setAvaliacaoFisica(true);
            clienteObservacao.setImportante(observacaoDTO.getImportante());
            if (!UteisValidacao.emptyNumber(observacaoDTO.getCodigo())) {
                clienteObservacaoDao.getCurrentSession(ctx).clear();
                ClienteObservacao byId = clienteObservacaoDao.findById(ctx, observacaoDTO.getCodigo());
                clienteObservacao.setAnexoKey(byId.getAnexoKey());
                long timestamp = Long.parseLong(observacaoDTO.getData());
                Instant instant = Instant.ofEpochMilli(timestamp);
                clienteObservacao.setDataObservacao(Date.from(instant));
            } else {
                clienteObservacao.setDataObservacao(Calendario.hoje());
            }


            if (UteisValidacao.emptyString(observacaoDTO.getObservacao())) {
                throw new ValidacaoException("É necessário preencher a observação.");
            }
            if (UteisValidacao.emptyNumber(clienteObservacao.getCodigo())) {
                gravarObservacaoCliente(ctx, clienteObservacao);
            }

            if (observacaoDTO.getArquivoUpload() != null) {
                ObservacaoArquivoUploadDTO objUp = new ObservacaoArquivoUploadDTO();
                objUp.setNome(observacaoDTO.getNomeArquivo());
                salvarArquivoNuvem(ctx, observacaoDTO.getArquivoUpload(), clienteObservacao, objUp);
            }
            alterarObservacaoCliente(ctx, clienteObservacao);
            ClienteSinteticoService cs = UtilContext.getBean(ClienteSinteticoService.class);
            cs.alterar(ctx, clienteSintetico);

            return new AlunoObservacaoDTO(clienteObservacao);
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_CADASTRAR_OBSERVACAO, e);
        } finally {
            leaveAcao();
        }
    }

    public void salvarArquivoNuvem(String ctx, String stringArquivo, ClienteObservacao clienteObservacao, ObservacaoArquivoUploadDTO objs) throws Exception {

        stringArquivo = stringArquivo.substring((stringArquivo.lastIndexOf(",")));
        byte[] data = DatatypeConverter.parseBase64Binary(stringArquivo);

        objs.setData(data);

        ClienteSintetico cs = obterPorCodigo(ctx, clienteObservacao.getCliente().getCodigo());
        String identificador = "observacao-" + clienteObservacao.getCodigo() + "-" + cs.getMatriculaString();


        MidiaEntidadeEnum tmidia = null;
        if (clienteObservacao.getFormatoArquivo() != null) {
            tmidia = MidiaEntidadeEnum.obterPorExtensao(
                    clienteObservacao.getFormatoArquivo().toLowerCase().substring(clienteObservacao.getFormatoArquivo().lastIndexOf(".")));
        } else {
            tmidia = MidiaEntidadeEnum.obterPorExtensao(
                    objs.getNome().toLowerCase().substring(objs.getNome().lastIndexOf(".")));
        }
        if (tmidia != null) {
            String key = MidiaService.getInstanceAvaliacaoFisica().uploadObjectFromByteArray(
                    ctx, tmidia, identificador, objs.getData());

            if (!UteisValidacao.emptyString(key)) {
                clienteObservacao.setAnexoKey(key);
            }
        }

    }

    private ClienteSintetico clienteSinteticoCadastro(AlunoDTO alunoDTO) {
        ClienteSintetico cs = new ClienteSintetico();
        cs.setPessoa(new Pessoa());

        return preencherClienteSintetico(cs, alunoDTO);
    }

    private ClienteSintetico preencherClienteSintetico(ClienteSintetico cs, AlunoDTO alunoDTO) {
        Pessoa pessoa = cs.getPessoa();
        pessoa.setNome(alunoDTO.getNome());
        pessoa.setDataNascimento(alunoDTO.getDataNascimento());
        pessoa.setSexo(alunoDTO.getSexo().getSexo());
        List<Email> listaEmails = new ArrayList<>();
        String emails = "";
        for (String email : alunoDTO.getEmails()) {
            listaEmails.add(new Email(email, pessoa));
            emails += ";" + email.trim();
        }
        emails = emails.replaceFirst(";", "");
        cs.setEmail(emails);
        pessoa.setEmails(listaEmails);
        pessoa.setTelefones(new ArrayList<Telefone>());
        for (TelefoneDTO telefone : alunoDTO.getFones()) {
            Telefone telefonePessoa = new Telefone();
            telefonePessoa.setPessoa(pessoa);
            if (telefone.getNumero().contains("-")) {
                telefone.setNumero(telefone.getNumero().replace("-", "").replace(" ", ""));
            }
            telefonePessoa.setTelefone(telefone.getNumero());
            telefonePessoa.setTipo(telefone.getTipo());
            pessoa.getTelefones().add(telefonePessoa);
            String telefones = "";
            for (Telefone tel : pessoa.getTelefones()) {
                telefones += "; " + tel.getTelefone();
            }
            telefones = telefones.replaceFirst("; ", "");
            cs.setTelefones(telefones);
        }
        return cs;
    }

    private void emailFoiUltizado(String ctx, AlunoDTO alunoDTO) throws ServiceException {
        try {
            if (!UteisValidacao.emptyString(alunoDTO.getAppUsername())) {
                Usuario usuario = usuarioService.consultarPorUserName(ctx, alunoDTO.getAppUsername());
                if (usuario != null
                        && ((usuario.getCliente() != null && usuario.getCliente().getCodigo() != null)
                        || (usuario.getProfessor() != null && usuario.getProfessor().getCodigo() != null && usuario.getProfessor().getCodigo() != 0))) {
                    if (!usuario.getCliente().getCodigo().equals(alunoDTO.getCodigo())) {
                        throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_USERNAME_EM_USO);
                    }
                }
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void validarCamposC(String ctx, AlunoDTO alunoDTO) throws ServiceException {
        if (alunoDTO.getUsarApp() != null && alunoDTO.getUsarApp() == true) {
            if (alunoDTO.getAppUsername() == null || alunoDTO.getAppUsername().isEmpty()) {
                throw new ServiceException(AlunoExcecoes.ERRO_USERNAME_ALUNO_NAO_INFORMADO);
            }
            try {
                Uteis.validarEmail(alunoDTO.getAppUsername());
            } catch (Exception e) {
                throw new ServiceException(AlunoExcecoes.USERNAME_ALUNO_NAO_EMAIL_VALIDO);
            }
        }
        if (alunoDTO.getEmails() != null) {
            try {
                for (String email : alunoDTO.getEmails()) {
                    Uteis.validarEmail(email);
                }
            } catch (Exception e) {
                throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_EMAIL_INVALIDO);
            }
        }
        if (isBlank(alunoDTO.getSituacaoAluno())) {
            throw new ServiceException(AlunoExcecoes.ERRO_SITUACAO_OBRIGATORIO);
        }
    }

    @Override
    public List<ClienteObservacaoDTO> obterObservacoes(Integer alunoID, Boolean codigoClienteZw, Boolean matricula) throws ServiceException {
        List<ClienteObservacaoDTO> clienteObservacaoDTO = new ArrayList<>();
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico cs = null;
            if (matricula != null && matricula) {
                cs = consultarPorMatricula(ctx, alunoID.toString());
            } else if (codigoClienteZw != null && codigoClienteZw) {
                cs = obterPorCodigoCliente(ctx, alunoID);
            } else {
                cs = obterPorId(ctx, alunoID);
            }
            List<AtestadoResponseTO> atestadoResponseTO = new ArrayList<>();
            List<ClienteObservacao> clienteObservacao = new ArrayList<>();

            if (!br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                atestadoResponseTO.addAll(montarListaAtestado(cs.getCodigo()));
            }
            clienteObservacao = clienteObservacaoDao.consultarPorMatriculaCliente(ctx, cs.getMatriculaString());

            for (ClienteObservacao co : clienteObservacao) {
                Usuario usu = usuarioService.obterPorId(ctx, co.getUsuario_codigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                clienteObservacaoDTO.add(new ClienteObservacaoDTO(co, usu));
            }
            for (AtestadoResponseTO at : atestadoResponseTO) {
                if (at.getNomeArquivo() != null) {
                    at.setUrlAnexo(downloadAnexoAtestado(cs.getCodigo(), at.getId()));
                }
            }

        } catch (Exception e) {
            Uteis.logar(e, ClienteSinteticoServiceImpl.class);
        }
        return clienteObservacaoDTO;
    }

    @Override
    public String obterClienteMensagem(String key, Integer alunoID, String tipoMensagem) {
        String mensagem = "";
        try {
            if (isBlank(key)) {
                key = sessaoService.getUsuarioAtual().getChave();
            }
            if (!br.com.pacto.controller.json.base.SuperControle.independente(key)) {
                ClienteSintetico cs = obterPorId(key, alunoID);
                Map<String, String> requestParams = new HashMap<>();
                requestParams.put("alunoID", cs.getCodigoCliente().toString());
                requestParams.put("tipoMensagem", tipoMensagem);

                mensagem = chamadaZW(key, "/prest/treino/cliente-mensagem", 0, requestParams);
            }
            return mensagem;
        } catch (Exception ex) {
            return mensagem;
        }
    }

    @Override
    public List<AtestadoResponseTO> obterAnexosAtestados(Integer alunoID) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            List<AtestadoResponseTO> atestadoResponseTO = new ArrayList<>();

            if (!br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                atestadoResponseTO.addAll(montarListaAtestado(alunoID));
            }
            return atestadoResponseTO;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_OBTER_ALUNO, e);
        }
    }

    @Override
    public List<ClienteObservacaoAnexosDTO> obterObservacoesAnexos(Integer alunoID, Boolean buscarAnexoZw, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico cs = obterPorId(ctx, alunoID);

            List<ClienteObservacaoAnexosDTO> clienteObservacaoAnexosDTO = new ArrayList<>();
            List<ClienteObservacao> clienteObservacaoAnexos = new ArrayList<>();

            clienteObservacaoAnexos = clienteObservacaoDao.consultarPorMatriculaCliente(ctx, cs.getMatriculaString(), true);

            if (buscarAnexoZw != null && buscarAnexoZw) {
                ClienteObservacaoAnexosDTO anexoZW = obterAnexoZW(ctx, cs);
                if (anexoZW != null) {
                    clienteObservacaoAnexosDTO.add(anexoZW);
                }
            }

            for (ClienteObservacao co : clienteObservacaoAnexos) {
                Usuario usu = usuarioService.obterPorId(ctx, co.getUsuario_codigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (co.getAnexoKey() != null && !co.getAnexoKey().isEmpty()) {
                    String anexoKeyTratado = removerPrefixosIndevidos(co.getAnexoKey());
                    co.setAnexoKey(Aplicacao.obterUrlFotoDaNuvem(anexoKeyTratado));
                }
                clienteObservacaoAnexosDTO.add(new ClienteObservacaoAnexosDTO(co, usu));
            }

            if (!clienteObservacaoAnexosDTO.isEmpty()) {
                Ordenacao.ordenarListaReverse(clienteObservacaoAnexosDTO, "data");
            }

            if (paginadorDTO != null
                    && paginadorDTO.getPage() != null
                    && paginadorDTO.getSize() != null) {

                List<ClienteObservacaoAnexosDTO> retorno = new ArrayList<>();
                int primeiroPaginacao = (int) (paginadorDTO.getPage() * paginadorDTO.getSize());
                int ultimoRegistro = (int) ((paginadorDTO.getPage() * paginadorDTO.getSize()) + paginadorDTO.getSize());
                if (ultimoRegistro > clienteObservacaoAnexosDTO.size()) {
                    retorno = clienteObservacaoAnexosDTO.subList(primeiroPaginacao, clienteObservacaoAnexosDTO.size());
                } else {
                    retorno = clienteObservacaoAnexosDTO.subList(primeiroPaginacao, ultimoRegistro);
                }
                paginadorDTO.setQuantidadeTotalElementos((long) clienteObservacaoAnexosDTO.size());
                return retorno;
            }

            return clienteObservacaoAnexosDTO;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_OBTER_ALUNO, e);
        }
    }

    private String removerPrefixosIndevidos(String anexoKey) {
        String[] prefixos = {
                "https://dt39m1atv5spm.cloudfront.net/",
                "https://cdn1.pactorian.net/",
                "https://s3-sa-east-1.amazonaws.com/prod-zwphotos/"
        };
        boolean prefixeExiste;
        do {
            prefixeExiste = false;
            for (String prefix : prefixos) {
                if (anexoKey.startsWith(prefix)) {
                    anexoKey = anexoKey.substring(prefix.length());
                    prefixeExiste = true;
                }
            }
        } while (prefixeExiste);
        return anexoKey;
    }

    private ClienteObservacaoAnexosDTO obterAnexoZW(String ctx, ClienteSintetico cs) {
        try {
            if (UteisValidacao.emptyNumber(cs.getCodigoPessoa())) {
                return null;
            }
            ClienteObservacaoAnexosDTO dto = null;
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try {
                    String sql = "select anexo, nomeanexo, datacadastroanexo from cliente where length(coalesce(anexo,'')) > 0 and pessoa = " + cs.getCodigoPessoa();
                    try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {
                        if (rs.next()) {
                            dto = new ClienteObservacaoAnexosDTO();
                            dto.setId(cs.getCodigoPessoa());
                            dto.setOrigem("ZW");
                            dto.setNomeArquivo(rs.getString("nomeanexo"));
                            String anexoBanco = rs.getString("anexo");
                            dto.setAnexo(Aplicacao.obterUrlFotoDaNuvem(anexoBanco));
                            dto.setData(rs.getTimestamp("datacadastroanexo"));
                            try {
                                dto.setFormatoArquivo(anexoBanco.substring(anexoBanco.lastIndexOf(".")));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                } catch (Exception e) {
                    Uteis.logar(e, ClienteSinteticoServiceImpl.class);
                }
            }
            return dto;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    @Override
    public ClienteObservacaoAnexosDTO obterObservacaoAnexos(Integer anexoID) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteObservacao co = clienteObservacaoDao.findById(ctx, anexoID);
            if (co == null) {
                throw new ServiceException(AlunoExcecoes.ERRO_OBSERVACAO_NAO_EXISTE);
            }

            Usuario usu = usuarioService.obterPorId(ctx, co.getUsuario_codigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (co.getAnexoKey() != null && !co.getAnexoKey().isEmpty()) {
                String anexoKeyTratado = removerPrefixosIndevidos(co.getAnexoKey());
                co.setAnexoKey(Aplicacao.obterUrlFotoDaNuvem(anexoKeyTratado));
            }
            return new ClienteObservacaoAnexosDTO(co, usu);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_OBTER_OBSERVACAO, e);
        }
    }

    @Override
    public AlunoResponseTO obterSomenteCodigo(Integer matricula, boolean importarNaoExistir) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            try (ResultSet rsAluno = clienteSinteticoDao.createStatement(ctx, "select codigo from clientesintetico where matricula = " + matricula)) {
                if (rsAluno.next()) {
                    AlunoResponseTO responseTO = new AlunoResponseTO();
                    responseTO.setId(rsAluno.getInt("codigo"));
                    return responseTO;
                } else {
                    if (importarNaoExistir) {
                        try {
                            Integer empresa = sessaoService.getUsuarioAtual().getEmpresaAtual();
                            DashboardBIService cs = (DashboardBIService) UtilContext.getBean(DashboardBIService.class);
                            cs.importarAlunoForaTreino(ctx, empresa, matricula);
                            return obterSomenteCodigo(matricula, false);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                    throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_EXISTE);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_EXISTE);
        }
    }

    @Override
    public AlunoResponseTO obterUmAluno(Integer alunoID, Integer matricula, Integer empresa, HttpServletRequest request) throws ServiceException {
        try {
            context.getRealPath("resource");
            String ctx = sessaoService.getUsuarioAtual().getChave();
            AlunoResponseTO responseTO = new AlunoResponseTO();
            boolean independente = br.com.pacto.controller.json.base.SuperControle.independente(ctx);
            StringBuilder sql = new StringBuilder();
            sql.append(" select e.nome as nomeempresa, c.nome, c.matricula, c.codigo, c.codigocliente, c.situacao, c.professorSintetico_codigo, p.codigocolaborador, p.nome as profnome, ");
            sql.append(" c.idade, c.nomeplano, c.email, c.telefones, c.DataVigenciaAteAjustada, c.SituacaoMatriculaContrato, c.SituacaoContrato, c.freePassInicio, c.freePassFim,  ");
            sql.append(" c.sexo, u.username, c.dataNascimento, c.nivelAluno_codigo, n.nome as nivnome, pes.fotoKey, c.codigopessoa, e.codzw as empresa_codzw, e.codigo as empresa_codigo, rcp.parqpositivo ");
            sql.append(" from clientesintetico c ");
            sql.append(" left join professorsintetico p on p.codigo = c.professorSintetico_codigo ");
            sql.append(" left join usuario u on c.codigo = u.cliente_codigo ");
            sql.append(" left join pessoa pes on pes.codigo = c.pessoa_codigo ");
            sql.append(" left join nivel n on n.codigo = c.nivelAluno_codigo ");
            sql.append(" left join empresa e on e.codzw = c.empresa ");
            sql.append(" left join respostaclienteparq rcp on rcp.cliente_codigo = c.codigo and rcp.ativo is true ");
            if (UteisValidacao.emptyNumber(alunoID)) {
                sql.append(" where c.matricula = ").append(matricula);
            } else {
                sql.append(" where c.codigo = ").append(alunoID);
            }

            try (ResultSet rsAluno = clienteSinteticoDao.createStatement(ctx, sql.toString())) {
                if (rsAluno.next()) {
                    responseTO.setId(rsAluno.getInt("codigo"));
                    responseTO.setEmpresaCodZW(rsAluno.getInt("empresa_codzw"));
                    responseTO.setEmpresaCodigo(rsAluno.getInt("empresa_codigo"));
                    responseTO.setParq_status(rsAluno.getBoolean("parqpositivo"));
                    try {
                        if (!UteisValidacao.emptyString(rsAluno.getString("fotoKey"))
                                && !"fotoPadrao.jpg".equals(rsAluno.getString("fotoKey"))) {
                            responseTO.setImageUri(Aplicacao.obterUrlFotoDaNuvem(rsAluno.getString("fotoKey")));
                        }

                    } catch (Exception e) {
                        Uteis.logar(e, ClienteSinteticoServiceImpl.class);
                    }
                    try {
                        responseTO.setNomeEmpresa(rsAluno.getString("nomeempresa"));
                    } catch (Exception e) {
                        Uteis.logar(e, ClienteSinteticoServiceImpl.class);
                    }
                    responseTO.setSituacaoAluno(SituacaoAlunoEnum.getInstance(rsAluno.getString("situacao")));
                    if (!independente) {
                        responseTO.setContratoZW(new ContratoZWDTO(rsAluno.getDate("DataVigenciaAteAjustada"),
                                TipoContratoZWEnum.getInstance(rsAluno.getString("SituacaoMatriculaContrato"))));
                        responseTO.setSituacaoContratoZW(SituacaoContratoZWEnum.getInstance(rsAluno.getString("SituacaoContrato")));
                        responseTO.setPlanoZW(new PlanoZWDTO(rsAluno.getString("nomeplano")));
                    }
                    Date freePassInicio = rsAluno.getDate("freePassInicio");
                    Date freePassFim = rsAluno.getDate("freePassFim");
                    if (freePassInicio != null && freePassFim != null && Calendario.entreComHoraZerada(Calendario.hoje(), freePassInicio, freePassFim)
                            && (responseTO.getSituacaoAluno() == SituacaoAlunoEnum.VISITANTE || responseTO.getSituacaoAluno() == SituacaoAlunoEnum.INATIVO)) {
                        responseTO.setSituacaoContratoZW(SituacaoContratoZWEnum.FREE_PASS);
                    }
                    if (rsAluno.getInt("nivelAluno_codigo") > 0) {
                        responseTO.setNivel(new NivelResponseTO(rsAluno.getInt("nivelAluno_codigo"), rsAluno.getString("nivnome")));
                    }
                    String sexo = rsAluno.getString("sexo");
                    if (sexo != null && !sexo.trim().isEmpty()) {
                        try {
                            responseTO.setSexo(SexoEnum.valueOf(sexo.substring(0, 1).toUpperCase()));
                        } catch (IllegalArgumentException e) {
                            responseTO.setSexo(SexoEnum.N);
                        }
                    } else {
                        responseTO.setSexo(SexoEnum.N);
                    }

                    int idade = (rsAluno.getInt("idade"));
                    Date dataNascimento = (rsAluno.getDate("dataNascimento"));
                    if (independente && idade == 0 && dataNascimento != null) {
                        responseTO.setIdade(Uteis.calcularIdadePessoa(Calendario.hoje(), rsAluno.getDate("dataNascimento")));
                    } else {
                        responseTO.setIdade(rsAluno.getInt("idade"));
                    }
                    responseTO.setCodigoCliente(rsAluno.getInt("codigocliente"));
                    responseTO.setMatriculaZW(rsAluno.getInt("matricula"));

                    if (rsAluno.getInt("professorSintetico_codigo") > 0) {
                        responseTO.setProfessor(new ProfessorResponseTO(rsAluno.getInt(independente ? "professorSintetico_codigo" : "codigocolaborador"), rsAluno.getString("profnome")));
                    }
                    responseTO.setEmails(Uteis.stringToList(rsAluno.getString("email"), ";"));
                    responseTO.setFones(new ArrayList<>());
                    for (String telefone : Uteis.stringToList(rsAluno.getString("telefones"), ";")) {
                        TelefoneDTO telefoneDTO = new TelefoneDTO();
                        telefoneDTO.setNumero(telefone);
                        if (telefone.length() == 13) {
                            telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                        } else {
                            telefoneDTO.setTipo(TipoTelefoneEnum.FIXO);
                        }
                        responseTO.getFones().add(telefoneDTO);
                    }
                    responseTO.setNome(rsAluno.getString("nome"));
                    responseTO.setDataNascimento(rsAluno.getDate("dataNascimento"));
                    String username = rsAluno.getString("username");
                    if (username != null) {
                        try {
                            if (username.matches("\\d+")) {
                                responseTO.setUsarApp(false);
                            } else {
                                responseTO.setAppUsername(username);
                                responseTO.setUsarApp(true);
                            }
                        } catch (Exception e) {
                            Uteis.logar(e, ClienteSinteticoServiceImpl.class);
                        }
                    }

                    StringBuilder sqlprograma = new StringBuilder();
                    sqlprograma.append(" select p.codigo, p.DiasPorSemana, p.nome, p.totalaulasprevistas, p.nrtreinosrealizados, p.datainicio, p.dataterminoprevisto from programatreino p ");
                    sqlprograma.append(" left join programatreinoandamento pa on pa.programa_codigo = p.codigo ");
                    sqlprograma.append(" where p.cliente_codigo = ").append(responseTO.getId());
                    sqlprograma.append(" and datainicio <= '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
                    sqlprograma.append("' order by p.dataTerminoPrevisto desc ");
                    sqlprograma.append(" limit 1 ");

                    try (ResultSet rsPrograma = clienteSinteticoDao.createStatement(ctx, sqlprograma.toString())) {
                        if (rsPrograma.next()) {
                            int codPrograma = rsPrograma.getInt("codigo");
                            ProgramaTreinoAndamento programaTreinoAndamento = ((ProgramaTreinoAndamentoDao) UtilContext.getBean(ProgramaTreinoAndamentoDao.class))
                                    .findObjectByAttributes(ctx, new String[]{"programa.codigo"}, new Object[]{codPrograma}, "codigo");

                            Integer quantidadeExecucoes = obterQuantidadeExecucoesTreinoRealizados(ctx, codPrograma);
                            if (programaTreinoAndamento != null && !UteisValidacao.emptyNumber(programaTreinoAndamento.getCodigo())) {
                                responseTO.setNomeProgramaAtual(programaTreinoAndamento.getPrograma().getNome());
                                responseTO.setTotalAulasPrevistas(rsPrograma.getInt("totalaulasprevistas"));
                                responseTO.setFrequencia(
                                        Uteis.arrendondarForcando2CadasDecimaisComVirgula(
                                                programaTreinoAndamento.getPercentualExecucoesFrequencia(quantidadeExecucoes, rsPrograma.getInt("totalaulasprevistas"))
                                        ) + "%"
                                );
                                responseTO.setNrTreinosRealizados(quantidadeExecucoes);
                            } else {
                                responseTO.setNomeProgramaAtual(rsPrograma.getString("nome"));
                                responseTO.setTotalAulasPrevistas(rsPrograma.getInt("totalaulasprevistas"));
                                responseTO.setNrTreinosRealizados(rsPrograma.getInt("nrtreinosrealizados"));
                                Date datainicio = rsPrograma.getDate("datainicio");
                                Date dataterminoprevisto = rsPrograma.getDate("dataterminoprevisto");
                                int diasPorSemana = rsPrograma.getInt("DiasPorSemana");

                                Double freq = 0.0;
                                if (datainicio != null && dataterminoprevisto != null && diasPorSemana > 0) {
                                    //numero de aulas previstas até a data de hoje
                                    Long dias = Uteis.nrDiasEntreDatas(datainicio, dataterminoprevisto);
                                    int nrAulasPrevistasAteHoje = (int) ((dias.doubleValue() / 7) * diasPorSemana);
                                    //numero de treinos realizados pelo aluno nesse programa até hoje/ nr de aulas previstas
                                    if (responseTO.getNrTreinosRealizados() > 0 && nrAulasPrevistasAteHoje > 0) {
                                        freq = (responseTO.getNrTreinosRealizados() / (double) nrAulasPrevistasAteHoje) * 100;
                                        freq = Uteis.arredondarForcando2CasasDecimais(freq);
                                    }
                                }
                                responseTO.setFrequencia(Uteis.arrendondarForcando2CadasDecimaisComVirgula(freq) + "%");
                            }
                        }
                    }

                    sqlprograma = new StringBuilder();
                    sqlprograma.append(" select p.codigo, p.nome, p.datainicio, p.dataterminoprevisto, p.isgeradoporia, p.emrevisaoprofessor, p.datalancamento from programatreino p ");
                    sqlprograma.append(" where p.cliente_codigo = ").append(responseTO.getId());
                    sqlprograma.append(" order by p.dataTerminoPrevisto desc ");
                    try (ResultSet rsProgramas = clienteSinteticoDao.createStatement(ctx, sqlprograma.toString())) {
                        responseTO.setProgramas(new ArrayList<ProgramaTreinoAlunoResponseDTO>() {{
                            while (rsProgramas.next()) {
                                boolean geradoPorIa = rsProgramas.getBoolean("isgeradoporia");
                                boolean emRevisaoProfessor = rsProgramas.getBoolean("emrevisaoprofessor");

                                ConfiguracaoSistema configObrigatoriedade = configuracaoSistemaService.consultarPorTipo(ctx, HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR);
                                boolean obrigatoriedadeAprovacaoProfessor = configObrigatoriedade != null && Boolean.parseBoolean(configObrigatoriedade.getValor());

                                if (!obrigatoriedadeAprovacaoProfessor && Boolean.TRUE.equals(geradoPorIa) && emRevisaoProfessor) {
                                    ConfiguracaoSistema configFluxoAprovacao = configuracaoSistemaService.consultarPorTipo(ctx, TEMPO_APROVACAO_AUTOMATICA);
                                    Integer tempoAprovacao = (configFluxoAprovacao != null && configFluxoAprovacao.getValor() != null && !UteisValidacao.emptyString(configFluxoAprovacao.getValor()))
                                            ? Integer.valueOf(configFluxoAprovacao.getValor())
                                            : 0;

                                    Date dataLancamento = rsProgramas.getTimestamp("datalancamento");
                                    long diferencaEmMinutos = ChronoUnit.MINUTES.between(dataLancamento.toInstant(), Calendario.hoje().toInstant());

                                    if (tempoAprovacao == 0 || diferencaEmMinutos > tempoAprovacao) {
                                        ProgramaTreino programaTreino = programatreinoDao.obterPorId(ctx, rsProgramas.getInt("codigo"));
                                        if (programaTreino == null) {
                                            throw new ServiceException(ProgramaTreinoExcecoes.PROGRAMA_TREINO_NAO_ENCONTRADO);
                                        }

                                        programaTreino.setEmRevisaoProfessor(false);
                                        programatreinoDao.alterar(ctx, programaTreino);
                                    }
                                }

                                ProgramaTreinoAlunoResponseDTO prog = new ProgramaTreinoAlunoResponseDTO();
                                prog.setId(rsProgramas.getInt("codigo"));
                                prog.setNome(rsProgramas.getString("nome"));
                                prog.setInicio(rsProgramas.getDate("datainicio"));
                                prog.setTermino(rsProgramas.getDate("dataterminoprevisto"));
                                prog.setGeradoPorIa(geradoPorIa);
                                prog.setRevisadoProfessor(!emRevisaoProfessor);
                                add(prog);
                            }
                        }});
                    }
                } else {
                    throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_EXISTE);
                }
            }

            try (ResultSet rsLastTreino = clienteSinteticoDao.createStatement(ctx, "select datainicio from treinorealizado t where cliente_codigo = "+ responseTO.getId() + " order by datainicio  desc limit 1")) {
                if (rsLastTreino.next()) {
                    responseTO.setDataUltimoTreino(rsLastTreino.getDate("datainicio"));
                }
            }

            responseTO.setResultadoParq(parQDao.consultarResultadoParqVigente(ctx, responseTO.getId()));

            if (responseTO.getResultadoParq()) {
                responseTO.setInfoParQ("POSITIVO");
            } else {
                try {
                    JSONArray jsonArray = parQDao.consultarClientesParQNaoAssinadoV2(ctx, responseTO.getEmpresaCodZW(), responseTO.getMatriculaZW().toString(), null, null);
                    if (jsonArray != null && !UteisValidacao.emptyNumber(jsonArray.length())) {
                        responseTO.setInfoParQ("NAO_ASSINADO");
                    } else {
                        responseTO.setInfoParQ("NEGATIVO");
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            return responseTO;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_OBTER_ALUNO, e);
        }
    }

    @Override
    public AlunoSimplesDTO obterAlunoSimples(Integer matricula) throws ServiceException {
        try {
            if (matricula == null) {
                throw new ServiceException("Matrícula não informada.");
            }
            context.getRealPath("resource");
            String ctx = sessaoService.getUsuarioAtual().getChave();
            AlunoSimplesDTO alunoDTO = new AlunoSimplesDTO();
            StringBuilder sql = new StringBuilder();
            sql.append(" select c.codigo, c.nome, c.email, c.telefones from clientesintetico c ");
            sql.append(" where c.matricula = ").append(matricula);

            try (ResultSet rsAluno = clienteSinteticoDao.createStatement(ctx, sql.toString())) {
                if (rsAluno.next()) {
                    alunoDTO.setId(rsAluno.getInt("codigo"));
                    alunoDTO.setNome(rsAluno.getString("nome"));
                    alunoDTO.setEmails(Uteis.stringToList(rsAluno.getString("email"), ";"));
                    alunoDTO.setFones(new ArrayList<>());
                    for (String telefone : Uteis.stringToList(rsAluno.getString("telefones"), ";")) {
                        TelefoneDTO telefoneDTO = new TelefoneDTO();
                        telefoneDTO.setNumero(telefone);
                        if (telefone.length() == 13) {
                            telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                        } else {
                            telefoneDTO.setTipo(TipoTelefoneEnum.FIXO);
                        }
                        alunoDTO.getFones().add(telefoneDTO);
                    }
                } else {
                    throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_EXISTE);
                }
            }
            return alunoDTO;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_OBTER_ALUNO, e);
        }
    }

    public Integer obterQuantidadeExecucoesTreinoRealizados(final String ctx, final Integer codigoPrograma) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT COUNT(codigo) as quantidadeexecucoes FROM treinorealizado WHERE programatreinoficha_codigo IN  \n");
            sql.append(" (SELECT codigo FROM programatreinoficha WHERE programa_codigo = ");
            sql.append(codigoPrograma);
            sql.append(" ) ");
            try (ResultSet rs = treinoRealizadoDao.createStatement(ctx, sql.toString())) {
                return rs.next() ? rs.getInt("quantidadeexecucoes") : 0;
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private void inicializarFilhas(ProgramaTreino programaTreino) {
        for (ProgramaTreinoFicha programaTreinoFicha : programaTreino.getProgramaFichas()) {
            if (programaTreinoFicha.getFicha() != null) {
                Hibernate.initialize(programaTreinoFicha.getFicha().getProgramas());
                for (AtividadeFicha atividadeFicha : programaTreinoFicha.getFicha().getAtividades()) {
                    Hibernate.initialize(atividadeFicha.getAtividade().getAnimacoes());
                    for (AtividadeAnimacao atividadeAnimacao : atividadeFicha.getAtividade().getAnimacoes()) {
                        Hibernate.initialize(atividadeAnimacao.getAnimacao());
                    }
                    Hibernate.initialize(atividadeFicha.getAtividade().getEmpresasHabilitadas());
                    Hibernate.initialize(atividadeFicha.getSeries());
                    Hibernate.initialize(atividadeFicha.getFicha().getProgramas());
                }
            }
        }
    }

    @Override
    public String downloadAnexoAtestado(Integer alunoId, Integer atestadoId) throws ServiceException {
        try {
            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            ClienteSintetico cs = obterPorId(ctx, alunoId);
            AtestadoClienteJSON atestadoClienteJSON = new AtestadoClienteJSON();
            List<AtestadoClienteJSON> atestadosCliente = integracaoWS.consultarAtestadoCliente(url, ctx, cs.getMatricula());

            for (AtestadoClienteJSON aj : atestadosCliente) {
                if (atestadoId == aj.getCodAtestado()) {
                    atestadoClienteJSON = aj;
                }
            }
            final String key = genKey(ctx, MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO, String.valueOf(atestadoClienteJSON.getCodAtestado()), atestadoClienteJSON.getExtensao());

            return Aplicacao.obterUrlFotoDaNuvem(key);

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_DOWNLOAD_ARQUIVO, e);
        }
    }

    public String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier, String extensao) throws Exception {
        return String.format("%s/%s/%s%s",
                chave,
                Uteis.encriptarAWS(tipo.name().toLowerCase()),
                Uteis.encriptarAWS(identifier),
                extensao);
    }


    public AlunoResponseTO alterarAluno(Integer id, AlunoDTO alunoDTO, HttpServletRequest request) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico cs = obterPorId(ctx, id);
            acao(AcaoAlunoEnum.ALTERACAO_PROFESSOR_NIVEL, cs.getMatriculaString());
            if (cs == null) {
                throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_EXISTE);
            }

            Usuario usuario = usuarioService.consultarPorCliente(ctx, cs.getCodigo());
            Usuario professor = usuarioService.consultarPorProfessor(ctx, alunoDTO.getProfessorId());
            if (br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                alunoDTO.setCodigo(cs.getCodigo());
                validarCamposC(ctx, alunoDTO);
                emailFoiUltizado(ctx, alunoDTO);
                pessoaService.deletarEmailTelefonePessoa(ctx, cs.getPessoa().getCodigo());
                cs.setTelefones("");
                cs.setEmail("");
                preencherClienteSintetico(cs, alunoDTO);

                if (!isBlank(alunoDTO.getImagemData()) && !isBlank(alunoDTO.getExtensaoImagem())) {
                    salvarMidiaNuvem(ctx, alunoDTO, cs.getPessoa());
                } else if (StringUtils.isNotEmpty(cs.getPessoa().getFotoKey()) && StringUtils.isEmpty(alunoDTO.getImagemData())) {
                    if (!cs.getPessoa().getFotoKey().contains("http")) {
                        deletarAlunosForaZW(cs.getPessoa().getFotoKey());
                    }
                    cs.getPessoa().setFotoKey(null);
                }

                cs.setPessoa(pessoaService.alterarPessoa(ctx, cs.getPessoa()));
                if (alunoDTO.getProfessorId() != null) {
                    ProfessorSintetico prof = professorSintetico.obterPorId(ctx, alunoDTO.getProfessorId());
                    cs.setProfessorSintetico(prof);
                }


                cs.getPessoa().setNome(cs.getPessoa().getNome().toUpperCase());
                cs.setSexo(cs.getPessoa().getSexo());
                cs.setNome(cs.getPessoa().getNome());
                if (StringUtils.isNotBlank(alunoDTO.getSituacaoAluno())) {
                    cs.setSituacao(SituacaoAlunoEnum.valueOf(alunoDTO.getSituacaoAluno()).getCodigo());
                }
                cs.setDataNascimento(cs.getPessoa().getDataNascimento());
                if (!UteisValidacao.emptyNumber(alunoDTO.getNivelId())) {
                    if (cs.getNivelAluno() == null) {
                        atualizarNivelAluno(ctx, alunoDTO.getNivelId(), cs);
                    } else if (!cs.getNivelAluno().getCodigo().equals(alunoDTO.getNivelId())) {
                        atualizarNivelAluno(ctx, alunoDTO.getNivelId(), cs);
                    }
                }

                String senha = alunoDTO.getAppPassword();
                if (usuario != null) {
                    if (senha != null && !senha.isEmpty()) {
                        usuario.setSenha(Uteis.encriptar(senha));
                    } else {
                        senha = "A senha não foi alterada";
                    }
                    if (alunoDTO.getUsarApp() != null && alunoDTO.getUsarApp()) {
                        usuario.setNome(alunoDTO.getAppUsername());
                        usuario.setUserName(alunoDTO.getAppUsername());
                    } else {
                        if (!UteisValidacao.emptyString(alunoDTO.getAppUsername()) && !usuario.getUserName().equals(alunoDTO.getAppUsername().trim())) {
                            usuario.setUserName(alunoDTO.getAppUsername().trim());
                            usuario.setNome(alunoDTO.getAppUsername().trim());
                        } else {
                            usuario.setNome(cs.getMatricula().toString());
                            usuario.setUserName(cs.getMatricula().toString());
                        }
                    }
                    usuarioService.alterar(ctx, usuario);
                }
                if (cs.getDataMatricula() == null) {
                    cs.setDataMatricula(Calendario.hoje());
                }
                alterar(ctx, cs);
            } else {
                IntegracaoCadastrosWSConsumer integracaoWs = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                Usuario usuarioLogado = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
                Nivel nivelAnteriorAluno = (cs.getNivelAluno() != null && !UteisValidacao.emptyNumber(cs.getNivelAluno().getCodigo())) ? cs.getNivelAluno() : null;
                Integer codNivelAnterior = (cs.getNivelAluno() != null) ? cs.getNivelAluno().getCodigo() : null;
                Integer codNovoNivel = (alunoDTO.getNivelId() != null) ?  alunoDTO.getNivelId() : null;
                String nomeNivelAnterior = (cs.getNivelAluno() != null) ? cs.getNivelAluno().getNome() : null;
                Nivel novoNivel = nivelService.obterPorId(ctx, alunoDTO.getNivelId() == null ? 0 : alunoDTO.getNivelId());
                String nomeNovoNivel = (novoNivel != null) ? novoNivel.getNome() : null;

                atualizarNivelAluno(ctx, alunoDTO.getNivelId(), cs);

                if (!Objects.equals(codNivelAnterior, codNovoNivel)) {
                    incluirLog(ctx, cs.getMatriculaString(), "", nomeNivelAnterior, nomeNovoNivel,
                            "ALTERAÇÃO", "ALTERAÇÃO DE NÍVEL DO ALUNO", EntidadeLogEnum.ALUNO, "Nível do Aluno", sessaoService, logDao, null, null);
                }
                if (!UteisValidacao.emptyNumber(alunoDTO.getProfessorId())) {
                    ProfessorSintetico professorSintetico = getProfessorSintetico().consultarPorCodigoColaborador(ctx, alunoDTO.getProfessorId());
                    if (professorSintetico != null && !UteisValidacao.emptyNumber(professorSintetico.getCodigo())) {
                        atualizarProfessorAluno(ctx, integracaoWs, professorSintetico.getCodigo(), cs, usuarioLogado.getUsuarioZW(), request);
                    }
                } else {
                    atualizarProfessorAluno(ctx, integracaoWs, alunoDTO.getProfessorId(), cs, usuarioLogado.getUsuarioZW(), request);
                }
            }
            return new AlunoResponseTO(cs, usuario, professor, null, null, null, null, null, null, br.com.pacto.controller.json.base.SuperControle.independente(ctx), null,null );
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_ALTERAR, e);
        } finally {
            leaveAcao();
        }
    }

    public void deletarUmAluno(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico cs = obterPorId(ctx, id);
            if (cs == null) {
                throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_EXISTE);
            }

            excluir(ctx, cs);
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_ALTERAR, e);
        }
    }

    public void deletarObservacao(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteObservacao co = clienteObservacaoDao.findById(ctx, id);
            if (co == null) {
                throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_EXISTE);
            }
            acao(AcaoAlunoEnum.EXCLUIR_OBSERVACAO, co.getCliente().getMatriculaString());
            clienteObservacaoDao.delete(ctx, co);
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_ALTERAR, e);
        }
    }

    public void editarSituacao(AlunoDTO alunoDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico cs = null;

            cs = obterPorId(ctx, alunoDTO.getCodigo());
            if (cs == null) {
                throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_EXISTE);
            }
            if (cs.getSituacao().equals("VI") || cs.getSituacao().equals("IN")) {
                cs.setSituacao("AT");
            } else {
                cs.setSituacao("IN");
            }

            alterar(ctx, cs);
        } catch (ServiceException e) {
            throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_ALTERAR_SITUACAO);
        }
    }

    @Override
    public AlunoOlympiaDTO buscarAlunoOlympia(String codigo) throws ServiceException {
        if (codigo == null || codigo.equals("")) {
            throw new ServiceException("Informe um CPF ou um ID para consultar", HttpStatus.BAD_REQUEST.value());
        }

        String metodoID = "/associados/atividade-fisica/";
        String metodoCPF = "/associados/atividade-fisica/cpf/";

        String metodo;
        if (codigo.length() == 11) {
            metodo = metodoCPF;
        } else {
            metodo = metodoID;
        }

        String key = sessaoService.getUsuarioAtual().getChave();
        String token = configuracaoSistemaService.obterTokenIntegracaoOlympia(key);
        if (UteisValidacao.emptyString(token)) {
            throw new ServiceException("Não foi possível obter o Token", HttpStatus.BAD_REQUEST.value());
        }

        Map<String, String> paramsHeader = new HashMap<String, String>();
        paramsHeader.put("Authorization", "Bearer " + token);

        try {
            Map<Integer, String> responseRest = ExecuteRequestHttpService.executeRequestGETEncodeResponseRest(validarUrlOlympia() + metodo + codigo, paramsHeader, "UTF-8", "UTF-8");
            Map.Entry<Integer, String> next = responseRest.entrySet().iterator().next();

            if (next.getKey() == 401) {
                throw new ServiceException("Buscar aluno com a integração Olympia não autorizada, por favor verificar com Olympia motivo de não autorização de acesso ao serviço de integração",
                        HttpStatus.BAD_REQUEST.value());
            } else {
                try {
                    JSONObject bodyJsonObject = new JSONObject(next.getValue());
                    if (next.getKey() < HttpURLConnection.HTTP_BAD_REQUEST) {
                        if (bodyJsonObject.has("mensagem")) {
                            throw new ServiceException(bodyJsonObject.getString("mensagem"), HttpStatus.BAD_REQUEST.value());
                        } else {
                            JSONObject jsonObject = new JSONObject(next.getValue());
                            return preencherAluno(jsonObject);
                        }
                    } else {
                        throw new ServiceException("Mensagem retorno Olympia: " + bodyJsonObject.getString("mensagem"), HttpStatus.BAD_REQUEST.value());
                    }
                } catch (JSONException e) {
                    throw new ServiceException(String.format("Olympia está fora do experado", next.getValue()), HttpStatus.BAD_REQUEST.value());
                }
            }
        } catch (IOException e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public Boolean validarCadastroOlympia(String codigoExterno) throws ServiceException {
        if (codigoExterno == null || codigoExterno.equals("")) {
            throw new ServiceException("Codigo externo é obrigatório", HttpStatus.PAYMENT_REQUIRED.value());
        }

        String key = sessaoService.getUsuarioAtual().getChave();
        String token = configuracaoSistemaService.obterTokenIntegracaoOlympia(key);
        if (UteisValidacao.emptyString(token)) {
            throw new ServiceException("Não foi possível obter o Token", HttpStatus.BAD_REQUEST.value());
        }

        String pathEndPoint = "/associados/atividade-fisica/";
        Map<String, String> paramsHeader = new HashMap<String, String>();
        paramsHeader.put("Authorization", "Bearer " + token);
        Map<Integer, String> responseRest = null;
        try {
            responseRest = ExecuteRequestHttpService.executeRequestGETEncodeResponseRest(validarUrlOlympia() + pathEndPoint + codigoExterno, paramsHeader, "UTF-8", "UTF-8");
            Map.Entry<Integer, String> next = responseRest.entrySet().iterator().next();
            if (next.getKey() == HttpStatus.UNAUTHORIZED.value()) {
                throw new ServiceException("Não Autorizado", HttpStatus.BAD_REQUEST.value());
            }
            try {
                JSONObject bodyJsonObject = new JSONObject(next.getValue());
                if (next.getKey() < HttpURLConnection.HTTP_BAD_REQUEST) {
                    if (bodyJsonObject.has("mensagem")) {
                        return true;
                    }
                } else {
                    throw new ServiceException(bodyJsonObject.getString("mensagem"), HttpStatus.BAD_REQUEST.value());

                }
            } catch (JSONException e) {
                JSFUtilities.storeOnSession("mensagemSESI", String.format("Atenção procedimento executado sem validação da integração Olympia, pois o retorno está fora do experado [%s]", next.getValue()));
            }
        } catch (IOException e) {
            throw new ServiceException(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
        return false;
    }

    @Override
    public List<AlunoResponseTO> listaAlunos(FiltroAlunoJSON filtros, PaginadorDTO paginadorDTO, HttpServletRequest request, Integer empresaId,
                                             Boolean permiteAlunoOutraEmpresa, Boolean incluirAutorizado) throws ServiceException {
        return  listaAlunos(filtros, paginadorDTO, request, empresaId, permiteAlunoOutraEmpresa, incluirAutorizado, true);
    }

    @Override
    public List<AlunoResponseTO> listaAlunosSelect(FiltroAlunoJSON filtros, PaginadorDTO paginadorDTO, HttpServletRequest request, Integer empresaId,
                                             Boolean permiteAlunoOutraEmpresa, Boolean incluirAutorizado) throws ServiceException {
        return  listaAlunos(filtros, paginadorDTO, request, empresaId, permiteAlunoOutraEmpresa, incluirAutorizado, false);
    }

    public List<AlunoResponseTO> listaAlunos(FiltroAlunoJSON filtros, PaginadorDTO paginadorDTO, HttpServletRequest request, Integer empresaId,
                                             Boolean permiteAlunoOutraEmpresa, Boolean incluirAutorizado, boolean consultarSemParam) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            if (!isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort(paginadorDTO.getSort().contains("situacaoAluno") ? "situacao," + paginadorDTO.getSortMap().get("situacaoAluno") : paginadorDTO.getSort());
                paginadorDTO.setSort(paginadorDTO.getSort().contains("nivelAluno") ? "nivelAluno.nome," + paginadorDTO.getSortMap().get("nivelAluno") : paginadorDTO.getSort());
            } else {
                paginadorDTO.setSort("nome,ASC");
            }
            if (!consultarSemParam && UteisValidacao.emptyString(filtros.getParametro())) {
                paginadorDTO.setQuantidadeTotalElementos(0l);
                paginadorDTO.setSize(0l);
                paginadorDTO.setPage(0l);
                return new ArrayList<>();
            }
            String niveisSelecionados = filtros.getNiveisIds() != null ? niveisSelecionados(filtros.getNiveisIds()).replaceFirst(",", "") : "";
            String situacoesEnumSelecionados = filtros.getSituacoesEnum() != null ? situacoesSelecionadas(filtros.getSituacoesEnum()).replaceFirst(",", "") : "";
            String statusClienteEnumSelecionado = filtros.getStatusClienteEnum() != null ? statusClienteSelecionado(filtros.getStatusClienteEnum()).replaceFirst(",", "") : "";
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            if (usuario != null) {
                for (Permissao permissao : usuario.getPerfil().getPermissoes()) {
                    if (permissao.getRecurso().equals(RecursoEnum.VER_ALUNOS_OUTRAS_CARTEIRAS)) {
                        if (UteisValidacao.emptyList(permissao.getTipoPermissoes()) && permissao.getTipoPermissoes().size() == 0) {
                            if (filtros.getColaboradorIds() == null) {
                                filtros.setColaboradorIds(new ArrayList<>());
                            }
                            filtros.getColaboradorIds().clear();
                            filtros.getColaboradorIds().add(usuario.getProfessor().getCodigoColaborador());
                        }
                    }
                }
            }
            Empresa empresa = empresaService.obterPorIdZW(ctx, empresaId);
            ConfiguracaoSistema cfgPermAlunoMarcarAulaOutraUnidade = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_ALUNO_MARCAR_AULA_POR_TIPO_MODALIDADE);
            if ((permiteAlunoOutraEmpresa != null && permiteAlunoOutraEmpresa) && (cfgPermAlunoMarcarAulaOutraUnidade != null && cfgPermAlunoMarcarAulaOutraUnidade.getValorAsBoolean())) {
                empresaId = 0;
            }
            Integer codigoProfessor = usuario.getProfessor().getCodigo();
            if (usuario!= null && !empresa.getCodZW().equals(usuario.getEmpresaZW())) {
                codigoProfessor = professorSintetico.consultarCodProfessorPorNomeECodigoEmpresa(ctx, usuario.getProfessor().getNome(), empresa.getCodigo());
            }
            List<ClienteSintetico> alunos = (incluirAutorizado != null && incluirAutorizado && UteisValidacao.emptyString(filtros.getParametro())) ?
                    new ArrayList<>() :
                    getClienteSinteticoDao().consultarClientesSinteticos(ctx, filtros, niveisSelecionados,
                            situacoesEnumSelecionados, statusClienteEnumSelecionado, paginadorDTO, empresaId, codigoProfessor, empresa.getCodZW());
            List<AlunoResponseTO> alunoResponseTOS = new ArrayList<>();

            for (ClienteSintetico cs : alunos) {
                if(!UteisValidacao.emptyString(cs.getPessoa().getFotoKey())
                    && !"fotoPadrao.jpg".equals(cs.getPessoa().getFotoKey())){
                    cs.setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(cs.getPessoa().getFotoKey()));
                }
                ProfessorSintetico professor = null;
                if(cs.getProfessorSintetico() != null && cs.getProfessorSintetico().getCodigo() != null) {
                    professor = professorSinteticoDao.obterPorId(ctx, cs.getProfessorSintetico().getCodigo());
                }
                alunoResponseTOS.add(new AlunoResponseTO(cs, false, br.com.pacto.controller.json.base.SuperControle.independente(ctx), professor, parQDao.consultarParQPositivoAssinaturaDigital(ctx, cs.getCodigo())));
            }
            if (incluirAutorizado != null && incluirAutorizado && !UteisValidacao.emptyString(filtros.getParametro())
                    && (cfgPermAlunoMarcarAulaOutraUnidade != null && cfgPermAlunoMarcarAulaOutraUnidade.getValorAsBoolean())) {
                consultarAutorizados(ctx, usuario.getEmpresaZW(), filtros.getParametro(), alunoResponseTOS);
            }
            return alunoResponseTOS;
        } catch (Exception ex) {
            throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS, ex);
        }
    }

    public List<AlunoResponseTO> listaPassivosFila(FiltroAlunoJSON filtros, Integer empresaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<AlunoResponseTO> passivos = new ArrayList<>();
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try {
                    StringBuilder sql = new StringBuilder();
                    sql.append("select codigo, nome from passivo where");
                    sql.append(" empresa = ").append(empresaId);
                    sql.append(" and origemsistema = ").append(OrigemSistemaEnum.FILA_ESPERA.getCodigo());
                    if(!UteisValidacao.emptyString(filtros.getParametro())) {
                        sql.append(" and nome ilike '%").append(filtros.getParametro()).append("%'");
                    }
                    try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                        while (rs.next()) {
                            AlunoResponseTO passivo = new AlunoResponseTO();
                            passivo.setCodigoPassivo(rs.getInt("codigo"));
                            passivo.setNome(rs.getString("nome"));
                            passivos.add(passivo);
                        }
                    }
                } catch (Exception e) {
                    Uteis.logar(e, ClienteSinteticoServiceImpl.class);
                }
            }
            return passivos;
        }catch (Exception exception){
            throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS, exception);
        }
    }

    private void consultarAutorizados(String ctx, Integer empresaId, String param,
                                      List<AlunoResponseTO> alunoResponseTOS) {
        try {
            JSONObject jsonObject = chamadaZW(ctx, "/prest/aulacheia/autorizados", empresaId, param);
            JSONArray content = jsonObject.optJSONArray("content");
            for (int i = 0; i < content.length(); i++) {
                JSONObject json = content.getJSONObject(i);
                alunoResponseTOS.add(new AlunoResponseTO(json.getInt("id"),
                        json.getString("nome") + " (integração)",
                        true,
                        json.getString("codAcesso")));
            }
        } catch (Exception e) {
            Uteis.logar(e, ClienteSinteticoServiceImpl.class);
        }

    }

    private JSONObject chamadaZW(String ctx,
                                 String endpoint,
                                 Integer empresa,
                                 String parametro
    ) throws Exception {

        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        if (empresa != null) {
            params.add(new BasicNameValuePair("empresa", empresa.toString()));
        }
        if (parametro != null) {
            params.add(new BasicNameValuePair("paramAutorizado", parametro));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    private String chamadaZW(String ctx,
                             String endpoint, Integer empresa, Map<String, String> requestParams) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        if (requestParams != null) {
            for (String k : requestParams.keySet()) {
                params.add(new BasicNameValuePair(k, requestParams.get(k)));
            }
        }

        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return body;
    }

    private ClienteSintetico verificarAlunoZW(String ctx, Integer empresaId, String matricula) throws Exception {
        Integer codigoMatricula = 0;
        try {
            codigoMatricula = Integer.valueOf(matricula);
        } catch (NumberFormatException ne) {
            return null;
        }
        try {
            addAluno(ctx, codigoMatricula, null, false, null, empresaId);
        } catch (ServiceException ig) {

        }
        return obterPorAtributo(ctx, "matricula", codigoMatricula);
    }

    private String niveisSelecionados(List<Integer> niveisIds) {
        String niveisSelecionados = "";
        for (Integer nivelId : niveisIds) {
            niveisSelecionados += "," + nivelId.toString();
        }
        return niveisSelecionados;
    }

    private String situacoesSelecionadas(List<String> situacoesEnum) {
        String situacoesSelecionadas = "";
        for (String situacaoEnum : situacoesEnum) {
            situacoesSelecionadas += ",'" + situacaoEnum + "'";
        }
        return situacoesSelecionadas;
    }

    private String statusClienteSelecionado(List<String> statusClienteEnum) {
        String statusClienteEnumSelecionado = "";
        for (String situacaoEnum : statusClienteEnum) {
            statusClienteEnumSelecionado += ",'" + situacaoEnum + "'";
        }
        return statusClienteEnumSelecionado;
    }

    @Override
    public List<AlunoSimplesDTO> all() throws ServiceException {
        try {
            List<AlunoSimplesDTO> all = new ArrayList<AlunoSimplesDTO>();
            String ctx = sessaoService.getUsuarioAtual().getChave();
            try (ResultSet resultSet = getClienteSinteticoDao().createStatement(ctx,
                    " select c.codigo, c.nome, n.codigo as nivel, n.nome as nivelnome from clientesintetico c  \n" +
                            " left join nivel n on c.nivelaluno_codigo = n.codigo" + " order by c.nome")) {
                while (resultSet.next()) {
                    AlunoSimplesDTO a = new AlunoSimplesDTO();
                    a.setId(resultSet.getInt("codigo"));
                    a.setNome(resultSet.getString("nome"));
                    a.setNivel(new NivelAlunoResponseTO(new Nivel()));
                    if (!UteisValidacao.emptyNumber(resultSet.getInt("nivel"))) {
                        a.getNivel().setId(resultSet.getInt("nivel"));
                        a.getNivel().setNome(resultSet.getString("nivelnome"));
                    }
                    all.add(a);
                }
            }
            return all;
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS, e);
        }
    }

    @Override
    public AvaliacaoAlunoResponseDTO carregarAvaliacoesAluno(Integer alunoId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            AvaliacaoFisica avaliacaoFisica = avaliacaoFisicaService.obterAvaliacaoVigente(ctx, alunoId);

            List<AvaliacaoFisica> avaliacoes = avaliacaoFisicaService.obterAvaliacaoCliente(ctx, alunoId);
            Comparator<AvaliacaoFisica> cmp = new Comparator<AvaliacaoFisica>() {
                @Override
                public int compare(AvaliacaoFisica o1, AvaliacaoFisica o2) {
                    return o1.getCodigo().compareTo(o2.getCodigo());
                }
            };
            AvaliacaoFisica avaliacaoInicial = Collections.min(avaliacoes, cmp);
            AvaliacaoFisica avaliacaoFinal = Collections.max(avaliacoes, cmp);
            List<Date> listDias = Uteis.getDiasEntreDatas(avaliacaoInicial.getDataAvaliacao(), avaliacaoFinal.getDataAvaliacao());

            int periodoAvaliacoes = listDias.size();
            int quantAvaliacao = avaliacoes.size();

            return new AvaliacaoAlunoResponseDTO(avaliacaoFisica, periodoAvaliacoes, quantAvaliacao, avaliacoes);
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_CARREGAR_AVALIACAO_FISICA);
        }
    }

    @Override
    public List<LinhaDeTempoAlunoResponseDTO> carregarLinhaDeTempoAluno(Integer matriculaAluno, FiltroLinhaTempoDTO filtroLinhaTempoDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico cs;
            if (matriculaAluno != null) {
                cs = consultarPorMatricula(ctx, matriculaAluno.toString());
            } else {
                throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS);
            }

            FiltrosLinhaTempo filtros = popularFiltros(filtroLinhaTempoDTO);

            List<LinhaDoTempoTO> listaLinhaDoTempoTO = linhaDoTempoService.montarLinha(ctx, cs, filtros, filtros.getInicio(), filtros.getFim());

            List<LinhaDeTempoAlunoResponseDTO> linhaDoTempoReturn = new ArrayList<>();

            for (int i = 0; i < MAXIMO_ITEMS_LINHA_DO_TEMPO; i++) {
                if (i <= (listaLinhaDoTempoTO.size() - 1)) {
                    if (listaLinhaDoTempoTO.get(i).getData() != null) {
                        linhaDoTempoReturn.add(new LinhaDeTempoAlunoResponseDTO(listaLinhaDoTempoTO.get(i)));
                    }
                } else {
                    break;
                }
            }

            return linhaDoTempoReturn;
        } catch (ServiceException e) {
            throw new ServiceException(AlunoExcecoes.ERRO_AO_CARREGAR_LINHA_DE_TEMPO_DO_ALUNO);
        }
    }

    public List<AtestadoResponseTO> montarListaAtestado(Integer alunoID) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            ClienteSintetico cs = obterPorId(ctx, alunoID);

            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            List<AtestadoClienteJSON> atestadosCliente = null;
            atestadosCliente = integracaoWS.consultarAtestadoCliente(url, ctx, cs.getMatricula());
            if (atestadosCliente != null) {
                Ordenacao.ordenarLista(atestadosCliente, "dataInicio");
                Collections.reverse(atestadosCliente);
                Boolean parq = atestadosCliente.isEmpty() ? false : atestadosCliente.get(0).getParq();
                if (cs.getParq() != parq) {
                    cs.setParq(parq);
                    ClienteSinteticoService cs2 = (ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class);
                    cs = cs2.alterar(ctx, cs);
                }
            }
            List<AtestadoResponseTO> listAtestados = new ArrayList<>();
            if (atestadosCliente != null) {
                for (AtestadoClienteJSON aj : atestadosCliente) {
                    final String key = genKey(ctx, MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO, String.valueOf(aj.getCodAtestado()), aj.getExtensao());
                    String urlBase = Aplicacao.getProp(Aplicacao.urlFotosNuvem);
                    listAtestados.add(new AtestadoResponseTO(aj) {{
                        setUrlAnexo(String.format("%s/%s", new Object[]{
                                urlBase,
                                key}));
                    }});
                }
            }

            return listAtestados;
        } catch (Exception e) {
            System.out.println(e.getMessage());
            Uteis.logar(e, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(AlunoExcecoes.ERRO_AO_CARREGAR_ATESTADO_ALUNO);
        }
    }

    private FiltrosLinhaTempo popularFiltros(FiltroLinhaTempoDTO filtroLinhaTempoDTO) throws ServiceException {
        FiltrosLinhaTempo filtros = new FiltrosLinhaTempo();
        if (filtroLinhaTempoDTO.getTiposEvento() != null && filtroLinhaTempoDTO.getTiposEvento().size() > 0) {
            filtros.getTipos().clear();
            for (TipoLinhaEnum tipo : filtroLinhaTempoDTO.getTiposEvento()) {
                GenericoTO genericoTO = new GenericoTO();
                genericoTO.setTipoLinha(tipo);
                genericoTO.setLabel(tipo.name());
                genericoTO.setEscolhido(true);
                filtros.getTipos().add(genericoTO);
            }
        }

        if (filtroLinhaTempoDTO.getDataInicio() == null) {
            Calendar dataInicio = Calendar.getInstance();
            dataInicio.add(Calendar.DATE, -365);
            dataInicio.set(Calendar.HOUR, 0);
            dataInicio.set(Calendar.MINUTE, 0);
            dataInicio.set(Calendar.SECOND, 0);

            filtros.setInicio(dataInicio.getTime());
        } else {
            if (filtroLinhaTempoDTO.getDataInicio().before(filtroLinhaTempoDTO.getDataFim())) {
                filtros.setInicio(filtroLinhaTempoDTO.getDataInicio());
            } else {
                throw new ServiceException(AlunoExcecoes.ERRO_DATA_INICIAL_MAIOR_QUE_DATA_FINAL);
            }
        }

        if (filtroLinhaTempoDTO.getDataFim() == null) {
            filtros.setFim(new Date());
        } else {
            filtros.setFim(filtroLinhaTempoDTO.getDataFim());
        }

        return filtros;
    }

    @Override
    public AvaliacaoAlunoResponseDTO alterarDataAvaliacaoFisica(Integer avaliacaoId, AvaliacaoAlunoDTO avaliacaoAlunoDTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        int usuarioId = sessaoService.getUsuarioAtual().getId();
        Usuario usuario = ((UsuarioService) UtilContext.getBean(UsuarioService.class)).obterPorId(ctx, usuarioId);
        AvaliacaoFisica avaliacaoFisica;
        if (avaliacaoId != null) {
            avaliacaoFisica = avaliacaoFisicaService.obterPorId(ctx, avaliacaoId);
        } else {
            throw new ServiceException(AlunoExcecoes.ERRO_AVALIACAO_FISICA_NAO_INFORMADO);
        }

        Agendamento agendamento;

        if (avaliacaoAlunoDTO.getData() != null) {
            if (new Date().before(avaliacaoAlunoDTO.getData())) {
                agendamento = avaliacaoFisica.getAgendamentoReavaliacao();

                Calendar dataAntigaInicio = Calendar.getInstance();
                dataAntigaInicio.setTime(agendamento.getInicio());

                Calendar dataNovaInicio = Calendar.getInstance();
                dataNovaInicio.setTime(avaliacaoAlunoDTO.getData());
                int hora = dataAntigaInicio.get(Calendar.HOUR);
                int minutos = dataAntigaInicio.get(Calendar.MINUTE);
                dataNovaInicio.set(Calendar.HOUR, hora);
                dataNovaInicio.set(Calendar.MINUTE, minutos);

                Calendar dataAntigaFim = Calendar.getInstance();
                dataAntigaFim.setTime(agendamento.getFim());

                Calendar datanovaFim = Calendar.getInstance();
                datanovaFim.setTime(avaliacaoAlunoDTO.getData());
                hora = dataAntigaFim.get(Calendar.HOUR);
                minutos = dataAntigaFim.get(Calendar.MINUTE);
                datanovaFim.set(Calendar.HOUR, hora);
                datanovaFim.set(Calendar.MINUTE, minutos);

                agendamento.setInicio(dataNovaInicio.getTime());
                agendamento.setFim(datanovaFim.getTime());
                agendamento.setUltimaAlteracao(Calendario.hoje());
                agendamento.setUsuarioUltAlteracao_codigo(usuario.getCodigo());
                agendamento.setStatus(StatusAgendamentoEnum.REAGENDADO);

            } else {
                throw new ServiceException(AlunoExcecoes.ERRO_DATA_INFORMADA_INFERIOR_DATA_ATUAL);
            }

            agendamento = ((AgendamentoService) UtilContext.getBean(AgendamentoService.class)).alterar(ctx, agendamento, usuario);

            if (agendamento.getStatus().equals(StatusAgendamentoEnum.REAGENDADO)) {
                avaliacaoFisica.setAgendamentoReavaliacao(agendamento);
                avaliacaoFisica.setDataProxima(avaliacaoAlunoDTO.getData());
                avaliacaoFisica = avaliacaoFisicaService.alterar(ctx, avaliacaoFisica);
            }
        }

        return new AvaliacaoAlunoResponseDTO(avaliacaoFisica, 0, 0, null);
    }

    @Override
    public String reenviarConfirmacaoApp(Integer alunoId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico clienteSintetico;
            String retorno = "";
            if (!UteisValidacao.emptyNumber(alunoId)) {
                clienteSintetico = obterPorCodigo(ctx, alunoId);
            } else {
                throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_INFORMADO);
            }

            if (clienteSintetico != null) {
                Usuario usuario = usuarioService.obterPorAtributo(ctx, "cliente.codigo", clienteSintetico.getCodigo());
                if (!UteisValidacao.validaEmail(usuario.getUserName())
                     || (UteisValidacao.validaEmail(clienteSintetico.getEmail()) && !usuario.getUserName().equals(clienteSintetico.getEmail()))){
                    usuario.setUserName(clienteSintetico.getEmail());
                    if (!UteisValidacao.validaEmail(usuario.getUserName())) {
                        throw new ServiceException(AlunoExcecoes.ERRO_USUARIO_INVALIDO);
                    }
                    usuarioService.alterar(ctx, usuario);
                }

                if (br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                    SecureRandom random = new SecureRandom();
                    String senha = new BigInteger(130, random).toString(32);
                    senha = senha.length() > 8 ? senha.substring(0, 8) : senha;

                    usuario.setSenha(Uteis.encriptar(senha));
                    usuarioService.alterar(ctx, usuario);

                    retorno = independenteService.enviarUsuarioMovelAluno(ctx, clienteSintetico, senha);
                } else {
                    String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                    IntegracaoCadastrosWSConsumer integracao = ((IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class));
                    retorno = integracao.alterarUsuarioMovelAluno(url, ctx, usuario.getUserName(), null, clienteSintetico.getCodigoCliente(),
                            br.com.pacto.controller.json.base.SuperControle.getNomeAppParaEmail(ctx),
                            br.com.pacto.controller.json.base.SuperControle.getAppUrlEmail(ctx));
                }


            } else {
                throw new ServiceException(AlunoExcecoes.ERRO_OBTER_ALUNO);
            }

            return retorno;
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_REENVIAR_USER_APP, e);
        }
    }

    public void refresh(final String ctx, ClienteSintetico object) throws Exception {
        getClienteSinteticoDao().refresh(ctx, object);
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void salvarMidiaNuvem(String chave, AlunoDTO alunoDTO, Pessoa pessoa) throws Exception {
        String key = UtilS3Base64Img.getUrlImage(alunoDTO.getImagemData(), alunoDTO.getExtensaoImagem(), chave, pessoa);
        Aplicacao.saveImageLocal(context, chave, key, true, null, false);
        pessoa.setFotoKey(key);
        pessoaDao.update(chave, pessoa);
    }

    private void deleteFotoNuvem(final String key) throws ServiceException {
        try {
            MidiaService.getInstanceWood().deleteObject(key);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public PessoaDao getPessoaDao() {
        return pessoaDao;
    }

    public void setPessoaDao(PessoaDao pessoaDao) {
        this.pessoaDao = pessoaDao;
    }

    @Override
    public List<AvaliacaoCatalogoAlunoDTO> obterCatalogoAlunosAvaliacaoFisica(HttpServletRequest request, String filtroNome, Integer empresaId) throws ServiceException {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            List<ClienteSintetico> clientes = new ArrayList<>();
            clientes = consultarPorMatriculaOuNome(chave, empresaId, null,
                    filtroNome, 25);

            List<AvaliacaoCatalogoAlunoDTO> ret = new ArrayList<AvaliacaoCatalogoAlunoDTO>();

            for (ClienteSintetico cliente : clientes) {
                if (cliente.getPessoa() != null) {
                    cliente.setUrlFoto(fotoService.defineURLFotoPessoa(request, cliente.getPessoa().getFotoKey(), cliente.getCodigoPessoa(), false, chave, !br.com.pacto.controller.json.base.SuperControle.independente(chave)));
                }
                ret.add(new AvaliacaoCatalogoAlunoDTO(cliente, avaliacaoFisicaService.obterResultadoQuestionarioParq(chave, cliente)));
            }
            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(CatalogoAlunoExcecoes.ERRO_CONSULTAR_CATALOGO_ALUNOS);
        }
    }


    public DetalheTreinoAlunoDTO detalhesTreinamentoAluno(Integer alunoId) throws ServiceException {
        try {
            return programaService.detalheTreinamentoCliente(alunoId);
        } catch (ServiceException e) {
            if (e.getMessage() == "Ocorreu um erro ao pesquisar o Programa Treino informado") {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMA_TREINO);
            } else {
                throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_DETALHES_PROGRAMA);
            }
        }
    }

    private AlunoOlympiaDTO preencherAluno(JSONObject obj) throws ServiceException {
        try {
            AlunoOlympiaDTO dto = new AlunoOlympiaDTO();
            dto.setCodigoExterno(String.valueOf(obj.optInt("idPessoa")));
            String sexo = obj.optString("sexo");
            if (sexo != null && !sexo.trim().isEmpty()) {
                try {
                    dto.setSexo(SexoEnum.getSexo(sexo));
                } catch (IllegalArgumentException e) {
                    dto.setSexo(SexoEnum.N);
                }
            } else {
                dto.setSexo(SexoEnum.N);
            }
            dto.setNome(obj.optString("nome"));
            dto.setDataNascimento(Uteis.getDate(obj.optString("dataNascimento"), "yyyy-MM-DD").getTime());

            String email = obj.optString("email");
            if (!UteisValidacao.emptyString(email)) {
                dto.getEmails().add(email);
            }

            JSONArray enderecos = obj.optJSONArray("enderecos");
            for (int e = 0; e < enderecos.length(); e++) {
                JSONObject end = enderecos.optJSONObject(e);
                String telefone = end.optString("tel2");
                if (!UteisValidacao.emptyString(telefone)) {
                    TelefoneDTO telefoneDTO = new TelefoneDTO();
                    telefoneDTO.setNumero(telefone);
                    dto.getFones().add(telefoneDTO);
                }
            }
            Long cpf = obj.optLong("cpf");

            validarCodigoExterno(dto.getCodigoExterno(), cpf);
            return dto;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage(), HttpStatus.BAD_REQUEST.value());
        }
    }

    private void validarCodigoExterno(String codigoExterno, Long cpf) throws ServiceException {
        String key = sessaoService.getUsuarioAtual().getChave();
        ClienteSintetico clienteSintetico = consultarPorCodigoExterno(key, codigoExterno);
        if (clienteSintetico != null) {
            throw new ServiceException("Já existe um aluno cadastrado com esse ID/CPF (ID: " + codigoExterno + " / CPF: " + cpf + "). Matrícula " +
                    (clienteSintetico.getMatriculaString() == null ? String.valueOf(clienteSintetico.getCodigo()) : clienteSintetico.getMatriculaString()), HttpStatus.BAD_REQUEST.value());
        }
    }

    private String validarUrlOlympia() throws ServiceException {
        String key = sessaoService.getUsuarioAtual().getChave();
        ConfiguracaoSistema cfgURLInteg = configuracaoSistemaService.consultarPorTipo(key, ConfiguracoesEnum.URL_SISTEMA_OLYMPIA);
        String url = cfgURLInteg.getValor();
        if (UteisValidacao.emptyString(url)) {
            throw new ServiceException("URL Integração não está preenchida.", HttpStatus.BAD_REQUEST.value());
        }
        return url;
    }

    @Override
    public List<AlunoZWResponseDTO> obterAlunoZW(String nome, String cpf, Integer matricula, Integer empresaId, HttpServletRequest request) throws ServiceException {
        try {
            if (!isBlank(nome) || !isBlank(cpf) || !UteisValidacao.emptyNumber(matricula)) {
                String ctx = sessaoService.getUsuarioAtual().getChave();
                Usuario usuarioLogado = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
                Boolean podeVisualizarAlunosOutraCarteira = usuarioLogado.isFuncionalidadeHabilitado(RecursoEnum.VER_ALUNOS_OUTRAS_CARTEIRAS.name());
                String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);

                List<AddClienteJSON> clientesZWJSON = integracaoWS.consultarClientesZW(url, ctx, empresaId, cpf, nome, matricula);
                List<AlunoZWResponseDTO> ret = new ArrayList<>();
                if (UteisValidacao.emptyList(clientesZWJSON)) {
                    throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_ENCONTRADO);
                }
                if (!podeVisualizarAlunosOutraCarteira) {
                    for (AddClienteJSON clienteJSON : clientesZWJSON) {
                        for (VinculoJSON vinculo : clienteJSON.getVinculos()) {
                            if (vinculo.getTipoVinculo().equals("TW")
                                    && usuarioLogado.getProfessor().getCodigoColaborador() != null
                                    && vinculo.getCodColaborador() != null
                                    && vinculo.getCodColaborador().intValue() == usuarioLogado.getProfessor().getCodigoColaborador().intValue()) {
                                ClienteSintetico clienteTreino = clienteSinteticoDao.findObjectByAttribute(ctx, "codigoCliente", clienteJSON.getCodigoCliente());
                                ClienteSintetico clienteZW = integracaoWS.consultarClienteSintetico(url, ctx, clienteJSON.getCodigoCliente());
                                if (clienteZW == null) {
                                    clienteZW = new ClienteSintetico();
                                }
                                if (clienteTreino != null && clienteTreino.getPessoa() != null) {
                                    clienteZW.setUrlFoto(fotoService.defineURLFotoPessoa(request, clienteZW.getPessoa().getFotoKey(), clienteZW.getCodigoPessoa(), false, ctx, !br.com.pacto.controller.json.base.SuperControle.independente(ctx)));
                                }
                                ret.add(new AlunoZWResponseDTO(clienteZW, (clienteTreino != null ? clienteTreino.getCodigo() : null)));
                            }
                        }
                    }
                } else {
                    for (AddClienteJSON clienteJSON : clientesZWJSON) {
                        ClienteSintetico clienteTreino = clienteSinteticoDao.findObjectByAttribute(ctx, "codigoCliente", clienteJSON.getCodigoCliente());
                        ClienteSintetico clienteZW = integracaoWS.consultarClienteSintetico(url, ctx, clienteJSON.getCodigoCliente());
                        if (clienteZW == null) {
                            clienteZW = new ClienteSintetico();
                        }
                        if (clienteTreino != null && clienteTreino.getPessoa() != null) {
                            clienteZW.setUrlFoto(fotoService.defineURLFotoPessoa(request, clienteZW.getPessoa().getFotoKey(), clienteZW.getCodigoPessoa(), false, ctx, !br.com.pacto.controller.json.base.SuperControle.independente(ctx)));
                        }
                        ret.add(new AlunoZWResponseDTO(clienteZW, (clienteTreino != null ? clienteTreino.getCodigo() : null)));
                    }
                }
                return ret;
            } else {
                throw new ServiceException(AlunoExcecoes.ERRO_INFORME_FILTRO);
            }
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_ENCONTRADO, e);
        }
    }

    @Override
    public void cadastrarAlunoZW(AlunoZWDTO alunoZWDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            ConfiguracaoSistema cfgAlunosAtivos = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_APENAS_ALUNOS_ATIVOS);
            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);

            ClienteSintetico clienteEscolhido = integracaoWS.consultarClienteSintetico(url, ctx, alunoZWDTO.getAlunoId());
            if (cfgAlunosAtivos.getValorAsBoolean() && !clienteEscolhido.getSituacao().equals("AT")) {
                throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_INATIVO);
            }
            ProfessorSintetico professor;
            if (!UteisValidacao.emptyNumber(alunoZWDTO.getProfessorId())) {
                professor = getProfessorSintetico().obterPorId(ctx, alunoZWDTO.getProfessorId());
                if (professor == null || UteisValidacao.emptyNumber(professor.getCodigo())) {
                    throw new ServiceException(AlunoExcecoes.PROFESSOR_NAO_ENCONTRADO);
                }
            } else {
                throw new ServiceException(AlunoExcecoes.PROFESSOR_NAO_ENCONTRADO);
            }
            if (ctx.equals(clienteEscolhido.getKey())) {
                Empresa empresa = empresaService.obterPorId(clienteEscolhido.getKey(), clienteEscolhido.getEmpresa());
                String retorno = concluirCadastro(ctx, clienteEscolhido, null, integracaoWS, alunoZWDTO.getEmail(),
                        alunoZWDTO.getSenha(), alunoZWDTO.getProfessorId(), alunoZWDTO.getUsarAplicativo(), usuario.getUsuarioZW(),
                        empresa.getNome());
                if (!retorno.contains("Cadastro realizado com sucesso para matrícula")) {
                    throw new ServiceException(AlunoExcecoes.ERRO_CADASTRAR_ALUNO_ZW);
                }
            } else {
                throw new ServiceException(AlunoExcecoes.ERRO_TOKEN_DIFERENTE_SESSAO);
            }
        } catch (Exception e) {
            throw new ServiceException(AlunoExcecoes.ERRO_CADASTRAR_ALUNO_ZW, e);
        }
    }

    @Override
    public List<ColetorJSON> consultarLocaisAcesso(final String ctx, final Integer empresa) throws Exception {

        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);

        return integracaoWS.consultarLocaisAcessoPorEmpresa(url, ctx, empresa);
    }

    @Override
    public ColetorJSON consultarLocalAcessoPorNFC(final String ctx, final String nfc) throws Exception {

        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);

        return integracaoWS.consultarLocaisAcessoPorEmpresa(url, ctx, nfc);
    }

    @Override
    public String sincronizarDadosClienteSintetico(String ctx) throws Exception {
        try {
            JSONObject chamada = chamadaZW(ctx, "/prest/manutencao/obter-dados-alunos-para-sincronizar");
            JSONArray content = chamada.optJSONArray("content");

            String retorno = "";
            Integer alunosAtualizados = 0;
            for (Object item : content) {
                JSONObject aluno = ((JSONObject) item);
                if (aluno != null) {
                    try {
                        ClienteSintetico cs = new ClienteSintetico();
                        cs = consultarPorMatricula(ctx, aluno.getInt("matricula") + "");
                        if (cs != null && (!cs.getNome().equals(aluno.getString("nomecliente")) ||
                                !cs.getNomeplano().equals(aluno.getString("nomeplano")) ||
                                !cs.getSexo().equals(aluno.getString("sexo")))) {
                            sincronizarClientes(ctx, aluno);
                            retorno += "<br/> MAT: " + aluno.getInt("matricula") + " Nome: " + aluno.getString("nomecliente");
                            alunosAtualizados++;
                        }
                    } catch (ServiceException e) {
                        e.printStackTrace();
                        throw new ServiceException("Erro ao sincronizar os alunos!", e.getMessage());
                    }
                }
            }

            if (alunosAtualizados == 0) {
                return "Todos alunos já se encontram atualizados!";
            } else {
                return "<br/><br/>" + alunosAtualizados + " Alunos sincronizados e atualizados com sucesso!<br/><br/>" + retorno + "<br/>";
            }
        } catch (ServiceException ex) {
            throw new ServiceException("Erro ao sincronizar os alunos!", ex.getMessage());
        }
    }

    @Override
    public String sincronizarMatriculaClienteSintetico(final String ctx, Integer empresaZW) throws Exception {
        try {
            Integer alunosVerificados = 0;
            Integer alunosAtualizados = 0;
            Map<Integer, Integer> mapaClientesTR = listarClientesTR(ctx, empresaZW);
            Map<Integer, Integer> mapaClientesZW = listarClientesZW(ctx, empresaZW);

            for (Map.Entry<Integer, Integer> itemMapa : mapaClientesTR.entrySet()) {
                alunosVerificados++;
                Integer codigoCliente = itemMapa.getKey();
                Integer matriculaTR = itemMapa.getValue();

                if (mapaClientesZW.containsKey(codigoCliente) && !mapaClientesZW.get(codigoCliente).equals(matriculaTR)) {
                    try {
                        StringBuilder update = new StringBuilder();
                        update.append(" UPDATE clientesintetico SET");
                        update.append(" matricula = ").append(mapaClientesZW.get(codigoCliente));
                        update.append(" WHERE codigoCliente = ").append(codigoCliente);

                        getClienteSinteticoDao().executeNativeSQL(ctx, update.toString());
                    } catch (Exception ex) {
                        throw new ServiceException("Ocorreu um erro ao atualizar o aluno codCliente: " + codigoCliente);
                    }
                    alunosAtualizados++;
                }
            }

            if (alunosAtualizados == 0) {
                return "Todas matriculas estão corretas!";
            } else {
                return  alunosVerificados + " alunos verificados e " + alunosAtualizados + " alunos que precisaram sincronizar a matrícula!";
            }
        } catch (ServiceException ex) {
            throw new ServiceException("Erro ao sincronizar matrícula dos alunos!", ex.getMessage());
        }
    }

    private Map<Integer, Integer> listarClientesTR(String ctx, Integer empresaZW) throws Exception {
        Connection conTR = clienteSinteticoDao.getConnection(ctx);
        Map<Integer, Integer> mapaClientes = new HashMap<>();
        try {
            String sql = "select codigocliente, matricula from clientesintetico c where empresa = " + empresaZW;
            try (ResultSet rs = conTR.prepareStatement(sql).executeQuery()) {
                while (rs.next()) {
                    mapaClientes.put(rs.getInt("codigocliente"), rs.getInt("matricula"));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
        }

        return mapaClientes;
    }

    private Map<Integer, Integer> listarClientesZW(String ctx, Integer empresaZW) throws Exception {
        Map<Integer, Integer> mapaClientes;
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            mapaClientes = new HashMap<>();
            try {
                String sql = "select codigo, codigomatricula from cliente where empresa = " + empresaZW;
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {
                    while (rs.next()) {
                        mapaClientes.put(rs.getInt("codigo"), rs.getInt("codigomatricula"));
                    }
                }
            } catch (Exception e) {
                Uteis.logar(e, ClienteSinteticoServiceImpl.class);
            }
        }

        return mapaClientes;
    }

    @Override
    public String buscarAssinaturaDigitalContratosAluno(String ctx, String matricula, String empresa, Boolean validar) throws ServiceException {
        try {
            return chamadaZW(ctx, matricula, empresa, null, null, null, null, null, null, null, validar, "/prest/treino/aluno-contrato-assinatura-digital");
        } catch (Exception ex) {
            throw new ServiceException("Ocorreu um erro ao buscar contratos da matricula: " + matricula);
        }
    }

    @Override
    public String buscarAssinaturaDigitalContratosAlunoByContrato(String ctx, String contrato, String empresa) throws ServiceException {
        try {
            return chamadaZW(ctx, null, empresa, contrato, null, null, null, null, null, null, true, "/prest/treino/aluno-contrato-assinatura-digital-by-contrato");
        } catch (Exception ex) {
            throw new ServiceException("Ocorreu um erro ao buscar contratos do contrato: " + contrato);
        }
    }

    @Override
    public String incluirAssinaturaDigitalContratoAluno(String ctx, String contrato, String aditivo, String assinatura, JSONObject json) throws ServiceException {
        try {
            if (json != null) {
                String documentos = json.optString("documentos");
                if (UteisValidacao.emptyString(documentos)) {
                    throw new ServiceException("O campo 'documentos' é obrigatório.");
                }
                String ass = json.optString("assinatura");
                if (UteisValidacao.emptyString(ass)) {
                    throw new ServiceException("O campo 'assinatura' é obrigatório.");
                }
                String ip = json.optString("ip");
                String regexIP = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
                if (UteisValidacao.emptyString(ip) || !ip.matches(regexIP)) {
                    throw new ServiceException("O campo ip está vazio ou mal formatado.");
                }
                String tipoAutenticacao = json.optString("tipoAutenticacao");
                if (UteisValidacao.emptyString(tipoAutenticacao) || !(tipoAutenticacao.equalsIgnoreCase("SMS") || tipoAutenticacao.equalsIgnoreCase("EMAIL"))) {
                    throw new ServiceException("O campo tipoAutenticacao está vazio ou possui um valor inválido.");
                }
                return chamadaZW(ctx, null, "0", contrato, aditivo, ass, documentos, ip, tipoAutenticacao, json.optString("dadosAutenticacao"), null, "/prest/treino/aluno-contrato-assinatura-digital-incluir");
            } else {
                return chamadaZW(ctx, null, "0", contrato, aditivo, assinatura, null, null, null, null, null, "/prest/treino/aluno-contrato-assinatura-digital-incluir");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (json != null) {
                throw new ServiceException(ex.getMessage());
            }
            throw new ServiceException("Ocorreu um erro ao incluir assinatura no contrato: " + contrato);
        }
    }

    private void sincronizarClientes(String ctx, JSONObject aluno) throws ServiceException {
        try {
            StringBuilder update = new StringBuilder();
            update.append("UPDATE clientesintetico SET \n");
            update.append("nome ='").append(aluno.getString("nomecliente")).append("', \n");
            update.append("nomeplano ='").append(aluno.getString("nomeplano")).append("', \n");
            update.append("sexo ='").append(aluno.getString("sexo")).append("' \n");
            update.append("WHERE matricula = ").append(aluno.getInt("matricula"));

            getClienteSinteticoDao().executeNativeSQL(ctx, update.toString());

        } catch (Exception ex) {
            throw new ServiceException("Ocorreu um erro ao atualizar o aluno " + aluno.getString("nomecliente") +
                    " - MAT: " + aluno.getInt("matricula"));
        }
    }

    private JSONObject chamadaZW(String ctx, String endpoint) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));

        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();

        return new JSONObject(body);
    }

    private String chamadaZW(String ctx, String matricula, String empresa, String contrato, String aditivo, String assinatura, String documentos, String ip, String tipoAutenticacao, String dadosAutenticacao, Boolean validar, String endpoint) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa));

        if (matricula != null) {
            params.add(new BasicNameValuePair("matricula", matricula));
        }
        if (contrato != null) {
            params.add(new BasicNameValuePair("contrato", contrato));
        }
        if (aditivo != null) {
            params.add(new BasicNameValuePair("aditivo", aditivo));
        }
        if (assinatura != null) {
            params.add(new BasicNameValuePair("assinatura", assinatura));
        }
        if (documentos != null) {
            params.add(new BasicNameValuePair("documentos", documentos));
        }
        if (ip != null) {
            params.add(new BasicNameValuePair("ip", ip));
        }
        if (tipoAutenticacao != null) {
            params.add(new BasicNameValuePair("tipoAutenticacao", tipoAutenticacao));
        }
        if (dadosAutenticacao != null) {
            params.add(new BasicNameValuePair("dadosAutenticacao", dadosAutenticacao));
        }
        if (validar != null) {
            params.add(new BasicNameValuePair("validar", validar.toString()));
        }

        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();

        return body;
    }

    @Override
    public void criarUsuarioMovelAluno(UsuarioSimplesAlunoDTO usuarioSimples) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.consultarPorCliente(ctx, usuarioSimples.getAlunoId());
            if (usuario == null) {
                usuario = new Usuario();
                usuario.setCliente(obterPorId(ctx, usuarioSimples.getAlunoId()));
            }
            usuario.setTipo(TipoUsuarioEnum.ALUNO);
            usuario.setCliente(usuario.getCliente());
            usuario.setStatus(StatusEnum.ATIVO);
            SecureRandom random = new SecureRandom();
            String senha = usuarioSimples.getPassword();
            if (senha == null || senha.isEmpty()) {
                senha = new BigInteger(130, random).toString(32);
                senha = senha.length() > 8 ? senha.substring(0, 8) : senha;
            }

            usuario.setSenha(Uteis.encriptar(senha));
            usuario.setNome(usuarioSimples.getUsername());
            usuario.setUserName(usuarioSimples.getUsername());
            validUser(ctx, usuario);

            if (br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                usuarioService.alterar(ctx, usuario);
                independenteService.enviarUsuarioMovelAluno(ctx, usuario.getCliente(), senha);
                adicionarUsuarioServicoDescobrir(ctx, usuarioSimples.getUsername());
            } else {
                IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                String retorno = integracaoWS.gerarUsuarioMovelAluno(url, ctx, usuarioSimples.getUsername(), senha,
                        usuario.getCliente().getCodigoCliente(), SuperControle.getNomeAppParaEmail(ctx), SuperControle.getAppUrlEmail(ctx),
                        SuperControle.getNomeAppParaEmail(ctx));
                if (retorno.contains("ERRO")) {
                    throw new ValidacaoException(retorno.replaceFirst("ERRO: ", ""));
                }
            }

        } catch (Exception ex) {
            throw new ServiceException(AlunoExcecoes.ERRO_AO_CRIAR_USUARIO, ex);
        }
    }

    private void validUser(String ctx, Usuario usuario) throws ServiceException {
        if (!UteisEmail.getValidEmail(usuario.getUserName())) {
            throw new ServiceException(AlunoExcecoes.ERRO_USUARIO_INVALIDO);
        }
        if (usuarioDao.exists(ctx, usuario, "userName", "codigo")) {
            throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_USERNAME_EM_USO);
        }
    }

    public ClientePesquisaService getClientePesquisaService() {
        return clientePesquisaService;
    }

    public void setClientePesquisaService(ClientePesquisaService clientePesquisaService) {
        this.clientePesquisaService = clientePesquisaService;
    }

    public List<ClienteSintetico> obterAlunosIntegracaoSistemaFera(String ctx) throws ServiceException {
        List<ClienteSintetico> clienteSinteticos = new ArrayList<ClienteSintetico>();
        try {
            return clienteSinteticos = clienteSinteticoDao.consultarClientesSinteticosIntegracaoFera(ctx);

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProgramaTreinoFichaDao getProgramaTreinoFichaDao() {
        return programaTreinoFichaDao;
    }

    public void setProgramaTreinoFichaDao(ProgramaTreinoFichaDao programaTreinoFichaDao) {
        this.programaTreinoFichaDao = programaTreinoFichaDao;
    }

    public ProgramaTreinoDao getProgramaTreinoDao() {
        return programaTreinoDao;
    }

    public void setProgramaTreinoDao(ProgramaTreinoDao programaTreinoDao) {
        this.programaTreinoDao = programaTreinoDao;
    }

    @Override
    public String consultarOrfaos(final String ctx, final String codigosPessoa) throws Exception {
        StringBuilder orfaos = new StringBuilder();
        try (ResultSet resultSet = getClienteSinteticoDao().createStatement(ctx,
                "select codigocliente from clientesintetico where codigopessoa not in (" + codigosPessoa + ")")) {
            while (resultSet.next()) {
                orfaos.append(resultSet.getInt("codigocliente")).append(",");
            }
        }
        return StringUtils.chop(orfaos.toString());
    }

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.context = servletContext;
    }

    public ClienteSintetico consultarPorGympassUniqueToken(String ctx, String gympassUniqueToken) throws ServiceException {
        try {
            return getClienteSinteticoDao().findObjectByAttribute(ctx, "gympassUniqueToken", gympassUniqueToken);
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    public ClienteSintetico consultarGympassBooking(String ctx, Empresa empresa, String gympassUniqueToken, String email) throws ServiceException {
        try {
            try(Connection con = conexaoZWService.conexaoZw(ctx)){
                try (ResultSet rsToken = ConexaoZWServiceImpl.criarConsulta("select codigo from cliente " +
                        "where gympassuniquetoken = '" + gympassUniqueToken + "'", con)) {

                    if (rsToken.next()) {
                        return obterPorCodigoCliente(ctx, rsToken.getInt(1));
                    }
                }
                StringBuilder sql = new StringBuilder();
                sql.append("select  \n");
                sql.append("cs.codigocliente  \n");
                sql.append("from clientesintetico cs  \n");
                sql.append("left join pessoa p on p.codigo = cs.pessoa_codigo \n");
                sql.append("left join usuario u on u.cliente_codigo = cs.codigo \n");
                sql.append("left join email e on e.pessoa_codigo = cs.pessoa_codigo \n");
                sql.append("where 1 = 1 \n");
                sql.append("and ( \n");
                sql.append("cs.email ilike '").append(email).append("' \n");
                sql.append("or e.email ilike '").append(email).append("' \n");
                sql.append("or u.username ilike '").append(email).append("' \n");
                sql.append("or u.email ilike '").append(email).append("') \n");
                try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, sql.toString())) {
                    while (rs.next()) {
                        int codigocliente = rs.getInt("codigocliente");
                        try (ResultSet rsClienteZW = ConexaoZWServiceImpl.criarConsulta("select matricula from cliente " +
                                "where codigo = " + codigocliente, con)) {
                            if (rsClienteZW.next()) {
                                return obterPorCodigoCliente(ctx, codigocliente);
                            }
                        }
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public ClienteSintetico consultarAtivoPorMatricula(String ctx, Integer matricula) throws ServiceException {
        try {
            String s = "select obj from ClienteSintetico obj where matricula = :matricula and ativo is true";
            Map<String, Object> p = new HashMap();
            p.put("matricula", matricula);
            return obterObjetoPorParam(ctx, s, p);
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<ClienteSintetico> matriculas(String ctx, Integer empresa) throws ServiceException {
        try {
            List<ClienteSintetico> matriculas = new ArrayList<>();
            try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, "select matricula, codigo, codigocliente, nome from clientesintetico where empresa = " + empresa)) {
                while (rs.next()) {
                    ClienteSintetico cs = new ClienteSintetico();
                    cs.setNome(rs.getString("nome"));
                    cs.setCodigo(rs.getInt("codigo"));
                    cs.setCodigoCliente(rs.getInt("codigocliente"));
                    cs.setMatricula(rs.getInt("matricula"));
                    matriculas.add(cs);
                }
            }
            return matriculas;
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    public void acao(final AcaoAlunoEnum acao, String matricula) {
        try {
            AuditUtilities.putAcaoAlunoFromCurrentThread(Thread.currentThread().getId(), acao.name() + "-" + matricula);
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
    }

    public void leaveAcao() {
        try {
            AuditUtilities.leaveAcaoAlunoFromCurrentThread();
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    @Override
    public boolean matriculaExiste(final String ctx, final Integer matricula) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" matricula = ").append(matricula);
            return getClienteSinteticoDao().existsWithParam(ctx, sql);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Integer obterUltimaMatriculaCadastrada(final String ctx) throws ServiceException {
        try {
            try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, "select matricula from clientesintetico c where matricula is not null order by matricula desc limit 1")) {
                if (rs.next()) {
                    return rs.getInt("matricula");
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
        return null;
    }

    @Override
    public void atualizarFotoAlunoApp(String key, UsuarioZW usuarioZW) throws Exception {
        try {
            if(usuarioZW.getUsuarioZW() != null) {
                Usuario usuario = usuarioDao.findById(key, usuarioZW.getUsuarioZW());
                if(usuario != null && usuario.getCodigo() != null) {
                    usuario.setFotoKeyApp(Aplicacao.obterUrlFotoDaNuvem(usuarioZW.getFotoKey()));
                    usuario.setVersaoFotoApp(usuario.getVersaoFotoApp() + 1);
                    usuarioDao.update(key, usuario);
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
        }
    }

    public AlunoAppInfoDTO alunoAppInfo(Integer alunoID, Integer pessoa, Integer cliente) throws ServiceException {
        try {
            if (UteisValidacao.emptyNumber(alunoID) &&
                    UteisValidacao.emptyNumber(pessoa) &&
                    UteisValidacao.emptyNumber(cliente)) {
                throw new ServiceException("Nenhum parametro informado");
            }
            AlunoAppInfoDTO dto = new AlunoAppInfoDTO();
            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT \n");
            sql.append("u.codigo, \n");
            sql.append("(u.idclienteapp IS NOT NULL OR u.dataregistrousoapp IS NOT NULL) as usaapp, \n");
            sql.append("u.idclienteapp, \n");
            sql.append("u.dataregistrousoapp \n");
            sql.append("FROM usuario u \n");
            sql.append("INNER JOIN clientesintetico c ON c.codigo = u.cliente_codigo \n");
            sql.append("WHERE 1 = 1 \n");
            if (!UteisValidacao.emptyNumber(alunoID)) {
                sql.append("AND c.codigo = ").append(alunoID).append(" \n");
            }
            if (!UteisValidacao.emptyNumber(pessoa)) {
                sql.append("AND c.codigoPessoa = ").append(pessoa).append(" \n");
            }
            if (!UteisValidacao.emptyNumber(cliente)) {
                sql.append("AND c.codigoCliente = ").append(cliente).append(" \n");
            }
            try (ResultSet rs = usuarioDao.createStatement(ctx, sql.toString())) {
                if (rs.next()) {
                    dto.setUsaApp(rs.getBoolean("usaapp"));
                    dto.setDataRegistroUsoApp(rs.getTimestamp("dataregistrousoapp"));
                    dto.setIdClienteApp(rs.getString("idclienteapp"));
                }
            }
            return dto;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public void alterarProfessorAluno(Integer matricula, Integer codNovoProfessor,
                                      HttpServletRequest request) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico cs = consultarPorMatricula(ctx, matricula.toString());
            if (cs == null) {
                throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_EXISTE);
            }
            acao(AcaoAlunoEnum.ALTERACAO_PROFESSOR_NIVEL, cs.getMatriculaString());

            IntegracaoCadastrosWSConsumer integracaoWs = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            Usuario usuarioLogado = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());

            if (!UteisValidacao.emptyNumber(codNovoProfessor)) {
                ProfessorSintetico professorSintetico = getProfessorSintetico().consultarPorCodigoColaborador(ctx, codNovoProfessor);
                if (professorSintetico != null && !UteisValidacao.emptyNumber(professorSintetico.getCodigo())) {
                    atualizarProfessorAluno(ctx, integracaoWs, professorSintetico.getCodigo(), cs, usuarioLogado.getUsuarioZW(), request);
                }
            } else {
                atualizarProfessorAluno(ctx, integracaoWs, null, cs, usuarioLogado.getUsuarioZW(), request);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_ALTERAR, e);
        } finally {
            leaveAcao();
        }
    }

    @Override
    public void alterarProfessorAlunos(HttpServletRequest request, JSONObject json) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuarioLogado = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());

            Integer professorId = json.optInt("professorId");
            JSONArray jsonArray = json.optJSONArray("matriculas");

            ProfessorSintetico professorSintetico = null;
            if (!UteisValidacao.emptyNumber(professorId)) {
                    professorSintetico = getProfessorSintetico().consultarPorCodigoColaborador(ctx, professorId);
                if ((professorSintetico == null)
                        || (UteisValidacao.emptyNumber(professorSintetico.getCodigo()))) {
                           throw new ServiceException("Professor não encontrado ou inválido: " + professorId);
                }
            }

            List<Object> rawList = jsonArray.toList();
            List<Integer> matriculas = rawList.stream()
                    .map(obj -> {
                        try {
                            return Integer.parseInt(obj.toString());
                        } catch (NumberFormatException e) {
                            throw new IllegalArgumentException("Matrícula inválida: " + obj);
                        }
                    })
                    .collect(Collectors.toList());

            if (matriculas.isEmpty()) {
                throw new ServiceException("Lista de matrículas está vazia.");
            }

            for (Integer matricula : matriculas) {
                ClienteSintetico cs = consultarPorMatricula(ctx, matricula.toString());
                if (cs == null) {
                    throw new ServiceException("Aluno não encontrado para matrícula: " + matricula);
                }

                if (professorSintetico != null && !UteisValidacao.emptyNumber(professorSintetico.getCodigo())) {
                    atualizarProfessorAluno(ctx, null, professorId, cs, usuarioLogado.getUsuarioZW(), request);
                } else {
                    atualizarProfessorAluno(ctx, null, null, cs, usuarioLogado.getUsuarioZW(), request);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_ALTERAR, e);
        } finally {
            leaveAcao();
        }
    }

    public void alterarNivelAluno(Integer matricula, Integer codNovoNivel) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico cs = consultarPorMatricula(ctx, matricula.toString());
            if (cs == null) {
                throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_NAO_EXISTE);
            }
            acao(AcaoAlunoEnum.ALTERACAO_PROFESSOR_NIVEL, cs.getMatriculaString());

            Integer codNivelAnterior = (cs.getNivelAluno() != null) ? cs.getNivelAluno().getCodigo() : null;
            String nomeNivelAnterior = (cs.getNivelAluno() != null) ? cs.getNivelAluno().getNome() : null;
            Nivel novoNivel = nivelService.obterPorId(ctx, codNovoNivel == null ? 0 : codNovoNivel);
            String nomeNovoNivel = (novoNivel != null) ? novoNivel.getNome() : null;

            atualizarNivelAluno(ctx, codNovoNivel, cs);

            if (!Objects.equals(codNivelAnterior, codNovoNivel)) {
                incluirLog(ctx, cs.getMatriculaString(), "", nomeNivelAnterior, nomeNovoNivel,
                        "ALTERAÇÃO", "ALTERAÇÃO DE NÍVEL DO ALUNO", EntidadeLogEnum.ALUNO, "Nível do Aluno", sessaoService, logDao, null, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(AlunoExcecoes.ERRO_ALUNO_ALTERAR, e);
        } finally {
            leaveAcao();
        }
    }

    public List<Integer> listaAlunosMatricula(FiltroAlunoJSON filtros, Integer empresaZw) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String niveisSelecionados = filtros.getNiveisIds() != null ? niveisSelecionados(filtros.getNiveisIds()).replaceFirst(",", "") : "";
            String statusClienteEnumSelecionado = filtros.getStatusClienteEnum() != null ? statusClienteSelecionado(filtros.getStatusClienteEnum()).replaceFirst(",", "") : "";
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            return getClienteSinteticoDao().consultarMatriculaClientesStatusNivel(ctx,
                    statusClienteEnumSelecionado, niveisSelecionados,
                    usuario.getProfessor().getCodigo(), empresaZw, filtros.getColaboradorZw());
        } catch (Exception ex) {
            throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS, ex);
        }
    }

    @Override
    public String consultaModalidades(String ctx, String matricula) throws Exception {
        String modalidades = "";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try {
                String sql = "SELECT STRING_AGG(m.codigo :: text, '|') AS codigos\n" +
                        "FROM contrato con\n" +
                        "         inner join contratomodalidade cm on cm.contrato = con.codigo\n" +
                        "         INNER JOIN usuario u ON u.codigo = con.responsavelcontrato\n" +
                        "         INNER JOIN modalidade m ON m.codigo = cm.modalidade\n" +
                        "         join pessoa p on con.pessoa = p.codigo\n" +
                        "         join cliente c on p.codigo = c.pessoa\n" +
                        "where c.codigomatricula = " + matricula + " \n" +
                        "  AND con.situacao = 'AT';";
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {
                    if (rs.next()) {
                        modalidades = rs.getString("codigos") != null ? rs.getString("codigos") : "";
                    }
                }
            } catch (Exception e) {
                throw e;
            }
        } catch (Exception e) {
            throw e;
        }
        return modalidades;
    }


    @Override
    public Integer nivelAluno(final Integer codigoCliente) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, "select nivelaluno_codigo from clientesintetico c\n" +
                "where codigoCliente = " + codigoCliente)) {
            return rs.next() ? rs.getInt("nivelaluno_codigo") : 0;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void enviaNotificacaoLembreteAulaEQuinzeMinutosAntesAula(String ctx, ClienteSintetico cliente, String inicio, String titulo, Integer codigoHorarioTurma, String nomeProfessor) {
        try {
            String formato = "dd/MM/yyyy HH:mm";
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formato);

            ZoneId zonaEmpresa = ZoneId.of(empresaService.obterFusoHorarioEmpresa(ctx, cliente.getEmpresa()));
            ZoneId zonaServidorPush = ZoneId.of("America/Sao_Paulo"); // Fuso horário de BSB

            LocalDateTime localDateTimeInicio = LocalDateTime.parse(inicio, formatter);
            ZonedDateTime zonedDateTimeInicioEmpresa = ZonedDateTime.of(localDateTimeInicio, zonaEmpresa);
            ZonedDateTime zonedDateTimeInicioServidorPush = zonedDateTimeInicioEmpresa.withZoneSameInstant(zonaServidorPush);
            ZonedDateTime zonedDateTimeMenos15MinutosServidorPush = zonedDateTimeInicioServidorPush.minusMinutes(15);

            Date dataComFusoMenos15Min = Date.from(zonedDateTimeMenos15MinutosServidorPush.toInstant());
            Date dataOriginalComFuso = Date.from(zonedDateTimeInicioServidorPush.toInstant());

            titulo = UteisValidacao.emptyString(titulo) ? "Bem vindo" : Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(titulo);

            for (Usuario usuarioCliente : cliente.getUsuarios()) {
                String refPush = PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, titulo, "Sua aula começa em 15 minutos", usuarioCliente.getUserName(), Uteis.getDataAplicandoFormatacao(dataComFusoMenos15Min, formato));
                notificacaoAulaAgendadaService.salvaNotificacaoAulaAgendada(ctx, cliente, codigoHorarioTurma, refPush, dataComFusoMenos15Min);

                String refPush2 = PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, "Agendamento iniciou", "Seu agendamento com o " + Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(nomeProfessor) + " iniciou. Vamos lá.", usuarioCliente.getUserName(), Uteis.getDataAplicandoFormatacao(dataOriginalComFuso, formato));
                notificacaoAulaAgendadaService.salvaNotificacaoAulaAgendada(ctx, cliente, codigoHorarioTurma, refPush2, dataOriginalComFuso);
            }
        } catch (Exception e) {
            Uteis.logar(null, "Erro ao enviar notificacao: " + e.getMessage());
        }
    }

    public String dataConvertidaFusoHorarioCliente(String ctx, Date dataSemAjusteFusoHorarioLocal, Integer empresaZW) {
        try {
            ZoneId fusoAluno = ZoneId.of(empresaService.obterFusoHorarioEmpresa(ctx, empresaZW));
            ZonedDateTime zonedDateTimeAluno = dataSemAjusteFusoHorarioLocal.toInstant().atZone(fusoAluno);

            ZoneId fusoBrasilia = ZoneId.of("America/Sao_Paulo");
            ZonedDateTime zonedDateTimeBrasilia = zonedDateTimeAluno.withZoneSameInstant(fusoBrasilia);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
            return zonedDateTimeBrasilia.format(formatter);
        } catch (Exception e) {
            Uteis.logar(null, "Erro ao converter data: " + e.getMessage());
            return null;
        }
    }

    public List<ClienteAcompanhamento> consultarClienteAcompanhamento(final String ctx, Date inicio, Date fim) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            query.append("SELECT obj FROM ClienteAcompanhamento obj INNER JOIN obj.cliente cliente INNER JOIN obj.professor prof ");
            query.append("WHERE obj.inicio between :inicio AND :fim ");
            query.append("ORDER BY obj.codigo DESC ");
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("inicio", Calendario.getDataComHoraZerada(inicio));
            p.put("fim", Calendario.getDataComHora(fim, "23:59:59"));
            return getClienteAcompanhamentoDao().findByParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ClienteAcompanhamentoJSON> consultarClienteAcompanhamentoJSON(final String ctx, Date inicio, Date fim) throws Exception {
        List<ClienteAcompanhamentoJSON> listaResposta = new ArrayList<>();
        List<ClienteAcompanhamento> lista = consultarClienteAcompanhamento(ctx, inicio, fim);
        for (ClienteAcompanhamento obj : lista) {
            try {
                listaResposta.add(new ClienteAcompanhamentoJSON(obj));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return listaResposta;
    }

    @Override
    public PerfilDISCVO salvarPerfilDISC(String ctx, PerfilDISCDTO perfilDISCDTO) throws ServiceException {
        try {
            PerfilDISC perfilDISC = new PerfilDISC();
            ClienteSintetico cliente = consultarSimplesPorMatricula(ctx, perfilDISCDTO.getMatricula());
            if (cliente == null) {
                throw new ServiceException("Cliente não encontrado");
            }
            perfilDISC.setCliente(cliente);
            perfilDISC.setConformidade(perfilDISCDTO.getConformidade());
            perfilDISC.setDominancia(perfilDISCDTO.getDominancia());
            perfilDISC.setInfluencia(perfilDISCDTO.getInfluencia());
            perfilDISC.setEstabilidade(perfilDISCDTO.getEstabilidade());
            perfilDISC.setDataCadastro(new Date(System.currentTimeMillis()));

            return new PerfilDISCVO(perfilDISCDao.insert(ctx, perfilDISC));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<PerfilDISCVO> buscarPerfilDISC(String ctx, Integer matricula) throws ServiceException {
        try {
            ClienteSintetico cliente = consultarSimplesPorMatricula(ctx, matricula);
            if (cliente == null) {
                throw new ServiceException("Cliente não encontrado");
            }
            List<PerfilDISCVO> lista = new ArrayList<>();
            for (PerfilDISC perfilDISC : perfilDISCDao.findByCliente(ctx, cliente)) {
                lista.add(new PerfilDISCVO(perfilDISC));
            }

            return lista;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public PerfilDISCVO atualizarPerfilDISC(String ctx, PerfilDISCDTO perfilDISCDTO, Integer codigo) throws ServiceException {
        try {
            PerfilDISC perfilDISC = perfilDISCDao.findById(ctx, codigo);
            if (perfilDISC == null) {
                throw new ServiceException("Perfil não encontrado");
            }
            perfilDISC.setConformidade(perfilDISCDTO.getConformidade());
            perfilDISC.setDominancia(perfilDISCDTO.getDominancia());
            perfilDISC.setInfluencia(perfilDISCDTO.getInfluencia());
            perfilDISC.setEstabilidade(perfilDISCDTO.getEstabilidade());
            perfilDISC.setDataCadastro(new Date(System.currentTimeMillis()));

             return new PerfilDISCVO(perfilDISCDao.update(ctx, perfilDISC));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<String> obterAlunoGympassPorToken(String ctx, String token) throws Exception {
        List<String> alunos = new ArrayList<>();

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try {
                StringBuilder sql = new StringBuilder();
                sql.append("select c.codigo as codigocliente, c.codigomatricula, c.gympassuniquetoken, p.nome \n");
                sql.append("from cliente c \n");
                sql.append("inner join pessoa p on p.codigo = c.pessoa \n");
                sql.append("where c.gympassuniquetoken ilike '%").append(token).append("%'");

                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                    while (rs.next()) {
                        String retorno = "";
                        retorno += "Nome: " + rs.getString("nome") + " - ";
                        retorno += "Cliente: " + rs.getInt("codigocliente") + " - ";
                        retorno += "Matrícula: " + rs.getInt("codigomatricula") + " - ";
                        retorno += "Token: " + rs.getString("gympassuniquetoken");

                        alunos.add(retorno);
                    }
                }
            } catch (Exception e) {
                Uteis.logar(e, ClienteSinteticoServiceImpl.class);
            }
        }

        return alunos;
    }

    @Override
    public Boolean verificaTotalPass(String ctx, Integer matricula) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT (sw.situacao IN ('VI', 'IN') AND EXISTS(SELECT codigo ");
            sql.append("FROM PeriodoAcessoCliente ");
            sql.append("WHERE pessoa = p.codigo ");
            sql.append("AND tipototalpass = TRUE)) AS totalpass ");
            sql.append("FROM pessoa p ");
            sql.append("INNER JOIN cliente c ON c.pessoa = p.codigo ");
            sql.append("LEFT JOIN situacaoclientesinteticodw sw ON sw.codigopessoa = p.codigo ");
            sql.append("WHERE c.codigomatricula = '").append(matricula).append("';");

            try (Connection con = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                    if (rs.next()) {
                        return rs.getBoolean("totalpass");
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
        }
        return false;
    }

    @Override
    public Boolean verificaGymPass(String ctx, Integer matricula) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT (sw.situacao IN ('VI', 'IN') AND EXISTS(SELECT codigo ");
            sql.append("FROM PeriodoAcessoCliente ");
            sql.append("WHERE pessoa = p.codigo ");
            sql.append("AND tipoAcesso = 'PL' ");
            sql.append("AND tokengympass IS NOT NULL ");
            sql.append("AND tokengympass <> '' ");
            sql.append("AND LENGTH(COALESCE(c.gympassUniqueToken, '')) > 0)) AS gympass ");
            sql.append("FROM pessoa p ");
            sql.append("INNER JOIN cliente c ON c.pessoa = p.codigo ");
            sql.append("LEFT JOIN situacaoclientesinteticodw sw ON sw.codigopessoa = p.codigo ");
            sql.append("WHERE c.codigomatricula = '").append(matricula).append("';");

            try (Connection con = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                    if (rs.next()) {
                        return rs.getBoolean("gympass");
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
        }
        return false;
    }

    @Override
    public RetrospectivaAnoVO obterRetrospectiva(String ctx, Integer ano, Integer matricula, Boolean atualizaCache) throws Exception {
        RetrospectivaAnoVO retrospectivaAnoVO = new RetrospectivaAnoVO();

        RetrospectivaCache retrospectivaCache = retrospectivaCacheDao.findByMatriculaAno(ctx, matricula, ano);

        if (retrospectivaCache != null && !atualizaCache) {
            retrospectivaAnoVO = new ObjectMapper().readValue(retrospectivaCache.getDados(), RetrospectivaAnoVO.class);
            return retrospectivaAnoVO;
        }

        retrospectivaAnoVO = obtemRetrospectivaAtualizada(ctx, ano, matricula);

        if (retrospectivaCache == null) {
            retrospectivaCache = new RetrospectivaCache();
            retrospectivaCache.setMatricula(matricula);
            retrospectivaCache.setAno(ano);
            retrospectivaCache.setDados(new ObjectMapper().writeValueAsString(retrospectivaAnoVO));
            retrospectivaCache.setUltimaAtualizacao(new Date(System.currentTimeMillis()));
            retrospectivaCacheDao.insert(ctx, retrospectivaCache);
        } else {
            retrospectivaCache.setDados(new ObjectMapper().writeValueAsString(retrospectivaAnoVO));
            retrospectivaCache.setUltimaAtualizacao(new Date(System.currentTimeMillis()));
            retrospectivaCacheDao.update(ctx, retrospectivaCache);
        }

            return retrospectivaAnoVO;
    }

    private RetrospectivaAnoVO obtemRetrospectivaAtualizada(String ctx, Integer ano, Integer matricula) throws Exception {
        RetrospectivaAnoVO retrospectivaAnoVO = new RetrospectivaAnoVO();

        String sqlFichaMaisExecutada = "SELECT t.programatreinoficha_codigo, ptf.ficha_codigo, COUNT(t.programatreinoficha_codigo) AS quantidade " +
                "FROM treinorealizado t " +
                "JOIN clientesintetico c ON c.codigo = t.cliente_codigo " +
                "JOIN programatreinoficha ptf ON t.programatreinoficha_codigo = ptf.codigo " +
                "WHERE c.matricula = " + matricula + " " +
                "AND EXTRACT(YEAR FROM t.datainicio) = " + ano + " " +
                "GROUP BY t.programatreinoficha_codigo, ptf.ficha_codigo " +
                "ORDER BY quantidade DESC";


        try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, sqlFichaMaisExecutada)) {
            while (rs.next()) {
                retrospectivaAnoVO.setQuantidadeTreinosNoAno(retrospectivaAnoVO.getQuantidadeTreinosNoAno() + rs.getInt("quantidade"));
                Integer qtdRealizada = rs.getInt("quantidade");

                String sqlAtividadesAgrupadas = "SELECT af.nome AS nome, sum(carga) as carga, COUNT(*) AS quantidade " +
                        "FROM serie s " +
                        "JOIN atividadeficha af ON af.codigo = s.atividadeficha_codigo " +
                        "WHERE af.ficha_codigo = " + rs.getInt(2) + " " +
                        "GROUP BY af.nome, carga " +
                        "ORDER BY quantidade DESC";

                try (ResultSet rsAtividades = clienteSinteticoDao.createStatement(ctx, sqlAtividadesAgrupadas)) {
                    while (rsAtividades.next()) {
                        if (retrospectivaAnoVO.getTop3Atividades().size() < 3) {
                            retrospectivaAnoVO.getTop3Atividades().add(Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(rsAtividades.getString("nome")));
                        }

                        retrospectivaAnoVO.setQuantidadeDePesoLevantado(retrospectivaAnoVO.getQuantidadeDePesoLevantado() + (rsAtividades.getInt("carga") * qtdRealizada));
                        retrospectivaAnoVO.setQuantidadeDeAtividadesConcluidas(retrospectivaAnoVO.getQuantidadeDeAtividadesConcluidas() + qtdRealizada);
                    }
                }
            }
        }

        String sqlAvaliacoesFisicasRealizadasNoAno = "SELECT * FROM avaliacaofisica af " +
                "JOIN clientesintetico c ON c.codigo = af.cliente_codigo " +
                "WHERE c.matricula = " + matricula + " " +
                "AND EXTRACT(YEAR FROM af.dataavaliacao) = " + ano + " " +
                "ORDER BY af.dataavaliacao DESC";

        try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, sqlAvaliacoesFisicasRealizadasNoAno)) {
            Double pesoPrimeiroRegistro = null;
            Double pesoUltimoRegistro = null;

            while (rs.next()) {
                retrospectivaAnoVO.setTotalDeAvaliacoesFisicas(retrospectivaAnoVO.getTotalDeAvaliacoesFisicas() + 1);

                if (pesoPrimeiroRegistro == null) {
                    pesoPrimeiroRegistro = rs.getDouble("peso");
                }

                pesoUltimoRegistro = rs.getDouble("peso");
            }

            if (pesoPrimeiroRegistro != null && pesoUltimoRegistro != null &&
                    retrospectivaAnoVO.getTotalDeAvaliacoesFisicas() > 1) {
                Double diferencaPeso = pesoPrimeiroRegistro - pesoUltimoRegistro;
                retrospectivaAnoVO.setPesoEntreAvaliacoes(diferencaPeso);
            }
        }
        String sqlPeridoTreino = "SELECT " +
                "SUM(CASE WHEN EXTRACT(HOUR FROM datainicio) BETWEEN 6 AND 12 THEN 1 ELSE 0 END)     AS manha, " +
                "SUM(CASE WHEN EXTRACT(HOUR FROM datainicio) BETWEEN 12 AND 18 THEN 1 ELSE 0 END)    AS tarde, " +
                "SUM(CASE WHEN EXTRACT(HOUR FROM datainicio) NOT BETWEEN 6 AND 18 THEN 1 ELSE 0 END) AS noite, " +
                "COUNT(*)                                                                            AS totaltreinos " +
                "FROM treinorealizado " +
                "WHERE EXTRACT(YEAR FROM datainicio) = " + ano + " " +
                "AND cliente_codigo = (SELECT codigo FROM clientesintetico WHERE matricula = " + matricula + ")";

        try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, sqlPeridoTreino)) {
            if (rs.next()) {
                retrospectivaAnoVO.setQtdTreinosManha(rs.getInt("manha"));
                retrospectivaAnoVO.setQtdTreinosTarde(rs.getInt("tarde"));
                retrospectivaAnoVO.setQtdTreinosNoite(rs.getInt("noite"));
            }
        }

        retrospectivaAnoVO.setQuantidadeDeCaloriasQueimadas(retrospectivaAnoVO.getQuantidadeTreinosNoAno() * 300);

        Usuario usuarioBanco = usuarioService.consultarPorMatricula(ctx, matricula);
        if (usuarioBanco != null) {
            String sqlScoreWod = "SELECT * FROM scoretreino WHERE usuario_codigo = " + usuarioBanco.getCodigo() + " AND EXTRACT(YEAR FROM lancamento) = " + ano;
            try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, sqlScoreWod)) {
                while (rs.next()) {
                    retrospectivaAnoVO.setTempoTotalWods(retrospectivaAnoVO.getTempoTotalWods() + rs.getInt("tempo"));
                    retrospectivaAnoVO.setWodsRealizados(retrospectivaAnoVO.getWodsRealizados() + 1);
                    Integer posicao = rs.getInt("posicao");
                    if (posicao == 1) {
                        retrospectivaAnoVO.setPosicaoRanking1lugar(retrospectivaAnoVO.getPosicaoRanking1lugar() + 1);
                    } else if (posicao == 2) {
                        retrospectivaAnoVO.setPosicaoRanking2lugar(retrospectivaAnoVO.getPosicaoRanking2lugar() + 1);
                    } else if (posicao == 3) {
                        retrospectivaAnoVO.setPosicaoRanking3lugar(retrospectivaAnoVO.getPosicaoRanking3lugar() + 1);
                    }
                }
            }
        }

        Date dataIncioAno = Uteis.obterPrimeiroDiaAno(ano);
        Date dataFimAno = Uteis.obterUltimoDiaAno(ano);

        List<AulaAlunoDTO> aulaAlunoDTOS = programaService.consultarAulasAgendadasPorAluno(matricula, Uteis.getDataAplicandoFormatacao(dataIncioAno, "dd/MM/yyyy"),
                Uteis.getDataAplicandoFormatacao(dataFimAno, "dd/MM/yyyy"), ctx, null);
        retrospectivaAnoVO.setTotalDeAulasMarcadas(aulaAlunoDTOS.size());
        retrospectivaAnoVO.setAulaFavorita(obterAulaMaisFrequente(aulaAlunoDTOS));

        return retrospectivaAnoVO;
    }

    public String obterAulaMaisFrequente(List<AulaAlunoDTO> lista) {
        Map<String, Integer> contagemAulas = new HashMap<>();

        for (AulaAlunoDTO aula : lista) {
            String nomeAula = aula.getNomeAula();
            contagemAulas.put(nomeAula, contagemAulas.getOrDefault(nomeAula, 0) + 1);
        }

        String aulaMaisFrequente = null;
        int maxOcorrencias = 0;

        for (Map.Entry<String, Integer> entry : contagemAulas.entrySet()) {
            if (entry.getValue() > maxOcorrencias) {
                maxOcorrencias = entry.getValue();
                aulaMaisFrequente = entry.getKey();
            }
        }

        return aulaMaisFrequente;
    }

    public void atualizarParQClienteZW(String ctx, Integer codigoCliZw, boolean isParQPositivo) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String sql = "UPDATE cliente SET parqpositivo = ? WHERE codigo = ?";
            int i = 1;
            try (PreparedStatement sqlAlterar = conZW.prepareStatement(sql)) {
                sqlAlterar.setBoolean(i++, isParQPositivo);
                sqlAlterar.setInt(i++, codigoCliZw);
                sqlAlterar.execute();
            }
        }
    }

    @Override
    public Integer obterPontosSaldo(String ctx, Integer matricula) {
        try {
            String sqlSaldoPontos = "SELECT * FROM historicopontos hp " +
                    "JOIN cliente c ON c.codigo = hp.cliente " +
                    "WHERE c.codigomatricula = " + matricula;
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlSaldoPontos, conZW)) {
                    if (rs.next()) {
                        return rs.getInt("pontostotal");
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
        }
        return 0;
    }

    @Override
    public List<BrindesVO> obterBrindes(String ctx, Boolean ativo) {
        List<BrindesVO> brindes = new ArrayList<>();
        try {
            String sqlBrindes = "SELECT * FROM brinde";

            if(ativo) {
                sqlBrindes += " WHERE ativo = true";
            } else {
                sqlBrindes += " WHERE ativo = false";
            }

            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlBrindes, conZW)) {
                    while (rs.next()) {
                        BrindesVO brinde = new BrindesVO();
                        brinde.setQuantidadeDePontos(rs.getInt("pontos"));
                        brinde.setNome(rs.getString("nome"));
                        brinde.setAtivo(rs.getBoolean("ativo"));
                        brindes.add(brinde);
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
        }
        return brindes;
    }

    @Override
    public HistoricoPresencasVO historicoPresenca(String ctx, Integer matricula, Integer empresa, Boolean atualizaCache) throws Exception {
        HistoricoPresencasVO historicoPresencasVO = new HistoricoPresencasVO();
        ClienteSintetico clienteSintetico = consultarPorMatricula(ctx, matricula.toString());

        if(clienteSintetico == null){
            throw new ServiceException("Cliente não encontrado");
        }

        //verifica se tem cache
        HistoricoPresenca historicoPresenca = historicoPresencaDao.findByMatricula(ctx, matricula);

        if(historicoPresenca != null){
            LocalDate dataAtualizacao = historicoPresenca.getDataAtualizacao()
                    .toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();

            LocalDate hoje = LocalDate.now();

            if(dataAtualizacao.isEqual(hoje) && !atualizaCache){
                historicoPresencasVO.setTotalAulasRealizadas(historicoPresenca.getTotalAulasRealizadas());
                historicoPresencasVO.setAulasMesAtual(historicoPresenca.getAulasMesAtual());
                historicoPresencasVO.setSemanasConsecutivas(historicoPresenca.getSemanasConsecutivas());
            } else {
                historicoPresencasVO = programaTreinoService.consultarAulasAgendadasPorAlunoTotaisEMesAtualEQtdAulasPorSemanaConsecutiva(matricula, ctx, empresa);
                StringBuilder sql = new StringBuilder();

                sql.append("UPDATE historicopresenca SET totalaulasrealizadas = " + historicoPresencasVO.getTotalAulasRealizadas() + ", aulasmesatual = " + historicoPresencasVO.getAulasMesAtual() + ", semanasconsecutivas = " + historicoPresencasVO.getSemanasConsecutivas() + ", dataatualizacao = current_date WHERE cliente_codigo = " + clienteSintetico.getCodigo());

                historicoPresencaDao.executeNativeSQL(ctx, sql.toString());
            }
        } else {
            historicoPresencasVO = programaTreinoService.consultarAulasAgendadasPorAlunoTotaisEMesAtualEQtdAulasPorSemanaConsecutiva(matricula, ctx, empresa);
            StringBuilder sql = new StringBuilder();

            sql.append("INSERT INTO historicopresenca (cliente_codigo, totalaulasrealizadas, aulasmesatual, semanasconsecutivas, dataatualizacao) VALUES " +
                    "(" + clienteSintetico.getCodigo() + ", " + historicoPresencasVO.getTotalAulasRealizadas() + ", " + historicoPresencasVO.getAulasMesAtual() + ", " + historicoPresencasVO.getSemanasConsecutivas() + ", current_date)");


            historicoPresencaDao.executeNativeSQL(ctx, sql.toString());
        }

        return historicoPresencasVO;
    }

    @Override
    public String obterSituacaoClienteZW(String ctx, Integer matricula) {
        String situacao = "";
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                StringBuilder sql = new StringBuilder();
                sql.append("select c.situacao from cliente c \n");
                sql.append("where c.codigomatricula = ").append(matricula);
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                    if (rs.next()) {
                        situacao = rs.getString("situacao");
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao tentar obter a situação do aluno no banco do zw: " + ex.getMessage());
        }
        return situacao;
    }

    @Override
    public String validarSincronizacaoAlunoZwTr(String ctx, Integer codigoClienteZw) throws Exception {
        JSONObject dadosClienteZW = null;
        JSONObject dadosClienteTR = null;

        // Validar parâmetro de entrada
        if (codigoClienteZw == null) {
            throw new ServiceException("codigoClienteZw não pode ser nula");
        }

        boolean alunoExisteBancoZW = false;
        // Primeira consulta - obter dados do cliente no banco ZW usando PreparedStatement
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String sqlZW = "select c.codigo, c.codigomatricula, c.situacao, v.tipovinculo, v.colaborador, c.empresa " +
                          "from cliente c " +
                          "left join vinculo v on v.cliente = c.codigo and v.tipovinculo = 'TW' " +
                          "where c.codigo = ? " +
                          "order by v.codigo desc " +
                          "limit 1";

            try (PreparedStatement psZW = conZW.prepareStatement(sqlZW)) {
                psZW.setInt(1, codigoClienteZw);
                try (ResultSet rs = psZW.executeQuery()) {
                    if (rs.next()) {
                        dadosClienteZW = new JSONObject();
                        dadosClienteZW.put("codigo", rs.getInt("codigo"));
                        dadosClienteZW.put("codigomatricula", rs.getInt("codigomatricula"));
                        dadosClienteZW.put("situacao", rs.getString("situacao"));
                        dadosClienteZW.put("tipovinculo", rs.getString("tipovinculo"));
                        dadosClienteZW.put("codigoColaborador", rs.getObject("colaborador"));
                        dadosClienteZW.put("empresaZW", rs.getObject("empresa"));
                        alunoExisteBancoZW = true;
                    } else {
                        throw new ServiceException("Cliente não encontrado no banco ZW para codigoClienteZw: " + codigoClienteZw);
                    }
                }
            }
        }

        if (!alunoExisteBancoZW) {
            throw new ServiceException("O aluno não foi localizado no banco do zw, o processo de sincronização será encerrado.");
        }

        // Segunda consulta - obter dados do cliente no banco sintético usando SQL nativo
        String sqlSintetico = "select c.codigo, c.codigocliente, c.matricula, c.situacao, c.professorsintetico_codigo, p.codigocolaborador " +
                              "from clientesintetico c " +
                              "left join professorsintetico p on p.codigo = c.professorsintetico_codigo " +
                              "where c.codigocliente = ?";

        boolean alunoExisteBancoTR = false;
        try (Connection conn = getClienteSinteticoDao().getConnection(ctx);
             PreparedStatement ps = conn.prepareStatement(sqlSintetico)) {

            ps.setInt(1, codigoClienteZw);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    alunoExisteBancoTR = true;
                    dadosClienteTR = new JSONObject();
                    dadosClienteTR.put("codigo", rs.getInt("codigo"));
                    dadosClienteTR.put("codigocliente", rs.getInt("codigocliente"));
                    dadosClienteTR.put("matricula", rs.getInt("matricula"));
                    dadosClienteTR.put("situacao", rs.getString("situacao"));
                    dadosClienteTR.put("professorsintetico_codigo", rs.getObject("professorsintetico_codigo"));
                    dadosClienteTR.put("codigocolaborador", rs.getObject("codigocolaborador"));
                }
            }
        }

        // se aluno não existe no banco do treino ele precisa ser importado
        if (!alunoExisteBancoTR) {
            addAlunoV2(
                    ctx,
                    dadosClienteZW.getInt("codigomatricula"),
                    dadosClienteZW.getInt("codigo"),
                    dadosClienteZW.has("codigoColaborador") && !dadosClienteZW.isNull("codigoColaborador")
                            ? dadosClienteZW.getInt("codigoColaborador")
                            : null,
                    false,
                    0,
                    dadosClienteZW.has("empresaZW") && !dadosClienteZW.isNull("empresaZW")
                            ? dadosClienteZW.getInt("empresaZW")
                            : null,
                    false
            );
            return "O cadastro de aluno não existia no banco do treino, e foi importado com sucesso.";
        }

        // Comparar os dados e verificar se o aluno está desatualizado
        boolean alunoDesatualizado = false;
        boolean situacaoDesatualizada = false;
        boolean vinculoDesatualizado = false;

        // Comparar situação
        String situacaoZW = dadosClienteZW.optString("situacao", "");
        String situacaoSintetico = dadosClienteTR.optString("situacao", "");
        if (!situacaoZW.equals(situacaoSintetico)) {
            situacaoDesatualizada = true;
        }

        // Comparar professor/colaborador
        Object colaboradorZW = dadosClienteZW.opt("codigoColaborador");
        Object colaboradorSintetico = dadosClienteTR.opt("codigocolaborador");

        // Tratar valores nulos para comparação
        String colaboradorZWStr = colaboradorZW != null ? colaboradorZW.toString() : "";
        String colaboradorSinteticoStr = colaboradorSintetico != null ? colaboradorSintetico.toString() : "";

        if (!colaboradorZWStr.equals(colaboradorSinteticoStr)) {
            vinculoDesatualizado = true;
        }

        String tipoAtualizacao = "";

        if (situacaoDesatualizada && vinculoDesatualizado) {
            tipoAtualizacao = "AMBOS";
            alunoDesatualizado = true;
        } else if (situacaoDesatualizada) {
            tipoAtualizacao = "SITUACAO";
            alunoDesatualizado = true;
        } else if (vinculoDesatualizado) {
            tipoAtualizacao = "VINCULO_PROFESSOR";
            alunoDesatualizado = true;
        }

        if (alunoDesatualizado) {
            sincronizarAlunoZwTr(ctx, dadosClienteTR.getInt("codigo"), (Integer) colaboradorZW, dadosClienteZW.getString("situacao"), tipoAtualizacao);
            return "O cadastro de aluno estava desatualizado, atualização realizada com sucesso.";
        } else {
            return "O cadastro de aluno já estava atualizado";
        }
    }

    private void sincronizarAlunoZwTr(String ctx, Integer codigoClienteSintetico, Integer codColaborador, String situacaoAluno, String tipoAtualizacao) throws Exception {
        // Validar parâmetros de entrada
        if (codigoClienteSintetico == null) {
            throw new ServiceException("Código do cliente sintético não pode ser nulo");
        }
        if (UteisValidacao.emptyString(tipoAtualizacao)) {
            throw new ServiceException("Tipo de atualização não pode ser vazio");
        }
        
        Integer codProfessorTW = null;

        // Buscar o código do professor sintético baseado no código do colaborador
        if (tipoAtualizacao.equalsIgnoreCase("VINCULO_PROFESSOR") || tipoAtualizacao.equalsIgnoreCase("AMBOS")) {
            if (codColaborador != null) {
                // Buscar professor quando há código do colaborador
                String sqlBuscarProfessor = "SELECT codigo FROM professorsintetico WHERE codigocolaborador = ?";

                try (Connection conn = getClienteSinteticoDao().getConnection(ctx);
                     PreparedStatement psBuscar = conn.prepareStatement(sqlBuscarProfessor)) {

                    psBuscar.setInt(1, codColaborador);
                    try (ResultSet rsProfessor = psBuscar.executeQuery()) {
                        if (rsProfessor.next()) {
                            codProfessorTW = rsProfessor.getInt("codigo");
                        } else {
                            throw new ServiceException("Professor sintético não encontrado para o código do colaborador: " + codColaborador);
                        }
                    }
                }
            } else {
                // codColaborador é null - aluno perdeu vínculo com professor, setar null
                codProfessorTW = null;
            }
        }

        try (Connection conn = getClienteSinteticoDao().getConnection(ctx)) {
            String sql = "";
            PreparedStatement ps = null;

            switch (tipoAtualizacao.toUpperCase()) {
                case "SITUACAO":
                    if (UteisValidacao.emptyString(situacaoAluno)) {
                        throw new ServiceException("Situação do aluno não pode ser vazia para atualização de situação");
                    }
                    sql = "UPDATE clientesintetico SET situacao = ? WHERE codigo = ?";
                    ps = conn.prepareStatement(sql);
                    ps.setString(1, situacaoAluno);
                    ps.setInt(2, codigoClienteSintetico);
                    break;

                case "VINCULO_PROFESSOR":
                    sql = "UPDATE clientesintetico SET professorsintetico_codigo = ? WHERE codigo = ?";
                    ps = conn.prepareStatement(sql);
                    if (codProfessorTW != null) {
                        ps.setInt(1, codProfessorTW);
                    } else {
                        ps.setNull(1, java.sql.Types.INTEGER);
                    }
                    ps.setInt(2, codigoClienteSintetico);
                    break;

                case "AMBOS":
                    if (UteisValidacao.emptyString(situacaoAluno)) {
                        throw new ServiceException("Situação do aluno não pode ser vazia para atualização completa");
                    }
                    sql = "UPDATE clientesintetico SET professorsintetico_codigo = ?, situacao = ? WHERE codigo = ?";
                    ps = conn.prepareStatement(sql);
                    if (codProfessorTW != null) {
                        ps.setInt(1, codProfessorTW);
                    } else {
                        ps.setNull(1, java.sql.Types.INTEGER);
                    }
                    ps.setString(2, situacaoAluno);
                    ps.setInt(3, codigoClienteSintetico);
                    break;
            }

            int rowsAffected = ps.executeUpdate();
            if (rowsAffected == 0) {
                throw new ServiceException("Nenhum registro foi atualizado. Verifique se o código do cliente existe: " + codigoClienteSintetico);
            }

        } catch (ServiceException e) {
            throw e; // Re-lançar ServiceException sem alterar
        } catch (Exception e) {
            Uteis.logar(e, ClienteSinteticoServiceImpl.class);
            throw new ServiceException("Erro ao atualizar aluno: " + e.getMessage(), e);
        }
    }

    @Override
    public String consultarUsuarioMovelPorClienteBdZw(String ctx, Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select nome from usuariomovel where cliente = ?");

        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             PreparedStatement ps = conZW.prepareStatement(sql.toString())) {

            ps.setInt(1, codigoCliente);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("nome");
                }
            }
        }
        return null;
    }

    @Override
    public String consultarEmailPorClienteBdZw(String ctx, Integer codigoCliente) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select email from email e ");
            sql.append("left join cliente c on e.pessoa  = c.pessoa ");
            sql.append("where c.codigo = ? ");
            sql.append("limit 1");

            try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                 PreparedStatement ps = conZW.prepareStatement(sql.toString())) {

                ps.setInt(1, codigoCliente);
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        return rs.getString("email");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao tentar obter o email do aluno no banco do zw: " + e.getMessage());
        }

        return "";
    }

    @Override
    public Integer buscarAcompanhamentoPorClienteEDia(String ctx, Integer codigoCliente, String dia) throws ServiceException {
        try {
            Date dataInicio = Calendario.getDate("yyyy-MM-dd", dia);
            Date dataFim = Calendario.getDataComHora(dataInicio, "23:59:59");
            dataInicio = Calendario.getDataComHoraZerada(dataInicio);

            StringBuilder hql = new StringBuilder();
            hql.append("SELECT obj FROM ClienteAcompanhamento obj ")
                    .append("WHERE obj.cliente.codigo = :codigoCliente ")
                    .append("AND obj.inicio BETWEEN :dataInicio AND :dataFim ")
                    .append("ORDER BY obj.inicio DESC");

            Map<String, Object> params = new HashMap<>();
            params.put("codigoCliente", codigoCliente);
            params.put("dataInicio", dataInicio);
            params.put("dataFim", dataFim);

            List<ClienteAcompanhamento> resultados = getClienteAcompanhamentoDao().findByParam(ctx, hql.toString(), params, 1);

            if (resultados.isEmpty()) {
                return null;
            }

            return resultados.get(0).getCodigo();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.bi.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gestao.ItemGraficoTO;
import br.com.pacto.bean.gestao.ItemGrupoIndicadores;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.HistoricoRevisaoProgramaTreino;
import br.com.pacto.bean.programa.OrigemExecucaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import br.com.pacto.controller.json.gestao.AvaliacaoAcompanhamentoDTO;
import br.com.pacto.controller.json.gestao.AvaliacaoTreinoDTO;
import br.com.pacto.controller.json.gestao.PeriodoDiaDTO;
import br.com.pacto.controller.json.gestao.TreinoRealizadoAppDTO;
import br.com.pacto.controller.json.gestao.TreinosExecutadosEAcessosPorDiaVO;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoSistemaDao;
import br.com.pacto.dao.intf.dashboardbi.*;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.dao.intf.grafico.ItemGrupoIndicadoresDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.programa.HistoricoRevisaoProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.serie.TreinoRealizadoDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.treinoEmCasa.ReplicadorTreinoEmCasa;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.gestao.BiAppService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.gestao.RankingProfessoresService;
import br.com.pacto.service.intf.processo.SincronizacaoEmpresaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.ProcessamentoParalelo;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.impl.Ordenacao;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import servicos.integracao.zw.json.ClienteBIJSON;
import servicos.integracao.zw.json.ModalidadeJSON;

import java.math.BigInteger;
import java.sql.Connection;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.List;

import static br.com.pacto.bean.bi.IndicadorDashboardEnum.VENCIDOS;

/**
 *
 * <AUTHOR>
 */
@Service
public class DashboardServiceImpl implements DashboardBIService {

    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    private DashboardBIDao dashboardDao;
    @Autowired
    private TreinoRealizadoDao treinoRealizadoDao;
    @Autowired
    private AcessosExecucoesBIDao acessosExecucoesBIDao;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    private DiasSemanaDashboardBIDao diasSemanaDao;
    @Autowired
    private ProgramaTreinoDao programaTreinoDao;
    @Autowired
    private AgendamentoDao agendamentoDao;
    @Autowired
    private ProfessorSinteticoDao professorDao;
    @Autowired
    private ProfessorSinteticoService ps;
    @Autowired
    private ClienteDashBIDao clienteDashDao;
    @Autowired
    private ConfiguracaoSistemaService configSistemaService;
    @Autowired
    private ProgramaTreinoService programaService;
    @Autowired
    private TipoEventoDisponibilidadeBIDao tEDao;
    @Autowired
    private AcessosExecucoesBIDao acessosDao;
    @Autowired
    private HistoricoRevisaoProgramaTreinoDao historicoRevisaoDao;
    @Autowired
    private ItemGrupoIndicadoresDao itemGrupoDao;
    @Autowired
    private ConfiguracaoRankingProfessoresDao cfgRankingDao;
    @Autowired
    private IndicadorRankingDao indicadorRankingDao;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private ConfiguracaoSistemaDao configuracaoSistemaDao;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private BiAppService biAppService;
    @Autowired
    private SincronizacaoEmpresaService sincronizacaoEmpresaService;
    @Autowired
    private RankingProfessoresService rankingProfessoresService;
    @Autowired
    private RankingProfessoresDao rankingProfessoresDao;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;

    Integer maxResults = 50;
    Integer indiceInicial = 0;


    @Override
    public void processarBIProfessores(String chave, Date data) throws ServiceException {
        try{
            List<Empresa> empresas = empresaDao.findAll(chave);
            ConfiguracaoSistema cfgDias = configSistemaService.consultarPorTipo(chave, ConfiguracoesEnum.PERIODO_USADO_BI);
            processarParaleloBIProfessoresEmpresas(chave, empresas, cfgDias, data);
        }catch(ServiceException s){
            throw  s;
        } catch(Exception e){
            throw  new ServiceException(e);
        }

    }

    private ConfiguracaoSistemaDao getConfiguracaoSistemaDao() {
        return this.configuracaoSistemaDao;
    }

    public DashboardBIService getService() {
        return UtilContext.getBean(DashboardBIService.class);
    }

    /**
     *
     * Realiza o processamento paralelo de todos os professores das empresas informadas {@link Empresa}
     * @param empresas
     * @param cfgDias
     * @throws Exception
     */
    private void processarParaleloBIProfessoresEmpresas(String chave, List<Empresa> empresas, ConfiguracaoSistema cfgDias, Date data) throws  Exception{
        FiltrosDashboard filtros = new FiltrosDashboard();
        filtros.setDiasParaFrente(cfgDias.getValorAsInteger());
        filtros.setDiasParaTras(cfgDias.getValorAsInteger());
        ProcessamentoParalelo processamentoParalelo = new ProcessamentoParalelo();
        final List<String> erros = new ArrayList<String>();
        for (Empresa empresa : empresas){
            List<ProfessorSintetico> professores = ps.consultarProfessores(chave, empresa.getCodZW());
            for(ProfessorSintetico professor : professores){

                Calendar apartir = Calendario.getInstance(data);
                Calendar database = Calendario.getInstance(data);

                do{// verifica se bi ja foi gerando para o dia.
                    if(!existeBI(chave, apartir.get(Calendar.DAY_OF_MONTH), apartir.get(Calendar.MONTH), apartir.get(Calendar.YEAR), professor.getCodigo(), empresa.getCodigo())) {
                        processamentoParalelo.adicionarExecutavel(criarProcessadorProfessor(chave, filtros, apartir.getTime(), empresa.getCodZW(), professor.getCodigo(), erros));
                    }
                    apartir.add(Calendar.DAY_OF_MONTH, -1);
                }while (apartir.get(Calendar.MONTH) == database.get(Calendar.MONTH));

            }
            //PROCESSA TAMBÉM O RESULTADO DE TODOS O PROFESSORES
            processamentoParalelo.adicionarExecutavel(criarProcessadorProfessor(chave, filtros, data, empresa.getCodZW(), 0, erros));
        }
        processamentoParalelo.executar();

        if(!erros.isEmpty()){
            throw  new ServiceException(erros.toString());
        }
    }

    /**
     * Cria um {@link Runnable} para o processamento do BI de um determinado professor
     * @param chave
     * @param filtros
     * @param data
     * @return
     */
    private Runnable criarProcessadorProfessor(String chave, FiltrosDashboard filtros, final Date data, Integer empresaId, Integer professor, final List<String> erros){
        final String chaveUsar = chave;
        final Integer codigoProfessor = professor;
        final int dia = Uteis.getDiaMesData(data), mes = Uteis.getMesData(data), ano = Uteis.getAnoData(data);
        final FiltrosDashboard filtro = filtros.clone();
        filtro.setEmpresa(empresaId);

        return () -> {
            int qtdVezesTentouExecutar = 0;
            boolean processou = false;
            while(qtdVezesTentouExecutar < 3 && !processou) {
                try {
                    System.out.println("Iniciado processamento BI professor " + codigoProfessor + " do dia "+ Calendario.getData(data, "dd/MM/yyyy") +" às " + Calendario.getData("dd/MM/yyyy HH:mm:ss"));
                    processarGestao(chaveUsar, codigoProfessor, data, filtro, true, false, empresaId);
                    System.out.println("Finalizado processamento BI professor " + codigoProfessor  + " do dia "+ Calendario.getData(data, "dd/MM/yyyy") +" às " + Calendario.getData("dd/MM/yyyy HH:mm:ss"));
                    processou = true;
                } catch (ServiceException e) {
                    qtdVezesTentouExecutar++;
                    if(qtdVezesTentouExecutar == 2){
                        e.printStackTrace();
                    }else{
                        try{
                            Thread.sleep(500);
                        }catch (Exception t){

                        }

                    }
                }
            }
            if(!processou){
                erros.add("Falha ao executar professor : " + codigoProfessor + " chave : " + chaveUsar + " data " + dia + "/" + mes + "/" + ano);
            }
        };
    }

    public Date getDataAtual(Empresa empresa) {
        Date atual = Calendario.hoje();
        try {
            String timeZoneDefault = empresa.getTimeZoneDefault();
            atual = Calendario.hoje(timeZoneDefault);
        }catch (Exception e){
            Uteis.logar(e, DashboardServiceImpl.class);
        }
        return atual;
    }

    @Override
    public DashboardBI processarGestao(final String key, final Integer professor, final Date dataProcessamento,
            final FiltrosDashboard filtros, final boolean processarDadosZW, boolean treinoIndependente, int codEmpresa) throws ServiceException {
        Map<String, Object> guardarDeletarDia;
        Map<String, Object> guardarRankingProfessores;

        try {
            Empresa empresa = empresaDao.findById(key, codEmpresa);
            //pesquisando o numero de dias da pesquisa do BI de acordo com a configuração do sistema.
            ConfiguracaoSistema configuracaoSistema = configuracaoSistemaService.consultarPorTipo(key, ConfiguracoesEnum.PERIODO_USADO_BI);
            int diasPesquisa = configuracaoSistema.getValorAsInteger();
            filtros.setDiasParaFrente(diasPesquisa > 0 ? diasPesquisa : 30);
            filtros.setDiasParaTras(diasPesquisa > 0 ? diasPesquisa : 30);
            Integer dia = Uteis.getDiaMesData(dataProcessamento);
            Integer mes = Uteis.getMesData(dataProcessamento);
            Integer ano = Uteis.getAnoData(dataProcessamento);
            guardarDeletarDia = obterDeletarDia(key, dia, mes, ano, professor, filtros,dataProcessamento);
            guardarRankingProfessores = rankingProfessoresService.obterDeletarDia(key, mes, ano, professor, filtros, dataProcessamento);
            deletarDiasGuardados(key, guardarDeletarDia);
            rankingProfessoresService.deletarDiasGuardados(key, guardarRankingProfessores);
            DashboardBI dash = new DashboardBI();
            dash.setInicioProcessamento(getDataAtual(empresa));
            dash.setCodigoProfessor(professor);
            dash.setEmpresa(filtros.getEmpresa());
            dash.setAno(ano);
            dash.setMes(mes);
            dash.setDia(dia);
            dash.setDiasConfiguracao(filtros.getDiasParaFrente());
            Date fim = getDataAtual(empresa);
            String hash = Aplicacao.processarTreinosRealizados + empresa
                    + (UteisValidacao.emptyNumber(professor) ? 0 : professor);
            if (!"true".equalsIgnoreCase(Aplicacao.getProp(key, hash))) {
                try {
                    Aplicacao.putProp(key, hash, Boolean.TRUE.toString());
                    processarTreinosRealizados(key, professor, filtros, dash, dataProcessamento, processarDadosZW);
                } catch (Exception e) {
                    Uteis.logar(e, this.getClass());
                } finally {
                    Aplicacao.putProp(key, hash, Boolean.FALSE.toString());
                }
            }

            if(!treinoIndependente) {
                processarCancelados(dash, mes, ano, professor, codEmpresa,key);
            }
            processarAlunos(key, professor, filtros, dash, treinoIndependente, dataProcessamento);
            processarAlunosTreinos(key, professor, filtros, dash, true);
            processarAlunosTreinos(key, professor, filtros, dash, false);
            processarProgramasTreinos(key, professor, filtros, dash, processarDadosZW);
            processarTreinosEmDia(key, professor, filtros, dash, true);
            processarTreinosEmDia(key, professor, filtros, dash, false);

            Date dataInicioPesquisa = Uteis.somarDias(fim, -(filtros.getDiasParaTras()));
            Date dataFimPesquisa = Calendario.getDataComHora(fim, "23:59");


            biAppService.processarDashAlunosComApp(key, codEmpresa, dash, professor, UteisValidacao.emptyNumber(professor), true);
            processarAgenda(key, professor, dash, dataInicioPesquisa, dataFimPesquisa, codEmpresa, dataProcessamento);
            processarProfessor(key, professor, filtros, dash, fim, treinoIndependente, null, null);
            dash.setPercentualEmDia(((dash.getTotalTreinosEmdia()) + dash.getTotalTreinosVencidos()) == 0
                    ? 0
                    : (dash.getTotalTreinosEmdia() * 100) / (dash.getTotalTreinosEmdia() + dash.getTotalTreinosVencidos()));
            obterTempoMedioPermanenciaNoTreino(key, professor, filtros, dash, fim);
            if (processarDadosZW) {
                processarDadosZW(key, dash, professor, filtros, dataProcessamento);
            }
            processarAvaliacoesFisicas(key, professor, filtros, dash, true, treinoIndependente, null, null);
            processarAvaliacoesFisicas(key, professor, filtros, dash, false, treinoIndependente, null, null);
            dash.setPercentualAvaliacoes(dash.getTotalAlunosAtivos() == 0
                    ? 0
                    : (dash.getTotalAlunosAvaliacoes() * 100) / dash.getTotalAlunosAtivos());
            dash.setFimProcessamento(getDataAtual(empresa));
            deletarDiasGuardados(key, guardarDeletarDia);
            return dashboardDao.insert(key, dash);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private JSONArray chamadaZW(String ctx,
                                String endpoint,  Integer empresa, Integer mes, Integer ano, Integer professor, Long data) throws Exception{
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        if(mes != null){
            params.add(new BasicNameValuePair("mes", mes.toString()));
        }
        if(ano != null){
            params.add(new BasicNameValuePair("ano", ano.toString()));
        }
        if(data != null){
            params.add(new BasicNameValuePair("data", data.toString()));
        }

        params.add(new BasicNameValuePair("professor", professor.toString()));

        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONArray(body);
    }

    public List<Integer> obterCancelados(String chave, Integer empresa, Integer mes, Integer ano, Integer professor) throws Exception {
        List<Integer> retorno =  new ArrayList<>();
        String modo = Aplicacao.getProp(Aplicacao.modoConsultaDashBI);
        if (modo.equals("consultaBD")) {
            obterCanceladosBDZW(chave, empresa, mes, ano, professor);
        } else {
            JSONArray ocupacao = chamadaZW(chave,
                    "/prest/rankingprofessores/listar-todos",
                    empresa, mes, ano, professor, null);
            JSONObject objects = new JSONObject();
            if(ocupacao.length()>0){
                for(int i=0;i<ocupacao.length();i++){
                    objects = ocupacao.optJSONObject(i);
                    retorno.add(objects.getInt("codigoContrato"));
                }
            }
        }
        return retorno ;
    }

    private List<Integer> obterCanceladosBDZW(String chave, Integer empresa, Integer mes, Integer ano, Integer professor) {
        try {
            DashboardBITreinoModoBDServiceImpl dashboardBITreinoModoBDService;
            try (Connection conZW = conexaoZWService.conexaoZw(chave)) {
                dashboardBITreinoModoBDService = new DashboardBITreinoModoBDServiceImpl(conZW, chave);
                return dashboardBITreinoModoBDService.consultarAlunosCancelados(
                        chave, empresa, mes, ano, professor
                );
            }
        } catch (Exception e) {
            Uteis.logar(e, DashboardServiceImpl.class);
            return new ArrayList<>();
        }
    }

    private void processarCancelados(DashboardBI dash,Integer mes, Integer ano, Integer professor, Integer empresa, String key)throws Exception {
        List<Integer> retorno = new ArrayList<>();
        ProfessorSintetico professorSintetico = ps.consultarPorCodigo(key, professor, empresa, true);

        if (professorSintetico != null && !UteisValidacao.emptyNumber(professorSintetico.getCodigo())) {
            retorno = (obterCancelados(key, empresa, mes, ano, professorSintetico.getCodigoColaborador()));
        }

        dash.setTotalAlunosCancelados(retorno.size());
        inserirClientesDash(key, retorno, dash, IndicadorDashboardEnum.ALUNOS_CANCELADOS);
    }

    @Override
    public List<ClienteSintetico> consultarClientesRenovaramOuNao(final String key, DashboardBI dash, final Integer professor,
            FiltrosDashboard filtros, Boolean naoRenovou) throws Exception {
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        List<ClienteBIJSON> clientesAVencerBI = integracaoWS.consultarClientesRenovaramOuNao(url, key, professor, filtros, naoRenovou);
        return clienteFromJSON(clientesAVencerBI);
    }

    private List<ClienteSintetico> clienteFromJSON(List<ClienteBIJSON> clientesAVencerBI) throws Exception {
        List<ClienteSintetico> clientes = new ArrayList<ClienteSintetico>();
        for (ClienteBIJSON cli : clientesAVencerBI) {
            ClienteSintetico cs = new ClienteSintetico();
            cs.setMatricula(cli.getMatricula());
            cs.setNome(cli.getNome());
            cs.setCodigoPessoa(cli.getCodigoPessoa());
            cs.setProfessorSintetico(new ProfessorSintetico(cli.getNomeProfessor()));
            try {
                cs.setDataVigenciaAteAjustada(Uteis.getDate(cli.getVigenciaate()));
            } catch (Exception e) {
            }
            clientes.add(cs);
        }
        return clientes;
    }

    private void processarDadosZW(final String key, DashboardBI dash, final Integer professor, FiltrosDashboard filtros, Date dataProcessamento) throws Exception {
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        Integer codColaborador = 0;
        if (professor != 0){
            codColaborador = professorDao.obterPorId(key,professor).getCodigoColaborador();
        }

        DadosBITreinoJSON dadosZW;
        String modo = Aplicacao.getProp(Aplicacao.modoConsultaDashBI);
        if (modo.equals("consultaBD")) {
            dadosZW = obterDadosBIModoBD(key, codColaborador, filtros, dataProcessamento);
        } else {
            dadosZW = integracaoWS.obterDadosBI(url, key, codColaborador, filtros, dataProcessamento);
        }

        dash.setTotalRenovacoesCarteira(dadosZW.getRenovados());
        dash.setTotalNaoRenovaramCarteira(dadosZW.getNaoRenovados());
        dash.setTotalNovosCarteira(dadosZW.getNovosNaCarteira());
        dash.setTotalTrocaramCarteira(dadosZW.getTrocasCarteira());
        dash.setTotalAlunosAvencer(dadosZW.getaVencer());
        obterNrForaTreino(key, dash, dadosZW);
        dash.setMaiorPermanencia(dadosZW.getMaiorPermanencia());
        dash.setMenorPermanencia(dadosZW.getMenorPermanencia());
        dash.setMatriculaClienteMaiorPermanencia(dadosZW.getMatriculaClienteMaiorPermanencia());
        dash.setMatriculaClienteMenorPermanencia(dadosZW.getMatriculaClienteMenorPermanencia());
        dash.setNomeClienteMaiorPermanencia(dadosZW.getNomeClienteMaiorPermanencia());
        dash.setNomeClienteMenorPermanencia(dadosZW.getNomeClienteMenorPermanencia());
        dash.setTempoMedioPermanenciaCarteira(dadosZW.getMediaPermanenciaCarteira());
        dash.setTotalNovosCarteiraNovos(dadosZW.getNovosNaCarteiraNovos());
        dash.setTotalNovosCarteiraTrocaram(dadosZW.getNovosNaCarteiraTrocaram());
        dash.setPercentualRenovacoes((dash.getTotalRenovacoesCarteira() + dash.getTotalNaoRenovaramCarteira()) == 0
                ? 0
                : (dash.getTotalRenovacoesCarteira() * 100) / (dash.getTotalRenovacoesCarteira() + dash.getTotalNaoRenovaramCarteira()));
        dash.setTotalAlunosAcessaram(dadosZW.getAlunosAcessaram());

        gravarClientes(key, dadosZW, dash);
    }

    private DadosBITreinoJSON obterDadosBIModoBD(final String key, final Integer professor,
                                                 FiltrosDashboard filtrosDashboard, Date dataProcessamento) throws Exception {
        try {
            Date inicioAVencer = dataProcessamento;
            Date fimAvencer = Uteis.somarDias(dataProcessamento, filtrosDashboard.getDiasParaFrente());
            Date fim = dataProcessamento;
            Date inicio = Uteis.somarDias(dataProcessamento, -filtrosDashboard.getDiasParaTras());
            DashboardBITreinoModoBDServiceImpl dashboardBITreinoModoBDService;
            try (Connection conZW = conexaoZWService.conexaoZw(key)) {
                dashboardBITreinoModoBDService = new DashboardBITreinoModoBDServiceImpl(conZW, key);
                String dadosBITreino = dashboardBITreinoModoBDService.consultarDadosBITreino(
                        key,
                        Uteis.getData(inicio) ,
                        Uteis.getData(fim),
                        Uteis.getData(inicioAVencer),
                        Uteis.getData(fimAvencer),
                        professor,
                        filtrosDashboard.getEmpresa());
                return JSONMapper.getObject(new JSONObject(dadosBITreino), DadosBITreinoJSON.class);
            }
        } catch (Exception e) {
            Uteis.logar(e, IntegracaoCadastrosWSConsumer.class);
            return new DadosBITreinoJSON();
        }
    }

    private List<AcessosExecucoesJSON> obterAcessosZW(final String key, final Integer professor,
                                             FiltrosDashboard filtrosDashboard, Date dataProcessamento) throws Exception {
        try {
            DashboardBITreinoModoBDServiceImpl dashboardBITreinoModoBDService;
            try (Connection conZW = conexaoZWService.conexaoZw(key)) {
                dashboardBITreinoModoBDService = new DashboardBITreinoModoBDServiceImpl(conZW, key);
                return dashboardBITreinoModoBDService.consultarAcessosZW(
                        key, professor, filtrosDashboard, dataProcessamento
                );
            }
        } catch (Exception e) {
            Uteis.logar(e, DashboardServiceImpl.class);
            return new ArrayList<>();
        }
    }

    public void obterNrForaTreino(String key, DashboardBI dash,DadosBITreinoJSON dadosZW) throws Exception {
        if(UteisValidacao.emptyString(dadosZW.getCodsAlunosAtivosForaTreino())){
            return;
        }
        String sql = "SELECT count(codigo) from ClienteSintetico WHERE codigocliente in ("+dadosZW.getCodsAlunosAtivosForaTreino()+")";
        List<BigInteger> todos = clienteSinteticoDao.listOfObjects(key, sql);
        Integer estaoTreino = todos == null || todos.isEmpty() ? 0 : todos.get(0).intValue();
        dash.setTotalAlunosAtivosForaTreino(dadosZW.getAlunosAtivosForaTreino() - estaoTreino);
    }

    public void gravarClientes(final String ctx, DadosBITreinoJSON dadosZW, DashboardBI dash) throws Exception {
        for (IndicadorDashboardEnum ind : IndicadorDashboardEnum.values()) {
            ClienteDashBI clientes = new ClienteDashBI();
            clientes.setIndicador(ind);
            clientes.setAno(dash.getAno());
            clientes.setMes(dash.getMes());
            clientes.setEmpresa(dash.getEmpresa());
            clientes.setDia(dash.getDia());
            clientes.setProfessor(dash.getCodigoProfessor());
            switch (ind) {
                case ACESSOS:
                    clientes.setClientes(dadosZW.getCodsAlunosAcessaram());
                    break;
                case RENOVARAM:
                    clientes.setClientes(dadosZW.getCodsRenovados());
                    break;
                case ALUNOS_A_VENCER:
                    clientes.setClientes(dadosZW.getCodsAVencer());
                    break;
                case NAO_RENOVARAM:
                    clientes.setClientes(dadosZW.getCodsNaoRenovados());
                    break;
                case TROCARAM_CARTEIRA:
                    clientes.setClientes(dadosZW.getCodsTrocasCarteira());
                    break;
                case NOVOS_CARTEIRA_NOVOS:
                    clientes.setClientes(dadosZW.getCodsNovosCarteiraNovos());
                    break;
                case NOVOS_CARTEIRA_TROCARAM:
                    clientes.setClientes(dadosZW.getCodsNovosCarteiraTrocaram());
                    break;
            }
            if (clientes.getClientes() != null && !clientes.getClientes().isEmpty()) {
                clienteDashDao.insert(ctx, clientes);
            }

        }
    }

    public void deletarDia(final String key, final Integer dia, final Integer mes, final Integer ano, final Integer professor, final FiltrosDashboard filtros, final Date dataProcessamento) throws Exception {
        dashboardDao.deleteComParam(key, new String[]{"dia", "mes", "ano", "codigoProfessor", "empresa"}, new Object[]{dia, mes, ano, professor, filtros.getEmpresa()});
        diasSemanaDao.deleteComParam(key, new String[]{"mes", "ano", "codigoProfessor", "empresa"}, new Object[]{mes, ano, professor, filtros.getEmpresa()});
        tEDao.deleteComParam(key, new String[]{"mes", "ano", "professor", "empresa"}, new Object[]{mes, ano, professor, filtros.getEmpresa()});
        deletarAcessosExecucoesBI(key,professor,dataProcessamento,filtros);
        clienteDashDao.deleteComParam(key, new String[]{"professor", "empresa"}, new Object[]{professor, filtros.getEmpresa()});
    }

    public void deletarDiasGuardados(final String key, Map<String, Object> objectMap) throws Exception {
        if (objectMap.get("DashboardBI") != null) {
            dashboardDao.delete(key, (DashboardBI) objectMap.get("DashboardBI"));
            try{
                if (objectMap.get("DiasSemanaDashboardBI") != null) {
                    for (DiasSemanaDashboardBI dsd : (List<DiasSemanaDashboardBI>) objectMap.get("DiasSemanaDashboardBI")) {
                        diasSemanaDao.delete(key, dsd);
                    }
                }
                if (objectMap.get("TipoEventoDisponibilidadeBI") != null) {
                    for (TipoEventoDisponibilidadeBI ted : (List<TipoEventoDisponibilidadeBI>) objectMap.get("TipoEventoDisponibilidadeBI")) {
                        tEDao.delete(key, ted);
                    }

                }
                if (objectMap.get("AcessosExecucoesBI") != null) {
                    for(AcessosExecucoesBI acessosBI : (List<AcessosExecucoesBI>) objectMap.get("AcessosExecucoesBI")){
                        acessosDao.delete(key, acessosBI);
                    }
                }
                if (objectMap.get("ClienteDashBI") != null) {
                    for (ClienteDashBI cb : (List<ClienteDashBI>) objectMap.get("ClienteDashBI")) {
                        clienteDashDao.delete(key, cb);
                    }
                }
            }catch (Exception ignore){
                //testando se tem problema se n excluir.
            }
        }
    }


    public Map<String, Object> obterDeletarDia(final String key, final Integer dia, final Integer mes, final Integer ano, final Integer professor, final FiltrosDashboard filtros, final Date dataProcessamento) throws Exception {
        Map<String, Object> backupDeletarDia = new HashMap<>();

        List<DashboardBI> ListaDashDeletar = dashboardDao.findListByAttributes(key, new String[]{"dia", "mes", "ano", "codigoProfessor", "empresa"}, new Object[]{dia, mes, ano, professor, filtros.getEmpresa()}, null,1,0);
        DashboardBI dashdeletar = null;
        if (!UteisValidacao.emptyList(ListaDashDeletar)){
            dashdeletar = ListaDashDeletar.get(0);
        }
        List<DiasSemanaDashboardBI> diasSemanaDeletar = diasSemanaDao.findListByAttributes(key, new String[]{"mes", "ano", "codigoProfessor", "empresa"}, new Object[]{mes, ano, professor, filtros.getEmpresa()}, null, 0,0);
        List<TipoEventoDisponibilidadeBI> tEDeletar = tEDao.findListByAttributes(key, new String[]{"mes", "ano", "professor", "empresa"}, new Object[]{mes, ano, professor, filtros.getEmpresa()}, null,0,0);
        List<AcessosExecucoesBI> listAcessosDeletar = obterDeletarAcessosExecucoesBI(key,professor,dataProcessamento,filtros);
        List<ClienteDashBI> clienteDeletar = clienteDashDao.findListByAttributes(key, new String[]{"professor", "empresa"}, new Object[]{professor, filtros.getEmpresa()}, null, 0, 0);

        if(dashdeletar != null){
            backupDeletarDia.put("DashboardBI", dashdeletar);
        }
        if(diasSemanaDeletar != null){
            backupDeletarDia.put("DiasSemanaDashboardBI", diasSemanaDeletar);
        }
        if(tEDeletar != null){
            backupDeletarDia.put("TipoEventoDisponibilidadeBI", tEDeletar);
        }
        if(listAcessosDeletar != null){
            backupDeletarDia.put("AcessosExecucoesBI", listAcessosDeletar);
        }
        if(clienteDeletar != null){
            backupDeletarDia.put("ClienteDashBI", clienteDeletar);
        }

        return backupDeletarDia;
    }

    @Override
    public List<DiasSemanaDashboardBI> obterDias(final String key, final Integer ano, final Integer mes, final Integer professor, final Integer empresa) throws Exception {
        return diasSemanaDao.findListByAttributes(key, new String[]{"ano", "mes", "codigoprofessor", "empresa"}, new Object[]{ano, mes, professor, empresa}, null, 0);
    }

    @Override
    public List<TipoEventoDisponibilidadeBI> obterTipos(final String key, final Integer ano, final Integer mes, final Integer professor, final Integer empresa) throws Exception {
        return tEDao.findListByAttributes(key, new String[]{"ano", "mes", "professor", "empresa"}, new Object[]{ano, mes, professor, empresa}, null, 0);
    }

    @Override
    public List<AcessosExecucoesBI> obterAcessos(final String key, final Date limite,
            final Integer professor, final Integer empresa) throws Exception {
        String sql = "select obj from AcessosExecucoesBI obj\n"
                + "where obj.professor = 0\n"
                + "AND dia >= :limite\n"
                + "order by dia, codigo";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("limite", limite);
        List<AcessosExecucoesBI> lista = acessosDao.findByParam(key, sql, params);
        Map<String, AcessosExecucoesBI> dados = new HashMap<String, AcessosExecucoesBI>();
        for(AcessosExecucoesBI aeb : lista){
            dados.put(Uteis.getDataAplicandoFormatacao(aeb.getDia(), "ddMMyy"), aeb);
        }
        return new ArrayList<AcessosExecucoesBI>(dados.values());
    }

    @Override
    public DashboardBI obterBI(final String key, final Integer dia, final Integer mes, final Integer ano, final Integer professor, final Integer empresa) throws Exception {
        List<DashboardBI> dashboardBIS = dashboardDao.findListByAttributes(key, new String[]{"dia", "mes", "ano", "codigoprofessor", "empresa"},
                new Object[]{dia, mes, ano, professor, empresa}, "fimprocessamento desc", 0);
        return dashboardBIS == null || dashboardBIS.isEmpty() ? null: dashboardBIS.get(0);
    }

    @Override
    public DashboardBI obterBIGeral(final String key, final Integer professor, final Integer empresa) throws Exception {
        return obterBIGeral(key, professor, empresa, 0);
    }

    @Override
    public DashboardBI obterBIGeral(final String key, final Integer professor, final Integer empresa, Integer maxResults) throws Exception {
        List<DashboardBI> dashboardBIS = dashboardDao.findListByAttributes(key, new String[]{"codigoprofessor", "empresa"},
                new Object[]{professor, empresa}, "fimprocessamento desc", maxResults);
        return dashboardBIS == null || dashboardBIS.isEmpty() ? null: dashboardBIS.get(0);
    }

    public boolean existeBI(final String key, final Integer dia, final Integer mes, final Integer ano, final Integer professor, final Integer empresa) throws Exception{
        String w = String.format(" WHERE dia = %s and mes = %s and ano = %s and codigoprofessor = %s and empresa = %s", dia, mes+1, ano, professor, empresa);
        return dashboardDao.countWithParam(key, "dia", new StringBuilder(w), null).intValue() > 0;
    }

    @Override
    public DashboardBI obterUltimoBI(final String key, final Integer professor, final Integer empresa) throws ServiceException {
        try {
            String s = "select obj from DashboardBI obj where obj.codigoProfessor = :professor AND obj.empresa = :empresa ORDER BY obj.codigo DESC";
            Map<String, Object> p = new HashMap<>();
            p.put("professor", professor);
            p.put("empresa", empresa);
            return dashboardDao.findFirstObjectByParam(key, s, p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    @Override
    public List<DashboardBI> obterBIsPeriodo(final String key, final Integer professor, final Integer empresa,final String mesAnoInicio,final String mesAnofim)
            throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT obj FROM DashboardBI obj\n");
            sql.append("WHERE ((obj.dia||'/'||obj.mes||'/'||obj.ano||'-'||obj.codigoProfessor||'-'||obj.empresa) IN (SELECT DISTINCT MAX(dia)||'/'||mes||'/'||ano||'-'||codigoProfessor||'-'||empresa\n");
            sql.append("    FROM DashboardBI obj \n");
            sql.append("    WHERE 1 = 1\n");
            sql.append("    AND date(to_date('01/'||obj.mes||'/'||obj.ano, 'DD/MM/YYYY')) BETWEEN :inicio AND :fim\n");
            sql.append("    AND obj.codigoProfessor = :professor\n");
            sql.append("    AND obj.empresa = :empresa\n");
            sql.append("    GROUP BY ano, mes,codigoprofessor,empresa))");

            sql.append("ORDER BY obj.codigo DESC\n");

            Map<String, Object> p = new HashMap<String, Object>();
            p.put("professor", professor);
            p.put("empresa", empresa);

            Date dataConsultarInicio = Calendario.getDate("MM/yyyy", mesAnoInicio);
            Date primeiroDiaMes = Uteis.obterPrimeiroDiaMes(dataConsultarInicio);
            p.put("inicio", primeiroDiaMes);

            Date dataConsultarFim = Calendario.getDate("MM/yyyy", mesAnofim);
            Date ultimoDiaMes = Uteis.obterUltimoDiaMes(dataConsultarFim);
            p.put("fim", ultimoDiaMes);

            return dashboardDao.findByParam(key, sql.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    /**
     * Usa native sql.
     * @param key
     * @param professor
     * @param filtros
     * @param dash
     * @param semTreino
     * @throws Exception
     */
    @Override
    public void processarAlunosTreinos(final String key, final Integer professor,
            final FiltrosDashboard filtros, DashboardBI dash, boolean semTreino) throws Exception {

        String sql = "SELECT obj.codigo FROM ClienteSintetico obj\n" +
                     "INNER JOIN professorsintetico ps on ps.codigo = obj.professorsintetico_codigo";
        StringBuilder query = new StringBuilder();
        query.append(" WHERE obj.empresa = ").append(filtros.getEmpresa());
        //informações da carteira considera apenas alunos ativos
        query.append(" AND obj.situacao = 'AT' ");
        if (!SuperControle.independente(key)) {
            query.append(" AND obj.situacaocontrato IN ('AV', 'NO') ");
        }

        if (professor != null && professor > 0) {
            query.append(" AND ps.codigo = ").append(professor);
        }
        if (semTreino) {
            query.append(" and NOT EXISTS (SELECT cliente.codigo FROM ProgramaTreino p inner join ClienteSintetico cliente\n")
                    .append("    on p.cliente_codigo = cliente.codigo\n")
                    .append("  WHERE obj.codigo = cliente.codigo)");
        } else {
            query.append(" and EXISTS (SELECT cliente.codigo FROM ProgramaTreino p inner join ClienteSintetico cliente\n")
                    .append("    on p.cliente_codigo = cliente.codigo\n" )
                    .append("  WHERE obj.codigo = cliente.codigo)");
        }

        List<Integer> todos = clienteSinteticoDao.listOfObjects(key, sql + query.toString());
        if (semTreino) {
            dash.setTotalAlunosSemTreino(todos.size());
            inserirClientesDash(key, todos, dash, IndicadorDashboardEnum.ATIVOS_SEM_TREINO);
        } else {
            dash.setTotalAlunosTreino(todos.size());
            inserirClientesDash(key, todos, dash, IndicadorDashboardEnum.ATIVOS_COM_TREINO);
        }
    }

    public void processarAlunosAtivosSemTreino(final String key, final Integer professor,  final FiltrosDashboard filtros, DashboardBI dash) throws Exception {
        String sql = "SELECT obj.codigo FROM ClienteSintetico obj ";
        StringBuilder query = new StringBuilder();
        query.append(" WHERE obj.empresa = ").append(filtros.getEmpresa());

        if (professor != null && professor > 0) {
            query.append(" AND obj.professorsintetico_codigo = ").append(professor);
        }

        query.append(" AND obj.situacao = 'AT' ");
        query.append(" AND NOT EXISTS (SELECT p.cliente_codigo FROM ProgramaTreino p WHERE obj.codigo = p.cliente_codigo) ");
        query.append(" AND (obj.ativo is true or obj.ativo is null) ");

        List<Integer> todos = clienteSinteticoDao.listOfObjects(key, sql + query.toString());
        dash.setTotalAlunosAtivosForaTreino(todos.size());
    }

    public void processarProgramasTreinos(final String key, final Integer professor,
            final FiltrosDashboard filtros, DashboardBI dash, boolean processarDadosZw) throws Exception {
        StringBuilder query = new StringBuilder();
        query.append(" SELECT distinct obj.cliente_codigo FROM ProgramaTreino obj  \n");
        query.append(" INNER JOIN ClienteSintetico cli ON cli.codigo = obj.cliente_codigo  \n");
        query.append(" INNER JOIN professorsintetico ps on ps.codigo = cli.professorsintetico_codigo  \n");
        query.append(" WHERE obj.dataTerminoPrevisto >= '");
        query.append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
        query.append(" AND obj.dataTerminoPrevisto <= '");
        ConfiguracaoSistema diasAntesVencimento = configSistemaService.consultarPorTipo(key, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
        query.append(Uteis.getDataAplicandoFormatacao(Uteis.somarDias(Calendario.hoje(), diasAntesVencimento.getValorAsInteger()), "yyyy-MM-dd "));
        query.append("' ");
        query.append(" AND NOT EXISTS(SELECT proN.codigo FROM ProgramaTreino proN ");
        query.append(" WHERE proN.cliente_codigo = obj.cliente_codigo  AND ");
        query.append(" proN.dataTerminoPrevisto > obj.dataTerminoPrevisto)");
        query.append(" AND cli.empresa = ").append(filtros.getEmpresa());
        query.append(" AND cli.situacao = 'AT' ");
        if(processarDadosZw){
            query.append(" AND cli.situacaocontrato IN ('AV', 'NO')");
        }
        if (professor != null && professor > 0) {
            query.append(" AND ps.codigo = ").append(professor);
        }
        List<Integer> lista = clienteSinteticoDao.listOfObjects(key, query.toString());
        dash.setTotalTreinosRenovar(lista.size());
        inserirClientesDash(key, lista, dash, IndicadorDashboardEnum.TREINOS_A_VENCER);
    }

    public void processarAvaliacoesFisicas(final String key, final Integer professor,
            final FiltrosDashboard filtros, DashboardBI dash, boolean semAvaliacao,boolean treinoIndependente, Date inicio, Date fim) throws Exception {
        StringBuilder query = new StringBuilder();
        StringBuilder where = new StringBuilder();
        montarWhereAlunos(key, professor, filtros, where, treinoIndependente);
        query.append("SELECT DISTINCT obj.codigo FROM clientesintetico obj \n" +
                "left join professorsintetico ps on ps.codigo = obj.professorsintetico_codigo\n");
        where.append(semAvaliacao ? " AND NOT " : " AND ");
        where.append("(EXISTS(");
        where.append(" SELECT ag.cliente_codigo FROM agendamento ag  \n");
        where.append(" INNER JOIN tipoevento te ON ag.tipoevento_codigo = te.codigo \n");
        where.append(" WHERE te.comportamento = ").append(TipoAgendamentoEnum.AVALIACAO_FISICA.ordinal());
        where.append(" AND ag.status = ").append(StatusAgendamentoEnum.EXECUTADO.ordinal());
        where.append(" AND ag.inicio BETWEEN '");
        if (inicio == null && fim == null) {
            where.append(Uteis.getDataAplicandoFormatacao(Uteis.somarDias(Calendario.hoje(), -filtros.getDiasParaTras()), "yyyy-MM-dd"));
            where.append(" 00:00:00' AND '");
            where.append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
        } else {
            where.append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd"));
            where.append(" 00:00:00' AND '");
            where.append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd"));
        }
        where.append(" 23:59:59'  AND ag.cliente_codigo = obj.codigo) ");
        where.append("  OR EXISTS( ");
        where.append("  SELECT av.cliente_codigo FROM avaliacaofisica  av ");
        if (inicio == null && fim == null) {
            where.append("  where  av.cliente_codigo = obj.codigo and dataavaliacao between '").append(Uteis.getDataAplicandoFormatacao(Uteis.somarDias(Calendario.hoje(), -filtros.getDiasParaTras()), "yyyy-MM-dd"));
            where.append(" 00:00:00' and '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
            where.append(" 23:59:59')) ");
        } else {
            where.append("  where  av.cliente_codigo = obj.codigo and dataavaliacao between '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd"));
            where.append(" 00:00:00' and '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd"));
            where.append(" 23:59:59')) ");
        }

        List<Integer> lista = clienteSinteticoDao.listOfObjects(key, query.toString() + where.toString());
        if (semAvaliacao) {
            dash.setTotalAlunosSemAvaliacoes(lista.size());
            inserirClientesDash(key, lista, dash, IndicadorDashboardEnum.SEM_AVALIACAO);
        } else {
            dash.setTotalAlunosAvaliacoes(lista.size());
            inserirClientesDash(key, lista, dash, IndicadorDashboardEnum.COM_AVALIACAO_FISICA);
        }
    }

    public void processarTreinosEmDia(final String key, final Integer professor,
                                      final FiltrosDashboard filtros, DashboardBI dash, boolean emDia) throws Exception {
        StringBuilder where = new StringBuilder();
        where.append(" SELECT DISTINCT obj.cliente_codigo FROM ProgramaTreino obj ");
        where.append(" INNER JOIN ClienteSintetico cli ON cli.codigo = obj.cliente_codigo ");
        where.append(" INNER JOIN professorsintetico ps on ps.codigo = cli.professorsintetico_codigo  \n");
        where.append(" WHERE cli.empresa = ").append(filtros.getEmpresa());
        where.append(" AND cli.situacao = 'AT' ");
        if (!SuperControle.independente(key)) {
            where.append(" AND cli.situacaocontrato IN ('AV', 'NO') ");
        }

        if (professor != null && professor > 0) {
            where.append(" and ps.codigo = ").append(professor);
        }
        if (emDia) {
            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema diasAntes = css.consultarPorTipo(key, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
            Date limitAVencer = Uteis.somarDias(Calendario.hoje(), diasAntes.getValorAsInteger());
            where.append(" AND obj.dataTerminoPrevisto > '");
            where.append(Uteis.getDataAplicandoFormatacao(limitAVencer, "yyyy-MM-dd"));
            where.append("'");
            // where.append(" AND terminoUltimoPrograma is not null ");
            List<Integer> lista = programaTreinoDao.listOfObjects(key, where.toString());
            dash.setTotalTreinosEmdia(lista.size());
            inserirClientesDash(key, lista, dash, IndicadorDashboardEnum.EM_DIA);
        } else {
            where.append(" AND obj.dataTerminoPrevisto < '");
            where.append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
            where.append("'");
            where.append(" AND NOT EXISTS(SELECT proN.codigo FROM ProgramaTreino proN ");
            where.append(" WHERE proN.cliente_codigo = obj.cliente_codigo  AND ");
            where.append(" proN.dataTerminoPrevisto > obj.dataTerminoPrevisto)");

            List<Integer> lista = programaTreinoDao.listOfObjects(key, where.toString());
            dash.setTotalTreinosVencidos(lista.size());
            inserirClientesDash(key, lista, dash, VENCIDOS);
        }
    }

    @Override
    public MicroCharts montarMicroCharts(final String key, final Integer professor,
            final FiltrosDashboard filtros) throws Exception {
        JSONArray aVencer = new JSONArray();
        JSONArray novosCarteira = new JSONArray();
        JSONArray trocaram = new JSONArray();
        JSONArray tempoMedioCarteira = new JSONArray();
        JSONArray ativosSemTreino = new JSONArray();
        JSONArray treinosRenovar = new JSONArray();
        JSONArray tempoMedioPrograma = new JSONArray();
        Map<String, Object> p = new HashMap<String, Object>();
        p.put("prof", professor);
        StringBuilder sql = new StringBuilder("SELECT obj FROM DashboardBI obj ");
        sql.append(" WHERE obj.codigoProfessor = :prof ");
        sql.append(" ORDER BY codigo DESC LIMIT 60");
        List<DashboardBI> lista = dashboardDao.findByParam(key, sql.toString(), p);
        if (lista.size() == 1) {
            lista.add(lista.get(0));
        }
        MicroCharts mc = new MicroCharts();
        Iterator<DashboardBI> iterator = lista.iterator();
        while (iterator.hasNext()) {
            DashboardBI dash = iterator.next();
            JSONObject avencer = new JSONObject();
            avencer.put("day", dash.getDia() + "/" + dash.getMes() + "/" + dash.getAno());
            avencer.put("value", dash.getTotalAlunosAvencer());
            JSONObject novosCarteiraO = new JSONObject();
            novosCarteiraO.put("day", dash.getDia() + "/" + dash.getMes() + "/" + dash.getAno());
            novosCarteiraO.put("value", dash.getTotalNovosCarteira());
            JSONObject trocaramO = new JSONObject();
            trocaramO.put("day", dash.getDia() + "/" + dash.getMes() + "/" + dash.getAno());
            trocaramO.put("value", dash.getTotalTrocaramCarteira());
            JSONObject tempoMedioCarteiraO = new JSONObject();
            tempoMedioCarteiraO.put("day", dash.getDia() + "/" + dash.getMes() + "/" + dash.getAno());
            tempoMedioCarteiraO.put("value", dash.getTempoMedioPermanenciaCarteira());
            JSONObject semTreino = new JSONObject();
            semTreino.put("day", dash.getDia() + "/" + dash.getMes() + "/" + dash.getAno());
            semTreino.put("value", dash.getTotalAlunosSemTreino());
            JSONObject treinosRenovarO = new JSONObject();
            treinosRenovarO.put("day", dash.getDia() + "/" + dash.getMes() + "/" + dash.getAno());
            treinosRenovarO.put("value", dash.getTotalTreinosAvencer());
            JSONObject tempoMedioProgramaO = new JSONObject();
            tempoMedioProgramaO.put("day", dash.getDia() + "/" + dash.getMes() + "/" + dash.getAno());
            tempoMedioProgramaO.put("value", dash.getTempoMedioPermanenciaTreino());
            if (!iterator.hasNext()) {
                avencer.put("bullet", "round");
                novosCarteiraO.put("bullet", "round");
                trocaramO.put("bullet", "round");
                tempoMedioCarteiraO.put("bullet", "round");
                semTreino.put("bullet", "round");
                treinosRenovarO.put("bullet", "round");
                tempoMedioProgramaO.put("bullet", "round");
            }
            aVencer.put(avencer);
            ativosSemTreino.put(semTreino);
            novosCarteira.put(novosCarteiraO);
            trocaram.put(trocaramO);
            tempoMedioCarteira.put(tempoMedioCarteiraO);
            tempoMedioPrograma.put(tempoMedioProgramaO);
            treinosRenovar.put(treinosRenovarO);
        }
        mc.setVencer(aVencer.toString());
        mc.setAtivosSemTreino(ativosSemTreino.toString());
        mc.setNovosCarteira(novosCarteira.toString());
        mc.setTrocaram(trocaram.toString());
        mc.setTempoMedioCarteira(tempoMedioCarteira.toString());
        mc.setTempoMedioPrograma(tempoMedioPrograma.toString());
        mc.setTreinosRenovar(treinosRenovar.toString());
        return mc;
    }

    public void montarWhereAlunos(final String key, final Integer professor,
            final FiltrosDashboard filtros, StringBuilder where, boolean treinoIndependente) {
        where.append(" WHERE obj.empresa = ").append(filtros.getEmpresa());
        where.append(" and obj.professorSintetico_codigo IS NOT NULL AND obj.professorSintetico_codigo > 0 ");
        where.append(" and obj.situacao NOT IN ('VI') ");
        if (!treinoIndependente) {
            where.append(" AND obj.situacaoContrato not in ('TV') ");
        }
        if (professor != null && professor > 0) {
            where.append(" and ps.codigo = ").append(professor);
        }
    }

    public void processarAlunos(final String key, final Integer professor,
            final FiltrosDashboard filtros, DashboardBI dash, boolean treinoIndependente, Date dataProcessamento) throws Exception {
        StringBuilder where = new StringBuilder();
        montarWhereAlunos(key, professor, filtros, where, treinoIndependente);

        String sql = "SELECT obj.codigo FROM ClienteSintetico obj\n" +
                "left join professorsintetico ps on ps.codigo = obj.professorsintetico_codigo";

        List<Integer> todos = clienteSinteticoDao.listOfObjects(key, sql + where.toString());
        dash.setTotalAlunos(todos.size());
        inserirClientesDash(key, todos, dash, IndicadorDashboardEnum.TOTAL_ALUNOS);
        List<Integer> ativos = new ArrayList<>();
        if(!treinoIndependente && professor!= null && Uteis.getMesData(dataProcessamento) != Uteis.getMesData(Calendario.hoje())){
            List<Integer> matriculas = obterAtivosNumaData(key, dash.getEmpresa(), dataProcessamento, professor);
            if(!matriculas.isEmpty()){
                String matriculasFiltro = "";
                for(Integer mat : matriculas){
                    matriculasFiltro += ","+mat;
                }
                ativos = clienteSinteticoDao.listOfObjects(key, sql +
                        (" WHERE obj.matricula in ("+matriculasFiltro.replaceFirst(",", "")+") "));
            }
        } else {
            ativos = clienteSinteticoDao.listOfObjects(key, sql + where.toString() +
                    (treinoIndependente ? "AND obj.situacao = 'AT' " : " AND obj.situacao in ('AT','NO','AV') AND obj.situacaocontrato IN ('AV', 'NO') "));
        }

        dash.setTotalAlunosAtivos(ativos.size());
        inserirClientesDash(key, ativos, dash, IndicadorDashboardEnum.ATIVOS);
        ConfiguracaoSistema somenteAlunoDesistente = configSistemaService.consultarPorTipo(key, ConfiguracoesEnum.SOMENTE_ALUNO_CONTRATO_DESISTENTE);
        String andInativos = somenteAlunoDesistente.getValorAsBoolean() ?
                " AND ( obj.situacao = 'IN' AND obj.situacaocontrato = 'DE')" :
                " AND ( obj.situacaocontrato in ('DE','VE','CA','TR') ) ";
        ConfiguracaoSistema inativosXdias = configSistemaService.consultarPorTipo(key, ConfiguracoesEnum.INATIVOS_A_X_DIAS);
        if (inativosXdias != null && inativosXdias.getValorAsInteger() != null && inativosXdias.getValorAsInteger() > 0) {
            Date dataLimite = Uteis.somarDias(Calendario.hoje(), -inativosXdias.getValorAsInteger());
            andInativos += " AND obj.datavigenciaateajustada IS NOT NULL AND obj.datavigenciaateajustada > '"
                    + Uteis.getDataAplicandoFormatacao(dataLimite, "yyyy-MM-dd") + "' ";
        }
        List<Integer> inativos = clienteSinteticoDao.listOfObjects(key, sql + where.toString() + (treinoIndependente ? " AND obj.situacao = 'IN' " : andInativos));
        dash.setTotalAlunosInativos(inativos.size());
        inserirClientesDash(key, inativos, dash, IndicadorDashboardEnum.INATIVOS);

        if (treinoIndependente) {
            List<Integer> visitantes = clienteSinteticoDao.listOfObjects(key, sql + where.toString() + "AND obj.situacao = 'VI' ");
            dash.setTotalAlunosVisitantes(visitantes.size());
            inserirClientesDash(key, visitantes, dash, IndicadorDashboardEnum.VISITANTES);
        }
    }

    private void inserirClientesDash(final String key, List<Integer> lista, DashboardBI dash, IndicadorDashboardEnum ind) throws Exception {
        ClienteDashBI clientes = new ClienteDashBI();
        clientes.setIndicador(ind);
        clientes.setAno(dash.getAno());
        clientes.setMes(dash.getMes());
        clientes.setDia(dash.getDia());
        clientes.setProfessor(dash.getCodigoProfessor());
        clientes.setClientes(Uteis.concatenarListaInteger(lista));
        clientes.setEmpresa(dash.getEmpresa());
        if (clientes.getClientes() != null && !clientes.getClientes().isEmpty()) {
            clienteDashDao.insert(key, clientes);
        }
    }

    public void obterTempoMedioPermanenciaNoTreino(final String key, final Integer professor,
            final FiltrosDashboard filtros, DashboardBI dash, Date fim) throws Exception {
        String hql = "SELECT cast(dataterminoprevisto as date) - cast(programatreino.datainicio  as date) FROM programatreino  "
                +"  INNER JOIN ClienteSintetico cli on cli.codigo = programatreino.cliente_codigo "
                +"  WHERE"
                + " cli.situacao = 'AT' "
                + (professor == null || professor == 0 ? ""
                : " AND professorcarteira_codigo = " + professor)
                + " AND '"
                + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")
                +"' BETWEEN cast(programatreino.datainicio as date) AND cast(dataterminoprevisto as date) ";
        List<Integer> valoresInt = programaTreinoDao.listOfObjects(key, hql);
        if(valoresInt == null || valoresInt.isEmpty()){
           dash.setTempoMedianaPermanenciaTreino(0);
           dash.setTempoMedioPermanenciaTreino(0);
           return;
        }
        Integer somatoria = 0;
        List<Integer> valoresAdd = new ArrayList<Integer>();
        for(Integer bv : valoresInt){
            if(bv != null){
                somatoria += bv;
                valoresAdd.add(bv);
            }
        }
        try {
            dash.setTempoMedianaPermanenciaTreino(Uteis.mediana(valoresAdd));
            dash.setTempoMedioPermanenciaTreino(somatoria/valoresAdd.size());
        } catch (Exception e){
            dash.setTempoMedianaPermanenciaTreino(0);
            dash.setTempoMedioPermanenciaTreino(0);
        }

    }

    public ItemMediaBI obterItemMediaTreino(final String key, final Integer professor, boolean maior) throws Exception {
        String hql = " SELECT codigo FROM (  "
                +"  SELECT (cast(dataterminoprevisto as date) - cast(programatreino.datainicio  as date)) AS dias, programatreino.codigo   FROM programatreino "
                +"  INNER JOIN ClienteSintetico cli on cli.codigo = programatreino.cliente_codigo "
                +"  WHERE"
                +"  cli.situacao = 'AT' "
                +   (professor == null || professor == 0 ? ""
                :"  AND professorcarteira_codigo = " + professor)
                +  "AND '"
                +  Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")
                +"' BETWEEN cast(programatreino.datainicio as date) AND cast(dataterminoprevisto as date) "
                +"  ) AS ITEM ORDER BY dias "
                +(maior ? "DESC " : "");
        List<Integer> valores = programaTreinoDao.listOfObjects(key, hql);
        if (valores == null || valores.isEmpty()) {
            return new ItemMediaBI();
        } else {
            ProgramaTreino programa = programaTreinoDao.findById(key, valores.get(0));
            ItemMediaBI item = new ItemMediaBI();
            if(programa.getDataInicio() != null && programa.getDataTerminoPrevisto() != null) {
                item.setDuracao(new Long(Uteis.nrDiasEntreDatas(programa.getDataInicio(), programa.getDataTerminoPrevisto())).intValue());
            }
            item.setMatricula(programa.getCliente().getMatriculaString());
            item.setNome(programa.getCliente().getNome());
            return item;
        }
    }

    public List<TreinoRealizado> obterListaTreinos(final String key, final Integer professor,
            final FiltrosDashboard filtros, DashboardBI dash, Date fim) throws Exception {
        Map<String, Object> p = new HashMap<String, Object>();
        p.put("inicio", Calendario.inicioMes(Uteis.somarDias(fim, -(filtros.getDiasParaTras()))));
        p.put("fim", fim);
        p.put("empresa", filtros.getEmpresa());

        StringBuilder where = new StringBuilder();
        where.append(" WHERE obj.cliente.empresa = :empresa and dataInicio BETWEEN :inicio AND :fim and obj.nota IS NOT NULL");
        if (professor != null && professor > 0) {
            p.put("professor", professor);
            where.append(" and professor.codigo = :professor ");
        }
        return treinoRealizadoDao.findByParam(key, where, p);
    }

    public void addTreinoExecutado(TreinoRealizado tr, Map<Date, AcessosExecucoesBI> mapaTreino){
        AcessosExecucoesBI aeBI = mapaTreino.get(Calendario.getDataComHoraZerada(tr.getDataInicio()));
        if(aeBI == null){
            aeBI = new AcessosExecucoesBI();
            aeBI.setDia(Calendario.getDataComHoraZerada(tr.getDataInicio()));
            mapaTreino.put(Calendario.getDataComHoraZerada(tr.getDataInicio()), aeBI);
        }
        aeBI.setAno(Uteis.getAnoData(tr.getDataInicio()));
        aeBI.setMes(Uteis.getMesData(tr.getDataInicio()));
        aeBI.setExecucoes(aeBI.getExecucoes() + 1);
        if(tr.getOrigem() != null && tr.getOrigem().equals(OrigemExecucaoEnum.SMARTPHONE)){
            aeBI.setSmartphone(aeBI.getSmartphone() + 1);
        }
    }

    public void processarTreinosRealizados(final String key, final Integer professor,
            final FiltrosDashboard filtros, DashboardBI dash, Date fim, boolean processarDadosZw) throws Exception {

        Map<Date, AcessosExecucoesBI> mapaAcessos = new HashMap<Date, AcessosExecucoesBI>();
        if(processarDadosZw){
            ProfessorSintetico professorSintetico = professorDao.findById(key, professor);
            String modo = Aplicacao.getProp(Aplicacao.modoConsultaDashBI);
            final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
            List<AcessosExecucoesJSON> acessos = new ArrayList<>();
            Integer codColaborador = 0;
            if ( null != professorSintetico){
                codColaborador = professorSintetico.getCodigoColaborador();
            }
            if (modo.equals("consultaBD")) {
                acessos = obterAcessosZW(key, codColaborador, filtros, fim);
            } else {
                IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                acessos = integracaoWS.obterAcessos(url, key, codColaborador, filtros, fim);
            }

            for(AcessosExecucoesJSON aeJson : acessos){
                AcessosExecucoesBI aeBI = new AcessosExecucoesBI(aeJson, dash);
                mapaAcessos.put(Calendario.getDataComHoraZerada(aeBI.getDia()), aeBI);
            }
        }

        List<Integer> lista = new ArrayList<Integer>();
        Map<DiasSemana, DiasSemanaDashboardBI> mapaDias = new EnumMap<DiasSemana, DiasSemanaDashboardBI>(DiasSemana.class);
        for (DiasSemana d : DiasSemana.values()) {
            DiasSemanaDashboardBI diaSemana = new DiasSemanaDashboardBI();
            diaSemana.setDiaSemana(d);
            diaSemana.setEmpresa(filtros.getEmpresa());
            diaSemana.setCodigoProfessor(professor);
            diaSemana.setAno(dash.getAno());
            diaSemana.setMes(dash.getMes());
            mapaDias.put(d, diaSemana);

        }
        List<TreinoRealizado> treinoRealizados = resultadosTreino(key, fim, filtros, professor);
        Integer somaAvaliacoes = 0;
        for (TreinoRealizado treino : treinoRealizados) {
            if (treino.getNota() != null && !treino.getNota().isEmpty() && !treino.getNota().equals("0")) {
                try {
                    lista.add(treino.getCodigo());
                    Integer nota = Integer.valueOf(treino.getNota());
                    dash.setNrAvaliacoesTreino(dash.getNrAvaliacoesTreino() + 1);
                    somaAvaliacoes += nota;
                    switch (nota) {
                        case 1:
                            dash.setNr1estrelas(dash.getNr1estrelas() + 1);
                            break;
                        case 2:
                            dash.setNr2estrelas(dash.getNr2estrelas() + 1);
                            break;
                        case 3:
                            dash.setNr3estrelas(dash.getNr3estrelas() + 1);
                            break;
                        case 4:
                            dash.setNr4estrelas(dash.getNr4estrelas() + 1);
                            break;
                        case 5:
                            dash.setNr5estrelas(dash.getNr5estrelas() + 1);
                            break;
                    }
                } catch (Exception e) {
                    Uteis.logar(e, GestaoServiceImpl.class);
                }
            }
            addTreinoExecutado(treino, mapaAcessos);
            DiasSemana d = DiasSemana.getDiaSemanaNumeral(Calendario.getInstance(treino.getDataInicio()).get(Calendar.DAY_OF_WEEK));
            DiasSemanaDashboardBI diaSemana = mapaDias.get(d);
            if (diaSemana == null) {
                diaSemana = new DiasSemanaDashboardBI();
                diaSemana.setDiaSemana(d);
                diaSemana.setCodigoProfessor(professor);
                diaSemana.setAno(dash.getAno());
                diaSemana.setMes(dash.getMes());
                mapaDias.put(d, diaSemana);
            }
            try {
                if (!diaSemana.getDias().contains(Calendario.getDataComHoraZerada(treino.getDataInicio()))) {
                    diaSemana.getDias().add(Calendario.getDataComHoraZerada(treino.getDataInicio()));
                    diaSemana.setNrDias(diaSemana.getNrDias() + 1);
                }
                Integer horarioInicio = Integer.valueOf(Uteis.gethoraHHMMAjustado(treino.getDataInicio()).replaceAll(":", ""));
                if (horarioInicio <= 1259) {
                    diaSemana.setTotalManha(diaSemana.getTotalManha() + 1);
                } else if (horarioInicio <= 1859) {
                    diaSemana.setTotalTarde(diaSemana.getTotalTarde() + 1);
                } else if (horarioInicio <= 2359) {
                    diaSemana.setTotalNoite(diaSemana.getTotalNoite() + 1);
                }
            } catch (Exception e) {
                Uteis.logar(e, GestaoServiceImpl.class);
            }
        }
        dash.setMediaValorAvaliacao(dash.getNrAvaliacoesTreino() == 0 ? 0 : Double.valueOf(somaAvaliacoes) / Double.valueOf(dash.getNrAvaliacoesTreino()));
        inserirClientesDash(key, lista, dash, IndicadorDashboardEnum.AVALIACOES);
        Calendar data = Calendar.getInstance();
        diasSemanaDao.deleteComParam(key,
                new String[]{"ano", "mes", "codigoProfessor", "empresa"},
                new Object[]{data.get(Calendar.YEAR), data.get(Calendar.MONTH) + 1, professor,dash.getEmpresa()});
        for (DiasSemanaDashboardBI dsdb : mapaDias.values()) {
            diasSemanaDao.insert(key, dsdb);
        }
        for(AcessosExecucoesBI ae : mapaAcessos.values()){
            ae.setProfessor(professor);
            ae.setEmpresa(filtros.getEmpresa());
            acessosDao.insert(key, ae);
        }
    }
    private List<TreinoRealizado> resultadosTreino(String key, Date fim, FiltrosDashboard filtros, Integer professor) throws Exception{
        Map<String, Object> p = new HashMap<String, Object>();
        Date diaLimite = Uteis.somarDias(Calendario.hoje(), -filtros.getDiasParaTras());
        Date inicio = Calendario.inicioMes(Uteis.somarDias(fim, - (filtros.getDiasParaTras())));
        fim = Calendario.fimMes(Calendario.getDataComHora(fim, "23:59"));
        StringBuilder sql = new StringBuilder();
        sql.append(" select DISTINCT ON (tr.codigo, matricula, professorCarteiraApresentar) tr.codigo, tr.nota, tr.datainicio, tr.origem, cl.matricula as matricula, ps.nome as professorCarteiraApresentar from treinorealizado tr ");
        sql.append(" inner join clientesintetico cl on cl.codigo = tr.cliente_codigo ");
        sql.append(" LEFT JOIN professorsintetico ps ON ps.codigo = cl.professorsintetico_codigo ");
        sql.append(" WHERE cl.empresa = ").append(filtros.getEmpresa());
        sql.append(" and tr.dataInicio >= '").append(Uteis.getDataFormatoBD(inicio)).append(" 00:00:00' ");
        sql.append(" and tr.dataFim <= '").append(Uteis.getDataFormatoBD(fim)).append(" 00:00:00' ");
        sql.append(" AND tr.nota <> '0' and ps.nome is not null ");

        if (professor != null && professor > 0) {
            sql.append(" and tr.professor_codigo notnull");
            sql.append(" and tr.professor_codigo = ").append(professor);
        }
        List<TreinoRealizado> resultadosTreino;
        try (ResultSet rs = treinoRealizadoDao.createStatement(key, sql.toString())) {
            resultadosTreino = new ArrayList<>();
            while (rs.next()) {
                TreinoRealizado tr = new TreinoRealizado();
                tr.setCodigo(rs.getInt("codigo"));
                tr.setNota(rs.getString("nota"));
                tr.setDataInicio(rs.getTimestamp("datainicio"));
                tr.setOrigem(OrigemExecucaoEnum.getFromId(rs.getInt("origem")));
                resultadosTreino.add(tr);
            }
        }
        return resultadosTreino;
    }

    @Override
    public List<ClienteBIJSON> consultarClientesAtivosForaTreino(final String key, final Integer codigoEmpresa, final List<ModalidadeJSON> modalidades) throws Exception {
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        String filtroModalidades = "";
        if(modalidades != null){
            for (ModalidadeJSON mod : modalidades) {
                if (mod.getEscolhido() != null) {
                    if (mod.getEscolhido()) {
                        filtroModalidades += "," + mod.getCodigoModalidade();
                    }
                }
            }
        }
        List<ClienteBIJSON> clientesImportar = new ArrayList<ClienteBIJSON>();
        List<ClienteBIJSON> clienteBIJSONs = integracaoWS.consultarClientesAtivosForaTreino(url, key, codigoEmpresa, filtroModalidades.replaceFirst(",", ""));
        if(clienteBIJSONs == null || clienteBIJSONs.isEmpty()){
            return clienteBIJSONs;
        }
        String cods = "";
        for(ClienteBIJSON c : clienteBIJSONs){
            cods += ","+c.getMatricula();
        }
        String sql = "SELECT matricula from ClienteSintetico WHERE matricula in ("+cods.replaceFirst(",", "")+")";
        List<Integer> todos = clienteSinteticoDao.listOfObjects(key, sql);
        for(ClienteBIJSON c : clienteBIJSONs){
            if(!todos.contains(c.getMatricula())){
                clientesImportar.add(c);
            }
        }
        return clientesImportar;
    }

    @Override
    public List<ClienteBIJSON> consultarClientesForaTreino(final String key, final Integer codigoEmpresa) throws Exception {
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        String filtroModalidades = "";
        List<ClienteBIJSON> clientesImportar = new ArrayList<>();
        List<ClienteBIJSON> clienteBIJSONs = integracaoWS.consultarClientesForaTreino(url, key, codigoEmpresa);
        if(clienteBIJSONs == null || clienteBIJSONs.isEmpty()){
            return clienteBIJSONs;
        }
        String cods = "";
        for(ClienteBIJSON c : clienteBIJSONs){
            cods += ","+c.getMatricula();
        }
        String sql = "SELECT matricula from ClienteSintetico WHERE matricula in ("+cods.replaceFirst(",", "")+")";
        List<Integer> todos = clienteSinteticoDao.listOfObjects(key, sql);
        for(ClienteBIJSON c : clienteBIJSONs){
            if(!todos.contains(c.getMatricula())){
                clientesImportar.add(c);
            }
        }
        return clientesImportar;
    }

    @Override
    public List<ModalidadeJSON> consultarModalidadesClientesAtivosForaTreino(final String key, final Integer codigoEmpresa) throws Exception {
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        return integracaoWS.consultarModalidadesClientesAtivosForaTreino(url, key, codigoEmpresa);
    }

    @Override
    public List<ClienteSintetico> consultarAlunos(final String key, DashboardBI dash, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter, Integer idProfessor) throws Exception {
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        ClienteDashBI clientes = obterCodigos(key, dash, indicador);
        if (clientes == null && paginadorDTO != null) {
            List<ClienteSintetico> lista = new ArrayList<>();
            paginadorDTO.setQuantidadeTotalElementos((long) lista.size());
            return lista ;
        }
        if (indicador.getOrigem().equals("TW") && indicador.getColunas().contains("dataPrograma")) {
            return consultarAlunosTreinoPrograma(key, dash, indicador, paginadorDTO, filter, idProfessor);
        }else if (indicador.getOrigem().equals("TW")) {
            return consultarAlunosTreino(key, dash, indicador, paginadorDTO, filter, idProfessor);
        } else if (clientes != null) {
            if (paginadorDTO == null){
                paginadorDTO = new PaginadorDTO();
            }
            List<ClienteBIJSON> clientesFromZW = integracaoWS.consultarAlunos(url, key, clientes.getClientes(),paginadorDTO.getSort() == null ? "" : paginadorDTO.getSort());
            List<ClienteBIJSON> clientesFromZWFiltrados = new ArrayList<>();
            boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
            if (buscaRapida){
                JSONObject objJson;
                filter = Uteis.retirarAcentuacaoRegex(filter);
                objJson = new JSONObject(filter);
                String pesquisa = objJson.getString("quicksearchValue").toUpperCase();
                int i = 0;
                for ( ClienteBIJSON cliente: clientesFromZW) {
                    try {
                        Integer matricula = Integer.valueOf(pesquisa);
                        if (cliente.getMatricula().equals(matricula)){
                            clientesFromZWFiltrados.add(cliente);
                        }
                        i++;
                        if (i == clientesFromZW.size()){
                            paginadorDTO.setQuantidadeTotalElementos((long) clientesFromZW.size());
                            return clienteFromJSON(clientesFromZWFiltrados);
                        }
                    } catch (NumberFormatException ignore) {
                        if (cliente.getNome().contains(pesquisa)){
                            clientesFromZWFiltrados.add(cliente);
                        }
                        i++;
                        if (i == clientesFromZW.size()){
                            paginadorDTO.setQuantidadeTotalElementos((long) clientesFromZW.size());
                            return clienteFromJSON(clientesFromZWFiltrados);
                        }
                    }
                }
            }
            //TODO: foi criado para paginar as infomrações que vem do ZW.
            if ( paginadorDTO.getSize() != null){
                if (paginadorDTO.getSize() > 0){
                    int i = 0;
                    int iPaginador = 0;
                    long pular;
                    for ( ClienteBIJSON cliente : clientesFromZW){
                        pular = paginadorDTO.getPage() * 10;
                        if (paginadorDTO.getPage() > 0){
                            if (iPaginador >= pular){
                                clientesFromZWFiltrados.add(cliente);
                                i++;
                            }
                            iPaginador++;
                        }else{
                            clientesFromZWFiltrados.add(cliente);
                            i++;
                            iPaginador++;
                        }
                        if (i == paginadorDTO.getSize() || clientesFromZW.size() - iPaginador == 0){
                            paginadorDTO.setQuantidadeTotalElementos((long) clientesFromZW.size());
                            return clienteFromJSON(clientesFromZWFiltrados);
                        }
                    }
                }
            }
            return clienteFromJSON(clientesFromZW);
        }

        return new ArrayList<>();

    }

    @Override
    public List<ClienteSintetico> consultarAlunosTreino(
            final String key, DashboardBI dash, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter, Integer idProfessor) throws Exception {
        ClienteDashBI clientes = obterCodigos(key, dash, indicador);
        if (clientes == null || clientes.getClientes() == null || clientes.getClientes().isEmpty()) {
            return new ArrayList<>();
        }
        boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        HashMap<String, Object> param = new HashMap<>();
        JSONObject objJson = new JSONObject();
        if (filter != null){
            filter = Uteis.retirarAcentuacaoRegex(filter);
            objJson = new JSONObject(filter);
        }
        hql.append("SELECT obj.codigo, obj.situacao, obj.matricula,obj.nome as nomeCliente,ps.nome as nomeProfessor, obj.dataultimoacesso as dataUltimoacesso, pt.dataTerminoPrevisto, " +
                " COALESCE(NULLIF(p.fotokey, ''), obj.fotokeyapp) as fotokeyapp FROM ClienteSintetico obj\n" +
        " INNER JOIN professorsintetico ps ON obj.professorsintetico_codigo = ps.codigo  " +
        " LEFT JOIN (SELECT *, ROW_NUMBER() OVER (PARTITION BY cliente_codigo ORDER BY dataTerminoPrevisto DESC) AS rn " +
        " FROM programatreino) pt ON obj.codigo = pt.cliente_codigo AND pt.rn = 1 " +
        " inner join pessoa p on obj.pessoa_codigo = p.codigo ");
        where.append("WHERE obj.codigo IN (");
        where.append(clientes.getClientes().replaceAll("\\|", ",")).append(")");
        if (idProfessor > 0){
            where.append(" AND obj.professorSintetico.codigo = :professorId");
            param.put("professorId", idProfessor);
        }
        if (buscaRapida){
            where.append(" AND (");
            String pesquisa = objJson.getString("quicksearchValue");
            try {
                Integer matricula = Integer.valueOf(pesquisa);
                where.append(" matricula = :matricula ");
                param.put("matricula", matricula);
                where.append(" OR");
            } catch (NumberFormatException ignore) {
            }
            where.append(" UPPER(UNACCENT(obj.nome)) LIKE UPPER(CONCAT(:nome,'%')))");
            param.put("nome", pesquisa.replaceAll(" ", "%"));
        }
        boolean pesquisaNative = verificaIndicador(indicador);
        hql.append(where);
        if (paginadorDTO != null) {
            return montarPaginadorDTO(key, where, hql, param, paginadorDTO, clienteSinteticoDao,
                    clientes.getClientes().split("\\|").length, idProfessor, pesquisaNative);
        }
        return clienteSinteticoDao.findByParam(key, where, param);
    }
    private boolean verificaIndicador(IndicadorDashboardEnum indicador) {
        boolean nativo = true;
        switch (indicador) {
            case ALUNOS_A_VENCER:
            case TREINOS_A_VENCER:
            case VENCIDOS:
                nativo = false;
                break;
        }
        return nativo;
    }

    private List montarPaginadorDTO(String key, StringBuilder where, StringBuilder hql, Map<String, Object> param, PaginadorDTO paginadorDTO, DaoGenerico dao, Integer idProfessor) throws Exception{
        return montarPaginadorDTO(key, where, hql, param, paginadorDTO, dao, null, idProfessor, true);
    }

    private List montarPaginadorDTO(String key, StringBuilder where, StringBuilder hql, Map<String, Object> param, PaginadorDTO paginadorDTO, DaoGenerico dao, Integer count, Integer idProfessor, boolean pesquisaNative) throws Exception{
        maxResults = paginadorDTO.getSize() == null ? 999999 : paginadorDTO.getSize().intValue();
        indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        paginadorDTO.setQuantidadeTotalElementos(count == null ? dao.countWithParam(key, "codigo", where, param).longValue() : count.longValue());
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        boolean ordenarProfessor = idProfessor == 0;
        if(paginadorDTO.getSort() != null){
            String retorno = traduzirColunasOrdenacao(paginadorDTO.getSort(), where, hql.toString());
            if(retorno != null) {
                if (ordenarProfessor || !(paginadorDTO.getSort().contains("nomeProfessor"))){
                    paginadorDTO.setSort(retorno);
                    where.append(paginadorDTO.getSQLOrderByUse());
                    if (!UteisValidacao.emptyString(hql.toString())) {
                        hql.append(paginadorDTO.getSQLOrderByUse());
                    }
                }
            }
        }else{
            if (hql != null) {
                hql.append(" order by obj.nome ");
            }
        }
        if (UteisValidacao.emptyString(hql.toString())
                || hql.toString().contains(":professorId")
                || (!pesquisaNative && !where.toString().contains("ps.nome"))){
            List clienteSinteticos = dao.findByParam(key, where, param, maxResults, indiceInicial);
            for (Object obj : clienteSinteticos) {
                if (obj instanceof ClienteSintetico) {
                    ClienteSintetico clienteS = (ClienteSintetico) obj;
                    clienteS.setFotoKeyApp(Uteis.getPaintFotoDaNuvem(UteisValidacao.emptyString(clienteS.getFotoKeyApp()) ? clienteS.getPessoa().getFotoKey() : clienteS.getFotoKeyApp()));
                }
            }
            return clienteSinteticos;
        }
        paginadorDTO.setQuantidadeTotalElementos((long) dao.findByParamNative(key, hql.toString(), param, 0, 0).size());
        //TODO : estou fazendo query nativa no caso de uma pesquisa de todos os alunos para não quebrar a consulta
        List<Object[]> list = new ArrayList<>(dao.findByParamNative(key, hql.toString(), param, maxResults, indiceInicial));
        List<ClienteSintetico> clienteSinteticos = new ArrayList<>();
        for (Object[] clienteSintetico : list) {
            ClienteSintetico clienteS = new ClienteSintetico();
            clienteS.setCodigo(Integer.parseInt(clienteSintetico[0].toString()));
            clienteS.setSituacao(clienteSintetico[1].toString());
            clienteS.setMatricula(Integer.parseInt(clienteSintetico[2].toString()));
            clienteS.setNome(clienteSintetico[3].toString());
            clienteS.setProfessorSintetico(new ProfessorSintetico());
            clienteS.getProfessorSintetico().setNome((clienteSintetico[4].toString()));
            clienteS.setDataUltimoacesso((Date) clienteSintetico[5]);
            if (clienteSintetico.length > 6){
                ProgramaTreino pt = new ProgramaTreino();
                pt.setDataTerminoPrevisto((Date) clienteSintetico[6]);
                clienteS.setProgramaVigente(pt);
            }
            if (clienteSintetico.length > 7) {
                clienteS.setFotoKeyApp(Uteis.getPaintFotoDaNuvem(clienteSintetico[7] == null ? fotoPessoaZW(sessaoService.getUsuarioAtual().getChave(), clienteS.getMatricula()) : clienteSintetico[7].toString()));
            }
            clienteSinteticos.add(clienteS);
        }
        return clienteSinteticos;
    }

    public String fotoPessoaZW(String chave, int codigoMatricula) {
        String sqlStr = "SELECT fotokey FROM pessoa p " +
                "JOIN cliente c on c.pessoa = p.codigo " +
                "WHERE c.codigomatricula = " + codigoMatricula;
        try (Connection conZW = conexaoZWService.conexaoZw(chave)) {
            try (ResultSet tabelaResultado = ConexaoZWServiceImpl.criarConsulta(sqlStr, conZW)) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getString("fotokey");
                }
            }
        } catch (Exception e) {
        }
        return "";
    }

    private String traduzirColunasOrdenacao(String property, StringBuilder where, String hql) {
        String valor = property.substring(0, property.indexOf(","));
        switch (valor) {
            case "nomeAbreviado":
                return property.replaceFirst("nomeAbreviado", "obj.nome");
            case "nomeProfessor":
                if (where.toString().contains("obj.professor")) {
                    return property.replaceFirst("nomeProfessor", "obj.professor.nome");
                } else if (!UteisValidacao.emptyString(hql)) {
                    return property.replaceFirst("nomeProfessor", "ps.nome");
                }
                return property.replaceFirst("nomeProfessor", "obj.professorSintetico.nome");
            case "nomeAluno":
                return property.replaceFirst("nomeAluno", "obj.cliente.nome");
            case "matricula":
                return property.replaceFirst("matricula", "obj.cliente.matricula");
            case "evento":
                return property.replaceFirst("evento", "obj.tipoEvento.nome");
            case "nome":
                return property.replaceFirst("evento", "obj.nome");
            case "dataPrograma":
                return property.replaceFirst("dataPrograma", "pt.dataTerminoPrevisto");
            case "situacao":
                return property.replaceFirst("situacao", "obj.situacao");

        }
        return null;
    }

    @Override
    public List<TreinoRealizado> consultarTreinos(final String key, DashboardBI dash, IndicadorDashboardEnum indicador) throws Exception {
        ClienteDashBI clientes = obterCodigos(key, dash, indicador);
        if (clientes == null) {
            return new ArrayList<TreinoRealizado>();
        }
        return treinoRealizadoDao.findByParam(key,
                "SELECT obj FROM TreinoRealizado obj WHERE obj.codigo IN (" + clientes.getClientes().replaceAll("\\|", ",") + ")", new HashMap<String, Object>());
    }

    private ClienteDashBI obterCodigos(final String key, DashboardBI dash, IndicadorDashboardEnum indicador) throws Exception {
        List<ClienteDashBI> listByAttributes = clienteDashDao.findListByAttributes(key, new String[]{"dia", "mes", "ano", "professor", "indicador", "empresa"},
                new Object[]{dash.getDia(), dash.getMes(), dash.getAno(), dash.getCodigoProfessor(),
                        indicador, dash.getEmpresa()}, null, 0);
        return listByAttributes == null || listByAttributes.isEmpty() ? null: listByAttributes.get(0);
    }

    @Override
    public List<ClienteSintetico> preencherDataPrograma(final String key, List<ClienteSintetico> clientes)   throws ServiceException {
        try {
            for (ClienteSintetico cli : clientes) {
                ProgramaTreino pt = programaService.consultarSomenteDataTreinoAtual(key, cli);
                cli.setProgramaVigente(pt);
            }
            return Ordenacao.ordenarLista(clientes, "dataPrograma");
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void processarAgenda(final String key, final Integer professor,
                                DashboardBI dash,
                                Date inicio,
                                Date fim,
                                final Integer empresa, Date dataProcessamento) throws Exception {
        Map<TipoAgendamentoEnum, TipoEventoDisponibilidadeBI> tipos = new EnumMap<>(TipoAgendamentoEnum.class);
        tipos.put(TipoAgendamentoEnum.CONTATO_INTERPESSOAL, new TipoEventoDisponibilidadeBI(dash, TipoAgendamentoEnum.CONTATO_INTERPESSOAL));
        tipos.put(TipoAgendamentoEnum.PRESCRICAO_TREINO, new TipoEventoDisponibilidadeBI(dash, TipoAgendamentoEnum.PRESCRICAO_TREINO));
        tipos.put(TipoAgendamentoEnum.REVISAO_TREINO, new TipoEventoDisponibilidadeBI(dash, TipoAgendamentoEnum.REVISAO_TREINO));
        tipos.put(TipoAgendamentoEnum.RENOVAR_TREINO, new TipoEventoDisponibilidadeBI(dash, TipoAgendamentoEnum.RENOVAR_TREINO));
        tipos.put(TipoAgendamentoEnum.AVALIACAO_FISICA, new TipoEventoDisponibilidadeBI(dash, TipoAgendamentoEnum.AVALIACAO_FISICA));
        long minutosDisponibilidade = 0;
        long minutosAtendimento = 0;
        List<Agendamento> agendamentosIndisponiveis = getService().obterAgendamentos(key, professor, dash, inicio, fim, false, null, null,null, null);
        List<Agendamento> agendamentosDisponiveis = getService().obterAgendamentos(key, professor, dash, inicio, fim, true, null, null,null, null);
        for (Agendamento agendamentoDisponivel : agendamentosDisponiveis){
            long min = Uteis.minutosEntreDatas(agendamentoDisponivel.getInicio(), agendamentoDisponivel.getFim());
            minutosDisponibilidade = minutosDisponibilidade + min;
            addInformacoesTipo(agendamentoDisponivel, tipos, min);
        }
        for (Agendamento agendamentoIndisponivel : agendamentosIndisponiveis) {
            long min = Uteis.minutosEntreDatas(agendamentoIndisponivel.getInicio(), agendamentoIndisponivel.getFim());
            minutosAtendimento = minutosAtendimento + (min == 59 ? 60 : min);
            dash.setAgendamentos(dash.getAgendamentos()+1);
            addInformacoesTipo(agendamentoIndisponivel, tipos, min);
            if (agendamentoIndisponivel.getStatus() != null && agendamentoIndisponivel.getStatus().equals(StatusAgendamentoEnum.EXECUTADO)) {
                dash.setCompareceram(dash.getCompareceram()+1);
            } else if (agendamentoIndisponivel.getStatus() != null && agendamentoIndisponivel.getStatus().equals(StatusAgendamentoEnum.CANCELADO)) {
                dash.setCancelaram(dash.getCancelaram()+1);
            } else if (agendamentoIndisponivel.getStatus() != null && agendamentoIndisponivel.getStatus().equals(StatusAgendamentoEnum.FALTOU)) {
                dash.setFaltaram(dash.getFaltaram()+1);
            }
            if(agendamentoIndisponivel.getTipoEvento()!=null){
                if(agendamentoIndisponivel.getTipoEvento().getComportamento() != null
                        && agendamentoIndisponivel.getTipoEvento().getComportamento().equals(TipoAgendamentoEnum.AVALIACAO_FISICA)){
                    dash.setAvaliacoesFisicas(dash.getAvaliacoesFisicas()+1);
                }
            }

        }
        dash.setHorasAtendimento(new Double(minutosAtendimento/60).intValue());
        dash.setHorasDisponibilidade(new Double(minutosDisponibilidade/60).intValue());
        dash.setOcupacao(dash.getHorasDisponibilidade() == 0 ? 0 : (new Double((dash.getHorasAtendimento().doubleValue()/dash.getHorasDisponibilidade())*100)).intValue());
        Calendar data = Calendar.getInstance();
        for(TipoEventoDisponibilidadeBI tedb : tipos.values()){
            tEDao.insert(key, tedb);
        }
        dash.setNovosTreinos(obterNumeroProgramasAgendamento(key, professor, dash.getEmpresa(), inicio, fim, TipoAgendamentoEnum.PRESCRICAO_TREINO));
        dash.setTreinosRenovados(obterNumeroProgramasAgendamento(key, professor, dash.getEmpresa(), inicio, fim, TipoAgendamentoEnum.RENOVAR_TREINO));
        dash.setTreinosRevisados(obterNumeroProgramasAgendamento(key, professor, dash.getEmpresa(), inicio, fim, TipoAgendamentoEnum.REVISAO_TREINO));
        boolean treinoIndependente = br.com.pacto.controller.json.base.SuperControle.independente(key);
        FiltrosDashboard filtros = new FiltrosDashboard();
        filtros.setEmpresa(empresa);
        processarProfessor(key, professor, filtros, dash, fim, treinoIndependente, inicio, fim);
        processarAvaliacoesFisicas(key, professor, filtros, dash, true, treinoIndependente, inicio, fim);
        processarAvaliacoesFisicas(key, professor, filtros, dash, false, treinoIndependente, inicio, fim);
        dash.setPercentualAvaliacoes(dash.getTotalAlunosAtivos() == 0
                ? 0
                : (dash.getTotalAlunosAvaliacoes() * 100) / dash.getTotalAlunosAtivos());
    }

    private void processarProfessor(final String key, final Integer professor,
                                    final FiltrosDashboard filtros, DashboardBI dash, Date fim, boolean treinoIndependente, Date dataInicio, Date dataFim) throws Exception {
        Map<String, Object> p = new HashMap<>();
        StringBuilder where = new StringBuilder();
        if (dash.getCodigoProfessor() != null && dash.getCodigoProfessor() > 0){
            where.append(" where codigo = :codigoProfessor");
            p.put("codigoProfessor", professor);
        }else{
            where.append(" WHERE obj.codigo IN (SELECT DISTINCT ag.professor.codigo FROM Agendamento ag ");
            where.append(" WHERE inicio BETWEEN :inicio AND :fim) ");
            if (dataInicio == null && dataFim == null) {
                p.put("inicio", Calendario.getDataComHoraZerada(Uteis.somarDias(Calendario.hoje(), -(filtros.getDiasParaTras()))));
                p.put("fim", Calendario.getDataComHora(Calendario.hoje(), "23:59"));
            } else {
                p.put("inicio", Calendario.getDataComHoraZerada(dataInicio));
                p.put("fim", Calendario.getDataComHora(dataFim, "23:59"));
            }

            if (filtros.getEmpresa() > 0) {
                p.put("empresa", filtros.getEmpresa());
                where.append(" and obj.empresa.codZW = :empresa");
            }
        }
        dash.setProfessores(professorDao.countWithParam(key,"codigo",where,p).intValue());
    }

    public void addInformacoesTipo(Agendamento agendamento, Map<TipoAgendamentoEnum, TipoEventoDisponibilidadeBI> tipos,
            Long minutos) {
        TipoEventoDisponibilidadeBI tipo = tipos.get((agendamento.getTipoEvento() != null &&
                agendamento.getTipoEvento().getCodigo() != null &&
                agendamento.getCodigo() > 0) ? agendamento.getTipoEvento().getComportamento() :
                TipoAgendamentoEnum.getFromId(agendamento.getHorarioDisponibilidade().getDisponibilidade().getComportamento()));
        if(agendamento.getDisponibilidade()){
            tipo.setDisponibilidade(tipo.getDisponibilidade() + minutos);
        }else if (agendamento.getStatus() != null && agendamento.getStatus().equals(StatusAgendamentoEnum.EXECUTADO)) {
            tipo.setExecutaram(tipo.getExecutaram() + minutos);
        } else if (agendamento.getStatus() != null && agendamento.getStatus().equals(StatusAgendamentoEnum.CANCELADO)) {
            tipo.setCancelaram(tipo.getCancelaram() + minutos);
        } else if (agendamento.getStatus() != null && agendamento.getStatus().equals(StatusAgendamentoEnum.FALTOU)) {
            tipo.setFaltaram(tipo.getFaltaram() + minutos);
        } else if (agendamento.getStatus() != null && agendamento.getStatus().equals(StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO)) {
            tipo.setAguardandoConfirmacao(tipo.getAguardandoConfirmacao() + minutos);
        } else if (agendamento.getStatus() != null && agendamento.getStatus().equals(StatusAgendamentoEnum.CONFIRMADO)) {
            tipo.setConfirmado(tipo.getConfirmado() + minutos);
        } else if (agendamento.getStatus() != null && agendamento.getStatus().equals(StatusAgendamentoEnum.REAGENDADO)) {
            tipo.setReagendado(tipo.getReagendado() + minutos);
        }
    }

    @Override
    public List<TreinoRealizado> obterListaExecucoes(final String key, boolean smartphone, Date dia, DashboardBI dash) throws Exception {
        Map<String, Object> p = new HashMap<String, Object>();
        p.put("inicio", Calendario.getDataComHoraZerada(dia));
        p.put("fim", Calendario.getDataComHora(dia, "23:59"));
        p.put("empresa", dash.getEmpresa());

        StringBuilder where = new StringBuilder();
        where.append(" WHERE obj.cliente.empresa = :empresa and obj.dataInicio BETWEEN :inicio AND :fim ");
        if(smartphone){
            p.put("origem", OrigemExecucaoEnum.SMARTPHONE);
            where.append(" AND obj.origem = :origem ");
        }
        if (dash.getCodigoProfessor() != null && dash.getCodigoProfessor() > 0) {
            p.put("professor", dash.getCodigoProfessor());
            where.append(" and professor.codigo = :professor ");
        }
        where.append(" ORDER BY obj.dataInicio ");
        return treinoRealizadoDao.findByParam(key, where, p);
    }
    @Override
    public List<AcessoBIJSON> obterListaAcesso(final String key,Date dia, DashboardBI dash, Boolean apenasTreino) throws Exception {
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        ProfessorSintetico professor = professorDao.findById(key, dash.getCodigoProfessor());
        return integracaoWS.obterListaAcessos(url, key, (professor == null ? 0 :  professor.getCodigoColaborador()), dia, dash.getEmpresa(),apenasTreino);
    }

    @Override
    public List<ProfessorSintetico> obterProfessoresAgenda(final String key,DashboardBI dash,
                final FiltrosDashboard filtros, final Date dia, PaginadorDTO paginadorDTO, String filter, Date inicio, Date fim) throws Exception {
        if (dash.getCodigoProfessor() != null && dash.getCodigoProfessor() > 0){
            Map<String, Object> p = new HashMap<>();
            StringBuilder where = new StringBuilder();
            StringBuilder hql = new StringBuilder();
            where.append(" where codigo = :codigoProfessor");
            p.put("codigoProfessor", dash.getCodigoProfessor());
            return montarPaginadorDTO(key, where, hql, p, paginadorDTO, professorDao,dash.getCodigoProfessor());

        }
        boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
        JSONObject objJson = new JSONObject();
        if (filter != null){
            filter = Uteis.retirarAcentuacaoRegex(filter);
            objJson = new JSONObject(filter);
        }
        Map<String, Object> p = new HashMap<>();
        if (inicio == null && fim == null) {
            p.put("inicio", Calendario.getDataComHoraZerada(Uteis.somarDias(dia, -(filtros.getDiasParaTras()))));
            p.put("fim", Calendario.getDataComHora(dia, "23:59"));
        } else {
            p.put("inicio", Calendario.getDataComHoraZerada(inicio));
            p.put("fim", Calendario.getDataComHora(fim, "23:59"));
        }
        StringBuilder where = new StringBuilder();
        where.append(" WHERE obj.codigo IN (SELECT DISTINCT ag.professor.codigo FROM Agendamento ag ");
        where.append(" WHERE inicio BETWEEN :inicio AND :fim) ");
        if (buscaRapida){
            where.append(" AND (");
            String pesquisa = objJson.getString("quicksearchValue");
            try {
                Integer codigo = Integer.valueOf(pesquisa);
                where.append(" codigo = :codigo ");
                p.put("codigo", codigo);
                where.append(" OR");
            } catch (NumberFormatException ignore) {
            }
            where.append(" UPPER(nome) LIKE UPPER(CONCAT(:nome,'%')))");
            p.put("nome", pesquisa.replaceAll(" ", "%"));
        }
        StringBuilder hql = new StringBuilder();

        if (filtros.getEmpresa() > 0) {
            p.put("empresa", filtros.getEmpresa());
            where.append(" and obj.empresa.codZW = :empresa");
        }

        if (paginadorDTO != null) {
            return montarPaginadorDTO(key, where, hql, p, paginadorDTO, professorDao, dash.getCodigoProfessor());
        }

        return professorDao.findByParam(key, where, p);
    }

    @Override
    public List<Agendamento> obterAgendamentos(final String key, final Integer professor,
            DashboardBI dash,
                                               Date inicio,
                                               Date fim,
                                               Boolean disponibilidade,
            TipoAgendamentoEnum tipo, StatusAgendamentoEnum status, PaginadorDTO paginadorDTO, String filter) throws Exception {
        Map<String, Object> p = new HashMap<>();
        boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
        JSONObject objJson = new JSONObject();
        if (filter != null){
            filter = Uteis.retirarAcentuacaoRegex(filter);
            objJson = new JSONObject(filter);
        }
        Date dataInicioPesquisa = Calendario.getDataComHoraZerada(inicio);
        Date dataFimPesquisa = Calendario.getDataComHora(fim, "23:59");
        p.put("inicio", dataInicioPesquisa);
        p.put("fim", dataFimPesquisa);

        StringBuilder where = new StringBuilder();
        where.append(" WHERE inicio BETWEEN :inicio AND :fim and obj.professor is not null ");
        if (professor != null && professor > 0) {
            p.put("professor", professor);
            where.append(" and professor.codigo = :professor ");
        }
        if (buscaRapida){
            where.append(" AND (");
            String pesquisa = objJson.getString("quicksearchValue");
            where.append(" UPPER(obj.cliente.nome) LIKE UPPER(CONCAT(:nome,'%')))");
            p.put("nome", pesquisa.replaceAll(" ", "%"));
        }
        if (buscaRapida){
            where.append(" AND (");
            String pesquisa = objJson.getString("quicksearchValue");
            where.append(" UPPER(obj.cliente.nome) LIKE UPPER(CONCAT(:nome,'%')))");
            p.put("nome", pesquisa.replaceAll(" ", "%"));
        }
        if(disponibilidade != null && disponibilidade){
            where.append(" AND disponibilidade is true");
        }else if(disponibilidade != null){
            where.append(" AND disponibilidade is false");
            p.put("empresa",dash.getEmpresa());
            where.append(" AND obj.cliente.empresa = :empresa ");
            if(status != null){
                where.append(" AND status = :sit");
                p.put("sit", status);
            }
        }

        if(tipo != null){
            p.put("tipo", tipo);
            where.append(" AND obj.tipoEvento != null AND obj.tipoEvento.comportamento = :tipo ");
        }

        StringBuilder hql = new StringBuilder();
        if (paginadorDTO != null){
            return montarPaginadorDTO(key, where, hql, p, paginadorDTO, agendamentoDao,dash.getCodigoProfessor());
        }
        return agendamentoDao.findByParam(key, where, p);
    }


    @Override
    public List<HistoricoRevisaoProgramaTreino> obterRevisoes(final String key, final Integer professor,
            DashboardBI dash, Date inicio, Date fim, PaginadorDTO paginadorDTO, String filter) throws Exception {
        Map<String, Object> p = new HashMap<>();
        p.put("inicio", Calendario.getDataComHoraZerada(inicio));
        p.put("fim", Calendario.getDataComHora(fim, "23:59"));
        p.put("tipo", TipoAgendamentoEnum.REVISAO_TREINO);
        boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
        JSONObject objJson = new JSONObject();
        if (filter != null){
            filter = Uteis.retirarAcentuacaoRegex(filter);
            objJson = new JSONObject(filter);
        }

        StringBuilder where = new StringBuilder();
        where.append(" WHERE dataRegistro BETWEEN :inicio AND :fim ");
        if(professor != null && professor > 0){
            where.append(" AND professorRevisou.codigo = :professor ");
            p.put("professor", professor);
        }
        where.append(" AND cliente.codigo IN (SELECT DISTINCT ag.cliente.codigo FROM Agendamento ag ");
        where.append(" WHERE inicio BETWEEN :inicio AND :fim ");
        where.append(" AND ag.tipoEvento.comportamento = :tipo )");
        if (buscaRapida){
            where.append(" AND (");
            String pesquisa = objJson.getString("quicksearchValue");
            try {
                Integer matricula = Integer.valueOf(pesquisa);
                where.append(" matricula = :matricula ");
                p.put("matricula", matricula);
                where.append(" OR");
            } catch (NumberFormatException ignore) {
            }
            where.append(" UPPER(nome) LIKE UPPER(CONCAT(:nome,'%')))");
            p.put("nome", pesquisa.replaceAll(" ", "%"));
        }
        StringBuilder hql = new StringBuilder();
        if (paginadorDTO != null) {
            return montarPaginadorDTO(key, where, hql, p, paginadorDTO, historicoRevisaoDao,dash.getCodigoProfessor());
        }
        where.append(" ORDER BY cliente.nome ");
        return historicoRevisaoDao.findByParam(key, where, p);
    }

    @Override
    public List<ProgramaTreino> obterTreinosAgenda(final String key, final Integer professor,
            DashboardBI dash,Date inicio, Date fim, boolean renovados, PaginadorDTO paginadorDTO, String filter) throws Exception {

        Map<String, Object> p = new HashMap<String, Object>();
        p.put("inicio", Calendario.getDataComHoraZerada(inicio));
        p.put("fim", Calendario.getDataComHora(fim, "23:59"));
        p.put("tipo", renovados ? TipoAgendamentoEnum.RENOVAR_TREINO : TipoAgendamentoEnum.PRESCRICAO_TREINO);
        boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
        JSONObject objJson = new JSONObject();
        if (filter != null){
            filter = Uteis.retirarAcentuacaoRegex(filter);
            objJson = new JSONObject(filter);
        }

        StringBuilder where = new StringBuilder();
        where.append(" WHERE dataLancamento BETWEEN :inicio AND :fim ");
        if(professor != null && professor > 0){
            where.append(" AND professorCarteira.codigo = :professor ");
            p.put("professor", professor);
        }
        where.append(" AND cliente.codigo IN (SELECT DISTINCT ag.cliente.codigo FROM Agendamento ag ");
        where.append(" WHERE inicio BETWEEN :inicio AND :fim ");
        where.append(" AND ag.tipoEvento.comportamento = :tipo )");
        if (buscaRapida){
            where.append(" AND (");
            String pesquisa = objJson.getString("quicksearchValue");
            try {
                Integer matricula = Integer.valueOf(pesquisa);
                where.append(" cliente.codigo = :matricula ");
                p.put("matricula", matricula);
                where.append(" OR");
            } catch (NumberFormatException ignore) {
            }
            where.append(" UPPER(cliente.nome) LIKE UPPER(CONCAT(:nome,'%')))");
            p.put("nome", pesquisa.replaceAll(" ", "%"));
        }
        StringBuilder hql = new StringBuilder();
        if (paginadorDTO != null) {
            return montarPaginadorDTO(key, where, hql, p, paginadorDTO, programaTreinoDao,dash.getCodigoProfessor());
        }
        where.append(" ORDER BY cliente.nome ");
        return programaTreinoDao.findByParam(key, where, p);
    }


    public Integer obterNumeroProgramasAgendamento(final String key, final Integer professor,
            Integer codEmpresaZw, Date inicio, Date fim, TipoAgendamentoEnum tipo) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(pro.codigo) FROM ");
        sql.append(tipo.equals(TipoAgendamentoEnum.REVISAO_TREINO) ? "historicorevisaoprogramatreino" : "programatreino");
        sql.append(" pro \n");
        sql.append(" INNER JOIN clientesintetico cli ON cli.codigo = pro.cliente_codigo AND cli.empresa = ");
        sql.append(codEmpresaZw).append("\n");
        sql.append(" LEFT JOIN professorsintetico ps ON cli.professorsintetico_codigo = ps.codigo \n");
        sql.append(" WHERE pro.");
        sql.append(tipo.equals(TipoAgendamentoEnum.REVISAO_TREINO) ? "dataregistro" : "datalancamento");
        sql.append(" between '");
        sql.append(Calendario.getDataComHoraZerada(inicio));
        sql.append("' \n and '");
        sql.append(Calendario.getDataComHora(fim, "23:59"));
        sql.append("' \n");
        if (professor > 0){
            sql.append(" AND ps.codigo = ");
            sql.append(professor);
            sql.append("\n");
        }
        sql.append(" AND pro.cliente_codigo IN ( \n");
        sql.append(" select cliente_codigo FROM agendamento ag \n");
        sql.append(" INNER JOIN tipoevento te ON te.codigo = ag.tipoevento_codigo AND te.comportamento = ");
        sql.append(tipo.ordinal());
        sql.append(" WHERE inicio between '");
        sql.append(Calendario.getDataComHoraZerada(inicio));
        sql.append("' \n and '");
        sql.append(Calendario.getDataComHora(fim, "23:59")).append("' \n");
        if (professor > 0) {
            sql.append(" and ag.professor_codigo = ");
            sql.append(professor);
            sql.append(" \n");
        }
        sql.append(")");
        List<BigInteger> todos = agendamentoDao.listOfObjects(key, sql.toString());
        return todos == null || todos.isEmpty() ? 0 : todos.get(0).intValue();
    }

    public List<DashboardBI> obterListaParaGrafico(final String ctx, final Integer empresa, final Integer professor) throws ServiceException{
          try {
            String sql = " SELECT obj FROM DashboardBI obj\n" +
                    "WHERE obj.codigoProfessor = "+professor+"\n" +
                    "AND obj.empresa = " + empresa + "\n" +
                    "ORDER BY obj.ano DESC, obj.mes DESC, obj.dia DESC LIMIT 1";
            return dashboardDao.findByParam(ctx, sql, new HashMap<String, Object>());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
   }

   @Override
   public void gravarGrupoIndicador(String ctx, String nome, List<ItemGraficoTO> itens, List<String> profs) throws ServiceException{
         try {
            if(UteisValidacao.emptyString(nome)){
                 throw new Exception("nomesalvargrupografico");
            }
            String professores = "";
            for(String s : profs){
                professores += ";"+s;
            }
            professores = professores.replaceFirst(";", "");
            for(ItemGraficoTO i : itens){
                if(i.isSelecionado()){
                    ItemGrupoIndicadores igi = new ItemGrupoIndicadores();
                    igi.setNome(nome);
                    igi.setIndicador(i.getIndicador());
                    igi.setTipo(i.getTipo());
                    igi.setProfessores(professores);
                    itemGrupoDao.insert(ctx, igi);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
   }

   @Override
   public List<ItemGrupoIndicadores> obterGruposIndicadores(final String ctx) throws ServiceException{
       try {
            return itemGrupoDao.findAll(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
   }

   @Override
   public void excluirGrupo(final String ctx, final String nome) throws ServiceException{
        try {
            itemGrupoDao.deleteComParam(ctx, new String[]{"nome"}, new Object[]{nome});
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
   }

    @Override
   public ConfiguracaoRankingProfessores gravarCfgRanking(final String ctx, final ConfiguracaoRankingProfessores cfg) throws ServiceException{
       try {
           if(cfg.getCodigo() != null && cfg.getCodigo() > 0){
               return cfgRankingDao.update(ctx, cfg);
           }
           return cfgRankingDao.insert(ctx, cfg);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
   }

   public List<IndicadorRanking> reloadIndicadores(final String ctx) throws ServiceException{
       try {
           indicadorRankingDao.deleteAll(ctx);
           for(IndicadorDashboardEnum ind : IndicadorDashboardEnum.values()){
               IndicadorRanking indicador = new IndicadorRanking();
               indicador.setLabel(ind.getLabelSort());
               indicador.setIndicador(ind);
               indicadorRankingDao.insert(ctx, indicador);
           }
           return indicadorRankingDao.findAll(ctx);
       } catch (Exception e) {
           throw new ServiceException(e.getMessage());
       }


   }

    @Override
   public void excluirCfgRanking(final String ctx, final ConfiguracaoRankingProfessores cfg) throws ServiceException{
       try {
           cfgRankingDao.delete(ctx, cfg);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
   }



    @Override
   public List<ConfiguracaoRankingProfessores> obterCfgRanking(final String ctx, PaginadorDTO paginadorDTO, final Integer empresa, JSONObject filters, boolean apenasAtivas) throws ServiceException{
       try {
           StringBuilder sql = new StringBuilder();
           sql.append(" SELECT obj.* FROM ConfiguracaoRankingProfessores obj \n");
           sql.append(" left join indicadorranking i on i.indicador = obj.indicador \n");
           sql.append(" where obj.empresa = ").append(empresa);
           if(apenasAtivas){
               sql.append(" and obj.ativo is true ");
           }
           if(filters != null && !UteisValidacao.emptyString(filters.optString("quicksearchValue"))){
               sql.append(" and unaccent(lower(i.label)) ilike  unaccent('%").append(filters.getString("quicksearchValue").toLowerCase()).append("%')");
           }
           String count = sql.toString().replace("obj.*", "count(obj.codigo) as total");
           if(paginadorDTO != null){
               if(!paginadorDTO.getSQLOrderBy().isEmpty()){
                   sql.append(paginadorDTO.getSQLOrderBy().replace("operacao", "positivo").replace("indicador", "i.label"));
               } else {
                   sql.append(" ORDER by codigo desc");
               }
               sql.append(" LIMIT ").append(paginadorDTO.getSize());
               sql.append(" OFFSET ").append(paginadorDTO.getSize() * paginadorDTO.getPage()).append(" ");
           }

           cfgRankingDao.getCurrentSession(ctx).clear();
           List<ConfiguracaoRankingProfessores> cfgs = new ArrayList<>();
           try (ResultSet rs = cfgRankingDao.createStatement(ctx, sql.toString())) {
               while (rs.next()) {
                   ConfiguracaoRankingProfessores cfg = new ConfiguracaoRankingProfessores();
                   cfg.setCodigo(rs.getInt("codigo"));
                   cfg.setEmpresa(rs.getInt("empresa"));
                   cfg.setIndicador(IndicadorDashboardEnum.getFromOrdinal(rs.getInt("indicador")));
                   cfg.setPositivo(rs.getBoolean("positivo"));
                   cfg.setAtivo(rs.getBoolean("ativo"));
                   cfg.setPeso(rs.getDouble("peso"));
                   cfgs.add(cfg);
               }
           }

           if(paginadorDTO != null){
               try {
                   try (ResultSet rsCount = cfgRankingDao.createStatement(ctx, count)) {
                       paginadorDTO.setQuantidadeTotalElementos(new Integer(rsCount.next() ? rsCount.getInt("total") : 0).longValue());
                   }
               }catch (Exception e){
                   Uteis.logar(e, DashboardServiceImpl.class);
               }
           }
           return cfgs;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
   }

    public void validarDadosConfiguracaoRankingProfessores(final String ctx, final ConfiguracaoRankingProfessores object) throws ValidacaoException {
        if (object.getIndicador() == null) {
            throw new ValidacaoException("validacao.indicador");
        }
        if (UteisValidacao.emptyNumber(object.getPeso())) {
            throw new ValidacaoException("validacao.peso");

        }
    }



    private void deletarAcessosExecucoesBI(String key, Integer professor, Date dataProcessamento, FiltrosDashboard filtros) throws Exception {
       String sql = "select obj from AcessosExecucoesBI obj\n"
                + "where obj.professor = :professor\n"
                + "AND empresa = :empresa\n"
                + "AND dia >= :inicio\n"
                + "AND dia <= :fim";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("professor", professor);
        params.put("empresa", filtros.getEmpresa());
        params.put("inicio", Uteis.somarDias(dataProcessamento, -filtros.getDiasParaTras()));
        params.put("fim", dataProcessamento);
        List<AcessosExecucoesBI> lista = acessosDao.findByParam(key, sql, params);
        for(AcessosExecucoesBI acessosBI : lista){
            acessosDao.delete(key, acessosBI);
        }
    }

    private List<AcessosExecucoesBI> obterDeletarAcessosExecucoesBI(String key, Integer professor, Date dataProcessamento, FiltrosDashboard filtros) throws Exception {
        String sql = "select obj from AcessosExecucoesBI obj\n"
                + "where obj.professor = :professor\n"
                + "AND empresa = :empresa\n"
                + "AND dia >= :inicio\n"
                + "AND dia <= :fim";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("professor", professor);
        params.put("empresa", filtros.getEmpresa());
        params.put("inicio", Uteis.somarDias(dataProcessamento, -filtros.getDiasParaTras()));
        params.put("fim", dataProcessamento);
        return acessosDao.findByParam(key, sql, params);

    }


    @Override
    public void ajustarAcessosExecucoesBI(String chave, Date data) throws ServiceException{

        try {
            List<Empresa> empresas = empresaDao.findAll(chave);
            ConfiguracaoSistema cfgDias = configSistemaService.consultarPorTipo(chave, ConfiguracoesEnum.PERIODO_USADO_BI);

            FiltrosDashboard filtros = new FiltrosDashboard();
            filtros.setDiasParaFrente(cfgDias.getValorAsInteger());
            filtros.setDiasParaTras(cfgDias.getValorAsInteger());

            Integer iDia = Uteis.getDiaMesData(data);
            Integer iMes = Uteis.getMesData(data);
            Integer iAno = Uteis.getAnoData(data);

            final List<String> erros = new ArrayList<String>();
            for (Empresa empresa : empresas) {
                List<ProfessorSintetico> professores = ps.consultarProfessores(chave, empresa.getCodZW());
                filtros.setEmpresa(empresa.getCodigo());
                for (ProfessorSintetico professor : professores) {
                    diasSemanaDao.deleteComParam(chave, new String[]{"mes", "ano", "codigoProfessor", "empresa"}, new Object[]{iMes, iAno, professor.getCodigo(), filtros.getEmpresa()});
                    clienteDashDao.deleteComParam(chave, new String[]{"professor", "empresa", "indicador"}, new Object[]{professor.getCodigo(), filtros.getEmpresa(), IndicadorDashboardEnum.AVALIACOES});
                    deletarAcessosExecucoesBI(chave, professor.getCodigo(),data,filtros);

                    DashboardBI dash = new DashboardBI();
                    dash.setInicioProcessamento(Calendario.hoje());
                    dash.setCodigoProfessor(professor.getCodigo());
                    dash.setEmpresa(filtros.getEmpresa());
                    dash.setAno(iAno);
                    dash.setMes(iMes);
                    dash.setDia(iDia);
                    dash.setDiasConfiguracao(filtros.getDiasParaFrente());
                    processarTreinosRealizados(chave, professor.getCodigo(), filtros, dash, data, true);
                }
            }

        }catch(Exception e){
            throw  new ServiceException(e);
        }

    }

    @Override
    public void importarAlunosForaTreino(final String chave, final Integer empresa) throws ServiceException {
        if(!"true".equalsIgnoreCase(Aplicacao.getProp(chave, Aplicacao.importandoClientesZw+empresa))) {
            new Thread() {
                @Override
                public void run() {
                    try {
                        Aplicacao.putProp(chave, Aplicacao.importandoClientesZw+empresa, Boolean.TRUE.toString());
                        sincronizacaoEmpresaService.sincronizarAtivosEmpresas(chave);
                    } catch (Exception e) {
                        Uteis.logar(e, this.getClass());
                    }finally {
                        Aplicacao.putProp(chave, Aplicacao.importandoClientesZw+empresa, Boolean.FALSE.toString());
                    }
                }
            }.start();
        }
    }

    @Override
    public void importarAlunosForaTreinoAll(final String chave, final Integer empresa) throws ServiceException {
        if(!"true".equalsIgnoreCase(Aplicacao.getProp(chave, Aplicacao.importandoClientesZw+empresa))) {
            new Thread() {
                @Override
                public void run() {
                    try {
                        Aplicacao.putProp(chave, Aplicacao.importandoClientesZw+empresa, Boolean.TRUE.toString());
                        List<ClienteBIJSON> clientes = consultarClientesForaTreino(chave, empresa);
                        if (clientes != null && !clientes.isEmpty()) {
                            for (ClienteBIJSON cliente : clientes) {
                                try {
                                    clienteSinteticoService.addAluno(chave, cliente.getMatricula(), cliente.getCodigoProfessor(), false, 0, empresa, false);
                                } catch (Exception e) {
                                    Uteis.logar(e, this.getClass());
                                }
                            }
                        }
                    } catch (Exception e) {
                        Uteis.logar(e, this.getClass());
                    }finally {
                        Aplicacao.putProp(chave, Aplicacao.importandoClientesZw+empresa, Boolean.FALSE.toString());
                    }
                }
            }.start();
        }
    }

    public String importarAlunoForaTreino(final String chave, final Integer empresa, final Integer matricula) throws Exception {
        List<ClienteBIJSON> clientes = consultarClientesForaTreino(chave, empresa);
        if (UteisValidacao.emptyList(clientes)) {
            throw new ServiceException("Cliente não encontrado");
        }

        for (ClienteBIJSON cliente : clientes) {
            if (!cliente.getMatricula().equals(matricula)) {
                continue;
            }
            return clienteSinteticoService.addAluno(chave, cliente.getMatricula(), cliente.getCodigoProfessor(), false, 0, empresa, false);
        }

        throw new ServiceException("Cliente não encontrado");
    }

    @Override
    public void replicarTreinoEmCasa(final String chave, final String documentKey) throws ServiceException {
            new Thread() {
                @Override
                public void run() {
                    try {
                        ReplicadorTreinoEmCasa replicadorTreinoEmCasa = new ReplicadorTreinoEmCasa();
                        replicadorTreinoEmCasa.replicar(chave, documentKey);
                    } catch (Exception e) {
                        Uteis.logar(e, this.getClass());
                    }finally {
                    }
                }
            }.start();
    }

    @Override
    public void processarRankingBIProfessoresEmpresa(String chave, Date data, Integer empresaId) throws ServiceException {
        try{
            ConfiguracaoSistema cfgDias = configSistemaService.consultarPorTipo(chave, ConfiguracoesEnum.PERIODO_USADO_BI);
            processarParaleloBIProfessoresEmpresa(chave, empresaId, cfgDias, data);
        }catch(ServiceException s){
            throw s;
        } catch(Exception e){
            throw new ServiceException(e);
        }

    }

    private void processarParaleloBIProfessoresEmpresa(String chave, Integer empresaId, ConfiguracaoSistema cfgDias, Date data) throws  Exception {
        FiltrosDashboard filtros = new FiltrosDashboard();
        filtros.setDiasParaFrente(cfgDias.getValorAsInteger());
        filtros.setDiasParaTras(cfgDias.getValorAsInteger());
        filtros.setEmpresa(empresaId);
        List<ProfessorSintetico> professores = ps.consultarProfessores(chave, empresaId, true);
        final int mes = Uteis.getMesData(data), ano = Uteis.getAnoData(data), dia = Uteis.getDiaMesData(data);
        final int mesAtual = Uteis.getMesData(Calendario.hoje()), anoAtual = Uteis.getAnoData(Calendario.hoje());
        if (mesAtual != mes || anoAtual != ano) {
            LocalDate dataInicial = LocalDate.of(ano, mes, dia);
            LocalDate dataUltimoDia = dataInicial.withDayOfMonth(dataInicial.lengthOfMonth());
            ZoneId defaultZoneId = ZoneId.systemDefault();
            data = Date.from(dataUltimoDia.atStartOfDay(defaultZoneId).toInstant());
        }
        int total = professores.size();
        int atual = 0;
        for (ProfessorSintetico professor : professores) {
            atual++;
            if (atual == 1) {
                System.out.println("INICIANDO");
            }
            processarGestao(chave, professor.getCodigo(), data, filtros, true, false, empresaId);
            System.out.println("Finalizado processamento BI professor " + professor.getCodigo() + " do dia " + Calendario.getData(data, "dd/MM/yyyy") + " às " + Calendario.getData("dd/MM/yyyy HH:mm:ss") + " processado: " + atual + " de " + total);
        }
        processarGestao(chave, 0, data, filtros, true, false, empresaId);
        System.out.println("FIM");
    }

    public List<AvaliacaoTreinoDTO> obterAvaliacoesTreino(final String key, Integer idProfessor,DashboardBI dash, Date dataInicio, Date fim, PaginadorDTO paginadorDTO, String filtros, Integer empresaId, IndicadorDashboardEnum indicadorSelecionado) throws Exception {
        try {
            Map<String, Object> p = new HashMap<String, Object>();
            Date inicio = Calendario.inicioMes(Uteis.somarDias(fim, - 30));
            fim = Calendario.fimMes(Calendario.getDataComHora(fim, "23:59"));
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT * FROM ( \n");
            sql.append("    SELECT DISTINCT ON (tr.codigo, matricula, professorCarteiraApresentar) cl.matricula as matricula, cl.nome as nomeAluno, tr.datainicio as dataHoraInicioApresentar, tr.comentario as comentario, tr.nota as nota, ps.nome as professorCarteiraApresentar  \n");
            sql.append("    FROM treinorealizado tr \n");
            sql.append("    INNER JOIN clientesintetico cl ON cl.codigo = tr.cliente_codigo  \n");
            sql.append("    LEFT JOIN professorsintetico ps ON ps.codigo = cl.professorsintetico_codigo  \n");
            sql.append("    WHERE cl.empresa = ").append(empresaId).append(" \n");
            sql.append("    AND tr.dataInicio >= '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd HH:mm:ss")).append("' \n");
            sql.append("    AND tr.dataFim <= '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd HH:mm:ss")).append("'  \n");
            sql.append("    AND ps.nome IS NOT NULL  \n");
            if (idProfessor != null && idProfessor > 0) {
                sql.append("    AND cl.professorsintetico_codigo NOTNULL  \n");
                sql.append("    AND cl.professorsintetico_codigo = ").append(idProfessor).append(" \n");
            }

            switch (indicadorSelecionado) {
                case AVALIACOES:
                    sql.append("    AND tr.nota <> '0' \n");
                    break;
                case ESTRELAS_1:
                    sql.append("    AND tr.nota = '1' \n");
                    break;
                case ESTRELAS_2:
                    sql.append("    AND tr.nota = '2' \n");
                    break;
                case ESTRELAS_3:
                    sql.append("    AND tr.nota = '3' \n");
                    break;
                case ESTRELAS_4:
                    sql.append("    AND tr.nota = '4' \n");
                    break;
                default:
                    sql.append("    AND tr.nota = '5' \n");
                    break;
            }

            JSONObject objJson = new JSONObject(filtros);
            String search = objJson.optString("quicksearchValue");
            if (!search.isEmpty()) {
                sql.append("    AND (");
                sql.append("        UPPER(cl.nome) LIKE UPPER('%").append(search).append("%') \n");
                sql.append("        OR UPPER(tr.comentario) LIKE UPPER('%").append(search).append("%') \n");
                sql.append("        OR UPPER(tr.nota) LIKE UPPER('%").append(search).append("%') \n");
                sql.append("        OR UPPER(ps.nome) LIKE UPPER('%").append(search).append("%') \n");
                sql.append("    ) \n");
            }

            sql.append(") sub \n");

            if (paginadorDTO.getSort() != null) {
                String[] sort = paginadorDTO.getSort().split(",");
                sql.append(" ORDER BY ").append(sort[0]).append(" ").append(sort[1]);
            }

            List<AvaliacaoTreinoDTO> listaAvaliacoes;
            try (ResultSet rs = treinoRealizadoDao.createStatement(key, sql.toString())) {
                listaAvaliacoes = new ArrayList();
                while (rs.next()) {
                    AvaliacaoTreinoDTO tr = new AvaliacaoTreinoDTO();
                    tr.setMatricula(rs.getInt("matricula"));
                    tr.setNomeAluno(rs.getString("nomeAluno"));
                    tr.setDataHoraInicioApresentar(rs.getTimestamp("dataHoraInicioApresentar"));
                    tr.setComentario(rs.getString("comentario"));
                    tr.setNota(rs.getString("nota"));
                    tr.setProfessorCarteiraApresentar(rs.getString("professorCarteiraApresentar"));
                    listaAvaliacoes.add(tr);
                }
            }

            paginadorDTO.setQuantidadeTotalElementos((long) listaAvaliacoes.size());
            return paginadorAvaliacoesTreino(listaAvaliacoes, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public List<AvaliacaoAcompanhamentoDTO> obterAvaliacoesAcompanhamento(final String key, Integer idProfessor, Date dataInicio, Date fim, PaginadorDTO paginadorDTO, String filtros, Integer empresaId, Integer tipoBusca) throws Exception {
        try {
            if (dataInicio == null) {
                dataInicio = Calendario.inicioMes(Uteis.somarDias(Calendario.hoje(), -30));
            }
            if (fim == null) {
                fim = Calendario.fimMes(Calendario.getDataComHora(Calendario.hoje(), "23:59"));
            }

            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT cs.matricula as matricula, cs.nome as nomeAluno, ca.inicio as dataHoraInicioApresentar, ");
            sql.append(" aval.comentario as comentario, aval.nota as nota, prof.nome as professorCarteiraApresentar ");
            sql.append(" FROM clienteacompanhamentoavaliacao aval ");
            sql.append(" JOIN clienteacompanhamento ca ON aval.cliente_acompanhamento_codigo = ca.codigo ");
            sql.append(" JOIN clientesintetico cs ON ca.cliente_codigo = cs.codigo ");
            sql.append(" LEFT JOIN professorsintetico prof ON ca.professor_codigo = prof.codigo ");
            sql.append(" WHERE cs.empresa = ").append(empresaId);
            sql.append(" AND ca.inicio >= '").append(Uteis.getDataAplicandoFormatacao(dataInicio, "yyyy-MM-dd HH:mm:ss")).append("'");
            sql.append(" AND ca.inicio <= '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd HH:mm:ss")).append("' ");
            sql.append(" AND prof.nome IS NOT NULL ");

            if (idProfessor != null && idProfessor > 0) {
                sql.append(" AND prof.codigo = ").append(idProfessor);
            }

            if (tipoBusca > 0) {
                sql.append(" AND aval.nota = ").append(tipoBusca);
            } else {
                sql.append(" AND aval.nota IS NOT NULL");
            }

            if (filtros != null && !filtros.isEmpty()) {
                JSONObject objJson = new JSONObject(filtros);
                String search = objJson.optString("quicksearchValue");
                if (!search.isEmpty()) {
                    sql.append(" AND (");
                    sql.append(" UPPER(cs.nome) LIKE UPPER('%").append(search).append("%')");
                    sql.append(" OR UPPER(aval.comentario) LIKE UPPER('%").append(search).append("%')");
                    sql.append(" OR UPPER(prof.nome) LIKE UPPER('%").append(search).append("%')");
                    sql.append(" ) ");
                }
            }

            if (paginadorDTO != null && paginadorDTO.getSort() != null && !paginadorDTO.getSort().isEmpty()) {
                String[] sort = paginadorDTO.getSort().split(",");
                sql.append(" ORDER BY ").append(sort[0]).append(" ").append(sort[1]);
            } else {
                sql.append(" ORDER BY dataHoraInicioApresentar DESC");
            }

            List<AvaliacaoAcompanhamentoDTO> listaAvaliacoes;
            try (ResultSet rs = treinoRealizadoDao.createStatement(key, sql.toString())) {
                listaAvaliacoes = new ArrayList<>();
                while (rs.next()) {
                    AvaliacaoAcompanhamentoDTO dto = new AvaliacaoAcompanhamentoDTO();
                    dto.setMatricula(rs.getInt("matricula"));
                    dto.setNomeAluno(rs.getString("nomeAluno"));
                    dto.setDataHoraInicioApresentar(rs.getTimestamp("dataHoraInicioApresentar"));
                    dto.setComentario(rs.getString("comentario"));
                    dto.setNota(rs.getString("nota"));
                    dto.setProfessorCarteiraApresentar(rs.getString("professorCarteiraApresentar"));
                    listaAvaliacoes.add(dto);
                }
            }

            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos((long) listaAvaliacoes.size());
            }

            return listaAvaliacoes;

        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public List<AvaliacaoTreinoDTO> paginadorAvaliacoesTreino(List<AvaliacaoTreinoDTO> listaAvaliacoes, PaginadorDTO paginadorDTO) {
        List<AvaliacaoTreinoDTO> listaAvaliacoesPaginada = new ArrayList();
        if (paginadorDTO.getSize() != null){
            if (paginadorDTO.getSize() > 0){
                int i = 0;
                int iPaginador = 0;
                long pular;
                for ( AvaliacaoTreinoDTO av : listaAvaliacoes){
                    pular = paginadorDTO.getPage() * 10;
                    if (paginadorDTO.getPage() > 0){
                        if (iPaginador >= pular){
                            listaAvaliacoesPaginada.add(av);
                            i++;
                        }
                        iPaginador++;
                    } else {
                        listaAvaliacoesPaginada.add(av);
                        i++;
                        iPaginador++;
                    }
                    if (i == paginadorDTO.getSize() || listaAvaliacoes.size() - iPaginador == 0){
                        paginadorDTO.setQuantidadeTotalElementos((long) listaAvaliacoes.size());
                        return listaAvaliacoesPaginada;
                    }
                }
            }
        }
        return listaAvaliacoes;
    }


    @Override
    public List<ClienteSintetico> consultarAlunosTreinoPrograma(
            final String key, DashboardBI dash, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter, Integer idProfessor) throws Exception {
        ClienteDashBI clientes = obterCodigos(key, dash, indicador);
        if (clientes == null || clientes.getClientes() == null || clientes.getClientes().isEmpty()) {
            return new ArrayList<>();
        }
        boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        HashMap<String, Object> param = new HashMap<>();
        JSONObject objJson = new JSONObject();
        if (filter != null){
            filter = Uteis.retirarAcentuacaoRegex(filter);
            objJson = new JSONObject(filter);
        }
        hql.append("SELECT distinct obj.codigo, obj.situacao, obj.matricula,obj.nome as nomeCliente,ps.nome as nomeProfessor, obj.dataultimoacesso as dataUltimoacesso, pt.dataTerminoPrevisto, " +
                " COALESCE(NULLIF(p.fotokey, ''), obj.fotokeyapp) as fotokeyapp " +
                " FROM ClienteSintetico obj\n" +
                " inner join professorsintetico ps on obj.professorsintetico_codigo = ps.codigo\n" +
                " inner join programatreino pt on obj.codigo = pt.cliente_codigo  \n" +
                " left join programatreino pt2 on obj.codigo = pt2.cliente_codigo and pt2.dataTerminoPrevisto > pt.dataTerminoPrevisto " +
                " inner join pessoa p on obj.pessoa_codigo = p.codigo ");
        where.append("WHERE pt2.codigo is null and obj.codigo IN (");
        where.append(clientes.getClientes().replaceAll("\\|", ",")).append(")");
        if (idProfessor > 0){
            where.append(" AND obj.professorSintetico_codigo = ").append(idProfessor);
        }
        if (buscaRapida){
            where.append(" AND (");
            String pesquisa = objJson.getString("quicksearchValue");
            try {
                Integer matricula = Integer.valueOf(pesquisa);
                where.append(" matricula = ").append(matricula);
                where.append(" OR ");
            } catch (NumberFormatException ignore) {
            }
            where.append(" UNACCENT(UPPER(obj.nome)) LIKE UPPER(CONCAT('").append(pesquisa.replaceAll(" ", "%")).append("','%')))");
        }
        hql.append(where);
        return montarPaginadorDTO(key, where, hql, param, paginadorDTO, clienteSinteticoDao,
                    clientes.getClientes().split("\\|").length, idProfessor, true);
    }

    public void atualizarHorariosGympass(String chave, Integer empresa){
        if(!"true".equalsIgnoreCase(Aplicacao.getProp(chave, Aplicacao.sincronizandoGympass+empresa))) {
            new Thread() {
                @Override
                public void run() {
                    try {
                        Aplicacao.putProp(chave, Aplicacao.sincronizandoGympass+empresa, Boolean.TRUE.toString());
                        AgendaService agendaService = UtilContext.getBean(AgendaService.class);
                        agendaService.sincronizarTurmasGympassBooking(chave, empresa, null);
                    } catch (Exception e) {
                        Uteis.logar(e, this.getClass());
                    }finally {
                        Aplicacao.putProp(chave, Aplicacao.sincronizandoGympass+empresa, Boolean.FALSE.toString());
                    }
                }
            }.start();
        }
    }


    public List<Integer> obterAtivosNumaData(String chave, Integer empresa, Date data, Integer professor) throws Exception {
        ProfessorSintetico professorSintetico = ps.obterPorId(chave, professor);
        List<Integer> retorno = new ArrayList<>();
        JSONArray ocupacao = chamadaZW(chave,
                "/prest/treino/ativos-profesor-data",
                empresa, null, null, professorSintetico.getCodigoColaborador(), data.getTime());
        if(ocupacao.length()>0){
            for(int i=0;i<ocupacao.length();i++){
                retorno.add(ocupacao.optInt(i));
            }
        }
        return retorno ;
    }

    @Override
    public List<TreinoRealizadoAppDTO> obterTreinosRealizadosPeloAppAgrupandoPorDiaDaSemana(String ctx, Date diaLimite, Integer idProfessor, Integer empresaId) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT EXTRACT(DOW FROM datainicio) AS diasemana, ");
        sql.append(" SUM(CASE WHEN EXTRACT(HOUR FROM datainicio) BETWEEN 6 AND 12 THEN 1 ELSE 0 END) AS manha, ");
        sql.append(" SUM(CASE WHEN EXTRACT(HOUR FROM datainicio) BETWEEN 12 AND 18 THEN 1 ELSE 0 END) AS tarde, ");
        sql.append(" SUM(CASE WHEN EXTRACT(HOUR FROM datainicio) NOT BETWEEN 6 AND 18 THEN 1 ELSE 0 END) AS noite, ");
        sql.append(" COUNT(*) AS totaltreinos ");
        sql.append(" FROM treinorealizado ");
        sql.append(" WHERE datainicio >= '").append(Uteis.getDataFormatoBD(diaLimite)).append(" 00:00:00' ");
        sql.append(" and origem = 2 ");
        if (idProfessor != null && idProfessor > 0) {
            sql.append(" and professor_codigo = ").append(idProfessor);
        }
        sql.append(" GROUP BY diasemana ");
        ResultSet rs = treinoRealizadoDao.createStatement(ctx, sql.toString());
        List<TreinoRealizadoAppDTO> resultadosTreino = new ArrayList();

        while (rs.next()) {
            TreinoRealizadoAppDTO tr = new TreinoRealizadoAppDTO();

            DiasSemana diaRealizado = DiasSemana.getDiaSemanaNumeral(rs.getInt("diasemana") + 1);
            tr.setDiaRealizado(diaRealizado);
            PeriodoDiaDTO periodoDiaDTO = new PeriodoDiaDTO();

            periodoDiaDTO.setManhaApp(rs.getInt("manha"));
            periodoDiaDTO.setTardeApp(rs.getInt("tarde"));
            periodoDiaDTO.setNoiteApp(rs.getInt("noite"));
            periodoDiaDTO.setTotalApp(rs.getInt("totaltreinos"));
            tr.setPeriodo(periodoDiaDTO);

            resultadosTreino.add(tr);
        }

        return resultadosTreino;
    }

    @Override
    public List<TreinosExecutadosEAcessosPorDiaVO> obterTreinosRealizadosAgrupandoPorDiaDaSemana(String ctx, Date dataInicio, Integer codigoColaborador) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT EXTRACT(DOW FROM datainicio) AS diasemana, ");
        sql.append(" COUNT(*) AS totaltreinos ");
        sql.append(" FROM treinorealizado tr");
        sql.append(" JOIN clientesintetico cs ON tr.cliente_codigo = cs.codigo ");
        sql.append(" INNER JOIN professorsintetico ps ON cs.professorsintetico_codigo = ps.codigo ");
        sql.append(" WHERE datainicio >= '").append(Uteis.getDataFormatoBD(dataInicio)).append(" 00:00:00' ");
        sql.append(" AND ps.codigocolaborador = ").append(codigoColaborador);
        sql.append(" GROUP BY diasemana ");
        ResultSet rs = treinoRealizadoDao.createStatement(ctx, sql.toString());
        List<TreinosExecutadosEAcessosPorDiaVO> resultadosTreino = new ArrayList();

        while (rs.next()) {
            TreinosExecutadosEAcessosPorDiaVO tr = new TreinosExecutadosEAcessosPorDiaVO();

            DiasSemana diaRealizado = DiasSemana.getDiaSemanaNumeral(rs.getInt("diasemana") + 1);
            tr.setDiaRealizado(diaRealizado);
            tr.setQuantidadeTreinos(rs.getInt("totaltreinos"));

            resultadosTreino.add(tr);
        }


        List<TreinosExecutadosEAcessosPorDiaVO> resultadosTreinoOrdenado = new ArrayList();
        for (int i = 0; i < 7; i++) {
            DiasSemana dia = DiasSemana.getDiaSemanaNumeral(i + 1);
            TreinosExecutadosEAcessosPorDiaVO treino = new TreinosExecutadosEAcessosPorDiaVO();
            treino.setDiaRealizado(dia);
            treino.setQuantidadeTreinos(0);
            treino.setQuantidadeAcessos(0);

            String sqlStr = "SELECT COUNT(*) AS totalacessos " +
                    "FROM acessocliente ac " +
                    " INNER JOIN cliente cli ON ac.cliente = cli.codigo " +
                    " INNER JOIN vinculo v ON cli.codigo = v.cliente AND v.tipovinculo = 'TW' " +
                    "WHERE ac.dthrentrada BETWEEN '" + Uteis.getDataFormatoBD(dataInicio) + "' AND '" + Uteis.getDataFormatoBD(new Date(System.currentTimeMillis())) + "' " +
                    "  AND v.colaborador = " + codigoColaborador +
                    "  AND EXTRACT(DOW FROM dthrentrada) = " + (i + 1);
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet tabelaResultado = ConexaoZWServiceImpl.criarConsulta(sqlStr, conZW)) {
                    if (tabelaResultado.next()) {
                        treino.setQuantidadeAcessos(tabelaResultado.getInt("totalacessos"));
                    }
                }
            } catch (Exception e) {
                Uteis.logar(e, this.getClass());
            }

            for (TreinosExecutadosEAcessosPorDiaVO tr : resultadosTreino) {
                if (tr.getDiaRealizado().equals(dia)) {
                    treino.setQuantidadeTreinos(tr.getQuantidadeTreinos());

                    break;
                }
            }
            resultadosTreinoOrdenado.add(treino);
        }

        return resultadosTreinoOrdenado;
    }

    @Override
    public List<AlunoSimplesDTO> consultarAlunosTreinoPrograma(String ctx, Date diaLimite, Integer empresa, Integer dia, String periodo, JSONObject filters, PaginadorDTO paginadorDTO) {
        int maxResults = 50;
        int indiceInicial = 0;

        StringBuilder sql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> p = null;
        String nome = null;

        if(filters!=null){
           nome  = filters.optString("quicksearchValue");
        }

        sql.append("select c.codigo, c.nome, c.matricula" +
        " from treinorealizado " +
        " inner join clientesintetico c on c.codigo = treinorealizado.cliente_codigo  ");
        where.append(" WHERE datainicio >= '").append(Uteis.getDataFormatoBD(diaLimite)).append(" 00:00:00' ");
//        where.append(" and origem > 0 ");
        where.append(" and empresa = ").append(empresa);
        where.append(" and EXTRACT(DOW FROM datainicio) =   ").append(dia);
        if(periodo.equals("Manhã")){
            where.append(" and (CASE WHEN EXTRACT(HOUR FROM datainicio) BETWEEN 0 AND 12 THEN 1 ELSE 0 END) = 1 ");
        }else
        if(periodo.equals("Tarde")){
            where.append(" and (CASE WHEN EXTRACT(HOUR FROM datainicio) BETWEEN 13 AND 18 THEN 1 ELSE 0 END) = 1 ");
        }else
        if(periodo.equals("Noite")){
            where.append(" and (CASE WHEN EXTRACT(HOUR FROM datainicio) NOT BETWEEN 0 AND 18 THEN 1 ELSE 0 END) = 1 ");
        }
        if(nome !=null){
            where.append(" and upper(C.NOME) LIKE '%" + nome.toUpperCase() + "%'");
        }

        sql.append(where.toString());
        if (paginadorDTO != null) {
            try {
                StringBuilder sqlCount = new StringBuilder();
                sqlCount.append(sql.toString().replaceFirst("select c.codigo, c.nome, c.matricula", "SELECT COUNT(*) "));
                ResultSet rsCount = null;
                Long totalCount = 0L;

                rsCount = treinoRealizadoDao.createStatement(ctx, sqlCount.toString());
                while (rsCount.next()) {
                    totalCount = rsCount.getLong("count");
                }
                maxResults = paginadorDTO.getSize() == null ? maxResults : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;

                if ((paginadorDTO.getSortMap() != null) && (paginadorDTO.getSortMap().size() > 0)) {
                    for (Map.Entry<String, String> entry : paginadorDTO.getSortMap().entrySet()) {
                        String colunaOrdenar = entry.getKey();
                        String direcao = entry.getValue();
                        sql.append(" order by ").append(colunaOrdenar).append(" "+direcao);
                    }
                }else{
                    sql.append(" order by c.nome asc ");
                }

                paginadorDTO.setQuantidadeTotalElementos(totalCount);
                sql.append(" LIMIT ").append(maxResults);
                sql.append(" OFFSET ").append(indiceInicial);

                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        ResultSet rs = null;
        try {
            rs = treinoRealizadoDao.createStatement(ctx, sql.toString());
            List<AlunoSimplesDTO> listaClienteSinteticos = new ArrayList<>();
            while (rs.next()) {
                AlunoSimplesDTO cliente = new AlunoSimplesDTO();

                cliente.setNome(rs.getString("nome"));
                cliente.setMatricula(rs.getInt("matricula"));
                listaClienteSinteticos.add(cliente);
            }
            return listaClienteSinteticos;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

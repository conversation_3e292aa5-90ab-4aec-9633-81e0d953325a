# Armazenar configuracoes Globais para o perfeito funcionamento do Sistema
# Ao mudar essas configuracoes, para que entrem em vigor,
# deve-se invocar o metodo br.com.pacto.objeto.Aplicacao.init()

version=@VERSION@
urlZillyonWeb = <into OAMD>
urlOAMD = http://host.docker.internal:8073/NewOAMD
discoveryUrls = http://host.docker.internal:8101
urlOAMDSegura = @URL_OAMD_SEGURA@
urlAdmApp = @URL_ADM_APP@
urlOAMDApiUsuarioApp=http://app.pactosolucoes.com.br/oamd
urlServicoDadosApp=https://us-central1-app-do-aluno-unificado.cloudfunctions.net
#urlAdmApp = http://localhost:8084/AdmAPP
keyUnitTests = @KEY_TESTS@
#keyUnitTests = in-memory
urlFeedPacto=@URL_FEED@
urlObterBanners = @URL_BANNERS@
urlLogin= @URL_LOGIN@
instanceSchedulingName=disable
redirecionar=@REDIR@
url_redirecionar=@URL_REDIR@
loadFactoryOnStart = @LOAD_FACTORY_START@
instanciasPropagar=host.docker.internal:8201
loadInstancesFromCloud=@LOAD_INSTANCES_CLOUD@
forceUrlFotos=@FORCE_URL_FOTOS@
cookieFailover=@COOKIE_FAIL@
dirMidiasEmbedded=@DIR_MIDIAS_EMBEDDED@
diretorioFotos = /opt/zw-photos/
diretorioArquivos = /opt/ZW_ARQ/
fotosParaNuvem=false
typeMidiasService=AWS_S3
urlFotosNuvem=https://s3-sa-east-1.amazonaws.com/prod-zwphotos
urlArquivoNuvem=@URL_ARQUIVOS_NUVEM@
redirecionarLogin=@REDIRECIONAR_LOGIN@
urlTreinoGooglePlay=https:\/\/play.google.com/store/apps/details?id=com.pacto
urlTreinoAppleStore=http:\/\/itunes.apple.com/us/app/treino/id862662527
urlSolicitacao=https://app.pactosolucoes.com.br/myUCP/atendimento
#smtpEmailNoReply=@SMTP_EMAIL_NOREPLY@
#smtpLoginRobo=@SMTP_LOGIN_ROBO@
#smtpSenhaRobo=@SMTP_SENHA_ROBO@
#smtpConexaoSeguraRobo=@SMTP_CONEXAOSEGURA_ROBO@
#smtpServerRobo=@SMTP_SERVER_ROBO@
smtpEmailNoReply=<EMAIL>
smtpEmailRobo=<EMAIL>
smtpLoginRobo=pactosolucoes2017
smtpSenhaRobo=RlOHgUGw8905
smtpConexaoSeguraRobo=true
smtpServerRobo=smtplw.com.br
buscarConhecimentoUCP=@BUSCAR_CONHECIMENTO_UCP@
usarUrlRecursoEmpresa = @USAR_URL_RECURSO_EMPRESA@
myUpUrlBase=@MY_URL_UP_BASE@
senhaPactoBr=123
senhaPactoBrTeste=123
balancaIniciar=http://localhost:1717/ControladorBalanca/ControleBalanca.LigarBalanca
balancaResultados=http://localhost:3000/peso
urlsocketaux=http://mock.pactosolucoes.com.br:8084
urlMidias=http://app.pactosolucoes.com.br/midias
AUTH_SECRET_PATH=C:\\opt\\council\\elrond.txt
AUTH_SECRET_PERSONA_PATH=C:\\opt\\council\\elrond.txt
urlAPIGymPassBookingToken=https://identity.gympass.com/auth/realms/master/protocol/openid-connect/token
urlAPIGymPassBookingSandbox=https://api.partners.gympass-staging.com
urlAPIGymPassBookingProduction=https://api.partners.gympass.com
tokenGymPassBookingWebhook=VDRLM05fUDRDdDBfR1lNUEFTUw==
tokenGymPassBookingBase=T4K3N_P4Ct0_GYMPASS
tokenGymPassV3=@TOKEN_GYMPASS_V3@
hibernate.hbm2ddl.auto=update
URL_HTTPS_PLATAFORMA_PACTO=@URL_HTTPS_PLATAFORMA_PACTO@
URL_HTTP_PLATAFORMA_PACTO=@URL_HTTP_PLATAFORMA_PACTO@
NUMEROS_WHATSAPP_PACTO=@NUMEROS_WHATSAPP_PACTO@
urlZillyonWebIntegProp=http://host.docker.internal:8200/ZillyonWeb
useBetaTesters=@USE_BETA_TESTERS@
instanceClassAllowDDL=@INSTANCE_ALLOW_DDL@
ipServidoresMemCached=@SERVIDOR_MEMCACHED@
enableNewLogin=@ENABLE_NEW_LOGIN@
ambienteTeste=@AMBIENTE_TESTE@
tokenPushMobile=******************
tokenPushAppAluno=sistemapacto
urlAppDoAlunoUnificado=https://app-do-aluno-unificado.web.app
tokenAcessoAPICliente=@TOKENS_ACESSO_API_CLIENTE@
logarDao=@LOGAR_DAO@
modoConsultaAgenda=@MODO_CONSULTA_AGENDA@
consultaContratoAssinaturaDigital=@CONSULTA_CONTRATO_ASSINATURA@
consultaUsuariosAppPeloFireBase=@CONSULTA_USUARIOS_APP_PELO_FIREBASE@
modoConsultaDashBI=@MODO_CONSULTA_DASHBI@
maximumPoolSize=300
tempoOciosoPool=10000
aesSecretKeyAppTreino=@AES_SECRET_APP_TREINO@

totalPassApi=https://api.totalpass.com/service/v1/track_usages
totalPassApiValidate=https://api.totalpass.com/service/v1/track_usages/validate
urlApiStaging = https://staging.totalpass.com/api/v1/track_usages
urlValidateStaging = https://staging.totalpass.com/api/v1/track_usages/validate
CHAVE_CRIPTO_ZW_UI=SisTeP4ctoZwUi25
urlTreinoIa=https://treino-ia-1048804581122.us-east1.run.app
accessTokenTreinoIa=d41d8cd98f00b204e9800998ecf8427e